from decimal import Decimal

from flask import current_app

from app.common.onchain import <PERSON>linePeriod
from app.common.onchain import TokenKline

from app.business.onchain.client import DataInterfaceHelper
from app.business.onchain.token import get_token_info
from app.business.prices import PriceManager

from app.caches.onchain import OnchainKlineCache
from app.caches.onchain import OnchainUpdateKlineMinCache
from app.caches.onchain import OnchainUpdateKlineHourCache
from app.caches.onchain import OnchainUpdateKlineDayCache
from app.caches.onchain import OnchainKlinePenetrationCache
from app.caches.onchain import OnchainInitKlineCache
from app.caches.onchain import OnchainTokenUpdatedAtBaseCache
from app.caches.onchain import OnchainTokenUpdatedAtKlineMinCache
from app.caches.onchain import OnchainTokenUpdatedAtKlineHourCache
from app.caches.onchain import OnchainTokenUpdatedAtKlineDayCache
from app.caches.onchain import OnchainCurrencyKlineCache

from app.utils.date_ import current_timestamp
from app.utils.onchain import normalise_time
from app.utils.onchain import decimal_add
from app.utils.onchain import decimal_div


def update_token_kline(helper: DataInterfaceHelper, token_id: int, period: KlinePeriod):
    token_info = get_token_info(token_id)
    if not token_info or not token_info.top_pool:
        return

    cache = OnchainKlineCache(token_id, period)

    latest_time = cache.latest_time()
    if not latest_time:
        OnchainInitKlineCache().add(token_id)  # 数据缺失时按照初始化K线的逻辑执行
        return
    if current_timestamp(to_int=True) - latest_time >= period.expired_seconds:
        # 数据过老时清除过期缓存并按照初始化K线的逻辑执行
        cache.clear()
        OnchainInitKlineCache().add(token_id)
        return

    try:
        kline_data = helper.get_kline(token_info.top_pool, period, start_time=latest_time - 10 * period.seconds)
        if len(kline_data) > 0:
            cache.set(kline_data)
    except Exception as err:
        current_app.logger.error(f'update token kline error: {err}')


class OnchainKlineHelper:
    UPDATE_API_LIMIT = 1  # 每次更新时接口请求调用第三方接口的次数
    INIT_API_LIMIT = 2  # 每次初始化时接口请求调用第三方接口的次数

    UPDATE_KLINE_MAP = {
        KlinePeriod.MIN: [OnchainUpdateKlineMinCache],
        KlinePeriod.HOUR: [OnchainUpdateKlineMinCache, OnchainUpdateKlineHourCache],
        KlinePeriod.DAY: [OnchainUpdateKlineHourCache, OnchainUpdateKlineDayCache],
    }   # 小时顺便更新分钟, 天顺便更新小时

    INTERVAL_EXPIRED_SECONDS_MAP = {
        KlinePeriod.MIN.seconds: 60 + 60,
        5 * KlinePeriod.MIN.seconds: 5 * 60,
        15 * KlinePeriod.MIN.seconds: 15 * 60,
        KlinePeriod.HOUR.seconds: 3600 + 60,
        4 * KlinePeriod.HOUR.seconds: 4 * 3600,
        KlinePeriod.DAY.seconds: 86400 + 60,
    }

    def __init__(self, token_id: int, interval: int, data_helper: DataInterfaceHelper, currency: str = None):
        self.token_id = token_id
        self.interval = interval
        self.period = self._get_period(self.interval)
        self.cache = OnchainKlineCache(self.token_id, self.period)
        self.data_helper = data_helper
        self.currency = currency if currency and currency in OnchainCurrencyKlineCache.SUPPORT_CURRENCY else None

    @classmethod
    def _get_period(cls, interval: int) -> KlinePeriod:
        if interval // KlinePeriod.DAY.seconds >= 1:
            return KlinePeriod.DAY
        elif interval // KlinePeriod.HOUR.seconds >= 1:
            return KlinePeriod.HOUR
        else:
            return KlinePeriod.MIN

    @classmethod
    def init_kline(cls, t: int, close_price: Decimal) -> TokenKline:
        return TokenKline(
            t=t,
            o=close_price,
            h=close_price,
            l=close_price,
            c=close_price,
            v=Decimal(),
        )

    @classmethod
    def merge_kline(cls, t: int, kline_list: list[TokenKline]) -> TokenKline | None:
        if len(kline_list) == 0:
            return None
        kline_list.sort(key=lambda x: x.t, reverse=False)
        k = kline_list[0]
        t, o, h, low, c, v = t, k.o, k.h, k.l, k.c, k.v
        for item in kline_list[1:]:
            v = decimal_add(v, item.v)
            c = item.c
            if item.h > h:
                h = item.h
            if item.l < low:
                low = item.l
        return TokenKline(
            t=t,
            o=o,
            h=h,
            l=low,
            c=c,
            v=v,
        )

    def init_data(self) -> list[TokenKline]:
        token_info = get_token_info(self.token_id)
        if not token_info:
            return []
        if not token_info.top_pool:
            return []

        try:
            init_kline_data = self.data_helper.init_kline(token_info.top_pool, self.period, limit=self.INIT_API_LIMIT)
        except Exception as err:
            current_app.logger.error(
                f'onchain kline api init_data err: {err}, token_id: {self.token_id}, pool: {token_info.top_pool}'
            )
            return []
        self.cache.set(init_kline_data)

        OnchainKlinePenetrationCache().add(self.token_id)

        return init_kline_data

    def update_data(self, update_start_time: int, update_end_time: int) -> list[TokenKline]:
        token_info = get_token_info(self.token_id)
        if not token_info:
            return []
        if not token_info.top_pool:
            return []
        if update_end_time <= int(token_info.top_pool_launch_time.timestamp()):
            return []

        try:
            update_kline_data = self.data_helper.get_kline(
                token_info.top_pool,
                self.period,
                start_time=update_start_time,
                end_time=update_end_time,
            )
        except Exception as err:
            current_app.logger.error(
                f'onchain kline api update_data err: {err}, token_id: {self.token_id}, pool: {token_info.top_pool}'
            )
            return []
        self.cache.set(update_kline_data)

        OnchainKlinePenetrationCache().add(self.token_id)

        return update_kline_data

    def get_data(self, start_time: int, end_time: int) -> dict[int, TokenKline]:
        data = self.cache.get(start_time, end_time)
        if not data:
            # 未命中任何缓存有两种情况, 一种是缓存为空, 另一种是查询的时间范围在缓存最早的时间之前(即K线请求时间不连续)
            # 缓存为空时正常更新缓存数据, K线请求时间不连续时忽略该请求
            if self.cache.exist():
                return data
            kline_data = self.init_data()
        else:
            data_start = min(data.keys())
            if data_start <= start_time:
                return data
            seconds = self.period.seconds
            update_end_time = data_start - seconds  # 以缓存的最早数据作为更新数据的结束时间
            update_start_time = update_end_time - self.UPDATE_API_LIMIT * self.data_helper.kline_limit() * seconds
            kline_data = self.update_data(update_start_time, update_end_time)

        for item in kline_data:
            if item.t >= start_time:
                data[item.t] = item

        return data

    def _update_at_cache(self) -> OnchainTokenUpdatedAtBaseCache:
        return {
            KlinePeriod.MIN: OnchainTokenUpdatedAtKlineMinCache,
            KlinePeriod.HOUR: OnchainTokenUpdatedAtKlineHourCache,
            KlinePeriod.DAY: OnchainTokenUpdatedAtKlineDayCache,
        }[self.period]()

    def check_cache_expired(self):
        """检查K线上次更新时间, 不同的interval对应的过期秒数阈值不一样, 判断数据较长时间未更新时间先自动更新K线最新数据"""
        at_cache = self._update_at_cache()
        expired_seconds = self.INTERVAL_EXPIRED_SECONDS_MAP[self.interval]
        if at_cache.need_update(self.token_id, cycle=expired_seconds):
            update_token_kline(self.data_helper, self.token_id, self.period)
            at_cache.update_one(self.token_id)
            current_app.logger.info(f'update onchain kline cache, token_id: {self.token_id}, period: {self.period}')

    @classmethod
    def _get_decimals(cls, d: Decimal, p: Decimal) -> int:
        exp = d.as_tuple().exponent
        decimals = -exp if exp < 0 else 0

        p_tup = p.as_tuple()
        total_digits = len(p_tup.digits)
        frac_digits = -p_tup.exponent if p_tup.exponent < 0 else 0
        return decimals + max(total_digits - frac_digits, 1)

    def _merge_kline(self, data: dict[int, TokenKline], start_time: int, end_time: int) -> list[TokenKline]:
        if len(data) == 0:
            return []
        result = []
        seconds = self.period.seconds
        limit = int(self.interval / seconds)
        last_kline = None
        update_at = normalise_time(self.interval, self._update_at_cache().get_one(self.token_id))
        if self.interval not in {KlinePeriod.MIN.seconds, KlinePeriod.HOUR.seconds, KlinePeriod.DAY.seconds}:
            update_at += self.interval
        range_end_time = max(max(data.keys()) + self.interval, update_at)
        range_end_time = min(range_end_time, end_time + self.interval)
        for i in range(int((range_end_time - start_time) / self.interval)):
            k_t = start_time + i * self.interval
            k_list = []
            for j in range(limit):
                t = k_t + j * seconds
                if t not in data:
                    continue
                k_list.append(data[t])
            k_info = self.merge_kline(k_t, k_list)
            if not k_info:
                if not last_kline:
                    continue
                k_info = self.init_kline(k_t, last_kline.c)
            result.append(k_info)
            last_kline = k_info
        return result

    def to_currency_price(self, full_kline: list[TokenKline], start_time: int, end_time: int) -> list[TokenKline]:
        if not self.currency or len(full_kline) == 0:
            return full_kline
        currency_data = OnchainCurrencyKlineCache(self.currency, self.period).get(start_time, end_time)
        merge_currency_data = {item.t: item for item in self._merge_kline(currency_data, start_time, end_time)}

        curr_price = PriceManager().asset_to_usd(self.currency)
        if len(merge_currency_data) > 0:
            curr_price = merge_currency_data[max(merge_currency_data.keys())].c
        latest_k = full_kline[-1]
        p_decimals = self._get_decimals(latest_k.o, curr_price)
        currency_kline = []
        for k in full_kline[::-1]:
            # 从end向start更新
            if k.t in merge_currency_data:
                currency_k = merge_currency_data[k.t]
                curr_price = currency_k.c
                item_o, item_h, item_l, item_c = currency_k.o, currency_k.h, currency_k.l, currency_k.c
            else:
                item_o, item_h, item_l, item_c = curr_price, curr_price, curr_price, curr_price
            currency_kline.append(TokenKline(
                t=k.t,
                o=decimal_div(k.o, item_o, decimals=p_decimals),
                h=decimal_div(k.h, item_l, decimals=p_decimals),
                l=decimal_div(k.l, item_h, decimals=p_decimals),
                c=decimal_div(k.c, item_c, decimals=p_decimals),
                v=k.v,
            ))
        return currency_kline[::-1]

    def merge_min_or_hour_kline(self, full_kline: list[TokenKline]) -> list[TokenKline]:
        """如果是小时/天级K线数据, 当前时间的K线数据应该通过分钟/小时线聚合"""
        if self.period == KlinePeriod.MIN:
            return full_kline
        if len(full_kline) == 0:
            return full_kline
        seconds = self.interval
        curr_ts = normalise_time(self.interval, current_timestamp(to_int=True))
        last_ts = curr_ts - seconds
        last_last_ts = last_ts - seconds
        next_ts = curr_ts + seconds
        if full_kline[-1].t not in {last_last_ts, last_ts, curr_ts}:
            # 当前时间K线与上一个时间K线按照分钟K线聚合
            return full_kline

        min_data = OnchainKlineCache(self.token_id, {
            KlinePeriod.HOUR: KlinePeriod.MIN,
            KlinePeriod.DAY: KlinePeriod.HOUR,
        }[self.period]).get(last_ts, next_ts)
        last_klines = []
        curr_klines = []
        for t, k in min_data.items():
            if last_ts < t < curr_ts:
                last_klines.append(k)
            elif curr_ts < t < next_ts:
                curr_klines.append(k)
        last_kline = self.merge_kline(last_ts, last_klines)
        curr_kline = self.merge_kline(curr_ts, curr_klines)
        if not last_kline or not curr_kline:
            return full_kline

        if full_kline[-1].t == last_last_ts:
            # 最新数据是上上时间戳时, 补齐之前和当前时间戳数据
            full_kline.append(last_kline)
            full_kline.append(curr_kline)
        elif full_kline[-1].t == last_ts:
            # 最新数据是之前时间戳时, 补齐当前时间戳数据
            full_kline.append(curr_kline)
        else:
            # 最新数据是当前时间戳时, 覆盖当前时间戳数据
            full_kline[-1] = curr_kline

        return full_kline

    def _get_kline_by_close_price(self, start_time: int, end_time: int) -> dict[int, TokenKline]:
        """查询到空数据时, 尝试从最接近start_time的K线获取收盘价补全这段时间的数据"""
        all_ts = self.cache.all_ts()
        if not all_ts:
            return {}
        all_ts.sort(reverse=True)
        close_ts = None
        for ts in all_ts:
            if ts < start_time:
                close_ts = ts
                break
        if not close_ts:
            return {}
        close_kline = self.cache.get_one(close_ts)
        data = {}
        for i in range(int((end_time - start_time) / self.period.seconds)):
            k_t = start_time + i * self.period.seconds
            data[k_t] = self.init_kline(k_t, close_kline.c)
        return data

    def full_kline_by_period(self, start_time: int, end_time: int) -> list[TokenKline]:
        self.check_cache_expired()

        data = self.get_data(start_time, end_time)
        if not data:
            # 尝试从最接近start_time的K线获取收盘价补全这段时间的数据
            data = self._get_kline_by_close_price(start_time, end_time)
            if not data:
                return []

        result = self._merge_kline(data, start_time, end_time)
        return self.to_currency_price(self.merge_min_or_hour_kline(result), start_time, end_time)

    def full_kline(self, start_time: int, end_time: int) -> list[TokenKline]:
        if self.interval not in self.INTERVAL_EXPIRED_SECONDS_MAP:
            return []

        end_time = min(end_time, current_timestamp(to_int=True))
        start_time = normalise_time(self.interval, start_time)
        _end_time = normalise_time(self.interval, end_time)
        if end_time == _end_time:
            end_time = _end_time
        else:
            end_time = _end_time + self.interval
        if start_time > end_time:
            return []

        for cache in self.UPDATE_KLINE_MAP[self.period]:
            cache().add(self.token_id)  # 添加K线更新队列

        return self.full_kline_by_period(start_time, end_time)
