from typing import Iterable

from decimal import Decimal

from enum import Enum

from flask import current_app
from flask_babel import gettext as _

from sqlalchemy import or_

from app.common import CeleryQueues
from app.common.onchain import Chain
from app.common.onchain import TokenBase
from app.common.constants import Language

from app.business.clients.ai_translate import FormatType
from app.business.clients.ai_translate import ModelFamilyName
from app.business.clients.ai_translate import AITranslateClient
from app.business.onchain.base import OnchainAddressHelper
from app.business.onchain.client import Gop<PERSON><PERSON>elper
from app.business.onchain.client import CoingeckoHelper
from app.business.onchain.client import DataInterfaceHelper
from app.business.onchain.client import OnchainSwapClient

from app.models import db
from app.models.onchain import OnchainToken
from app.models.onchain import OnchainTokenInfo
from app.models.onchain import OnchainTokenHotConfig
from app.models.onchain import OnchainTokenAboutSource
from app.models.mongo.translation import TranslationTaskMySQL

from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainUpdateTokenInfoCache
from app.caches.onchain import OnchainInitKlineCache
from app.caches.onchain import OnchainWalletSupportSwapTokenAddressSOLCache
from app.caches.onchain import OnchainTokenUpdatedAtQuoteCache

from app.utils import celery_task
from app.utils import upload_link
from app.utils.date_ import now
from app.utils.net import validate_url
from app.utils.onchain import amount_to_str

_headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/60.0.3112.90 Safari/537.36',
}


class TokenRiskType(Enum):
    IS_NOT_HONEYPOT = 'is_not_honeypot'  # 蜜罐
    IS_REASONABLE_TAX = 'is_reasonable_tax'  # 税费异常
    IS_OPEN_SOURCE = 'is_open_source'  # 开源
    IS_LP_LOCKED = 'is_lp_locked'  # 流动性锁仓
    IS_NOT_OWNER = 'is_not_owner'  # 合约修改
    IS_NOT_MINTABLE = 'is_not_mintable'  # 增发
    IS_NOT_BLACKLISTED = 'is_not_blacklisted'  # 黑名单
    IS_NOT_FREEZABLE = 'is_not_freezable'  # 冻结
    IS_NOT_MUTABLE = 'is_not_mutable'  # 元数据修改
    IS_REASONABLE_HOLDER = 'is_reasonable_holder'  # 持仓集中

    @property
    def title(self) -> str:
        return {
            self.IS_NOT_HONEYPOT: _('蜜罐风险'),
            self.IS_REASONABLE_TAX: _('交易税费异常'),
            self.IS_OPEN_SOURCE: _('合约不透明风险'),
            self.IS_LP_LOCKED: _('流动性池风险'),
            self.IS_NOT_OWNER: _('合约修改风险'),
            self.IS_NOT_MINTABLE: _('增发权限'),
            self.IS_NOT_BLACKLISTED: _('黑名单功能'),
            self.IS_NOT_FREEZABLE: _('冻结权限'),
            self.IS_NOT_MUTABLE: _('元数据修改风险'),
            self.IS_REASONABLE_HOLDER: _('持仓过度集中'),
        }[self]

    def title_msg(self, value: bool | None) -> str:
        if value is None:
            return _('%(title)s未知', title=self.title)
        if value:
            return _('未发现%(title)s', title=self.title)
        else:
            return _('检测到%(title)s', title=self.title)

    def content_msg(self) -> str:
        return {
            self.IS_NOT_HONEYPOT: _('该代币买入后可能无法卖出，是典型的骗局代币，风险极高。'),
            self.IS_REASONABLE_TAX: _('该代币在买入或卖出时，会被合约收取高额的“税”，可能导致亏损。'),
            self.IS_OPEN_SOURCE: _('合约代码不公开，其具体功能不透明，可能隐藏恶意代码或漏洞。'),
            self.IS_LP_LOCKED: _('项目方未锁定资金池，可随时撤走所有资金，导致代币价值归零。'),
            self.IS_NOT_OWNER: _('合约所有者未放弃权限，未来可以修改合约规则，例如暂停交易。'),
            self.IS_NOT_MINTABLE: _('项目方可以随时创建新的代币，可能导致您持有的代币价值被稀释。'),
            self.IS_NOT_BLACKLISTED: _('合约包含黑名单功能，项目方可以将您的地址拉黑，使您无法交易。'),
            self.IS_NOT_FREEZABLE: _('项目方可以冻结任何持有者的代币，导致用户无法转账或交易。'),
            self.IS_NOT_MUTABLE: _('项目方可随时修改代币名称、图标等信息，存在钓鱼欺诈风险。'),
            self.IS_REASONABLE_HOLDER: _('少数地址持有大量代币，若其选择抛售，可能导致代币价格剧烈下跌。'),
        }[self]


def get_risk_msg_list(risk_data: dict, chain: Chain) -> list[dict]:
    risk_list = [risk for risk in TokenRiskType]
    if chain == Chain.SOL:
        risk_list = [
            TokenRiskType.IS_NOT_MINTABLE,
            TokenRiskType.IS_NOT_FREEZABLE,
            TokenRiskType.IS_LP_LOCKED,
            TokenRiskType.IS_NOT_MUTABLE,
            TokenRiskType.IS_REASONABLE_HOLDER,
        ]
    risk_msg = []
    for risk in risk_list:
        if risk.value in risk_data:
            value = risk_data[risk.value]
            risk_msg.append(dict(
                key=risk.value,
                value=value,
                title=risk.title_msg(value),
                content=risk.content_msg(),
            ))
    return risk_msg


def get_token(token_id: int) -> OnchainToken:
    return OnchainToken.query.filter(OnchainToken.id == token_id).first()


def get_token_by_chain_and_contract(chain: Chain, contract: str) -> OnchainToken:
    return OnchainToken.query.filter(
        OnchainToken.chain == chain,
        OnchainToken.contract == OnchainAddressHelper(chain).normalise_address(contract),
    ).first()


def get_token_info(token_id: int) -> OnchainTokenInfo:
    return OnchainTokenInfo.query.filter(OnchainTokenInfo.token_id == token_id).first()


def batch_get_token(token_ids: Iterable[int]) -> dict[int, OnchainToken]:
    return {
        item.id: item
        for item in OnchainToken.query.filter(
            OnchainToken.id.in_(token_ids),
        ).all()
    }


def batch_get_token_info(token_ids: Iterable[int]) -> dict[int, OnchainTokenInfo]:
    return {
        item.token_id: item
        for item in OnchainTokenInfo.query.filter(
            OnchainTokenInfo.token_id.in_(token_ids),
        ).all()
    }


def all_token_by_chain(chain: Chain) -> list[OnchainToken]:
    return OnchainToken.query.filter(OnchainToken.chain == chain).all()


def get_token_about_source(token_id: int) -> OnchainTokenAboutSource | None:
    return OnchainTokenAboutSource.query.filter(
        OnchainTokenAboutSource.token_id == token_id,
    ).order_by(
        OnchainTokenAboutSource.id.desc(),
    ).first()


def is_en_lang(text: str) -> bool:
    if not text:
        return True

    total_chars = len(text)
    english_chars = sum(1 for c in text if ord(c) < 128)

    return (Decimal(english_chars) / Decimal(total_chars)) >= Decimal('0.5')


def lang_classify(text: str) -> Language:
    if is_en_lang(text):
        # ASCII超过一定比例直接认为是英语
        return Language.EN_US

    import langid   # 这玩意导入大概要0.1s

    lang_set = {
        'en': Language.EN_US,
        'zh': Language.ZH_HANS_CN,
        'ja': Language.JA_JP,
        'ru': Language.RU_KZ,
        'ko': Language.KO_KP,
        'id': Language.ID_ID,
        'es': Language.ES_ES,
        'fa': Language.FA_IR,
        'tr': Language.TR_TR,
        'vi': Language.VI_VN,
        'ar': Language.AR_AE,
        'fr': Language.FR_FR,
        'pt': Language.PT_PT,
        'de': Language.DE_DE,
        'th': Language.TH_TH,
        'it': Language.IT_IT,
        'pl': Language.PL_PL,
    }
    for classify in langid.rank(text):
        if classify[0] in lang_set:
            return lang_set[classify[0]]
    return Language.EN_US


@celery_task(queue=CeleryQueues.ONCHAIN)
def send_token_about_translate_task(token_id: int):
    """检查最新的About是否有更新, 更新时新增OnchainTokenAboutSource记录并发起翻译请求"""
    token_info = get_token_info(token_id)
    if not token_info or not token_info.about:
        return
    token_about_source = get_token_about_source(token_id)
    if token_about_source and token_about_source.about == token_info.about:
        return
    if token_info.about.startswith('http') and validate_url(token_info.about):
        return

    source_lang = lang_classify(token_info.about)

    new_token_about_source = OnchainTokenAboutSource(
        token_id=token_id,
        lang=source_lang,
        about=token_info.about,
    )
    db.session_add_and_commit(new_token_about_source)

    translator = AITranslateClient(
        format_type=FormatType.TEXT,
        model_family=ModelFamilyName.GEMINI,
        business=TranslationTaskMySQL.Business.ONCHAIN,
    )
    for target_lang in Language:
        if target_lang == source_lang:
            continue
        try:
            translator.translate_async(
                content=new_token_about_source.about,
                source=source_lang,
                target=target_lang,
                business_id=str(new_token_about_source.id),
            )
            current_app.logger.warning(f'onchain token source translate async complete, '
                                       f'token_id: {token_id}, target_lang: {target_lang}')
        except Exception as e:
            current_app.logger.exception(f'onchain token source translate async complete, '
                                         f'token_id: {token_id}, target_lang: {target_lang}, err: {e}')


def get_token_base_from_coinex_wallet(chain: Chain, token_address: str) -> TokenBase | None:
    """从CoinEx Wallet获取Token基本信息"""
    address_helper = OnchainAddressHelper(chain)
    token_address = address_helper.normalise_address(token_address)
    wallet_chain_type = {
        Chain.SOL: 'SOL',
        Chain.ERC20: 'ETH',
        Chain.BSC: 'BNB',
    }[chain]
    for item in OnchainSwapClient().coin_search(token_address):
        if item['type'] != wallet_chain_type:
            continue
        if address_helper.normalise_address(item['address']) != token_address:
            continue
        return TokenBase(
            chain=chain,
            contract=token_address,
            symbol=item['symbol'],
            name=item['name'],
            decimals=item['decimal'],
            logo=item['logo'],
        )
    return None


def batch_update_token_info(helper: DataInterfaceHelper, token_ids: list[int]):
    token_id_token_map = batch_get_token(token_ids)
    token_id_token_info_map = batch_get_token_info(token_ids)
    if not token_id_token_map or not token_id_token_info_map:
        return

    goplus_helper = GoplusHelper(helper.chain)

    for token_id in token_ids:
        if token_id not in token_id_token_map:
            continue
        token: OnchainToken = token_id_token_map[token_id]
        if token_id not in token_id_token_info_map:
            continue
        token_info: OnchainTokenInfo = token_id_token_info_map[token_id]
        old_about = token_info.about
        try:
            info_data = helper.get_token_info(token.contract)
            token_info.website = info_data.website
            token_info.community = info_data.community
            token_info.about = info_data.about
            token_info.holders_count = info_data.holders_count
            token_info.top10_percentage = info_data.top10_percentage
            risk_data = goplus_helper.get_risk_data(token.contract)
            if risk_data:
                token_info.risk = risk_data
        except Exception as e:
            current_app.logger.warning(f'update token info error: {e}')
        if old_about != token_info.about:
            send_token_about_translate_task.delay(token_id)
    db.session.commit()

    current_app.logger.info(f'batch update {helper.chain.name} info success, number of calls: {helper.number_of_calls}')


def _update_token_logo(token: OnchainToken, logo_url: str):
    if token.logo:
        return
    try:
        token.logo = upload_link(logo_url, headers=_headers)
    except Exception as e:
        current_app.logger.warning(f'upload token logo err: {e}, url: {logo_url}')


def batch_update_token(helper: DataInterfaceHelper, token_addresses: Iterable[str]) -> list[int]:
    token_addresses = helper.address_helper.normalise_addresses(token_addresses)

    token_base_map = {}
    for token_address in token_addresses:
        token_base = get_token_base_from_coinex_wallet(helper.chain, token_address)
        if not token_base:
            continue
        token_base_map[token_address] = token_base
    if len(token_base_map) == 0:
        return []

    token_data_map = {item.contract: item for item in helper.get_token_data_list(list(token_base_map.keys()))}

    update_token_quote_data = {}
    token_ids, new_token_ids = [], []

    for token_address, token_base in token_base_map.items():
        token: OnchainToken = OnchainToken.get_or_create(chain=token_base.chain, contract=token_base.contract)
        new_flag = not token.id
        token.symbol = token_base.symbol
        token.name = token_base.name
        token.decimals = token_base.decimals
        _update_token_logo(token, token_base.logo)
        db.session.add(token)
        db.session.flush()
        if new_flag:
            new_token_ids.append(token.id)
        token_info: OnchainTokenInfo = OnchainTokenInfo.get_or_create(token_id=token.id)
        if token_address in token_data_map:
            token_data = token_data_map[token_address]
            _update_token_logo(token, token_data.logo)
            if token_data.top_pool:
                token_info.top_pool = token_data.top_pool
                token_info.top_pool_launch_time = token_data.top_pool_launch_time
                token_info.top_pool_name = token_data.top_pool_name
                token_info.top_pool_liquidity = amount_to_str(token_data.top_pool_liquidity) \
                    if token_data.top_pool_liquidity else None
            if token_data.total_supply:
                token_info.total_supply = amount_to_str(token_data.total_supply)
            if token_data.circulating_supply:
                token_info.circulating_supply = amount_to_str(token_data.circulating_supply)

            update_token_quote_data[token.id] = token_data
        db.session.add(token_info)

        token_ids.append(token.id)

    db.session.commit()

    if len(update_token_quote_data) > 0:
        OnchainTokenQuoteCache().save_many(update_token_quote_data)

    if len(new_token_ids) > 0:
        OnchainUpdateTokenInfoCache().batch_add(new_token_ids)  # 新增的Token更新一下Info信息
        OnchainInitKlineCache().batch_add(new_token_ids)  # 新增的Token更新一下K线信息

    current_app.logger.info(f'batch update {helper.chain.name} base success, number of calls: {helper.number_of_calls}')

    return token_ids


def batch_get_or_update_token(helper: DataInterfaceHelper, token_addresses: Iterable[str]) -> list[int]:
    """批量获取Token ID, 不存在时自动更新"""
    if not token_addresses:
        return []

    token_addresses = helper.address_helper.normalise_addresses(token_addresses)

    address_token_id_map = {
        item.contract: item.id for item in OnchainToken.query.filter(
            OnchainToken.chain == helper.chain,
            OnchainToken.contract.in_(token_addresses)
        )
    }
    miss_token_addresses = []
    for token_address in token_addresses:
        if token_address not in address_token_id_map:
            miss_token_addresses.append(token_address)

    token_ids = list(address_token_id_map.values())
    update_token_ids = batch_update_token(helper, miss_token_addresses)
    if len(update_token_ids) > 0:
        token_ids.extend(update_token_ids)
    return token_ids


def batch_update_token_quote(helper: DataInterfaceHelper, token_ids: list[int]):
    """批量更新Token的Quote数据"""
    if not token_ids:
        return
    address_to_token_id_map = {
        token.contract: token_id for token_id, token in batch_get_token(token_ids).items()
    }
    if not address_to_token_id_map:
        return

    data = {}
    for quote_data in helper.get_token_quote_list(address_to_token_id_map.keys()):
        if quote_data.contract not in address_to_token_id_map:
            continue
        data[address_to_token_id_map[quote_data.contract]] = quote_data

    if len(data) > 0:
        OnchainTokenQuoteCache().save_many(data)

    current_app.logger.info(f'update {helper.chain.name} quote success, number of calls: {helper.number_of_calls}')


def check_and_get_token_quote(token: OnchainToken) -> dict[str, Decimal]:
    if OnchainTokenUpdatedAtQuoteCache().need_update(token.id):
        batch_update_token_quote(CoingeckoHelper(token.chain), [token.id])
        OnchainTokenUpdatedAtQuoteCache().update_one(token.id)
    return OnchainTokenQuoteCache().get_one(token.id)


def get_or_create_token_by_token_address(chain: Chain, token_address: str) -> int | None:
    token_address = OnchainAddressHelper(chain).normalise_address(token_address)

    token_model: OnchainToken = OnchainToken.query.filter(
        OnchainToken.chain == chain,
        OnchainToken.contract == token_address,
    ).first()
    if token_model:
        return token_model.id

    token_ids = batch_update_token(CoingeckoHelper(chain), [token_address])
    if len(token_ids) == 0:
        return None
    return token_ids[0]


def token_result_from_token_ids(token_ids: list[int], with_is_top: bool = False) -> list[dict]:
    """根据Token ID列表生成Token列表数据返回给前端, with_is_top字段为True时额外查询OnchainTokenHotConfig表添加is_top字段"""
    if len(token_ids) == 0:
        return []
    quote_map = OnchainTokenQuoteCache().get_many(token_ids)
    token_map = batch_get_token(token_ids)
    token_info_map = batch_get_token_info(token_ids)
    is_top_token_ids = set()
    if with_is_top:
        is_top_token_ids = {
            top_config.token_id for top_config in OnchainTokenHotConfig.query.filter(
                OnchainTokenHotConfig.token_id.in_(token_ids),
                or_(
                    OnchainTokenHotConfig.expired_at >= now(),
                    OnchainTokenHotConfig.expired_at.is_(None),
                ),
                OnchainTokenHotConfig.is_top.is_(True),
            ).with_entities(
                OnchainTokenHotConfig.token_id,
            ).all()
        }
    result = []
    for token_id in token_ids:
        if token_id not in token_map or token_id not in token_info_map:
            continue
        token = token_map[token_id]
        token_info = token_info_map[token_id]
        token_quote = quote_map.get(token_id, {})
        item = dict(
            token_id=token_id,
            chain=token.chain.get_display_name(),
            contract=token.contract,
            symbol=token.symbol,
            name=token.name,
            decimals=token.decimals,
            total_supply=token_info.total_supply,
            logo=token.logo,
            holders_count=token_info.holders_count,
            amount_24h=token_quote.get('amount_24h'),
            price=token_quote.get('price'),
            change_24h=token_quote.get('change_24h'),
        )
        if with_is_top:
            item['is_top'] = token_id in is_top_token_ids
        result.append(item)
    return result


def is_support_swap(chain: Chain, token_address: str) -> bool:
    """这里最初设计是根据wallet返回的支持swap的Token列表判断, 但是后面迭代为wallet未返回支持swap的Token依然可以正常swap,
    具体是否能swap要报价阶段才知道, 因此这里统一改为返回True"""
    if chain in {Chain.ERC20, Chain.BSC, Chain.SOL}:
        return True
    return OnchainWalletSupportSwapTokenAddressSOLCache().is_support(
        OnchainAddressHelper(chain).normalise_address(token_address),
    )
