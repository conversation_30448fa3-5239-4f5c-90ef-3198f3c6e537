# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from typing import List

from flask import current_app
from sqlalchemy import func, or_
from werkzeug.datastructures import MultiDict
from app.business.email import send_trade_rank_actitivity_gift_email

from app.config import config
from app.business import lock_call, CacheLock, \
    LockKeys
from app.business.vip import VipHelper
from app.common import CeleryQueues, BalanceBusiness
from app.models import User, SubAccount, BindingAddress, \
    GiftHistory, db, DepositActivity, DepositActivityRank, \
    DepositActivityRule, Deposit, TradeRankActivity, \
    LockedAssetBalance, AdminGiftBalanceHistory
from app.models.operation import DepositBonusActivityUserInfo, TradeRankActivityUserInfo, OperationAmbassadorActivity, \
    AmbassadorActivityUserInfo, ChannelRewardActivity, ChannelRewardHistory
from app.utils import (
    now, celery_task, route_module_to_celery_queue,
    timestamp_to_datetime
)
from .activity import DepositActivityRankProcessor
from .clients import ServerClient
from .push import send_channel_reward_activity_push
from ..exceptions import InvalidArgument


route_module_to_celery_queue(__name__, CeleryQueues.GIFT)


@celery_task
@lock_call(with_args=True)
def add_deposit_gift_task(user_id: int, asset: str, amount: str):
    amount = Decimal(amount)
    user: User = User.query.get(user_id)
    if not user:
        return
    deposit_activities: List[DepositActivity] = DepositActivity.query.filter(
        DepositActivity.status == DepositActivity.Status.PASSED,
        DepositActivity.type.in_(
            [DepositActivity.Type.FIX, DepositActivity.Type.RATE]),
        DepositActivity.deposit_asset == asset,
        DepositActivity.started_at <= now(),
        DepositActivity.ended_at > now()
    ).all()
    if not deposit_activities:
        return
    for deposit_activity in deposit_activities:
        left_amount = deposit_activity.get_left_amount()
        if left_amount <= Decimal('0.0001'):
            deposit_activity.status = DepositActivity.Status.FINISHED
            deposit_activity.left_amount = Decimal()
            db.session.commit()
            continue
        rule: DepositActivityRule = DepositActivityRule.query.filter(
            DepositActivityRule.deposit_activity_id == deposit_activity.id
        ).first()
        if not rule:
            continue
        coin_deposit = Deposit.query.filter(
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.user_id == user_id,
            Deposit.asset == asset,
            or_(
                Deposit.status == Deposit.Status.CONFIRMING,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.status == Deposit.Status.TO_HOT
            ),
            Deposit.created_at >= deposit_activity.started_at,
            Deposit.created_at < deposit_activity.ended_at
        ).with_entities(
            func.sum(Deposit.amount).label('deposit_amount')
        ).first()
        deposit_amount = coin_deposit.deposit_amount \
            if coin_deposit.deposit_amount else Decimal()
        if deposit_amount <= deposit_activity.least_amount:
            continue
        remark = json.loads(rule.rule_data)
        record = GiftHistory.query.filter(
            GiftHistory.activity_id == deposit_activity.activity_id,
            GiftHistory.user_id == user_id,
            GiftHistory.asset == deposit_activity.gift_asset,
            GiftHistory.created_at >= deposit_activity.started_at,
            GiftHistory.created_at < deposit_activity.ended_at,
            or_(
                GiftHistory.status == GiftHistory.Status.CREATED,
                GiftHistory.status == GiftHistory.Status.FINISHED
            )
        ).with_entities(
            func.sum(GiftHistory.amount).label('gift_amount')
        ).first()
        user_total_gift_amount = record.gift_amount \
            if record.gift_amount else Decimal()
        if deposit_activity.type == DepositActivity.Type.RATE:
            user_max_amount = Decimal(remark['rate_upper_limit'])
            if user_total_gift_amount >= user_max_amount:
                continue
            calc_amount = Decimal(amount) * Decimal(remark['rate_gift'])
            user_left_max_amount = user_max_amount - user_total_gift_amount
            real_amount = min(calc_amount, user_left_max_amount, left_amount)
        elif deposit_activity.type == DepositActivity.Type.FIX:
            user_gift_amount = Decimal(remark['fix_gift_amount'])
            if user_total_gift_amount >= user_gift_amount:
                continue
            if deposit_amount < Decimal(remark['fix_threshold']):
                continue
            real_amount = min(user_gift_amount, left_amount)
        else:
            continue
        add_activity_gift(
            user_id, deposit_activity.activity_id, deposit_activity.gift_asset,
            str(real_amount), remark='gift for deposit')
        deposit_activity.left_amount = deposit_activity.get_left_amount()
        if deposit_activity.left_amount <= Decimal('0.0001'):
            deposit_activity.status = DepositActivity.Status.FINISHED
        db.session.commit()


def add_activity_gift(user_id: int, activity_id: int, gift_asset: str,
                      amount: str, remark='gift', commit=True):
    history = GiftHistory(
        user_id=user_id,
        activity_id=activity_id,
        asset=gift_asset,
        amount=amount,
        remark=remark,
        status=GiftHistory.Status.CREATED
    )
    db.session.add(history)
    if commit:
        db.session.commit()


@celery_task
def update_deposit_activity_rank_task(deposit_activity_id):
    with CacheLock(LockKeys.activity_deposit_update_rank(deposit_activity_id),
                   wait=False):
        DepositActivityRankProcessor(deposit_activity_id).process()


@celery_task
@lock_call(with_args=True)
def send_deposit_activity_gift_task(deposit_activity_id, user_id):
    deposit_activity: DepositActivity = DepositActivity.query.filter(
        DepositActivity.id == deposit_activity_id,
        DepositActivity.type.in_([DepositActivity.Type.PARTITION,
                                  DepositActivity.Type.RANK]),
        DepositActivity.status == DepositActivity.Status.FINISHED,
        DepositActivity.ended_at <= now()
    ).first()
    if not deposit_activity:
        return
    rank: DepositActivityRank = DepositActivityRank.query.filter(
        DepositActivityRank.activity_id == deposit_activity.activity_id,
        DepositActivityRank.user_id == user_id
    ).first()
    if not rank:
        return
    if rank.gift_amount <= Decimal():
        return
    has_record = GiftHistory.query.filter(
        GiftHistory.user_id == user_id,
        GiftHistory.asset == deposit_activity.gift_asset,
        GiftHistory.activity_id == deposit_activity.activity_id,
        or_(
            GiftHistory.status == GiftHistory.Status.CREATED,
            GiftHistory.status == GiftHistory.Status.FINISHED
        )
    ).first()
    if has_record:
        return
    add_activity_gift(
        user_id, rank.activity_id, rank.gift_asset, rank.gift_amount,
        remark='gift for deposit')


@celery_task
@lock_call(with_args=True)
def send_trade_activity_gift_task(trade_activity_id, user_id):
    trade_activity: TradeRankActivity = TradeRankActivity.query.filter(
        TradeRankActivity.id == trade_activity_id,
        TradeRankActivity.status == TradeRankActivity.Status.FINISHED,
        TradeRankActivity.ended_at <= now()
    ).first()
    if not trade_activity:
        return
    rank: TradeRankActivityUserInfo = TradeRankActivityUserInfo.query.filter(
        TradeRankActivityUserInfo.trade_activity_id == trade_activity.id,
        TradeRankActivityUserInfo.user_id == user_id
    ).first()
    if not rank:
        return
    if rank.gift_amount <= Decimal():
        return
    has_record = GiftHistory.query.filter(
        GiftHistory.user_id == user_id,
        GiftHistory.asset == trade_activity.gift_asset,
        GiftHistory.activity_id == trade_activity.activity_id,
        or_(
            GiftHistory.status == GiftHistory.Status.CREATED,
            GiftHistory.status == GiftHistory.Status.FINISHED
        )
    ).first()
    if has_record:
        return
    trade_activity.left_gift_amount -= rank.gift_amount
    add_activity_gift(
        user_id, trade_activity.activity_id, rank.gift_asset, rank.gift_amount,
        remark='gift for trade', commit=False)
    db.session.commit()
    send_trade_rank_actitivity_gift_email.delay(user_id, trade_activity_id,
                                                rank.gift_amount, rank.gift_asset)


@celery_task
@lock_call(with_args=True)
def send_ambassador_activity_gift_task(activity_id, user_id):
    activity: OperationAmbassadorActivity = OperationAmbassadorActivity.query.filter(
        OperationAmbassadorActivity.id == activity_id,
        OperationAmbassadorActivity.status == OperationAmbassadorActivity.Status.FINISHED,
        OperationAmbassadorActivity.ended_at <= now()
    ).first()
    if not activity:
        return
    rank: AmbassadorActivityUserInfo = AmbassadorActivityUserInfo.query.filter(
        AmbassadorActivityUserInfo.activity_id == activity.id,
        AmbassadorActivityUserInfo.user_id == user_id
    ).first()
    if not rank:
        return
    if rank.gift_amount <= Decimal():
        return
    has_record = GiftHistory.query.filter(
        GiftHistory.user_id == user_id,
        GiftHistory.asset == activity.gift_asset,
        GiftHistory.activity_id == activity.activity_id,
        or_(
            GiftHistory.status == GiftHistory.Status.CREATED,
            GiftHistory.status == GiftHistory.Status.FINISHED
        )
    ).first()
    if has_record:
        return
    add_activity_gift(
        user_id, activity.activity_id, activity.gift_asset, rank.gift_amount,
        remark='gift for ambassador activity', commit=False)
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def send_deposit_bonus_activity_gift_task(gift_id):
    """发放充值福利活动奖励 - 通过 DepositBonusActivityUserGiftRow 状态控制"""
    from app.business.activity.deposit_bonus import DepositBonusGiftManager

    gift_manager = DepositBonusGiftManager(gift_id)
    if gift_manager.send_user_gifts():
        # 发奖成功后发送邮件通知
        gift_manager.send_reward_email()
    else:
        # 发奖失败，记录日志
        current_app.logger.error(f"Failed to send gifts for activity {gift_id}")


# 保留原函数名作为兼容性接口，但内部调用新的拆分函数
@celery_task
@lock_call(with_args=True)
def send_deposit_bonus_activity_reward_task(activity_id, gift_id, user_id):
    """结算充值福利活动奖励 - 生成 DepositBonusActivityUserGiftRow 记录"""
    from app.business.activity.deposit_bonus import DepositBonusGiftManager
    
    user_info = DepositBonusActivityUserInfo.query.filter(
        DepositBonusActivityUserInfo.id == gift_id,
        DepositBonusActivityUserInfo.deposit_bonus_id == activity_id,
        DepositBonusActivityUserInfo.user_id == user_id
    ).first()
    if not user_info:
        return

    gift_manager = DepositBonusGiftManager(user_info.id)
    if gift_manager.settle_user_gifts():
        # 结算成功后触发发奖任务
        send_deposit_bonus_activity_gift_task.delay(gift_id)
    else:
        # 结算失败，记录日志
        current_app.logger.error(f"Failed to settle gifts for activity {activity_id}, user {user_id}")


@celery_task
@lock_call(with_args=True)
def send_channel_reward_activity_gift_task(channel_reward_activity_id):
    channel_reward_activity = ChannelRewardActivity.query.filter(
        ChannelRewardActivity.id == channel_reward_activity_id,
        ChannelRewardActivity.status == ChannelRewardActivity.Status.FINISHED,
    ).first()
    if not channel_reward_activity:
        return
    user_rows = ChannelRewardHistory.query.filter(
        ChannelRewardHistory.channel_reward_activity_id == channel_reward_activity.id,
        ChannelRewardHistory.status == ChannelRewardHistory.Status.VALID,
    ).all()
    if not user_rows:
        return
    has_record = GiftHistory.query.filter(
        GiftHistory.activity_id == channel_reward_activity.activity_id,
        or_(
            GiftHistory.status == GiftHistory.Status.CREATED,
            GiftHistory.status == GiftHistory.Status.FINISHED
        )
    ).first()
    if has_record:
        return
    for row in user_rows:
        add_activity_gift(
            row.user_id, channel_reward_activity.activity_id, row.asset, row.amount,
            remark='gift for channel reward', commit=False)
    db.session.commit()
    update_gift_history_task(channel_reward_activity.activity_id, BalanceBusiness.GIFT.value,
                             wait=False,
                             pay_from_admin_user_id=config['MARKET_ADMIN_USER_ID'])

    send_channel_reward_activity_push.delay(channel_reward_activity_id)


@celery_task
@lock_call(with_args='activity_id')
def update_gift_history_task(
        activity_id,
        business: str,
        *,
        pay_from_admin=True,
        pay_from_admin_user_id=0,
        wait=True,
        is_lock=False,
):
    """
    更新赠送历史
    """
    from app.business import LockAssetHelper

    if wait:  # 检查当前是否有新记录在插入
        def _get_last_id():
            return GiftHistory.query.filter(GiftHistory.activity_id == activity_id) \
                .with_entities(func.max(GiftHistory.id)).scalar()

        last_id = _get_last_id()
        db.session.rollback()
        time.sleep(5)
        if last_id != _get_last_id():
            current_app.logger.info('update_gift_history_task: There are new gift_history records, '
                                    f'activity_id: {activity_id}, task exit.')
            return

    histories = GiftHistory.query.filter(
        GiftHistory.status == GiftHistory.Status.CREATED,
        GiftHistory.activity_id == activity_id,
        GiftHistory.admin_history_id.is_(None)
    ).all()

    if not pay_from_admin:
        client = ServerClient(logger=current_app.logger)
        for history in histories:
            if history.lock_time == 0:
                rpc = client.add_user_balance
            else:
                rpc = client.add_and_lock_user_balance
            try:
                rpc(
                    user_id=history.user_id,
                    asset=history.asset,
                    amount=history.amount,
                    business=business,
                    business_id=history.id
                )
            except Exception as e:
                current_app.logger.error(f'update gift history failed, ' \
                                         f'user_id: {history.user_id}, ' \
                                         f'amount: {history.amount} {history.asset}' \
                                         f'error: {e}')
                continue

            if history.lock_time == 0:
                history.status = GiftHistory.Status.FINISHED
            else:
                history.status = GiftHistory.Status.FROZEN
            db.session.commit()
    else:
        # 生成admin待扣减资产记录及关联关系
        asset_histories = defaultdict(list)
        for history in histories:
            asset_histories[history.asset].append(history)

        for asset, histories in asset_histories.items():
            amount = sum(x.amount for x in histories)
            record = AdminGiftBalanceHistory(
                user_id=pay_from_admin_user_id or config['OFFICIAL_ADMIN_USER_ID'],
                asset=asset,
                amount=-amount,
                business=business
            )
            db.session.add(record)
            db.session.flush()

            for history in histories:
                history.admin_history_id = record.id
        db.session.commit()
        # 执行资产变更
        admin_histories = AdminGiftBalanceHistory.query.filter(AdminGiftBalanceHistory.status.in_((
            AdminGiftBalanceHistory.Status.CREATED, AdminGiftBalanceHistory.Status.PROCESSING
        ))).all()
        for history in admin_histories:
            _update_gift_balance_history(history)

    # 锁定记录
    histories = GiftHistory.query.filter(
        GiftHistory.status == GiftHistory.Status.FROZEN,
        GiftHistory.activity_id == activity_id,
    ).all()

    for history in histories:
        try:
            remark = history.remark or ""
            LockAssetHelper.add_and_lock(
                business=LockedAssetBalance.Business.GIFT,
                business_id=history.id,
                user_id=history.user_id,
                asset=history.asset,
                amount=history.amount,
                created_by=history.user_id,
                unlocked_at=now() + timedelta(seconds=history.lock_time),
                remark=f'{remark} gift {history.id}',
            )
            if is_lock:
                history.status = GiftHistory.Status.LOCKED
            else:
                # 兼容旧逻辑
                history.status = GiftHistory.Status.FINISHED
        except Exception:
            current_app.logger.error(f'gift history locked balance failed, row id: {history.id}')
            continue
    db.session.commit()


def _update_gift_balance_history(admin_history):
    client = ServerClient(logger=current_app.logger)
    if admin_history.status == AdminGiftBalanceHistory.Status.CREATED:
        try:
            client.add_user_balance(
                user_id=admin_history.user_id,
                asset=admin_history.asset,
                amount=admin_history.amount,
                business=admin_history.business,
                business_id=admin_history.id
            )
        except Exception as e:
            error_msg = f'update admin gift history failed, ' \
                        f'user_id: {admin_history.user_id}, ' \
                        f'amount: {admin_history.amount} {admin_history.asset}' \
                        f'error: {e}'
            current_app.logger.error(error_msg)
            return

        admin_history.status = AdminGiftBalanceHistory.Status.PROCESSING
        db.session.commit()

    if admin_history.status != AdminGiftBalanceHistory.Status.PROCESSING:
        return

    gift_histories = GiftHistory.query.filter(
        GiftHistory.admin_history_id == admin_history.id,
        GiftHistory.status == GiftHistory.Status.CREATED
    ).all()
    success = True
    for history in gift_histories:
        if history.lock_time == 0:
            try:
                client.add_user_balance(
                    user_id=history.user_id,
                    asset=admin_history.asset,
                    amount=history.amount,
                    business=admin_history.business,
                    business_id=history.id
                )
            except Exception as e:
                current_app.logger.error('update gift balance history failed, ' \
                                         f'user_id: {history.user_id}, ' \
                                         f'amount: {history.amount}, ' \
                                         f'error: {e}')
                success = False
                continue
            history.status = GiftHistory.Status.FINISHED
        else:
            history.status = GiftHistory.Status.FROZEN
        db.session.commit()

    if success:
        admin_history.status = AdminGiftBalanceHistory.Status.FINISHED
        db.session.commit()


def gift_history_real_freeze(gift_id):
    from app.business import LockAssetHelper

    model = GiftHistory
    gift_row = model.query.get(gift_id)
    if gift_row.status == model.Status.LOCKED:
        LockAssetHelper.set_unlock_at(
            LockedAssetBalance.Business.GIFT, gift_row.id, gift_row.user_id, LockAssetHelper.INFINITE_DATETIME,
            is_commit=False,
        )
        gift_row.status = model.Status.REAL_FROZEN
        db.session.commit()


def gift_history_unfreeze(gift_id):
    from app.business import LockAssetHelper

    model = GiftHistory
    gift_row = model.query.get(gift_id)
    if gift_row.status == model.Status.REAL_FROZEN:
        lock_row = LockAssetHelper.get_uni_row(LockedAssetBalance.Business.GIFT, gift_row.id, gift_row.user_id)
        unlocked_at = lock_row.created_at + timedelta(seconds=gift_row.lock_time)
        LockAssetHelper.set_unlock_at(
            LockedAssetBalance.Business.GIFT, gift_row.id, gift_row.user_id, unlocked_at,
            is_commit=False,
        )
        gift_row.status = model.Status.LOCKED
        db.session.commit()


def gift_history_to_revoke(gift_id):
    """对应补偿任务 fix_gift_history_to_revoke"""
    from app.business import LockAssetHelper

    model = GiftHistory
    THRESHOLD = 300
    gift_row = model.query.get(gift_id)
    if gift_row.status in {model.Status.TO_REVOKE, model.Status.REVOKED}:
        return True
    if gift_row.status not in {model.Status.LOCKED, model.Status.REAL_FROZEN}:
        raise InvalidArgument(message=f'当前礼物记录状态 {gift_row.status}, 不能撤销')
    lock_row = LockAssetHelper.get_uni_row(LockedAssetBalance.Business.GIFT, gift_row.id, gift_row.user_id)
    # 如果临近解锁时间, 防止并发冲突，不能撤销
    if (gift_row.status != model.Status.REAL_FROZEN
            and lock_row.unlocked_at and lock_row.unlocked_at - now() <= timedelta(seconds=THRESHOLD)):
        return False
    gift_row.status = model.Status.TO_REVOKE
    db.session.commit()
    # 解冻扣款资产
    gift_history_revoke(gift_row)
    return True


def gift_history_revoke(gift_row):
    from app.business import LockAssetHelper
    business = BalanceBusiness.GIFT_REVOKE
    LockAssetHelper.unlock_and_sub(LockedAssetBalance.Business.GIFT, gift_row.id, gift_row.user_id)
    gift_row.status = GiftHistory.Status.REVOKED

    # 创建 admin gift 撤销记录
    a_model = AdminGiftBalanceHistory
    history_row = a_model.query.get(gift_row.admin_history_id)
    new_history = AdminGiftBalanceHistory(
        user_id=history_row.user_id,
        asset=history_row.asset,
        amount=gift_row.amount,
        business=business.value,
        status=a_model.Status.TO_REVOKE
    )
    db.session.add(new_history)
    db.session.commit()
    admin_gift_history_revoke(new_history)


def admin_gift_history_revoke(history_row):
    # 补偿任务 fix_admin_gift_history_revoke
    from app.business import ignore_duplicate_error

    client = ServerClient()
    with ignore_duplicate_error():
        client.add_user_balance(
            user_id=history_row.user_id,
            asset=history_row.asset,
            amount=history_row.amount,
            business=history_row.business,
            business_id=history_row.id
        )
    history_row.status = AdminGiftBalanceHistory.Status.REVOKED
    db.session.commit()


class CetTool:

    @classmethod
    def get_sub_user_map(cls):
        sub_query = SubAccount.query.filter(
            SubAccount.status == SubAccount.Status.VALID
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        )
        user_with_sub_user_dict = MultiDict(
            [(v.main_user_id, v.user_id) for v in sub_query])
        return user_with_sub_user_dict

    @classmethod
    def sub_user_main_user_map(cls):
        sub_query = SubAccount.query.filter(
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        )
        return {v.user_id: v.main_user_id for v in sub_query}

    @classmethod
    def get_binding_user_list(cls, binding_time):
        binding_query = BindingAddress.query.filter(
            BindingAddress.status == BindingAddress.StatusType.PASS,
            BindingAddress.binding_time < timestamp_to_datetime(binding_time)
        ).with_entities(
            BindingAddress.user_id.distinct().label('user_id')
        )
        return [v.user_id for v in binding_query]

    @classmethod
    def get_user_out_side_cet(cls, user_id, ts):
        # noinspection PyBroadException
        try:
            out_site_cet_amount = VipHelper.get_out_site_cet(user_id, ts)
        except Exception:
            out_site_cet_amount = Decimal()
        return out_site_cet_amount
