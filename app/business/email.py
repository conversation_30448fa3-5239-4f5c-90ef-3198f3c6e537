# -*- coding: utf-8 -*-
import copy
import datetime
import json
import random
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from hashlib import sha256
from hmac import HMAC
from typing import Any, Dict, List, Optional, Union
from urllib.parse import quote_plus

from flask import current_app, render_template, render_template_string
from flask_babel import force_locale, gettext
from sqlalchemy import func

from app.caches.perpetual import PerpetualMarketCache
from app.models.operation import (EmailPushContent, FooterConfig,
                                  TradeRankActivityDetail, OperationTemplateContent)
from app.models.referral import AmbassadorPackageBatch, PackageSettlementHistory, UserAmbassadorPackage
from app.models.user import KycVerificationPro, SignOffUser, LivenessCheckHistory, KycVerificationHistory, KYCFlow, \
    UserInactiveNotifyLog, UserEmergencyContact
from app.utils.push import WebPagePath
from app.utils.rand import new_hex_token

from ..assets import get_asset_chain_config, is_multi_chain
from ..caches import (EmailSendCountCache,
                      WithdrawalApproverCache)
from ..caches.assets import ChainDisplayMappingCache
from ..common import (CeleryQueues, EmailCodeType, Language, Media,
                      MessageContent, MessageTitle, MessageWebLink,
                      PerpetualMarketType, PositionSide, PrecisionEnum,
                      get_country)
from ..config import config
from ..exceptions import InvalidArgument
from ..models import (Ambassador, AmbassadorAgent, ApiAuth,
                      ApiWithdrawalAddressRequest, AppraisalHistory, Deposit,
                      EmailPush, EmailPushUnsubscription, EmailToken,
                      KYCInstitution, KycVerification, LoginHistory,
                      MarketMaker, Message, ReferralAssetHistory,
                      SecurityResetApplication, SubAccount,
                      SubAccountManagerRelation, User, Withdrawal,
                      WithdrawalApprover, db)
from ..models.auto_invest import AutoInvestPlanNoticeConfig, AutoInvestTask
from ..models.exchange import (AssetExchangeOrder,
                               AssetExchangeOrderTransferHistory)
from ..models.wallet import AbnormalDepositApplication, KYTDepositAudit, EDDAudit, DepositAudit
from ..utils import (AWSEmail, AWSCommentEmail, amount_to_str, batch_iter, celery_task,
                     datetime_to_str, hide_email, hide_mobile, now,
                     route_module_to_celery_queue, timestamp_to_datetime,
                     url_join, GeoIP, MailgunEmail)
from ..utils.email import BaseEmail
from ..utils.files import AWSBucketPublic
from .clients import PerpetualServerClient, WalletClient, monitor_wrap
from .fee import FeeFetcher
from .push import get_market_business_name
from .referral import AmbassadorAgentBusiness, ReferralBusiness
from .user import UserPreferences, UserSettings

route_module_to_celery_queue(__name__, CeleryQueues.EMAIL)


def get_notice_users(user: User) -> List[User]:
    """ 子账号一些邮件触达，需发送给主账号 和 托管关系生效中的托管主账号 """
    if not user.is_sub_account:
        return [user] if user.email else []
    sub_user = SubAccount.query.filter(
        SubAccount.user_id == user.id
    ).first()
    if sub_user.type != SubAccount.Type.NORMAL:
        return []
    notice_users = [user.main_user]
    if sub_user.manage_status == SubAccount.ManageStatus.HOSTING:
        notice_users = []
    relations = SubAccountManagerRelation.query.filter(
        SubAccountManagerRelation.user_id == user.id,
        SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
    ).all()
    manager_user_ids = [i.manager_id for i in relations]
    manager_users = User.query.filter(User.id.in_(manager_user_ids)).all()  # 最多5个
    notice_users.extend(manager_users)
    return [i for i in notice_users if i.email]


def get_show_sub_acc_info(user: User) -> tuple[bool, str]:
    """ 子账号一些邮件触达，需要显示子帐号名称。 """
    is_sub_acc = user.user_type == User.UserType.SUB_ACCOUNT
    sub_account_name = user.name_displayed if is_sub_acc else ""
    is_show_sub_account_name = bool(is_sub_acc and sub_account_name)
    return is_show_sub_account_name, sub_account_name


def get_anti_phishing_code_by_email(email):
    user: User = User.query.filter(User.email == email).first()
    return UserPreferences(user.id).anti_phishing_code if user else ''


class EmailSender:

    EMAIL_SUBJECT_MAPPING = {
        "notice": {
            "sign_up": gettext("【CoinEx】欢迎注册"),
            "sign_in": gettext("【CoinEx】登录提醒"),
            "sign_off": gettext("【CoinEx】账号注销提醒"),
            "withdrawal_cancelled_notice": gettext("【CoinEx】提现撤销"),
            "perpetual_liquidation": gettext("【CoinEx】合约强制平仓通知"),
            "sign_in_fail": gettext("【CoinEx】多次尝试登录提醒"),
            "2fa_reset_fail": gettext("【CoinEx】重置安全项失败"),
            "update_trade_password": gettext("【CoinEx】修改交易密码成功"),
            "kyc_pass": gettext("【CoinEx】你的实名认证已通过审核"),
            "kyc_fail": gettext("【CoinEx】你的实名认证未通过审核"),
            "kyc_pro_pass": gettext("【CoinEx】KYC高级认证通过"),
            "kyc_pro_fail": gettext("【CoinEx】KYC高级认证不通过"),
            "kyt_deposit_info_required": gettext("【CoinEx】请提供充值证明文件"),
            "kyt_deposit_extra_info_required": gettext("【CoinEx】请重新提供充值证明文件"),
            "kyt_deposit_freezing": gettext("【CoinEx】充值证明审核不通过"),
            "kyt_deposit_withdrawal_only": gettext("【CoinEx】帐户只支持提现资产"),
            "edd_info_required": gettext("【CoinEx】请提供充值证明"),
            "edd_extra_info_required": gettext("【CoinEx】请提供充值证明补充资料"),
            "edd_freezing": gettext("【CoinEx】充值证明审核不通过"),
            "edd_manual_info_required": gettext("【CoinEx】请提供增强尽职调查证明"),
            "edd_manual_extra_info_required": gettext("【CoinEx】请提供增强尽职调查证明补充资料"),
            "edd_manual_pass": gettext("【CoinEx】增强尽职调查证明审核通过"),
            "risk_screen_fail": gettext("【CoinEx】请尽快提币或处置资产"),
            "deposit_pass_notice": gettext("【CoinEx】充值成功"),
            "deposit_pass_notice_deleted_address": gettext("【CoinEx】充值到旧地址入账成功"),
            "deposit_privacy_asset_require_kyc": gettext("【CoinEx】充值隐私币请完成实名认证"),
            "kyc_institution_pass": gettext("机构认证通过"),
            "kyc_institution_fail": gettext("机构认证失败"),
            "deposit_amount_too_small": gettext("【CoinEx】充值低于最小金额"),
            "margin_liquidation": gettext("【CoinEx】强平通知"),
            "perpetual_market_maker_level_appraisal": gettext("【CoinEx】合约做市商月度考核"),
            "sign_in_unusual": gettext("【CoinEx】异地登录提醒"),
            "mobile_reset_pass": gettext("【CoinEx】重置手机成功"),
            "ambassador_agent_appraisal": gettext("【CoinEx】大使推荐官月度考核"),
            "edit_trade_password": gettext("【CoinEx】资金密码修改提醒"),
            "perpetual_adl": gettext("【CoinEx】合约自动减仓通知"),
            "perpetual_position_reduce_notice": gettext("【CoinEx】合约降档减仓通知"),
            "margin_loan_order_renew_succeeded": gettext("【CoinEx】杠杆续借成功通知"),
            "margin_renew_failed": gettext("续借失败通知"),
            "ambassador_level_appraisal": gettext("【CoinEx】大使月度考核"),
            "ambassador_level_change": gettext("【CoinEx】大使等级变更"),
            "ambassador_level_change_pre_notify": gettext("【CoinEx】大使预降级提醒"),
            "withdrawal_expired_notice": gettext("【CoinEx】提现撤销"),
            "ambassador_train_book": gettext("CoinEx大使培训手册"),
            "bus_ambassador_train_book": gettext("CoinEx大使培训手册"),
            "bus_ambassador_delete_pre_notice": gettext("【CoinEx】大使预淘汰提醒"),
            "bus_ambassador_delete_notice": gettext("【CoinEx】大使淘汰提醒"),
            "coin_application_rejected": gettext("【CoinEx】上币申请反馈"),
            "ieo_application_rejected": gettext("【CoinEx Dock】合作申请反馈"),
            "deposit_to_receive": gettext("【CoinEx】充值待入账"),
            "maker_cashback_level_appraisal": gettext("【CoinEx】现货Maker返现机制月度考核"),
            "maker_cashback_level_change": gettext("【CoinEx】现货Maker返现等级变更"),
            "sub_account_register_notice": gettext("【CoinEx】新建子账号"),
            "get_promotion_gift": gettext("【CoinEx】活动奖励通知"),
            "send_coin_withdraw_notice": gettext("【CoinEx】提现审核通过"),
            "red_packet_register_notice": gettext("【CoinEx】亲，恭喜你抢到{{ amount }} {{ coin_type }}的C-Box，注册后即可到账！"),
            "red_packet_register_expiring_notice": gettext("【CoinEx】C-Box即将失效"),
            "sign_up_success": gettext("【CoinEx】注册成功"),
            "set_login_password_success": gettext("【CoinEx】设置密码成功"),
            "vip_level_gain": gettext("【CoinEx】恭喜成为VIP{{ new_level }}，多项专属权益已解锁！"),
            "vip_level_up": gettext("【CoinEx】恭喜晋升VIP{{ new_level }}，专属权益已升级！"),
            "vip_level_down": gettext("【CoinEx】VIP等级变动提醒：VIP{{ old_level }} >> VIP{{ new_level }}，点击查看调整后权益"),
            "vip_level_lose": gettext("【CoinEx】你的VIP权益已失效，重新升级，享更多专属福利！"),
            "deposit_resumed_notice": gettext("【CoinEx】充值恢复通知"),
            "maker_cashback_user_daily_report": gettext("【CoinEx】现货Maker返现"),
            "withdrawal_resumed_notice": gettext("【CoinEx】提现恢复通知"),
            "edit_login_password": gettext("【CoinEx】安全设置修改提醒"),
            "margin_loan_order_expired": gettext("杠杆还币提醒"),
            "mining_rewords": gettext("【CoinEx】挖矿奖励发放"),
            "perpetual_liquidation_warning": gettext("【CoinEx】强平预警通知"),
            "email_reset_pass": gettext("【CoinEx】重置邮箱成功"),
            "email_registered": gettext("【CoinEx】邮箱已注册"),
            "margin_loan_order_force_flat": gettext("强制还币通知"),
            "spot_market_maker_level_appraisal": gettext("【CoinEx】现货做市商月度考核"),
            "totp_reset_pass": gettext("【CoinEx】重置TOTP成功"),
            "webauthn_reset_pass": gettext("【CoinEx】重置通行密钥成功"),
            "margin_liquidation_warning": gettext("【CoinEx】强平预警通知"),
            "edit_totp": gettext("【CoinEx】安全设置修改提醒"),
            "edit_mobile": gettext("【CoinEx】安全设置修改提醒"),
            "perpetual_position_close_result_notice": gettext("【CoinEx】合约平仓失败通知"),
            "perpetual_profit_loss_notice": gettext("【CoinEx】止盈止损通知"),
            # "perpetual"
            "credit_risk_withdraw_close_pre_warning": gettext("提现功能—预警通知"),
            "credit_risk_withdraw_close": gettext("提现功能—关闭通知"),
            "airdrop_rewords_success": gettext("【CoinEx】空投中奖通知"),
            "airdrop_rewords_fail": gettext("【CoinEx】空投未中奖通知"),
            "airdrop_rewords_result": gettext("【CoinEx】参与空投结果通知"),
            "dibs_rewords_success": gettext("【CoinEx】Dibs中奖通知"),
            "dibs_rewords_fail": gettext("【CoinEx】Dibs未中奖通知"),
            "sotoshi_bid_success": gettext("【CoinEx】稀有聪竞标成功！"),
            "ieo_rewords_success": gettext("已中签！"),
            "ieo_rewords_fail": gettext("很遗憾，未中签"),
            "abnormal_deposit_application_additional_info": gettext("【CoinEx】充值未到账找回申请资料补充"),
            "abnormal_deposit_application_fee": gettext("【CoinEx】充值未到账找回申请手续费补充"),
            "abnormal_deposit_application_finished": gettext("【CoinEx】充值未到账找回成功"),
            "abnormal_deposit_application_rejected": gettext("【CoinEx】充值未到账找回申请审核失败"),
            "abnormal_deposit_application_passed": gettext("【CoinEx】充值自助找回审核通过"),
            "exchange_order_partial": gettext("【CoinEx】部分兑换成功通知"),
            "exchange_order_failed": gettext("【CoinEx】兑换失败通知"),
            "exchange_order_finished": gettext("【CoinEx】完全兑换成功通知"),
            "auto_invest_deal_partial": gettext("【CoinEx】自动定投部分成交通知"),
            "auto_invest_deal_all": gettext("【CoinEx】自动定投全部成交通知"),
            "auto_invest_deal_failed": gettext("【CoinEx】自动定投未成交通知"),
            "auto_invest_profit_amount": gettext("【CoinEx】自动定投到达盈利目标通知"),
            "auto_invest_profit_rate": gettext("【CoinEx】自动定投到达盈利目标通知"),
            "auto_invest_failed": gettext("【CoinEx】自动定投失败通知"),
            "auto_invest_paused": gettext("【CoinEx】自动定投暂停通知"),
            "auto_invest_closed": gettext("【CoinEx】自动定投关闭通知"),
            "experience_fee/send_coupon_notice": gettext("【CoinEx】恭喜获得{{ value }} {{ value_type }}合约体验金，快来领取吧～"),
            "experience_fee/active_deadline_notice": gettext("【CoinEx】合约体验金即将到期，请尽快领取！"),
            "experience_fee/used_deadline_notice": gettext("【CoinEx】{{ value }} {{ value_type }}合约体验金即将过期"),
            "experience_fee/early_maturity_notice": gettext("【CoinEx】{{ value }} {{ value_type }}合约体验金已满足赠送标准"),
            "trading_gift/send_coupon_notice": gettext("CoinEx送你{{ value }} {{ value_type }} 现金福利了！"),
            "trading_gift/random_send_coupon_notice": gettext("CoinEx喊你开盲盒，领取现金福利！"),
            "trading_gift/active_deadline_notice": gettext("【CoinEx】{{ value }} {{ value_type }} 交易赠金券，即将过期"),
            "trading_gift/random_active_deadline_notice": gettext("【CoinEx】交易赠金券盲盒即将过期"),
            "trading_gift/used_deadline_notice": gettext("【CoinEx】{{ value }} {{ value_type }}交易赠金券，即将过期"),
            "trading_gift/early_maturity_notice": gettext("【CoinEx】恭喜你，交易赠金券已激活"),
            "investment_increase_rate/active_deadline_notice": gettext(
                "【CoinEx】{{ value }}{{ value_type }} 加息券即将到期，请尽快领取！"
            ),
            "investment_increase_rate/send_coupon_notice": gettext("【CoinEx】恭喜获得 {{ value }}{{ value_type }} 加息券，快来领取吧～"),
            "investment_increase_rate/delivery_coupon_notice": gettext(
                "【CoinEx】恭喜获得 {{ value }}{{ value_type }} 加息券，请尽快激活使用"
            ),
            "investment_increase_rate/using_deadline_notice": gettext("【CoinEx】{{ value }}{{ value_type }} 加息券即将失效，请尽快使用！"),
            "cashback_fee/active_deadline_notice": gettext("【CoinEx】{{value}} {{value_type}} 返现券即将到期，请尽快领取！"),
            "cashback_fee/delivery_coupon_notice": gettext("【CoinEx】{{value}} {{value_type}} 返现券待激活"),
            "cashback_fee/send_coupon_notice": gettext("【CoinEx】恭喜获得{{value}} {{value_type}} 返现券，快来领取吧～"),
            "cashback_fee/used_deadline_notice": gettext("【CoinEx】返现券即将过期"),
            "cashback_fee/using_deadline_notice": gettext("【CoinEx】{{value}} {{value_type}} 返现券即将截止激活"),
            "perpetual_subsidy/send_coupon_notice": gettext("【CoinEx】送你 {{value}} {{value_type}} 合约补贴金！"),
            "perpetual_subsidy/delivery_coupon_notice": gettext("【CoinEx】送你 {{value}} {{value_type}} 合约补贴金！"),
            "perpetual_subsidy/active_deadline_notice": gettext("【CoinEx】{{value}} {{value_type}} 合约补贴金，即将过期"),
            "perpetual_subsidy/used_deadline_notice": gettext("【CoinEx】{{value}} {{value_type}} 合约补贴金，即将过期"),
            "vip_upgrade/send_coupon_notice": gettext("【CoinEx】CoinEx给你送VIP升级券了！"),
            "vip_upgrade/active_deadline_notice": gettext("【CoinEx】VIP升级券即将过期"),
            "vip_upgrade/used_deadline_notice": gettext("【CoinEx】VIP升级券即将过期"),
            "copy_trading_experience_fee/send_coupon_notice": gettext("【CoinEx】CoinEx给你送合约跟单体验金券了！"),
            "copy_trading_experience_fee/active_deadline_notice": gettext("【CoinEx】合约跟单体验金券即将过期"),
            "copy_trading_experience_fee/using_deadline_notice": gettext("【CoinEx】合约跟单体验金券即将过期"),
            "equity_center/cashback_expiring": gettext("【CoinEx】手续费返现权益即将到期"),
            "equity_center/cashback_delivery": gettext("【CoinEx】手续费返现权益发放"),
            "equity_center/invest_increase_delivery": gettext("【CoinEx】活期理财加息权益发放"),
            "equity_center/invest_increase_activation_half_expired": gettext("【CoinEx】活期理财加息权益激活期限过半"),
            "equity_center/invest_increase_usage_half_expired": gettext("【CoinEx】活期理财加息权益使用期限过半"),
            "equity_center/airdrop_delivery": gettext("【CoinEx】空投奖励发放"),
            "mission_center/new_mission_notice": gettext("【CoinEx】请查收新任务"),
            "mission_center/mission_expiring": gettext("【CoinEx】任务即将到期"),
            "mission_center/mission_reward_sent": gettext("【CoinEx】任务奖励发放"),
            "mission_center/mission_middle_expiring": gettext("【CoinEx】你还有丰厚的新手福利待领取"),
            "ambassador_application_submit": gettext("【CoinEx】 大使提交申请提醒"),
            "ambassador_application_approve": gettext("【CoinEx】 大使申请审核通过提醒"),
            "ambassador_application_reject": gettext("【CoinEx】大使申请审核未通过提醒"),
            "potential_ambassador": gettext("【CoinEx大使邀请函】50%永久返佣，USDT日结"),
            "potential_invalid_ambassador": gettext("【CoinEx】大使月度考核"),
            "multi_approve_join": gettext("【CoinEx】成员加入提现审核通知"),
            "multi_approve_reject": gettext("【CoinEx】提现审核邀请失败"),
            "multi_approve_leave": gettext("【CoinEx】提现审核移除成功"),
            "api_address_cancelled": gettext("【CoinEx】添加API提现白名单地址取消"),
            "trade_rank_activity_gift": gettext("【CoinEx】交易排位赛奖励发放"),
            "tax_export_success": gettext("账户数据已生成"),
            "unfreeze_account_pass": gettext("【CoinEx】解冻账号成功"),
            "unfreeze_account_fail": gettext("【CoinEx】解冻账号失败"),
            "spot_grid_triggered": gettext("【CoinEx】{{market}}现货市场已达网格触发价"),
            "spot_grid_take_profit": gettext("【CoinEx】{{market}}现货市场已达网格止盈价"),
            "spot_grid_stop_loss": gettext("【CoinEx】{{market}}现货市场已达网格止损价"),
            "spot_grid_exceed_price_range": gettext("【CoinEx】{{market}}现货市价超出网格价格区间"),
            "spot_grid_exceed_recommend_run_days": gettext("【CoinEx】{{market}}现货网格策略已超时"),
            "pledge_liq_warning": gettext("【CoinEx】借贷补仓通知"),
            "pledge_liq": gettext("【CoinEx】借贷强平通知"),
            "pledge_loan_order_will_renew": gettext("【CoinEx】借贷订单即将到期提醒"),
            "pledge_loan_order_will_expired": gettext("【CoinEx】借贷订单即将到期还币提醒"),
            "pledge_loan_order_renew_success": gettext("【CoinEx】借贷订单续借成功通知"),
            "pledge_loan_order_renew_first_fail": gettext("【CoinEx】借贷订单进入宽限期通知"),
            "pledge_loan_order_renew_fail": gettext("【CoinEx】借贷订单续借状态通知"),
            "pledge_loan_order_force_repay": gettext("【CoinEx】借贷订单强平通知"),
            "pledge_loan_order_auto_repay": gettext("【CoinEx】借贷订单自动还币通知"),
            "pre_trading_settlement": gettext("【CoinEx】预测市场 {{asset}}已完成交割"),
            "sub_account_bind_manager": gettext("【CoinEx】子账号授权成功"),
            "sub_account_cancel_manager": gettext("【CoinEx】子账号授权解除"),
            "sub_account_bind_hosting": gettext("【CoinEx】子账号托管成功"),
            "sub_account_cancel_hosting": gettext("【CoinEx】子账号托管解除"),
            "trade_rank_activity_notice": gettext("【CoinEx】交易排位赛未达标提醒"),
            "margin_function_introduction": gettext("【CoinEx】恭喜你成功开通CoinEx杠杆功能"),
            "perpetual_trading_introduction": gettext("【CoinEx】恭喜你成功开通CoinEx合约功能"),
            "broker_approve": gettext("【CoinEx】恭喜成为CoinEx经纪商"),
            "broker_auth_approve": gettext("【CoinEx】成功开启API授权"),
            "withdraw_password_reset_pass": gettext("【CoinEx】提现密码重置成功"),
            "withdraw_password_reset_fail": gettext("【CoinEx】提现密码重置失败"),
            "edit_withdraw_password": gettext("【CoinEx】提现密码修改成功"),
            "perpetual_funding_interval_update": gettext("【CoinEx】{{market}}市场资金费用收取周期调整"),
            "novice_package": gettext("【CoinEx】{{activate_title}}，新用户好礼活动即将结束！"),
            "perpetual_open_position_stop_loss_notice": gettext("【CoinEx】开仓止盈止损设置失效提醒"),
            "perpetual_open_position_take_profit_notice": gettext("【CoinEx】开仓止盈止损设置失效提醒"),
            "staking_remove_success": gettext("【CoinEx】成功赎回{{ asset }}"),
            "staking_remove_notice": gettext("【CoinEx】质押生效提醒"),
            "anniversary_of_registration": gettext("【CoinEx】您加入CoinEx已经{{ years }}年啦！"),
            "second_anniversary_of_registration": gettext("【CoinEx】您加入CoinEx已经{{ years }}年啦！"),
            "third_anniversary_of_registration": gettext("【CoinEx】您加入CoinEx已经{{ years }}年啦！"),
            "fourth_anniversary_of_registration": gettext("【CoinEx】您加入CoinEx已经{{ years }}年啦！"),
            "fifth_anniversary_of_registration": gettext("【CoinEx】您加入CoinEx已经{{ years }}年啦！"),
            "copy_trading/follower_close_position": gettext("【CoinEx】跟单交易平仓成功"),
            "copy_trading/follower_position_liquidation": gettext("【CoinEx】跟单员强平通知"),
            "copy_trading/follower_open_position": gettext("【CoinEx】跟单成功通知"),
            "copy_trading/follower_open_position_failed": gettext("【CoinEx】跟单开仓失败"),
            "copy_trading/follower_stop_loss": gettext("【CoinEx】跟单止损触达成功"),
            "copy_trading/follower_follow_finished": gettext("【CoinEx】结束跟单通知"),
            "copy_trading/follower_take_profit": gettext("【CoinEx】跟单止盈触发成功"),
            "copy_trading/follower_suggest_add_margin": gettext("【CoinEx】交易员转入保证金"),
            "copy_trading/follower_profit_share": gettext("【CoinEx】分润结算通知"),
            "copy_trading/follower_profit_share_rate_changed": gettext("【CoinEx】分润比例调整通知"),
            "copy_trading/trader_apply_success": gettext("【CoinEx】恭喜成为跟单交易员"),
            "copy_trading/trader_adjust_profit_share_rate": gettext("【CoinEx】分润比例调整成功"),
            "copy_trading/trader_open_position": gettext("【CoinEx】交易员开仓成功"),
            "copy_trading/trader_close_position": gettext("【CoinEx】交易员平仓成功"),
            "copy_trading/trader_liquidation": gettext("【CoinEx】交易员强平通知"),
            "copy_trading/trader_stop_loss": gettext("【CoinEx】止损触发成功"),
            "copy_trading/trader_trade_finished": gettext("【CoinEx】结束带单通知"),
            "copy_trading/trader_trade_system_finished": gettext("【CoinEx】交易员身份取消通知"),
            "copy_trading/trader_trade_pre_cancel": gettext("【CoinEx】交易员身份取消提醒"),
            "copy_trading/trader_take_profit": gettext("【CoinEx】止盈触发成功"),
            "payment_received": gettext("【CoinEx】收款成功"),
            "p2p/add_payment_channel": gettext("【CoinEx】P2P新增收款方式"),
            "p2p/auto_offline_advertising": gettext("【CoinEx】P2P广告单被下架"),
            "p2p/buyer_cancelled_order": gettext("【CoinEx】P2P订单已取消"),
            "p2p/buyer_payment_deadline": gettext("【CoinEx】P2P订单付款提醒"),
            "p2p/canceled_complaint": gettext("【CoinEx】P2P订单申诉已取消"),
            "p2p/created_complaint": gettext("【CoinEx】P2P订单发起申诉"),
            "p2p/finished_complaint": gettext("【CoinEx】P2P订单申诉已完成"),
            "p2p/finished_order": gettext("【CoinEx】P2P订单已完成"),
            "p2p/merchant_reject_order": gettext("【CoinEx】P2P拒绝接单提醒"),
            "p2p/operation_complaint_for_cancel_to_buyer": gettext("【CoinEx】P2P订单申诉已处理"),
            "p2p/operation_complaint_for_cancel_to_seller": gettext("【CoinEx】P2P订单申诉已处理"),
            "p2p/operation_complaint_for_release_to_buyer": gettext("【CoinEx】P2P订单申诉已处理"),
            "p2p/operation_complaint_for_release_to_seller": gettext("【CoinEx】P2P订单申诉已处理"),
            "p2p/order_auto_cancelled": gettext("【CoinEx】P2P订单已取消"),
            "p2p/paid_wait_release_asset": gettext("【CoinEx】P2P订单放币提醒"),
            "p2p/payment_channel_invalid": gettext("【CoinEx】P2P支付渠道失效"),
            "p2p/received_order_to_payment": gettext("【CoinEx】P2P订单已确认"),
            "p2p/received_order_wait_payment": gettext("【CoinEx】P2P订单已确认"),
            "p2p/remind_receive_order": gettext("【CoinEx】P2P接单提醒"),
            "p2p/restart_complaint": gettext("【CoinEx】P2P订单申诉重新开启"),
            "p2p/updated_complaint": gettext("【CoinEx】P2P申诉订单进度更新"),
            "p2p/become_merchant": gettext("【CoinEx】商家身份开通成功"),
            "p2p/become_merchant_not_2fa": gettext("【CoinEx】商家身份开通成功"),
            "activity/spot_trade_rank_start": gettext("【CoinEx】{{ title }}"),
            "activity/perpetual_trade_rank_start": gettext("【CoinEx】恭喜！您具备合约交易赛报名资格"),
            "activity/airdrop_activity_start": gettext("【CoinEx】{{ title }}"),
            "activity/asset_airdrop_activity_start": gettext("【CoinEx】参与{{ asset }}空投活动，赢取{{ amount }} {{ asset }}"),
            "activity/dibs_activity_start": gettext("【CoinEx】{{ title }}"),
            "activity/launch_pool_mining_start": gettext("【CoinEx】{{ title }}"),
            "coupon_balance_warn": gettext("【CoinEx】卡券账户资金不足，请补充资金"),
            "liveness_check": gettext("【CoinEx】请完成身份验证"),
            # p2p商家活动
            "p2p/mer_act_audit_pass": gettext("【CoinEx】P2P{{ act_name }}报名成功通知"),
            "p2p/mer_act_audit_fail": gettext("【CoinEx】P2P{{ act_name }}报名结果通知"),
            "p2p/mer_act_reward_success": gettext("【CoinEx】P2P{{ act_name }}奖励发放通知"),
            "p2p/mer_act_reward_fail": gettext("【CoinEx】P2P{{ act_name }}奖励发放失败通知"),
            # p2p保证金
            "p2p/p2p_margin_payment": gettext("【CoinEx】P2P商家保证金补充通知"),
            "p2p/p2p_margin_shortfall": gettext("【CoinEx】P2P商家权限受限通知"),
            "p2p/p2p_margin_change": gettext("【CoinEx】P2P商家保证金调整通知"),
            "p2p/p2p_mer_cancel": gettext("【CoinEx】P2P商家身份取消通知"),
            "p2p/p2p_mer_compensation": gettext("【CoinEx】商家保证金扣除通知"),
            "p2p/p2p_mer_penalty": gettext("【CoinEx】商家违规扣款通知"),
            "p2p/p2p_mer_excess_refund": gettext("【CoinEx】商家保证金返还通知"),
            "p2p/p2p_user_compensation": gettext("【CoinEx】P2P交易赔付到账通知"),
            "deposit_bonus_rewords_result": gettext("【CoinEx】充值福利：奖励发放通知"),
            "risk_screen_kyc_edd": gettext("【CoinEx】【IMPORTANT】Further information request for CoinEx KYC verification"),
            "comment_warning": gettext("【CoinEx】内容违规警告"),
            "guide_to_rate_trustpilot_new": "【CoinEx】How was your first trading experience with CoinEx - "
            "Feel free to share with us now",  # 引导在Trustpilot评分, 只发英语版本, 合作至2026-3-31
            "guide_to_rate_trustpilot_old": "【CoinEx】Thank You for Trading with CoinEx - Share your thoughts about us",
            "ambassador_package_suspend": gettext("【CoinEx】大使激励包停止发放提醒"),
            "ambassador_package_fail_assessment": gettext("【CoinEx】大使激励包未达标提醒"),
            "ambassador_package_fail_final": gettext("【CoinEx】大使激励包未达标提醒"),
            "ambassador_package_success": gettext("【CoinEx】大使激励包发放提醒"),
            "ambassador_package_success_final": gettext("【CoinEx】大使激励包发放提醒"),
            "ambassador_package_stop": gettext("【CoinEx】大使激励包暂停发放提醒"),
            "ambassador_package_invite": gettext("【CoinEx】你的专属{{package_amount}} {{asset}}激励包已就位，立即领取！"),
            "ambassador_package_fail_amb_invalid": gettext("【CoinEx】立即成为大使，持续解锁{{package_amount}} {{asset}}激励包"),
            "account_statement_export_success": gettext("【CoinEx】您的账户结单{{export_data}}已生成"),
            "account_statement_export_resend": gettext("【CoinEx】您的账户结单{{export_data}}已生成"),
            "emergency_contact/inactive_notice": gettext("【CoinEx】账号长时间未活跃提醒"),
            "emergency_contact/emergency_notice": gettext("【CoinEx】紧急联系人通知"),
        },
        "confirmation": {
            "red_packet_confirm": gettext("【CoinEx】红包确认"),
            "withdraw_coin": gettext("【CoinEx】提现确认"),
            "withdraw_coin_resend": gettext("【CoinEx】提现确认(补发)"),
            "api_expiration_extend": gettext("【CoinEx】API到期提醒"),
            "multi_approve_confirm": gettext("【CoinEx】多人审核提现确认"),
            "multi_approve_join": gettext("【CoinEx】邀请加入提现审核"),
            "multi_approve_leave": gettext("【CoinEx】退出提现审核确认"),
            "add_api_address": gettext("【CoinEx】添加API提现白名单地址确认"),
        },
        "verification_code": {
            "add_anti_phishing_code": gettext("【CoinEx】邮箱验证"),
            "add_api_withdraw_address": gettext("【CoinEx】邮箱验证"),
            "add_email": gettext("【CoinEx】邮箱验证"),
            "add_mobile": gettext("【CoinEx】邮箱验证"),
            "unbind_mobile": gettext("【CoinEx】邮箱验证"),
            "add_totp": gettext("【CoinEx】邮箱验证"),
            "unbind_totp": gettext("【CoinEx】邮箱验证"),
            "add_withdraw_password": gettext("【CoinEx】邮箱验证"),
            "edit_anti_phishing_code": gettext("【CoinEx】邮箱验证"),
            "edit_mobile": gettext("【CoinEx】邮箱验证"),
            "edit_new_email": gettext("【CoinEx】邮箱验证"),
            "edit_old_email": gettext("【CoinEx】邮箱验证"),
            "edit_totp": gettext("【CoinEx】邮箱验证"),
            "edit_withdraw_password": gettext("【CoinEx】邮箱验证"),
            "reset_2fa": gettext("【CoinEx】邮箱验证"),
            "unfreeze_account": gettext("【CoinEx】邮箱验证"),
            "reset_login_password": gettext("【CoinEx】邮箱验证"),
            "non_login_reset_login_password": gettext("【CoinEx】邮箱验证"),
            "non_login_bind_third_party_account": gettext("【CoinEx】邮箱验证"),
            "bind_third_party_account": gettext("【CoinEx】邮箱验证"),
            "set_login_password": gettext("【CoinEx】邮箱验证"),
            "reset_withdraw_password": gettext("【CoinEx】邮箱验证"),
            "add_trade_password": gettext("【CoinEx】设置交易密码"),
            "edit_trade_password": gettext("【CoinEx】修改交易密码"),
            "remove_trade_password": gettext("【CoinEx】关闭交易密码"),
            "sign_in": gettext("【CoinEx】授权新设备"),
            "sign_up": gettext("【CoinEx】欢迎注册"),
            "sign_off": gettext("【CoinEx】邮箱验证"),
            "non_login_edit_new_email": gettext("【CoinEx】邮箱验证"),
            "send_c_box": gettext("【CoinEx】邮箱验证"),
            "bind_admin_webauthn": gettext("【CoinEx】邮箱验证"),
            "bind_webauthn": gettext("【CoinEx】邮箱验证"),
            "unbind_webauthn": gettext("【CoinEx】邮箱验证"),
            "reset_webauthn": gettext("【CoinEx】邮箱验证"),
        },
    }

    DO_NOT_TRANSLATE = \
        {
            'confirmation':
                {
                    'id_ID': [],
                    'tr_TR': [],
                    'zh_Hant_HK': []
                },
            'notice': {
                'es_ES': [],
                'fr_FR': [],
                'id_ID': [],
                'tr_TR': [],
                'ja_JP': [],
                'ko_KP': [],
                'ru_KZ': [],
                'zh_Hant_HK': [],
                'vi_VN': [],
                'ar_AE': [],
            },
            'verification_code': {
                'ko_KP': [],
                'zh_Hant_HK': []}
        }

    @classmethod
    def email_frequency_check(cls, email: str) -> bool:
        """ 检查邮件发送是否超过频率限制 """
        if not email:
            return False

        cache = EmailSendCountCache()
        if cache.hincrby_if_fewer_than(email):
            return True
        return False

    @classmethod
    def pick_hander(cls):
        if random.random() < 0.002:
            return [MailgunEmail]
        return [AWSEmail]

    @classmethod
    def send(cls,
             recipient: Union[List[str], str],
             subject: str,
             content: str,
             sender: str = None,
             handlers: List[BaseEmail] = None,
             cc_address: Union[List[str], str] = None,
             bcc_address: Union[List[str], str] = None):
        recipients = recipient if isinstance(recipient, list) else [recipient]
        recipients = list(filter(cls.email_frequency_check, recipients))
        if not recipients:
            return
        current_app.logger.info(f'recipients:{recipients} \n'
                                f'subject: {subject} \n')
        # 优先aws发送
        handlers = handlers or cls.pick_hander()
        for address in recipients:
            for handler in handlers:
                # wrap with monitor
                monitored_send = monitor_wrap(
                    handler.send,
                    "email_client",
                    "job",
                    labels={"email_type": handler.MONITOR_KEY},
                    failed_checker=lambda s: s is False,
                )
                with monitored_send(
                        address,
                        subject,
                        content,
                        sender=sender,
                        cc_address=cc_address,
                        bcc_address=bcc_address,
                ) as ok:
                    if ok:
                        break
                    else:
                        current_app.logger.error(f"failed to send email to {address} by {handler.MONITOR_KEY}")

    @classmethod
    def send_from_template(cls,
                           template_category: str,
                           template_type: str,
                           template_args: Dict[str, Any],
                           recipient: str,
                           lang: str,
                           *,
                           subject: str = None,
                           handlers: List[BaseEmail] = None,
                           cc_address: Union[List[str], str] = None,
                           bcc_address: Union[List[str], str] = None):
        from .security import update_security_statistics, SecuritySettingType

        if lang is None or not isinstance(lang, str):
            raise ValueError(lang)
        black_list = cls.DO_NOT_TRANSLATE.\
            get(template_category, {}).\
            get(lang, [])
        # 用默认语言替换掉目前使用语言
        if template_type in black_list:
            lang = Language.DEFAULT.value
        template_args.update(lang=lang)
        template_args.update(icon_link_mapper={
            key: value.get(lang)
            for key, value in cls.current_email_link_map().items()
        })
        if not (anti_phishing_code := template_args.get('anti_phishing_code')):
            anti_phishing_code = get_anti_phishing_code_by_email(recipient)
            template_args.update(anti_phishing_code=anti_phishing_code)
        if not template_args.get('support_url'):
            template_args.update(support_url=config['SUPPORT_URL'])
        # 防止和业务参数同名
        if not template_args.get('_site_url'):
            template_args.update(_site_url=config['SITE_URL'])
        if not (user_id := template_args.get("user_id")):
            user = User.query.filter(User.email == recipient).with_entities(User.id).first()
            if user:
                user_id = user.id
                template_args.update(user_id=user_id)
        template_args.update(recipient_email=recipient)
        template_path = f'email/{template_category}/{template_type}.j2'
        with force_locale(lang):
            if subject is None:
                # noinspection PyUnresolvedReferences
                subject = render_template_string(
                    gettext(cls.EMAIL_SUBJECT_MAPPING[template_category][template_type]),
                    **template_args
                )
            # noinspection PyUnresolvedReferences
            content = render_template(
                template_path,
                **template_args
            )
        subject = config['EMAIL_PREFIX'] + subject
        EmailSender.send(recipient, subject, content, handlers=handlers, cc_address=cc_address, bcc_address=bcc_address)
        if anti_phishing_code:
            if user_id:
                update_security_statistics([user_id], SecuritySettingType.PHISHING_CODE)
            else:
                update_security_statistics([], SecuritySettingType.PHISHING_CODE)

    @classmethod
    def current_email_link_map(cls) -> Dict:
        from . import cached

        @cached(ttl=6 * 60)
        def get_icon_link_map():
            return FooterConfig.gen_media_lang_dic()

        res = get_icon_link_map()
        return res


class _Path:
    def __getattribute__(self, __name: str) -> Any:
        attr = getattr(WebPagePath, __name)
        val = attr.value
        if '?' in val:
            return val + '&sourcefrom=autoemail'
        else:
            return val + '?sourcefrom=autoemail'

_web_path = _Path()

@celery_task
def send_email(recipient: Union[List[str], str],
               subject: str,
               content: str,
               sender: str = None):
    EmailSender.send(recipient, subject, content, sender)


ORI_EMAIL_LANGUAGE_ICON_LINK_MAPPER = {
    Media.TELEGRAM.value: {
        Language.EN_US.value: 'https://t.me/CoinExOfficialEN',
        Language.ZH_HANS_CN.value: 'https://t.me/CoinExCN',
        Language.ZH_HANT_HK.value: 'https://t.me/CoinExTraditionalCN',
        Language.JA_JP.value: 'https://t.me/CoinEx_Japanese',
        Language.RU_KZ.value: 'https://t.me/CoinEx_Russia',
        # Language.KO_KP.value: 'https://t.me/CoinEx_korea_official',
        Language.ID_ID.value: 'https://t.me/CoinExChat',
        Language.ES_ES.value: 'https://t.me/CoinEx_Spanish',
        Language.FA_IR.value: 'https://t.me/CoinExOfficialPersian',
        Language.TR_TR.value: 'https://t.me/CoinEx_Turkey',
        Language.VI_VN.value: 'https://t.me/CoinEx_Vietnam',
        Language.AR_AE.value: 'https://t.me/CoinExGlobalArabic',
        Language.FR_FR.value: 'https://t.me/CoinEx_France',
        Language.PT_PT.value: 'https://t.me/CoinEx_Portuguese',
        Language.DE_DE.value: 'https://t.me/CoinEx_Germany',
    },
    Media.TWITTER.value: {
        Language.EN_US.value: 'https://twitter.com/coinexcom',
        Language.JA_JP.value: 'https://twitter.com/CoinEXJapan1',
        Language.RU_KZ.value: 'https://twitter.com/CoinexR',
        # Language.KO_KP.value: 'https://twitter.com/CoinexK',
        Language.ID_ID.value: 'https://twitter.com/CoinexIndonesia',
        Language.ES_ES.value: 'https://twitter.com/CoinExSpanish',
        Language.FA_IR.value: 'https://twitter.com/CoinEx_official',
        Language.TR_TR.value: 'https://twitter.com/coinex_turkiye',
        Language.AR_AE.value: 'https://twitter.com/CoinexArabic',
        Language.FR_FR.value: 'https://twitter.com/CoinexF',
        Language.PT_PT.value: 'https://twitter.com/CoinexPortugues',
        Language.DE_DE.value: 'https://twitter.com/CoinexG',
    },
    Media.FACEBOOK.value: {
        Language.EN_US.value: 'https://www.facebook.com/TheCoinEx',
        Language.ZH_HANS_CN.value: 'https://www.facebook.com/CoinExChinese',
        Language.RU_KZ.value: 'https://www.facebook.com/CoinEx-Russia-CIS-111675464484049',
        Language.ID_ID.value: 'https://www.facebook.com/CoinExIndonesia',
        Language.ES_ES.value: 'https://www.facebook.com/CoinExSpanish',
        Language.VI_VN.value: 'https://www.facebook.com/CoinExVietNamFan',
        Language.AR_AE.value: 'https://www.facebook.com/CoinExMENA',
        Language.FR_FR.value: 'https://www.facebook.com/CoinEx-French-108725188027387/',
        Language.PT_PT.value: 'https://www.facebook.com/CoinEx-Portugu%C3%AAs-106768021655206',
        Language.DE_DE.value: 'https://www.facebook.com/coinexdeu',

    },
    Media.MEDIUM.value: {
        Language.EN_US.value: 'https://medium.com/@CoinEx',
        Language.ZH_HANS_CN.value: 'https://medium.com/@coinex_33354',
        Language.JA_JP.value: 'https://coinexjapan.medium.com',
        Language.RU_KZ.value: 'https://medium.com/@coinexrus',
        # Language.KO_KP.value: 'https://coinexkrorea.medium.com/',
        Language.ES_ES.value: 'https://medium.com/@spanish-marketing',
        Language.FA_IR.value: 'https://coinexpersianofficial.medium.com',
        Language.TR_TR.value: 'https://coinex-turkey.medium.com/',
        Language.VI_VN.value: 'https://coinexvietnam.medium.com/',
        Language.FR_FR.value: 'https://coinexfrench.medium.com/',
        Language.PT_PT.value: 'https://medium.com/@CoinexPortugues',
        Language.DE_DE.value: 'https://coinexdeu.medium.com',

    },
    Media.REDDIT.value: {
        Language.EN_US.value: 'https://www.reddit.com/r/Coinex'
    },
    Media.INSTAGRAM.value: {
        Language.EN_US.value: 'https://www.instagram.com/coinexcom/',
        Language.ZH_HANT_HK.value: 'https://www.instagram.com/coinexweb/',
        Language.JA_JP.value: 'https://www.instagram.com/coinex_japan/',
        # Language.KO_KP.value: 'https://www.instagram.com/coinex_korea/',
        Language.ID_ID.value: 'https://www.instagram.com/coinex_indonesia/',
        Language.FA_IR.value: 'https://www.instagram.com/coinex_persianofficial/',
        Language.TR_TR.value: 'https://www.instagram.com/coinexturkeyofficial/',
        Language.AR_AE.value: 'https://www.instagram.com/coinex_arabic/',
        Language.FR_FR.value: 'https://www.instagram.com/coinexfr/',
        Language.PT_PT.value: 'https://www.instagram.com/coinex_portugues/',
        Language.DE_DE.value: 'https://www.instagram.com/coinexdeu/',
    },
    Media.YOUTUBE.value: {
        Language.EN_US.value: 'https://www.youtube.com/channel/UCMAuqO8ZqfBwgL51-fY5n4g/videos',
        Language.ZH_HANS_CN.value: 'https://www.youtube.com/channel/UCPKTMpggv4BUOQTe2c0Ti8A',
        Language.JA_JP.value: 'https://www.youtube.com/channel/UCMibfnR_DDWqzsdA4T7ya_Q',
        # Language.KO_KP.value: 'https://www.youtube.com/channel/UC5piupLPOwMQHc5hH0GRtTQ',
        Language.ES_ES.value: 'https://www.youtube.com/channel/UCg3pLsE2sNQcEtO7P8H2FOg',
        Language.FA_IR.value: 'https://www.youtube.com/channel/UC22ftvkdS57B_2Ib_X8opwQ',
        Language.TR_TR.value: 'https://www.youtube.com/channel/UCj8b3-MsrsAKji-Q6qK0TKQ',
        Language.VI_VN.value: 'https://www.youtube.com/channel/UCEZLLCWdqfXG8HmtVSlHHUg',
        Language.AR_AE.value: 'https://www.youtube.com/channel/UCDp23XEh7Eo7ICZsi-_Czcw',
        Language.FR_FR.value: 'https://www.youtube.com/channel/UCXtoj13lVp8NP1qY_KaQLYQ',
        Language.PT_PT.value: 'https://www.youtube.com/channel/UChCgNcu-o6i6f1rYlfSLzvQ',
        Language.DE_DE.value: 'https://www.youtube.com/channel/UC8sOFmkvy0yfjJusf8FNgfA',
    }
}


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_verification_code_email(email: str,
                                 code_type: Union[EmailCodeType, str],
                                 name: str,
                                 code: str,
                                 lang: str):
    anti_phishing_code = get_anti_phishing_code_by_email(email)

    EmailSender.send_from_template(
        'verification_code',
        code_type.value if isinstance(code_type, EmailCodeType) else code_type,
        dict(
            name=name,
            validate_code=code,
            anti_phishing_code=anti_phishing_code,
            reset_url=_web_path.ACCOUNT_SETTING,
            forbid_url=_web_path.ACCOUNT_FORBID,
            submit_url=_web_path.SUPPORT_CENTER_SUBMIT_TICKET,
        ),
        email,
        lang
    )


def _send_confirmation_email(email: str,
                            email_type: str,
                            token: str,
                            url: str,
                            template_args: Dict[str, Any],
                            lang, *,
                            subject: str = None):
    _template_args = dict(
        site_url=url.format(token=token, email=email),
        name=email
    )
    _template_args.update(template_args)
    EmailSender.send_from_template(
        'confirmation',
        email_type,
        _template_args,
        email,
        lang,
        subject=subject
    )


@celery_task
def send_notice_email(email: str,
                      email_type: str,
                      template_args: Dict[str, Any],
                      lang: str,
                      subject: str = None,
                      handlers: List[BaseEmail] = None,
                      cc_address: Union[List[str], str] = None,
                      bcc_address: Union[List[str], str] = None
                      ):
    EmailSender.send_from_template(
        'notice',
        email_type,
        template_args,
        email,
        lang,
        subject=subject,
        handlers=handlers,
        cc_address=cc_address,
        bcc_address=bcc_address,
    )
    

@celery_task(queue=CeleryQueues.EMAIL_PUSH)
def _batch_send_push_email(push_id: int, user_ids: list[int], lang: str):
    current_app.logger.warning(f"batch_send_push_email: {push_id} user len: {len(user_ids)} lang: {lang}")

    # 过滤流失用户
    from app.business.admin_tag import AdminTagHelper
    valied_user_ids = list(AdminTagHelper.filter_inactive_users(user_ids))

    push = EmailPush.query.get(push_id)
    if push.status is EmailPush.Status.CANCELLED:
        return
    
    c_model = EmailPushContent
    content_obj = c_model.query.filter(c_model.email_push_id == push_id, c_model.lang == Language(lang)).first()
    if not content_obj or not content_obj.title or not content_obj.content:
        current_app.logger.warning(f"{push_id} lang: {lang} content_obj is None")
        return
    support_unsubscribe = False
    if push.push_type != EmailPush.PushType.SYSTEM.value:
        support_unsubscribe = True
    
    
    token_model = EmailPushUnsubscription
    exists_rows = token_model.query.filter(
        token_model.push_id == push_id,
        token_model.user_id.in_(valied_user_ids)
    ).with_entities(token_model.user_id).all()
    exists_ids = {i.user_id for i in exists_rows}
    _user_ids = set(valied_user_ids) - exists_ids
    
    u_model = User
    users = u_model.query.filter(
        u_model.id.in_(_user_ids)
        ).all()
    user_map = {i.id: (i.email, i.name_displayed) for i in users if i.email}
    
    new_user_ids = list(user_map.keys())
    token_map = {i: new_hex_token(64) for i in new_user_ids}
    
    new_rows = [
        EmailPushUnsubscription(
            user_id=i, 
            push_id=push_id, 
            token=token_map[i]
        )
    for i in new_user_ids]
    try:
        db.session.bulk_save_objects(new_rows)
        db.session.commit()
        current_app.logger.warning(f"{push_id} lang: {lang} write db len: {len(new_user_ids)} success")
    except Exception as e:
        current_app.logger.error(f"{push_id} {new_user_ids} bulk_save_objects error: {e}")
    
    for user_id, (email, name) in user_map.items():
        token = token_map[user_id]
        user_token = HMAC(str(push_id).encode(), email.encode(), sha256).hexdigest()
        subject = content_obj.title if content_obj.title.startswith('【CoinEx】') else f'【CoinEx】{content_obj.title}'
        try:
            EmailSender.send_from_template(
                    'notice',
                    'announcement_notice',
                    dict(
                        name=name,
                        site_url=config['SITE_URL'],
                        title=content_obj.title,
                        content=content_obj.content,
                        manager_id=push_id,
                        unsubscribe_uri=token,
                        user_token=user_token,
                        support_unsubscribe=support_unsubscribe
                    ),
                    email,
                    lang,
                    subject=subject,
                )
        except Exception as e:
            current_app.logger.error(f"{push_id} send_push_email {user_id} {email} error: {e}")
    current_app.logger.warning(f"{push_id} lang: {lang} send email len: {len(new_user_ids)} success")
    

@celery_task(queue=CeleryQueues.EMAIL_PUSH)
def batch_send_push_email(push_id: int, user_ids: list[int], lang: str):
    _batch_send_push_email(push_id, user_ids, lang)

@celery_task(queue=CeleryQueues.BIG_EMAIL_PUSH)
def batch_send_big_push_email(push_id: int, user_ids: list[int], lang: str):
    _batch_send_push_email(push_id, user_ids, lang)


@celery_task
def send_resume_deposit_withdrawal_notice_email(
        user_ids: list,
        type_: str,
        asset: str,
        asset_chain_str: str,
):
    """ 充值订阅恢复、提现订阅恢复 """
    if type_ == "DEPOSIT":
        url_path = WebPagePath.DEPOSIT
        email_type = "deposit_resumed_notice"
    else:
        url_path = WebPagePath.WITHDRAW
        email_type = "withdrawal_resumed_notice"

    users: List[User] = []
    for chunk_user_ids in batch_iter(user_ids, 1000):
        chunk_users = User.query.filter(User.id.in_(chunk_user_ids)).all()
        users.extend(chunk_users)
    for user in users:
        if (email := user.main_user_email) is None:
            continue

        pref = UserPreferences(user.id)
        lang = pref.language.value
        # url: https://www.coinex.com/asset/withdraw?type=USDT
        url = url_path.format_page(type=asset, lang=lang)
        send_notice_email(
            email=email,
            email_type=email_type,
            template_args=dict(
                name=user.name_displayed,
                asset=asset,
                asset_chain_str=asset_chain_str,
                url=url,
            ),
            lang=lang,
        )


@celery_task
def send_expired_withdrawal_notice_email(withdrawal_id: int):
    """ 超时未确认取消提现 """
    from app.assets import get_chain

    if (withdrawal := Withdrawal.query.get(withdrawal_id)) is None:
        raise ValueError(f"withdrawal {withdrawal_id} does not exist")
    user: User = withdrawal.user
    if (email := user.main_user_email) is None:
        return None

    asset_chain_str = withdrawal.asset
    if withdrawal.chain:
        if is_multi_chain(withdrawal.asset):
            chain_name = get_chain(withdrawal.chain).display_name
            if chain_name != withdrawal.asset:
                asset_chain_str = f"{withdrawal.asset}-{chain_name}"

    pref = UserPreferences(user.id)
    send_notice_email(
        email=email,
        email_type="withdrawal_expired_notice",
        template_args=dict(
            name=user.name_displayed,
            amount=f"{amount_to_str(withdrawal.amount)}",
            withdrawal_time_str=datetime_to_str(withdrawal.created_at, pref.timezone_offset),
            asset_chain_str=asset_chain_str,
            address=withdrawal.address,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_cancelled_withdrawal_notice_email(withdrawal_id: int):
    """ 其他原因取消提现 """
    from app.assets import get_chain

    if (withdrawal := Withdrawal.query.get(withdrawal_id)) is None:
        raise ValueError(f"withdrawal {withdrawal_id} does not exist")
    user: User = withdrawal.user
    if (email := user.main_user_email) is None:
        return None

    asset_chain_str = withdrawal.asset
    if withdrawal.chain:
        if is_multi_chain(withdrawal.asset):
            chain_name = get_chain(withdrawal.chain).display_name
            if chain_name != withdrawal.asset:
                asset_chain_str = f"{withdrawal.asset}-{chain_name}"

    pref = UserPreferences(user.id)
    send_notice_email(
        email=email,
        email_type="withdrawal_cancelled_notice",
        template_args=dict(
            name=user.name_displayed,
            amount=f"{amount_to_str(withdrawal.amount)}",
            withdrawal_time_str=datetime_to_str(withdrawal.created_at, pref.timezone_offset),
            asset_chain_str=asset_chain_str,
            address=withdrawal.address,
            url=_web_path.WITHDRAW_RECORD
        ),
        lang=pref.language.value,
    )


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_withdrawal_confirmation_email(approver_id: Optional[int], withdrawal_id: int, token: str,
                                       host_url: str, resend: bool = False):
    withdrawal = Withdrawal.query.get(withdrawal_id)
    from_user = User.query.get(withdrawal.user_id)
    pref = UserPreferences(from_user.id)
    lang = pref.language.value
    if not approver_id:  # 无多人审核，发给用户自己
        is_self = True
        email = from_user.email
        from_name = ''
        anti_phishing_code = pref.anti_phishing_code
        with force_locale(lang):
            effective_time_prompt = gettext('为保障账号安全，该链接30分钟内有效。提现申请24小时内未确认将自动取消。')
    else:   # 多人审核，发给自己或其他人
        approver = WithdrawalApprover.query.get(approver_id)
        is_self = approver.is_self
        email = approver.email
        from_name = WithdrawalApprover.get_self(from_user.id).name
        anti_phishing_code = UserPreferences(approver.user_id).anti_phishing_code
        with force_locale(lang):
            effective_time_prompt = gettext('为保障账号安全，该链接1小时内有效。提现申请24小时内未确认将自动取消。')

    name = from_user.name_displayed if is_self else ""
    role = 'initiator' if is_self else 'partner'
    url = url_join(host_url, '/asset/withdraw/approve', token=token, role=role, lang=lang)
    subject = None
    if resend and not is_self:    # when resend to others, title is different
        with force_locale(lang):
            subject = gettext(EmailSender.EMAIL_SUBJECT_MAPPING['confirmation']['withdraw_coin_resend'])

    deflate = False
    chain = None
    chain_name = None
    if withdrawal.type == Withdrawal.Type.ON_CHAIN:
        c = get_asset_chain_config(withdrawal.asset, withdrawal.chain)
        deflate = c.deflation_rate != 0
        chain = withdrawal.chain
        chain_name = ChainDisplayMappingCache().hget(chain) or ''

    _send_confirmation_email(email, 'withdraw_coin', token, url, dict(
        name=name,
        from_name=from_name,
        from_account=from_user.email,
        is_self=is_self,
        time=datetime_to_str(withdrawal.created_at, pref.timezone_offset),
        coin_type=withdrawal.asset,
        address=withdrawal.address,
        memo=withdrawal.memo,
        amount=amount_to_str(withdrawal.amount),
        deflate=deflate,
        support_url=config['SUPPORT_URL'],
        reset_url=_web_path.ACCOUNT_SETTING,
        forbid_url=_web_path.ACCOUNT_FORBID,
        submit_url=_web_path.SUPPORT_CENTER_SUBMIT_TICKET,
        anti_phishing_code=anti_phishing_code,
        chain=chain,
        chain_name=chain_name,
        effective_time_prompt=effective_time_prompt,
    ), lang=lang, subject=subject)


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_withdrawal_approver_confirmation_email(user_id: int, request_id: str, token: str, host_url: str):
    cache = WithdrawalApproverCache(user_id, request_id)
    approvers = json.loads(cache.read())
    user = User.query.get(user_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value

    url = url_join(host_url, '/approve/multiple/validate', token=token, lang=lang)
    _send_confirmation_email(user.email, 'multi_approve_confirm', token, url, dict(
        name=user.name_displayed,
        approvers=approvers,
        support_url=config['SUPPORT_URL'],
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=lang)


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_withdrawal_approver_join_confirmation_email(approver_id: int, token: str, host_url: str):
    approver = WithdrawalApprover.query.get(approver_id)
    from_user = User.query.get(approver.user_id)
    pref = UserPreferences(from_user.id)
    lang = pref.language.value

    url = url_join(host_url, '/approve/multiple/join', token=token, lang=lang)
    _send_confirmation_email(approver.email, 'multi_approve_join', token, url, dict(
        name="",
        from_name=WithdrawalApprover.get_self(from_user.id).name,
        from_account=from_user.email
    ), lang=lang)


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_withdrawal_approver_leave_confirmation_email(approver_id: int, token: str, host_url: str):
    approver = WithdrawalApprover.query.get(approver_id)
    from_user = User.query.get(approver.user_id)
    pref = UserPreferences(from_user.id)
    lang = pref.language.value

    url = url_join(host_url, '/approve/multiple/quit', token=token, lang=lang)
    _send_confirmation_email(approver.email, 'multi_approve_leave', token, url, dict(
        name="",
        from_name=WithdrawalApprover.get_self(from_user.id).name,
        from_account=from_user.email
    ), lang=lang)


@celery_task
def send_withdrawal_approver_join_notice_email(approver_id: int):
    approver = WithdrawalApprover.query.get(approver_id)
    user =  User.query.get(approver.user_id)
    pref = UserPreferences(user.id)

    send_notice_email(user.email, 'multi_approve_join', dict(
        name=user.name_displayed,
        target_name=approver.name,
        account=approver.account,
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=pref.language.value)


@celery_task
def send_withdrawal_approver_reject_notice_email(approver_id: int):
    approver = WithdrawalApprover.query.get(approver_id)
    user = User.query.get(approver.user_id)
    pref = UserPreferences(user.id)

    send_notice_email(user.email, 'multi_approve_reject', dict(
        name=user.name_displayed,
        target_name=approver.name,
        account=approver.account,
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=pref.language.value)


@celery_task
def send_withdrawal_approver_leave_notice_email(approver_id: int):
    approver = WithdrawalApprover.query.get(approver_id)
    user =  User.query.get(approver.user_id)
    pref = UserPreferences(user.id)

    send_notice_email(user.email, 'multi_approve_leave', dict(
        name=user.name_displayed,
        target_name=approver.name,
        account=approver.account,
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=pref.language.value)


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_api_withdrawal_address_confirmation_email(approver_id: Optional[int], request_id: int, token: str, host_url: str):
    req = ApiWithdrawalAddressRequest.query.get(request_id)
    from_user = User.query.get(req.user_id)
    pref = UserPreferences(from_user.id)
    lang = pref.language.value
    anti_phishing_code = pref.anti_phishing_code
    if not approver_id:  # 无多人审核，发给用户自己
        is_self = True
        email = from_user.email
        from_name = ''
        with force_locale(lang):
            effective_time_prompt = gettext('为保障账号安全，该链接30分钟内有效。24小时内未确认将自动取消。')
    else:   # 多人审核，发给自己或其他人
        approver = WithdrawalApprover.query.get(approver_id)
        is_self = approver.is_self
        email = approver.email
        from_name = WithdrawalApprover.get_self(from_user.id).name
        anti_phishing_code = UserPreferences(approver.user_id).anti_phishing_code
        with force_locale(lang):
            effective_time_prompt = gettext('为保障账号安全，该链接1小时内有效。24小时内未确认将自动取消。')

    name = from_user.name_displayed if is_self else ""
    role = 'initiator' if is_self else 'partner'
    url = url_join(host_url, '/apikey/whitelist/approve', token=token, role=role, lang=lang)
    _send_confirmation_email(email, 'add_api_address', token, url, dict(
        name=name,
        from_name=from_name,
        from_account=from_user.email,
        is_self=is_self,
        time=datetime_to_str(req.created_at, pref.timezone_offset),
        addresses=json.loads(req.data),
        support=config['SUPPORT_URL'],
        anti_phishing_code=anti_phishing_code,
        effective_time_prompt=effective_time_prompt,
    ), lang=lang)


@celery_task
def send_api_withdrawal_address_cancel_email(request_id: int):
    req = ApiWithdrawalAddressRequest.query.get(request_id)
    user = User.query.get(req.user_id)
    pref = UserPreferences(user.id)

    send_notice_email(user.email, 'api_address_cancelled', dict(
        name=user.name_displayed,
        time=datetime_to_str(req.created_at, pref.timezone_offset),
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=pref.language.value)


@celery_task
def send_email_registered_notice_email(email: str):
    """ 邮箱已注册过的通知 """
    user = User.query.filter(User.email == email).first()
    if not user:
        return
    pref = UserPreferences(user.id)
    send_notice_email(
        email,
        'email_registered',
        dict(
            name=user.name_displayed,
            email=email,
            url=_web_path.SIGNIN,
            reset_url=_web_path.ACCOUNT_SETTING,
            forbid_url=_web_path.ACCOUNT_FORBID,
            submit_url=_web_path.SUPPORT_CENTER_SUBMIT_TICKET,
            anti_phishing_code=pref.anti_phishing_code,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_registration_notice_email(user_id: int):
    user: User = User.query.get(user_id)
    if user is None:
        raise ValueError(f'invalid user id: {user_id}')
    if not (email := user.main_user_email):
        return

    pref = UserPreferences(user_id)
    # 发送特定模版的邮件
    template_id = config.get("SIGN_EMAIL_TEMPLATE_ID")
    lang = pref.language
    model = OperationTemplateContent
    content_obj = model.query.filter(
        model.template_id == template_id,
        model.lang == lang
    ).first()
    if not content_obj:
        return
    subject = content_obj.title if content_obj.title.startswith('【CoinEx】') else f'【CoinEx】{content_obj.title}'
    send_notice_email(
        email,
        'sign_up_success',
        dict(
            name=user.name_displayed,
            anti_phishing_code=pref.anti_phishing_code,
            title=content_obj.title,
            content=content_obj.content,
        ),
        lang=pref.language.value,
        subject=subject,
    )


def is_sign_in_unusual(last_login_history: LoginHistory) -> bool:
    """检查过去30天登录记录中, 该IP所在的城市是否登陆过"""
    last_city = GeoIP(last_login_history.ip).city
    if not last_city:
        # 没有分析出city时不做检查
        return False

    history_ips = set()
    for login_history in LoginHistory.query.filter(
        LoginHistory.user_id == last_login_history.user_id,
        LoginHistory.successful.is_(True),
        LoginHistory.id != last_login_history.id,
        LoginHistory.updated_at >= now() - timedelta(days=30),
    ).with_entities(
        LoginHistory.ip,
    ).all():
        history_ips.add(login_history.ip)
    if not history_ips:
        # 没有历史登录记录的不做检查
        return False
    history_city_set = set()
    for ip in history_ips:
        history_city = GeoIP(ip).city
        if history_city:
            history_city_set.add(history_city)
    return last_city not in history_city_set


@celery_task
def send_login_notice_email(login_history_id: int):
    history: LoginHistory = LoginHistory.query.get(login_history_id)
    if history is None:
        raise ValueError(f'login history {login_history_id!r} does not exist')
    user: User = history.user
    if not user.main_user_email:
        return

    pref = UserPreferences(user.id)
    lang = pref.language.value

    if is_sign_in_unusual(history):
        send_notice_email(user.main_user_email, 'sign_in_unusual', dict(
            name=user.name_displayed,
            create_time=datetime_to_str(history.created_at,
                                        pref.timezone_offset),
            ip=history.ip,
            location=history.location,
            site_url=config['SITE_URL'],
            support_url=config['SUPPORT_URL'],
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)
        db.session_add_and_commit(
            Message(
                user_id=user.id,
                title=MessageTitle.SIGN_IN_UNUSUAL.name,
                content=MessageContent.SIGN_IN_UNUSUAL.name,
                params=json.dumps(
                    dict(
                        ip=history.ip,
                        time=datetime_to_str(history.created_at, pref.timezone_offset),
                        location=history.location,
                    )
                ),
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ACCOUNT_SETTING_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )
    else:
        send_notice_email(user.main_user_email, 'sign_in', dict(
            name=user.name_displayed,
            create_time=datetime_to_str(history.created_at,
                                        pref.timezone_offset),
            ip=history.ip,
            location=history.location,
            anti_phishing_code=pref.anti_phishing_code,
            reset_url=_web_path.ACCOUNT_SETTING,
            forbid_url=_web_path.ACCOUNT_FORBID,
            submit_url=_web_path.SUPPORT_CENTER_SUBMIT_TICKET,
        ), lang=lang)


@celery_task
def send_sign_off_notice_email(user_id: int, ts: int):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    record = SignOffUser.query.filter(
        SignOffUser.user_id == user_id,
    ).first()
    send_notice_email(record.email, 'sign_off', dict(
        name=user.name_displayed or record.email,
        time=timestamp_to_datetime(ts).strftime('%Y-%m-%d %H:%M:%S'),
    ), lang=pref.language.value)


@celery_task
def send_login_failure_notice_task(user_id: int):
    user: User = User.query.get(user_id)
    if user is None:
        raise ValueError(f'invalid user id: {user_id!r}')

    pref = UserPreferences(user_id)

    send_notice_email(user.main_user_email, 'sign_in_fail', dict(
        name=user.communication_name,
        create_time=datetime_to_str(now(), pref.timezone_offset),
        site_url=config['SITE_URL'],
        support_url=config['SUPPORT_URL'],
        anti_phishing_code=pref.anti_phishing_code,
    ), lang=pref.language.value)


@celery_task
def send_too_small_deposit_notice_email(deposit_id: int):
    """ 充值低于最小金额 """
    from app.assets import get_asset_chain_config, get_chain

    if (deposit := Deposit.query.get(deposit_id)) is None:
        raise ValueError(f"deposit {deposit_id} does not exist")
    user: User = deposit.user
    if (email := user.main_user_email) is None:
        return None

    address, memo = deposit.address, deposit.memo
    asset, chain = deposit.asset, deposit.chain
    binary = func.binary
    total_too_small_amount = (
        Deposit.query.filter(
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.address == binary(address),
            Deposit.memo == binary(memo),
            Deposit.chain == binary(chain),
            Deposit.asset == binary(asset),
            # status的过滤和_handle_too_small_deposits保持一致
            Deposit.status.in_([Deposit.Status.PROCESSING, Deposit.Status.CONFIRMING, Deposit.Status.TOO_SMALL]),
        )
        .with_entities(func.sum(Deposit.amount))
        .all()
    )
    total_too_small_amount = total_too_small_amount[0][0] if total_too_small_amount else Decimal()
    min_amount = get_asset_chain_config(asset, chain).min_deposit_amount
    if total_too_small_amount < min_amount:
        asset_chain_str = deposit.asset
        if deposit.chain:
            if is_multi_chain(deposit.asset):
                chain_name = get_chain(deposit.chain).display_name
                if chain_name != deposit.asset:
                    asset_chain_str = f"{deposit.asset}-{chain_name}"

        diff_amount = min_amount - total_too_small_amount
        pref = UserPreferences(user.id)
        lang = pref.language.value
        url = WebPagePath.DEPOSIT.format_page(type=deposit.asset, lang=lang)
        send_notice_email(
            email=email,
            email_type="deposit_amount_too_small",
            template_args=dict(
                name=user.name_displayed,
                diff_amount=f"{amount_to_str(diff_amount)}",
                amount=f"{amount_to_str(deposit.amount)}",
                min_amount=f"{amount_to_str(min_amount)}",
                deposit_time_str=datetime_to_str(deposit.created_at, pref.timezone_offset),
                asset_chain_str=asset_chain_str,
                url=url
            ),
            lang=lang,
        )


@celery_task
def send_pending_deposit_notice_email(deposit_id: int):
    if (deposit := Deposit.query.get(deposit_id)) is None:
        raise ValueError(f'deposit {deposit_id} does not exist')

    user: User = deposit.user
    pref = UserPreferences(user.id)

    if pref.allows_deposit_withdrawal_emails:
        from ..assets import get_asset_chain_config
        ac_conf = get_asset_chain_config(deposit.asset, deposit.chain)
        send_notice_email(user.main_user_email, 'deposit_to_receive', dict(
            name=user.name_displayed,
            amount=f'{amount_to_str(deposit.amount)} {deposit.asset}',
            confirmations=0,
            freeze_confirmations=ac_conf.safe_confirmations,
            tx_id=(tx_id := deposit.tx_id),
            tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=UserPreferences(user.id).language.value)


@celery_task
def send_credited_deposit_notice_email(deposit_id: int):

    if (deposit := Deposit.query.get(deposit_id)) is None:
        raise ValueError(f'deposit {deposit_id} does not exist')

    user: User = deposit.user
    user_id = user.id
    pref = UserPreferences(user_id)

    amount_str = f'{amount_to_str(deposit.amount)} {deposit.asset}'
    date_str = datetime_to_str(deposit.created_at, pref.timezone_offset)
    is_deleted_address = deposit.is_from_deleted_address

    if pref.allows_deposit_withdrawal_emails:
        template = 'deposit_pass_notice_deleted_address' if is_deleted_address else 'deposit_pass_notice'
        send_notice_email(user.main_user_email, template, dict(
            name=user.name_displayed,
            amount=amount_str,
            create_time=date_str,
            transaction_id=deposit.tx_id,
            url=_web_path.DEPOSIT_RECORD + f'&type={deposit.asset}',
            type=deposit.type.name,
            anti_phishing_code=pref.anti_phishing_code,
        ), pref.language.value)

    msg_title = MessageTitle.DEPOSIT.name
    msg_display_type = Message.DisplayType.TEXT
    if deposit.type in [Deposit.Type.ON_CHAIN, Deposit.Type.LOCAL]:
        if deposit.type == Deposit.Type.ON_CHAIN:
            if is_deleted_address:
                msg_title = MessageTitle.DEPOSIT_DELETED_ADDRESS.name
                content = MessageContent.DEPOSIT_DELETED_ADDRESS.name
                params = dict(
                    amount=amount_str,
                    create_time=date_str,
                    tx_id=deposit.tx_id,
                )
                msg_display_type = Message.DisplayType.POPUP_WINDOW
            else:
                content = MessageContent.ON_CHAIN_DEPOSIT.name
                params = dict(
                    amount=amount_str,
                    create_time=date_str,
                    tx_id=deposit.tx_id,
                )
        else:
            content = MessageContent.LOCAL_DEPOSIT.name
            params = dict(
                amount=amount_str,
                create_time=date_str,
            )
        db.session_add_and_commit(
            Message(
                user_id=user_id,
                title=msg_title,
                content=content,
                params=json.dumps(params),
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.DEPOSIT_RECORD_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=msg_display_type,
                channel=Message.Channel.DEPOSIT_WITHDRAWAL,
            )
        )


@celery_task
def send_privacy_asset_deposit_require_kyc_notice_email(deposit_id: int):
    """ 充值隐私币请完成实名认证 """
    if (deposit := Deposit.query.get(deposit_id)) is None:
        raise ValueError(f'deposit {deposit_id} does not exist')

    user: User = deposit.user
    user_id = user.id
    pref = UserPreferences(user_id)

    amount_str = f'{amount_to_str(deposit.amount)} {deposit.asset}'
    send_notice_email(
        user.main_user_email,
        'deposit_privacy_asset_require_kyc',
        dict(
            name=user.name_displayed,
            amount=amount_str,
            site_url=config['SITE_URL'],
            anti_phishing_code=pref.anti_phishing_code,
        ),
        pref.language.value,
    )


@celery_task
def send_sent_withdrawal_notice_email(withdrawal_id: int):
    if (withdrawal := Withdrawal.query.get(withdrawal_id)) is None:
        raise ValueError(f'withdrawal {withdrawal_id} does not exist')

    user: User = withdrawal.user
    user_id = user.id
    pref = UserPreferences(user_id)

    amount_str = f'{amount_to_str(withdrawal.amount)} {withdrawal.asset}'
    date_str = datetime_to_str(withdrawal.created_at, pref.timezone_offset)

    if pref.allows_deposit_withdrawal_emails:
        send_notice_email(user.main_user_email, 'send_coin_withdraw_notice', dict(
            name=user.name_displayed,
            amount=amount_str,
            coin_type=withdrawal.asset,
            time=date_str,
            anti_phishing_code=pref.anti_phishing_code,
            address=withdrawal.address,
            tx_id=withdrawal.tx_id,
            type=withdrawal.type.name,
            url=_web_path.WITHDRAW_RECORD
        ), pref.language.value)

    if withdrawal.type == Withdrawal.Type.ON_CHAIN:
        content = MessageContent.ON_CHAIN_WITHDRAW.name
        params = dict(
            amount=amount_str,
            create_time=date_str,
            tx_id=withdrawal.tx_id,
        )
    else:
        content = MessageContent.LOCAL_WITHDRAW.name
        params = dict(
            amount=amount_str,
            create_time=date_str,
        )
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.WITHDRAW.name,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.WITHDRAWAL_RECORD_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.DEPOSIT_WITHDRAWAL,
        )
    )


@celery_task
def send_sub_account_creation_notice_email(sub_user_id: int, email: str,
                                           password: str):
    sub_account: SubAccount = SubAccount.query.filter(
        SubAccount.user_id == sub_user_id,
        SubAccount.type == SubAccount.Type.NORMAL,
        SubAccount.is_visible.is_(True),
    ).first()
    if sub_account is None:
        raise ValueError(f'sub account user {sub_user_id!r} does not exist')

    pref = UserPreferences(sub_user_id)
    anti_phishing_code = get_anti_phishing_code_by_email(email)

    send_notice_email(email, 'sub_account_register_notice', dict(
        start_time=datetime_to_str(sub_account.created_at,
                                   pref.timezone_offset),
        user_name=sub_account.user.name,
        password=password,
        domain=sub_account.main_user.sub_domain,
        name=sub_account.main_user.email,
        anti_phishing_code=anti_phishing_code,
    ), lang=pref.language.value)


@celery_task
def send_sub_account_batch_cancel_manage_notice_email(relation_ids: List[int]):
    """ 主账号冻结、注销时的 解除授权 通知 """
    rows: List[SubAccountManagerRelation] = SubAccountManagerRelation.query.filter(
        SubAccountManagerRelation.id.in_(relation_ids)
    ).all()
    for row in rows:
        sub_acc: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == row.user_id,
            SubAccount.type == SubAccount.Type.NORMAL,
        ).first()
        sub_user: User = User.query.get(row.user_id)
        main_user: User = User.query.get(row.main_user_id)
        main_user_email = main_user.email
        if not main_user_email:
            # 注销用户清空邮箱了
            sign_off_record = SignOffUser.query.filter(
                SignOffUser.user_id == row.main_user_id,
            ).first()
            if sign_off_record:
                main_user_email = sign_off_record.email
        manager_user: User = User.query.get(row.manager_id)

        pref = UserPreferences(row.manager_id)
        lang = pref.language.value
        with force_locale(lang):
            permissions = [gettext("现货交易"), gettext("理财服务")]  # 加上默认权限
            permissions.extend(["AMM" if i.name == "AMM" else gettext(i.value) for i in sub_acc.enum_permissions])
        template_args = dict(
            name=manager_user.name_displayed,
            sub_user_name=sub_user.name_displayed,
            main_user_email=main_user_email,
            manager_email=manager_user.email,
            permissions="、".join(permissions),
            anti_phishing_code=pref.anti_phishing_code,
        )
        template = "sub_account_cancel_manager"
        if row.manage_type == SubAccountManagerRelation.ManageType.HOSTING:
            template = "sub_account_cancel_hosting"
        send_notice_email(
            email=manager_user.email,
            email_type=template,
            template_args=template_args,
            lang=pref.language.value,
        )


@celery_task
def send_sub_account_manage_notice_email(relation_id: int):
    """ 子账号添加授权 或 解除授权 通知邮件，发给被授权人 """
    row: SubAccountManagerRelation = SubAccountManagerRelation.query.get(relation_id)
    sub_acc: SubAccount = SubAccount.query.filter(
        SubAccount.user_id == row.user_id,
        SubAccount.type == SubAccount.Type.NORMAL,
    ).first()
    sub_user: User = User.query.get(row.user_id)
    main_user: User = User.query.get(row.main_user_id)
    manager_user: User = User.query.get(row.manager_id)

    pref = UserPreferences(row.manager_id)
    lang = pref.language.value
    with force_locale(lang):
        permissions = [gettext("现货交易"), gettext("理财服务")]  # 加上默认权限
        permissions.extend(["AMM" if i.name == "AMM" else gettext(i.value) for i in sub_acc.enum_permissions])
    template_args = dict(
        name=manager_user.name_displayed,
        sub_user_name=sub_user.name_displayed,
        main_user_email=main_user.email,
        manager_email=manager_user.email,
        permissions="、".join(permissions),
        anti_phishing_code=pref.anti_phishing_code,
    )
    if row.status == SubAccountManagerRelation.Status.VALID:
        msg_templates = {
            SubAccountManagerRelation.ManageType.MANAGED: {
                'template': 'sub_account_bind_manager',
                'title': MessageTitle.SUB_ACCOUNT_BIND_MANAGER.name,
                'content': MessageContent.SUB_ACCOUNT_BIND_MANAGER.name,
                'web_link': MessageWebLink.SUB_ACCOUNT_AUTHORIZE_PAGE.value,
            },
            SubAccountManagerRelation.ManageType.HOSTING: {
                'template': 'sub_account_bind_hosting',
                'title': MessageTitle.SUB_ACCOUNT_BIND_HOSTING.name,
                'content': MessageContent.SUB_ACCOUNT_BIND_HOSTING.name,
                'web_link': MessageWebLink.SUB_ACCOUNT_AUTHORIZE_PAGE.value,
            }
        }
        template = msg_templates[row.manage_type]['template']
        title = msg_templates[row.manage_type]['title']
        content = msg_templates[row.manage_type]['content']
        web_link = msg_templates[row.manage_type]['web_link']
    else:
        msg_templates = {
            SubAccountManagerRelation.ManageType.MANAGED: {
                'template': 'sub_account_cancel_manager',
                'title': MessageTitle.SUB_ACCOUNT_CANCEL_MANAGER.name,
                'content': MessageContent.SUB_ACCOUNT_CANCEL_MANAGER.name,
                'web_link': '',
            },
            SubAccountManagerRelation.ManageType.HOSTING: {
                'template': 'sub_account_cancel_hosting',
                'title': MessageTitle.SUB_ACCOUNT_CANCEL_HOSTING.name,
                'content': MessageContent.SUB_ACCOUNT_CANCEL_HOSTING.name,
                'web_link': '',
            }
        }
        template = msg_templates[row.manage_type]['template']
        title = msg_templates[row.manage_type]['title']
        content = msg_templates[row.manage_type]['content']
        web_link = msg_templates[row.manage_type]['web_link']

    send_notice_email(
        email=manager_user.email,
        email_type=template,
        template_args=template_args,
        lang=pref.language.value,
    )

    msg_params = dict(
        sub_user_name=sub_user.name_displayed,
        main_user_email=main_user.email,
        manager_email=manager_user.email,
        permissions="、".join(permissions),
    )
    db.session_add_and_commit(
        Message(
            user_id=manager_user.id,
            title=title,
            content=content,
            params=json.dumps(msg_params),
            extra_info=json.dumps(
                dict(
                    web_link=web_link,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.SYSTEM,
        )
    )


@celery_task
def send_kyc_result_email(verification_id: int):
    kyc: KycVerification = KycVerification.query.get(verification_id)
    if kyc is None:
        raise ValueError
    if kyc.status not in [KycVerification.Status.PASSED, KycVerification.Status.REJECTED]:
        return
    user = kyc.user
    pref = UserPreferences(user.id)

    if not (email := user.main_user_email):
        return

    site_url = config['SITE_URL']
    support_url = config['SUPPORT_URL']
    lang = UserPreferences(user.id).language.value

    if kyc.status is KycVerification.Status.PASSED:
        send_notice_email(email, 'kyc_pass', dict(
            name=user.name_displayed,
            home_link=site_url,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)
        content = MessageContent.KYC_PASS.name
        params = dict()
    else:
        with force_locale(lang):
            send_notice_email(email, 'kyc_fail', dict(
                name=user.name_displayed,
                verify_id_result=kyc.get_reject_reason(),
                account_link=_web_path.ACCOUNT_BASIC,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)
        content = MessageContent.KYC_FAIL.name
        reason = kyc.get_reject_reason(translate=False)
        pro_msg_reason_list = []
        if kyc.rejection_reason == KycVerification.RejectionReason.KYC_PRO_FAILED.name:
            reason = KycVerification.RejectionReason.KYC_PRO_FAILED.value
            kyc_pro: KycVerificationPro = KycVerificationPro.query.filter(
                KycVerificationPro.user_id == kyc.user_id,
                KycVerificationPro.flow == KYCFlow.IDENTITY_AND_ADDRESS,
                KycVerificationPro.status.in_([
                    KycVerificationPro.Status.REJECTED, KycVerificationPro.Status.CANCELLED
                ])
            ).order_by(KycVerificationPro.id.desc()).first()
            pro_reason = kyc_pro.get_reject_reason(translate=False) if kyc_pro else None
            pro_msg_reason_list = pro_reason.split(';') if pro_reason else []

        msg_reason_list = reason.split(';')
        msg_reason_list += pro_msg_reason_list
        params = {
            'need_translates': [
                {
                    'name': 'reject_reason',
                    'text': '{reject_reason}',
                    'params': {'reject_reason': msg_reason_list}
                }
            ]
        }

    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=MessageTitle.KYC_RESULT.name,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_KYC_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        )
    )


@celery_task
def send_kyc_institution_result_email(id_: int):
    kyc: KYCInstitution = KYCInstitution.query.get(id_)
    if kyc is None:
        raise ValueError
    user = User.query.get(kyc.user_id)
    pref = UserPreferences(user.id)

    if not (email := user.main_user_email):
        return

    site_url = config['SITE_URL']
    support_url = config['SUPPORT_URL']
    lang = UserPreferences(user.id).language.value

    if kyc.status is KYCInstitution.Status.PASSED:
        send_notice_email(email, 'kyc_institution_pass', dict(
            name=user.name_displayed,
            home_link=site_url,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)
    else:
        with force_locale(lang):
            send_notice_email(email, 'kyc_institution_fail', dict(
                name=user.name_displayed,
                reject_reason=kyc.get_reject_reason(),
                account_link=_web_path.ACCOUNT_BASIC,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)


@celery_task
def send_risk_screen_failed_email(user_id: int):
    """ 发送风险筛查不通过的邮件 """
    user = User.query.get(user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(user.id)
    site_url = config["SITE_URL"]
    support_url = config["SUPPORT_URL"]
    lang = UserPreferences(user.id).language.value

    send_notice_email(
        email,
        "risk_screen_fail",
        dict(
            name=user.name_displayed,
            home_link=site_url,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        ),
        lang=lang,
    )
    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=MessageTitle.RISK_SCREEN_FAIL.name,
            content=MessageContent.RISK_SCREEN_FAIL.name,
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ASSET_DASHBOARD_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=now() + timedelta(days=7),
            channel=Message.Channel.ACCOUNT_SECURITY,
        )
    )


@celery_task
def send_margin_liquidation_email(user_id: int,
                                  market_name: str,
                                  index_price: str,
                                  risk_rate: str,
                                  loan_asset_map: Dict[str, str],
                                  ):
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='margin_liquidation',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market_name=get_market_business_name(market_name),
                anti_phishing_code=anti_phishing_code,
                index_price=index_price,
                site_url=_web_path.MARGIN_HISTORY,
                user_name=user.name_displayed,
                name=user.name_displayed,
                risk_rate=risk_rate,
                loan_asset_map=loan_asset_map,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_margin_liquidation_warning_email(user_id: int, market_name: str, risk_limit: str):
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='margin_liquidation_warning',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market_name=get_market_business_name(market_name),
                risk_limit=amount_to_str(Decimal(risk_limit) * 100, 2),
                anti_phishing_code=anti_phishing_code,
                site_url=_web_path.MARGIN_ASSET,
                user_name=user.name_displayed,
                name=user.name_displayed,
            ),
            recipient=notice_user.email,
            lang=lang
        )


@celery_task
def send_margin_renew_succeeded_email(user_id: int,
                                      market: str,
                                      asset: str,
                                      amount: str,
                                      day_rate: str,
                                      expired_time: str,
                                      renew_days: int,
                                      site_url: str,
                                      ):
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        template_type = "margin_loan_order_renew_succeeded"  # 现有文案会显示子帐号的名称
        EmailSender.send_from_template(
            template_category='notice',
            template_type=template_type,
            template_args=dict(
                asset=asset,
                expired_time=expired_time,
                anti_phishing_code=anti_phishing_code,
                market=get_market_business_name(market),
                day_rate=day_rate,
                renew_days=renew_days,
                amount=amount,
                site_url=site_url,
                user_name=user.name_displayed,
                name=user.name_displayed,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_margin_renew_notice_email(user_id: int,
                                   asset: str,
                                   expired_time: str,
                                   template_type: str,
                                   market_name: str,
                                   amount: str,
                                   **extra,
                                   ):

    """
    template_type in (
        "margin_loan_order_expired",
        "margin_loan_order_force_flat",
        "margin_renew_failed"
    """
    if template_type == 'margin_loan_order_force_flat':
        url = _web_path.MARGIN_HISTORY
    else:
        url = _web_path.MARGIN_RECORD

    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type=template_type,
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                coin_type=asset,
                expired_time=expired_time,
                amount=amount,
                asset=asset,
                anti_phishing_code=anti_phishing_code,
                market_name=get_market_business_name(market_name),
                url=url,
                name=user.name_displayed,
                **extra,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_perpetual_liquidation_email(
        user_id: int, market: str, side_type: str, leverage: str, amount: str,
        sign_price: str, bkr_price: str, liq_price: str, direction_type: str,
):
    """
    合约爆仓邮件
    """
    asset = PerpetualMarketCache().get_amount_asset(market)
    info = PerpetualMarketCache().get_market_info(market)
    if info['type'] == PerpetualMarketType.INVERSE.value:
        market_type = PerpetualMarketType.INVERSE.name
    else:
        market_type = PerpetualMarketType.DIRECT.name
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_liquidation',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                name=user.name_displayed,
                market=market,
                market_type=market_type,
                side=side_type,
                leverage=leverage,
                amount=amount,
                asset=asset,
                direction=direction_type,
                liq_price=f"{liq_price} {info['money']}",
                sign_price=f"{sign_price} {info['money']}",
                bkr_price=f"{bkr_price} {info['money']}",
                site_url=_web_path.PERPETUAL_DEAL_RECORD,
                anti_phishing_code=anti_phishing_code,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_perpetual_adl_email(user_id: int, market: str, deal_type: str, amount: str, price: str):
    """
    合约自动减仓邮件
    """
    info = PerpetualMarketCache().get_market_info(market)
    if info['type'] == PerpetualMarketType.INVERSE.value:
        market_type = PerpetualMarketType.INVERSE.name
    else:
        market_type = PerpetualMarketType.DIRECT.name
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_adl',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market=market,
                market_type=market_type,
                name=user.name_displayed,
                liq_amount=amount,
                asset=PerpetualMarketCache.get_amount_asset(market),
                liq_price=f'{price} {info["money"]}',
                deal_type=deal_type,
                site_url=_web_path.PERPETUAL_DEAL_RECORD,
                anti_phishing_code=anti_phishing_code,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_perpetual_liquidation_warning_email(user_id: int, market: str, risk: str):
    """
    合约爆仓警告邮件
    """
    info = PerpetualMarketCache().get_market_info(market)
    if info['type'] == PerpetualMarketType.INVERSE.value:
        market_type = PerpetualMarketType.INVERSE.name
    else:
        market_type = PerpetualMarketType.DIRECT.name
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_liquidation_warning',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                name=user.name_displayed,
                market=market,
                market_type=market_type,
                risk=risk,
                site_url=_web_path.PERPETUAL_CURRENT_POSITION_RECORD,
                anti_phishing_code=anti_phishing_code,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_perpetual_profit_loss_email(user_id: int, market: str, account_type: str, operation: str,
                                     amount: str, asset: str, success: bool, market_type: str):
    """
    合约触发止盈止损邮件
    """
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        with force_locale(lang):
            if success:
                if operation == 'STOP_LOSS':
                    subject = gettext("合约止损成功通知")
                else:
                    subject = gettext("合约止盈成功通知")
            else:
                if operation == 'STOP_LOSS':
                    subject = gettext('合约止损部分失败通知')
                else:
                    subject = gettext('合约止盈部分失败通知')
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_profit_loss_notice',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                name=user.name_displayed,
                market=market,
                account=user.name_displayed,
                account_type=account_type,
                operation=operation,
                success=success,
                home_link=WebPagePath.PERPETUAL_STOP_HISTORY_RECORD.value,
                amount=amount,
                asset=asset,
                anti_phishing_code=anti_phishing_code,
                market_type=market_type,
            ),
            recipient=notice_user.email,
            subject=subject,
            lang=lang,
        )


@celery_task
def send_perpetual_position_close_result_email(user_id: int, market: str,
                                               operation: str, amount: str, asset: str):
    market_type = None
    if market:
        info = PerpetualMarketCache().get_market_info(market)
        if info['type'] == PerpetualMarketType.INVERSE.value:
            market_type = PerpetualMarketType.INVERSE.name
        else:
            market_type = PerpetualMarketType.DIRECT.name
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_position_close_result_notice',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market=market or '',
                market_type=market_type,
                name=user.name_displayed,
                operation=operation,
                home_link=_web_path.PERPETUAL_HISTORY_RECORD,
                amount=amount,
                asset=asset,
                anti_phishing_code=anti_phishing_code,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_perpetual_position_reduce_email(user_id: int, market: Optional[str], amount: str, sign_price: str):
    market_type = money_asset = None
    if market:
        info = PerpetualMarketCache().get_market_info(market)
        if info['type'] == PerpetualMarketType.INVERSE.value:
            market_type = PerpetualMarketType.INVERSE.name
        else:
            market_type = PerpetualMarketType.DIRECT.name
        money_asset = info['money']
    asset = PerpetualMarketCache.get_amount_asset(market) if market else None
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        anti_phishing_code = pref.anti_phishing_code
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type='perpetual_position_reduce_notice',
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market=market or '',
                market_type=market_type,
                name=user.name_displayed,
                user_name=user.name_displayed,
                site_url=_web_path.PERPETUAL_CURRENT_POSITION_RECORD,
                amount=amount,
                asset=asset,
                money_asset=money_asset,
                sign_price=f"{sign_price} {money_asset}",
                anti_phishing_code=anti_phishing_code,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_red_packet_registration_email(
        lang: str, send_user_id: int, grab_id: int, email: str, host_url: str):
    from app.business.red_packet.constants import GRAB_EXPIRED_DAY
    from app.models import RedPacketHistory

    def format_datetime_str(value, use_utc=False):
        if use_utc:
            return f'{datetime_to_str(value)} (UTC)'
        return f'{datetime_to_str(value, 480)} (UTC + 8)'

    # noinspection PyBroadException
    try:
        lang = Language(lang)
    except Exception:
        return
    history: RedPacketHistory = RedPacketHistory.query.filter(
        RedPacketHistory.id == grab_id,
        RedPacketHistory.email == email,
        RedPacketHistory.status == RedPacketHistory.Status.UNREGISTER,
        RedPacketHistory.grab_at.isnot(None),
        RedPacketHistory.grab_at > now() + timedelta(days=-GRAB_EXPIRED_DAY)
    ).first()
    if not history:
        return
    utc_flag = lang not in (Language.ZH_HANS_CN, Language.ZH_HANT_HK)
    anti_phishing_code = get_anti_phishing_code_by_email(email)
    EmailSender.send_from_template(
        template_category='notice',
        template_type='red_packet_register_notice',
        template_args=dict(
            name=email,
            receive_time=format_datetime_str(history.grab_at, utc_flag),
            expired_time=format_datetime_str(
                history.grab_at + timedelta(days=GRAB_EXPIRED_DAY), utc_flag),
            amount=amount_to_str(history.amount),
            coin_type=history.asset,
            register_url=f'{host_url}register?'
                         f'refer_code={ReferralBusiness.get_code_by_user(send_user_id)}&'
                         f'channel=C-Box',
            anti_phishing_code=anti_phishing_code,
        ),
        recipient=email,
        lang=lang.value
    )


@celery_task
def send_red_packet_registration_expiring_email(send_user_id: int, email: str, expiring_hours: int, lang: str, host_url: str):
    """ 未注册用户领取了红包，红包剩余24小时失效 """
    lang = lang or Language.DEFAULT.value
    host_url = host_url or config['SITE_URL']  # 暂不处理透传的 path
    send_user = User.query.get(send_user_id)
    EmailSender.send_from_template(
        template_category='notice',
        template_type='red_packet_register_expiring_notice',
        template_args=dict(
            sender_name=send_user.nickname,
            expiring_hours=expiring_hours,
            name=email,
            register_url=f'{host_url}register?refer_code={ReferralBusiness.get_code_by_user(send_user_id)}',
            anti_phishing_code='',
        ),
        recipient=email,
        lang=lang,
    )


@celery_task
def send_activity_email(user_id: int, email: str, title: str,
                        content: str, lang: str):

    """
    用于活动通知
    """

    token = EmailPushUnsubscription.get_or_new(user_id)
    EmailSender.send_from_template(
        'notice',
        'announcement_notice',
        dict(
            site_url=config['SITE_URL'],
            content=content,
            unsubscribe_uri=quote_plus(token)
        ),
        email,
        lang,
        subject=title
    )


@celery_task
def send_user_trade_summary_email(email: str, title: str, report_date: str, lang: str,
                                  export_url: str,
                                  name: str = None,
                                  market_maker_ranking_data: dict = None,
                                  maker_cashback_ranking_data: dict = None,
                                  spot_user_trade_data: dict = None,
                                  spot_user_trade_type_data: dict = None,
                                  perpetual_user_trade_data: dict = None,
                                  perpetual_user_trade_type_data: dict = None):

    """做市商交易日报邮件"""
    EmailSender.send_from_template(
        'notice',
        'user_trade_summary',
        dict(
            report_date=report_date,
            name=name or email,
            market_maker_ranking_data=market_maker_ranking_data,
            maker_cashback_ranking_data=maker_cashback_ranking_data,
            spot_user_trade_data=spot_user_trade_data,
            spot_user_trade_type_data=spot_user_trade_type_data,
            perpetual_user_trade_data=perpetual_user_trade_data,
            perpetual_user_trade_type_data=perpetual_user_trade_type_data,
            export_url=export_url,
        ),
        email,
        lang,
        subject=title
    )


@celery_task(queue=CeleryQueues.EMAIL_PUSH)
def send_appraisal_email(user_id: int,
                         appraisal_history_id: int):
    from app.business.ambassador import InvalidAmbassadorSource
    from app.business.fee import UserFeeParser
    from app.business.fee_constant import (NORMAL_FEE_DICT, NORMAL_LEVEL,
                                           PERPETUAL_MARKET_MAKER_DICT,
                                           VIP_LEVEL_DICT)
    from app.business.market_maker import MarketMakerHelper
    from app.business.referral import ReferralBusiness
    from app.common import TradeBusinessType, TradeType
    from app.models import Ambassador, ReferralAssetHistory
    from app.models.user import AppraisalHistory

    record: AppraisalHistory = AppraisalHistory.query.get(appraisal_history_id)
    if not record:
        return

    user: User = User.query.get(user_id)
    if not user or user.user_type == User.UserType.SUB_ACCOUNT \
        or UserSettings(user_id).login_disabled_by_admin:
        return

    user_name = user.name_displayed

    email = user.email
    lang = UserPreferences(user.id).language

    def convert_percent_format(r: Dict[Any, Any]):
        # only one depth.
        convert_result = {}
        for key, value in r.items():
            if isinstance(value, Decimal) and value < 1 and 'rate' in key:
                format_value = value * 100
                convert_result[key] = f'{format_value.normalize():f}%'
            else:
                convert_result[key] = value
        return convert_result

    business_type = record.business_type.value

    if business_type == AppraisalHistory.BusinessType.VIP.value:
        if record.result_status == AppraisalHistory.ResultStatus.NOT_CHANGE:
            return
        data = json.loads(record.result)
        level = data['new_level']
        old_level = data['old_level']
        if level > old_level == 0:
            # 普通用户(VIP0)升级成VIP用户
            template_type = "vip_level_gain"
        elif level > old_level > 0:
            # VIP用户升级，如vip1升级到vip2
            template_type = "vip_level_up"
        elif old_level > level > 0:
            # VIP用户降级，如vip2降级到vip1
            template_type = "vip_level_down"
        elif old_level > level == 0:
            # VIP用户降级成普通用户(VIP0)
            template_type = "vip_level_lose"
        else:
            return
        fee_deduction_rate = FeeFetcher(user_id).fetch_fee_deduction_rate()
        fee_result = copy.copy(VIP_LEVEL_DICT[level]) if level != NORMAL_LEVEL else copy.copy(NORMAL_FEE_DICT)
        old_fee_result = copy.copy(VIP_LEVEL_DICT[old_level]) if old_level != NORMAL_LEVEL else copy.copy(NORMAL_FEE_DICT)
        min_fee_result = copy.copy(VIP_LEVEL_DICT[max(VIP_LEVEL_DICT)])
        referral_rate = ReferralBusiness.VIP_LEVEL_RATE_MAP[level]
        old_referral_rate = ReferralBusiness.VIP_LEVEL_RATE_MAP[old_level]
        max_referral_rate = max(ReferralBusiness.VIP_LEVEL_RATE_MAP.values())
        spot_fee_rate = fee_result['taker_fee_rate']
        old_spot_fee_rate = old_fee_result['taker_fee_rate']
        min_spot_fee_rate = min_fee_result['taker_fee_rate']
        vip_min_cet_amount = VIP_LEVEL_DICT[1]['rule']['cet_amount']
        fee_result.update(
            dict(
                name=user_name,
                new_level=level,
                old_level=old_level,
                support_url=config['SUPPORT_URL'],
                spot_fee_rate=spot_fee_rate,
                old_spot_fee_rate=old_spot_fee_rate,
                min_spot_fee_rate=min_spot_fee_rate,
                spot_cet_fee_rate=spot_fee_rate * fee_deduction_rate,
                old_spot_cet_fee_rate=old_spot_fee_rate * fee_deduction_rate,
                min_spot_cet_fee_rate=min_spot_fee_rate * fee_deduction_rate,
                old_perpetual_taker_fee_rate=old_fee_result['perpetual_taker_fee_rate'],
                old_perpetual_maker_fee_rate=old_fee_result['perpetual_maker_fee_rate'],
                min_perpetual_taker_fee_rate=min_fee_result['perpetual_taker_fee_rate'],
                min_perpetual_maker_fee_rate=min_fee_result['perpetual_maker_fee_rate'],
                referral_rate=referral_rate,
                old_referral_rate=old_referral_rate,
                max_referral_rate=max_referral_rate,
                vip_min_cet_amount=vip_min_cet_amount,
                cet_market_url=WebPagePath.SPOT_MARKET.format_page(path='/CET-USDT'),
                vip_url=_web_path.VIP_PAGE,
                borrowing_fee_url=_web_path.BORROWING_FEE_PAGE,
                refer_url=_web_path.REFER_PAGE,
            )
        )
        EmailSender.send_from_template(
            'notice',
            template_type,
            convert_percent_format(fee_result),
            email,
            str(lang.value)
        )
    if business_type == AppraisalHistory.BusinessType.SPOT.value:
        data = json.loads(record.result)
        fee_deduction_rate = FeeFetcher(user_id).fetch_fee_deduction_rate()
        # level = max(data['new_level'], 1)
        level = data['new_level']
        month_count = data.get('month_count', 0)
        if month_count < 3 and level == 0:
            level = 1
        fee_result = copy.copy(data)
        fee_parser = UserFeeParser(user_id)
        settle_fee = fee_parser.get_settle_fee(TradeBusinessType.SPOT)
        fee_result["maker_fee_rate"] = settle_fee[TradeType.MAKER]
        fee_result["taker_fee_rate"] = settle_fee[TradeType.TAKER]
        cet_taker_fee_rate = fee_result['taker_fee_rate'] * fee_deduction_rate

        if fee_result['maker_fee_rate'] >= Decimal():
            cet_maker_fee_rate = fee_result['maker_fee_rate'] * fee_deduction_rate
        else:
            cet_maker_fee_rate = fee_result['maker_fee_rate']

        limit_trade_amount = MarketMakerHelper(MarketMaker.MakerType.SPOT
                                               ).get_maker_trade_amount_limit()
        fee_result.update(
            dict(
                name=user_name,
                user_trade_amount=data['trade_amount'],
                month_count=data.get('month_count', 0),
                support_url=config['SUPPORT_URL'],
                new_level=level,
                limit_trade_amount=str(limit_trade_amount),
                result_status=record.result_status.value,
                cet_taker_fee_rate=cet_taker_fee_rate,
                cet_maker_fee_rate=cet_maker_fee_rate
            )
        )
        EmailSender.send_from_template(
            'notice',
            'spot_market_maker_level_appraisal',
            convert_percent_format(fee_result),
            email,
            str(lang.value)
        )

    if business_type == AppraisalHistory.BusinessType.PERPETUAL.value:
        limit_trade_amount = MarketMakerHelper(MarketMaker.MakerType.PERPETUAL
                                               ).get_maker_trade_amount_limit()
        data = json.loads(record.result)
        # level = max(data['new_level'], 1)
        level = data['new_level']
        fee_result = copy.copy(data)
        fee_result.update(PERPETUAL_MARKET_MAKER_DICT[level] if level != NORMAL_LEVEL else
                          copy.copy(NORMAL_FEE_DICT))
        fee_result.update(
            dict(
                name=user_name,
                user_trade_amount=data['trade_amount'],
                month_count=data.get('month_count', 0),
                support_url=config['SUPPORT_URL'],
                new_level=level,
                limit_trade_amount=str(limit_trade_amount),
                result_status=record.result_status.value,
                perpetual_maker_fee_rate=fee_result['maker_fee_rate'],
                perpetual_taker_fee_rate=fee_result['taker_fee_rate']
            )
        )
        EmailSender.send_from_template(
            'notice',
            'perpetual_market_maker_level_appraisal',
            convert_percent_format(fee_result),
            email,
            str(lang.value)
        )

    if business_type == AppraisalHistory.BusinessType.AMBASSADOR.value:
        data = json.loads(record.result)
        if record.result_status == AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL and data["month_count"] > 3 \
                and user_id in InvalidAmbassadorSource().get_send_push_user_set():
            """如果大使考核结果失效并且用户在大使线索库中邮件由大使线索库来发送"""
            return
        with force_locale(lang.value):
            result = dict(
                name=user_name,
                support_url=config['SUPPORT_URL'],
                result_status=record.result_status.value,
                level_name=gettext(data['new_level']),
                old_level_name=gettext(data['old_level']),
                referral_rate=max(ReferralBusiness.get_referral_rate(user.id).values()),
                referral_asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR],
                least_referral_rate=ReferralBusiness.AMBASSADOR_LEVEL_RATE_MAP[Ambassador.Level.SILVER],
                least_deal_amount=ReferralBusiness.AMBASSADOR_LEVEL_DEAL_AMOUNT_MAP[Ambassador.Level.SILVER],
                deal_amount=data['deal_amount'],
                month_count=data['month_count']
            )
        EmailSender.send_from_template(
            'notice',
            'ambassador_level_appraisal',
            convert_percent_format(result),
            email,
            str(lang.value)
        )

    if business_type == AppraisalHistory.BusinessType.AMBASSADOR_AGENT.value:
        model = AmbassadorAgent
        agent = model.query.filter(model.user_id == user_id).first()
        referral_rate = agent.referral_rate
        data = json.loads(record.result)
        context = dict(
            name=user_name,
            support_url=config['SUPPORT_URL'],
            detail_url=WebPagePath.AMBASSADOR_ACTIVITY.format_page(tab='agent'),
            fail_count=data['month_count'],
            referral_rate=amount_to_str(referral_rate * 100, 4) + "%"
        )
        context['result'] = 'success' if data['month_count'] == 0 else 'fail'
        EmailSender.send_from_template(
            'notice',
            'ambassador_agent_appraisal',
            context,
            email,
            str(lang.value)
        )


@celery_task
def send_cashback_detail_email(report_date_str: str):
    """
    report_date: "%Y-%m-%d"
    """
    from app.business.makercashback import (MAKER_CASHBACK_LEVEL_RATE_MAP,
                                            CashBackDataCalculator)
    from app.models.makercashback import MakerCashBackHistory

    report_date = datetime.datetime.strptime(
        report_date_str, "%Y-%m-%d"
    ).date()
    q = (
        MakerCashBackHistory.query.filter(
            MakerCashBackHistory.report_date == report_date
        )
        .with_entities(
            MakerCashBackHistory.user_id,
            MakerCashBackHistory.asset,
            MakerCashBackHistory.amount,
        )
        .group_by(MakerCashBackHistory.user_id, MakerCashBackHistory.asset)
    )
    result = defaultdict(lambda: defaultdict(str))
    for v in q:
        v: MakerCashBackHistory
        result[v.user_id][v.asset] = f"{v.amount:f}"
    user_ids = list(result.keys())
    sub_accounts = []
    all_user_ids = list(user_ids)
    for ids_ in batch_iter(user_ids, 1000):
        tmp = SubAccount.query.filter(
            SubAccount.user_id.in_(ids_),
            SubAccount.type == SubAccount.Type.NORMAL,).all()
        sub_accounts.extend(tmp)
        all_user_ids.extend([item.main_user_id for item in tmp])
    user_emails = User.query.filter(User.id.in_(all_user_ids)).with_entities(User.id, User.email).all()
    user_email_dict: Dict[int, str] = dict(user_emails)
    sub_user_list_map = defaultdict(list)
    for account in sub_accounts:
        sub_user_list_map[account.main_user_id].append(account)
    user_level_dict = CashBackDataCalculator.get_cashback_user_info()
    level_rate_dict = {
        level: rate
        for level, rate in MAKER_CASHBACK_LEVEL_RATE_MAP.items()
    }
    for user_id, email in user_email_dict.items():
        if not email:
            continue
        sub_accounts = sub_user_list_map[user_id]
        sub_account_data_map = dict()
        for account in sub_accounts:
            if account.user_id not in result:
                continue
            sub_account_data_map[account.user.name_displayed] = result[account.user_id]

        lang = UserPreferences(user_id).language
        if lang not in [
            Language.EN_US,
            Language.ZH_HANS_CN,
            Language.ZH_HANT_HK,
        ]:
            lang = Language.DEFAULT
        rate = level_rate_dict[user_level_dict[user_id]] * 100
        total_result = [result[user_id]] + list(sub_account_data_map.values())
        total_cashback = defaultdict(Decimal)
        for _cash_value in total_result:
            for _asset, _amount in _cash_value.items():
                total_cashback[_asset] += Decimal(_amount)
        EmailSender.send_from_template(
            "notice",
            "maker_cashback_user_daily_report",
            dict(
                name=email,
                report_date=report_date_str,
                level=user_level_dict.get(user_id, min(MAKER_CASHBACK_LEVEL_RATE_MAP)),
                rate=f"-{rate.normalize():f}%",
                cashback_data=dict(result[user_id]),
                sub_account_cashback_data=sub_account_data_map,
                total_cashback=total_cashback,
            ),
            email,
            str(lang.value)
        )


def _get_perpetual_position_users(market: str) -> List[User]:
    limit = 100
    client = PerpetualServerClient()
    user_ids = set()
    for side in PositionSide:
        page = 1
        while True:
            result = client.position_list(market, side.value, page, limit)
            positions = result['records']
            for item in positions:
                user_ids.add(item['user_id'])
            if len(positions) < limit:
                break
            page += 1

    users: List[User] = []
    for chunk_user_ids in batch_iter(user_ids, 5000):
        chunk_users = User.query.filter(User.id.in_(chunk_user_ids)).all()
        users.extend(chunk_users)
    return users


@celery_task
def send_update_funding_interval_email(markets: dict):
    email_type = "perpetual_funding_interval_update"
    for market, funding_interval in markets.items():
        users = _get_perpetual_position_users(market)
        market_data = PerpetualMarketCache().get_market_info(market)
        stock = market_data['stock']
        money = market_data['money']
        market_name = f'{stock}-{money}'
        for user in users:
            if (email := user.main_user_email) is None:
                continue
            pref = UserPreferences(user.id)
            lang = pref.language.value
            url = WebPagePath.PERPETUAL_MARKET.format_page(path=f'/{market_name}', lang=lang)
            send_notice_email.delay(
                email=email,
                email_type=email_type,
                template_args=dict(
                    name=user.name_displayed,
                    market=market,
                    funding_interval=funding_interval,
                    url=url,
                ),
                lang=lang,
            )


@celery_task
def send_mining_reword_email(user_id, lang, amount, asset, activity_name, show_spot=True):
    user: User = User.query.get(user_id)
    if not user or user.user_type == User.UserType.SUB_ACCOUNT:
        return
    email = user.email
    EmailSender.send_from_template(
        "notice",
        "mining_rewords",
        dict(
            name=email,
            amount=amount,
            asset=asset,
            activity_name=activity_name,
            show_spot=show_spot,
        ),
        email,
        lang
    )


@celery_task
def send_update_ambassador_agent_email(user_id: int,
                                       month_count: int,
                                       referral_rate_str: str,
                                       result: str):
    user = User.query.get(user_id)
    EmailSender.send_from_template(
                'notice',
                'ambassador_agent_appraisal_update',
                dict(name=user.name_displayed,
                     support_url=config['SUPPORT_URL'],
                     fail_count=month_count,
                     referral_rate=referral_rate_str,
                     result=result,
                     update=True),
                user.email,
                UserPreferences(user_id).language.value
            )


@celery_task
def send_update_ambassador_level_email(user_id: int, new_level_value: str, old_level_value=None):
    cmp = 1
    if old_level_value:
        cmp = ReferralBusiness.compare_ambassador_level(new_level_value, old_level_value)
    if cmp:
        user = User.query.get(user_id)
        lang = UserPreferences(user.id).language
        if lang not in [Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK]:
            lang = Language.DEFAULT
        result_status = AppraisalHistory.ResultStatus.LEVEL_DOWN.value \
            if cmp < 0 else AppraisalHistory.ResultStatus.LEVEL_UP.value
        referral_rate = max(ReferralBusiness.get_referral_rate(user.id).values())
        with force_locale(lang.value):
            result = dict(
                name=user.name_displayed,
                support_url=config['SUPPORT_URL'],
                result_status=result_status,
                level_name=gettext(new_level_value),
                referral_rate=f'{(referral_rate * 100).normalize():f}%',
                referral_asset=ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR],
            )
        EmailSender.send_from_template(
            'notice',
            'ambassador_level_change',
            result,
            user.email,
            subject=None,
            lang=str(lang.value)
        )


@celery_task
def send_ambassador_train_book_email(user_id):
    user = User.query.get(user_id)
    lang = UserPreferences(user.id).language
    if lang not in [Language.ZH_HANS_CN, Language.EN_US, Language.ZH_HANT_HK]:
        lang = Language.DEFAULT
    fname = "CoinEx-Ambassador-Manual.pdf"
    data = dict(
        name=user.name_displayed,
        support_url=config['SUPPORT_URL'],
        site_url=config['SITE_URL'],
        admin_telegram_url='https://t.me/CoinExAmbassadorAdmin',
        admin1_telegram_url='https://t.me/CoinExAmbassadorAdmin1',
        admin2_telegram_url='https://t.me/CoinExAmbassadorAdmin2',
        download_url=AWSBucketPublic.get_file_url(fname),
    )
    EmailSender.send_from_template(
        'notice',
        'ambassador_train_book',
        data,
        user.email,
        str(lang.value),
    )


@celery_task
def send_bus_ambassador_train_book_email(user_id):
    user = User.query.get(user_id)
    lang = UserPreferences(user.id).language
    if lang not in [Language.ZH_HANS_CN, Language.EN_US, Language.ZH_HANT_HK]:
        lang = Language.DEFAULT
    fname = "CoinEx-Business-Ambassador-Manual.pdf"
    data = dict(
        name=user.name_displayed,
        support_url=config['SUPPORT_URL'],
        site_url=config['SITE_URL'],
        download_url=AWSBucketPublic.get_file_url(fname),
    )
    EmailSender.send_from_template(
        'notice',
        'bus_ambassador_train_book',
        data,
        user.email,
        str(lang.value),
    )


@celery_task
def send_bus_amb_notice_email(user_id: int, template: str, params: dict):
    """ 商务大使相关 通知邮件 """
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    send_notice_email(
        email=user.email,
        email_type=template,
        template_args=dict(
            name=user.name_displayed,
            **params,
        ),
        lang=lang,
    )


@celery_task
def send_reset_security_notice_email(user_id: int, reset_type: str, result: str, reason: str = None, email: str = None):
    user = User.query.get(user_id)
    lang = UserPreferences(user.id).language
    template_map = {
        SecurityResetApplication.StatusType.PASSED.name: {
            SecurityResetApplication.ResetType.TOTP.name: 'totp_reset_pass',
            SecurityResetApplication.ResetType.MOBILE.name: 'mobile_reset_pass',
            SecurityResetApplication.ResetType.EMAIL.name: 'email_reset_pass',
            SecurityResetApplication.ResetType.WEBAUTHN.name: 'webauthn_reset_pass',
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD.name: 'withdraw_password_reset_pass',
            SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT.name: 'unfreeze_account_pass',
        },
        SecurityResetApplication.StatusType.REJECTED.name: {
            SecurityResetApplication.ResetType.TOTP.name: '2fa_reset_fail',
            SecurityResetApplication.ResetType.MOBILE.name: '2fa_reset_fail',
            SecurityResetApplication.ResetType.WEBAUTHN.name: '2fa_reset_fail',
            SecurityResetApplication.ResetType.EMAIL.name: '2fa_reset_fail',
            SecurityResetApplication.ResetType.WITHDRAW_PASSWORD.name: 'withdraw_password_reset_fail',
            SecurityResetApplication.ResetType.UNFREEZE_ACCOUNT.name: 'unfreeze_account_fail',
        }
    }
    template_name = template_map[result][reset_type]
    data = dict(
        support_url=config['SUPPORT_URL'],
        site_url=config['SITE_URL'],
        unfreeze_account_url=_web_path.ACCOUNT_UNFROZEN,
        reset_withdraw_password_url=_web_path.RESET_WITHDRAW_PASSWORD,
        name=user.name_displayed
    )
    if reason:
        data['reason'] = reason
    EmailSender.send_from_template(
        'notice',
        template_name,
        data,
        email or user.email,
        str(lang.value)
    )


@celery_task
def send_coin_application_rejected_notice_email(user_id: int, email: str, project_name: str):
    user = User.query.get(user_id)
    lang = UserPreferences(user.id).language
    email = user.email or email
    data = dict(
        name=user.name_displayed,
        project_name=project_name
    )
    EmailSender.send_from_template(
        'notice',
        'coin_application_rejected',
        data,
        email,
        str(lang.value)
    )


@celery_task
def send_ieo_application_rejected_notice_email(user_id: int, email: str, project_name: str):
    user = User.query.get(user_id)
    lang = UserPreferences(user.id).language
    email = user.email or email
    data = dict(
        name=user.name_displayed,
        project_name=project_name
    )
    EmailSender.send_from_template(
        'notice',
        'ieo_application_rejected',
        data,
        email,
        str(lang.value)
    )


@celery_task
def send_set_login_password_success_notice_email(user_id: int):
    """ 首次设置密码通知 """

    user = User.query.get(user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(user.id)
    template_args = dict(
        site_url=config["SITE_URL"],
        support_url=config["SUPPORT_URL"],
        name=user.name_displayed,
    )
    send_notice_email(
        email=email,
        email_type='set_login_password_success',
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_edit_security_notice_email(security_type: str, user_id: int):
    """ 修改登入密码、手机号、邮箱、TOTP后，给对应的用户发邮件通知 """
    security_type_template_map = {
        "totp": "edit_totp",
        "mobile": "edit_mobile",
        "email": "email_reset_pass",  # 修改邮箱 和 重置邮箱的 邮件相同
        "login_password": "edit_login_password",  # 修改、重置登入密码
        "withdraw_password": "edit_withdraw_password",  # 修改提现密码
    }
    if security_type not in security_type_template_map:
        return

    user = User.query.get(user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(user.id)
    template_args = dict(
        site_url=config["SITE_URL"],
        support_url=config["SUPPORT_URL"],
        name=user.name_displayed,
    )
    send_notice_email(
        email=email,
        email_type=security_type_template_map[security_type],
        template_args=template_args,
        lang=pref.language.value,
    )

    params = dict()
    if security_type == "email":
        title = MessageTitle.RESET_EMAIL_PASS.name
        content = MessageContent.RESET_EMAIL_PASS.name
        params = dict(email=hide_email(user.email))
    elif security_type == "mobile":
        if user.mobile_num:
            title = MessageTitle.EDIT_MOBILE.name
            content = MessageContent.EDIT_MOBILE_SUCCESS.name
            params = dict(mobile=hide_mobile(user.mobile_num))
        else:
            title = MessageTitle.RESET_MOBILE.name
            content = MessageContent.RESET_MOBILE_SUCCESS.name
    elif security_type == "totp":
        title = MessageTitle.RESET_TOTP_PASS.name
        content = MessageContent.RESET_TOTP_PASS.name
    elif security_type == "withdraw_password":
        title = MessageTitle.EDIT_WITHDRAW_PASSWORD.name
        content = MessageContent.EDIT_WITHDRAW_PASSWORD_SUCCESS.name
    else:
        title = MessageTitle.RESET_PASSWORD.name
        content = MessageContent.RESET_PASSWORD.name
    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=title,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_SECURITY_SETTING_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACCOUNT_SECURITY,
        )
    )


@celery_task
def send_trade_password_update_email(user_id: int):
    user = User.query.get(user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(user.id)
    template_args = dict(
        site_url=config["SITE_URL"],
        support_url=config["SUPPORT_URL"],
        name=user.name_displayed,
    )
    send_notice_email(
        email=email,
        email_type='update_trade_password',
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_internal_user_email(
        email: str,
        email_content: str,
        subject: str,
        lang: str,
):
    template_args = dict(
        content=email_content,
        name='all',
    )
    EmailSender.send_from_template(
        'notice',
        'internal_user_notice',
        template_args,
        email,
        lang,
        subject=subject
    )


@celery_task
def send_new_coin_up_down_notice_email(
        email: str,
        subject: str,
        lang: str,
        **template_args
):
    EmailSender.send_from_template(
        'notice',
        'monthly_new_coin_up_down_notice',
        template_args,
        email,
        lang,
        subject=subject
    )


@celery_task
def send_credit_user_risk_notice_email(
    notice_type: str,
    user_id: int,
    cur_warn_rate: str,
    withdraw_rate: str,
):
    """ 发送授信用户-当前风险率的通知 """
    notice_type_template_map = {
        "withdraw_pre_warning": "credit_risk_withdraw_close_pre_warning",
        "withdraw_close": "credit_risk_withdraw_close",
    }
    if notice_type not in notice_type_template_map:
        return

    user = User.query.get(user_id)
    if (email := user.main_user_email) is None:
        return

    d_num_100 = Decimal("100")
    pref = UserPreferences(user.id)
    template_args = dict(
        name=user.name_displayed,
        cur_warn_rate=f"{amount_to_str(Decimal(cur_warn_rate) * d_num_100, 2)}%",
        withdraw_rate=f"{amount_to_str(Decimal(withdraw_rate) * d_num_100, 2)}%",
    )
    send_notice_email(
        email=email,
        email_type=notice_type_template_map[notice_type],
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_airdrop_reword_email(user_id, rewards, title, unlocked_at, site_url, activity_url, template_name, lang):
    user: User = User.query.get(user_id)
    email = user.main_user_name_displayed
    EmailSender.send_from_template(
        "notice",
        template_name,
        dict(
            name=email,
            rewards=rewards,
            title=title,
            site_url=site_url,
            activity_url=activity_url,
            unlocked_at=unlocked_at,
        ),
        user.email,
        lang
    )


@celery_task
def send_dibs_reword_email(user_id, amount, asset, title, site_url, template_name, lang):
    user: User = User.query.get(user_id)
    email = user.main_user_name_displayed
    EmailSender.send_from_template(
        "notice",
        template_name,
        dict(
            name=email,
            amount=amount,
            asset=asset,
            title=title,
            site_url=site_url,
        ),
        user.email,
        lang
    )



@celery_task
def send_ieo_reword_email(
        user_id, amount, asset, pledge_asset, count, rule_url, order_url, project_name, template_name, lang, unlocked_at=None
):
    user: User = User.query.get(user_id)
    email = user.email
    EmailSender.send_from_template(
        "notice",
        template_name,
        dict(
            name=user.main_user_name_displayed,
            amount=amount,
            count=count,
            asset=asset,
            pledge_asset=pledge_asset,
            rule_url=rule_url,
            order_url=order_url,
            project_name=project_name,
            unlocked_at=unlocked_at,
        ),
        email,
        lang
    )


@celery_task
def send_abnormal_deposit_application_additional_info_email(application_id: int):
    """ 发送 充值未到账找回申请-资料补充 的邮件 """
    row = AbnormalDepositApplication.query.get(application_id)
    user = User.query.get(row.user_id)
    if (email := user.main_user_email) is None:
        return

    if (row.status == AbnormalDepositApplication.Status.CREATED
            and row.additional_info_status == AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE):
        url = WebPagePath.DEPOSIT_APPLICATION.format_page(path=f'/{application_id}')
    else:
        url = WebPagePath.DEPOSIT_RECOVERY.value

    pref = UserPreferences(row.user_id)
    template_args = dict(
        name=user.name_displayed,
        time=datetime_to_str(row.created_at, pref.timezone_offset),
        amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
        asset=row.asset,
        url=url,
    )
    send_notice_email(
        email=email,
        email_type="abnormal_deposit_application_additional_info",
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_abnormal_deposit_application_fee_email(application_id: int):
    """ 发送 充值未到账找回申请手续费补充 的邮件 """
    row = AbnormalDepositApplication.query.get(application_id)
    user = User.query.get(row.user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(row.user_id)
    template_args = dict(
        name=user.name_displayed,
        time=datetime_to_str(row.created_at, pref.timezone_offset),
        amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
        asset=row.asset,
        fee_amount=amount_to_str(AbnormalDepositApplication.SERVICE_FEE_AMOUNT, PrecisionEnum.COIN_PLACES),
        fee_asset=AbnormalDepositApplication.SERVICE_FEE_ASSET,
    )
    send_notice_email(
        email=email,
        email_type="abnormal_deposit_application_fee",
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_abnormal_deposit_application_rejected_email(application_id: int):
    """ 发送 充值未到账找回申请-审核失败 的邮件 """
    row = AbnormalDepositApplication.query.get(application_id)
    if row.status != AbnormalDepositApplication.Status.REJECTED:
        return
    user = User.query.get(row.user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(row.user_id)
    lang = pref.language.value
    with force_locale(lang):
        rejection_reason_str = row.rejection_reason_str

    template_args = dict(
        name=user.name_displayed,
        time=datetime_to_str(row.created_at, pref.timezone_offset),
        amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
        asset=row.asset,
        rejection_reason=rejection_reason_str,
        url=WebPagePath.DEPOSIT_RECOVERY.value,
    )
    send_notice_email(
        email=email,
        email_type="abnormal_deposit_application_rejected",
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_abnormal_deposit_application_passed_email(application_id: int):
    """ 发送 充值未到账找回申请-审核通过 的邮件 """
    row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
    if row.status != AbnormalDepositApplication.Status.CHECKED:
        return
    if not (row.is_new and row.is_need_fee):
        return
    if not row.expect_fee_asset or not row.expect_fee_amount:
        return

    user = User.query.get(row.user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(row.user_id)
    template_args = dict(
        name=user.name_displayed,
        time=datetime_to_str(row.created_at, pref.timezone_offset),
        amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
        asset=row.asset,
        fee_amount=amount_to_str(row.expect_fee_amount, PrecisionEnum.COIN_PLACES),
        fee_asset=row.expect_fee_asset,
        url=WebPagePath.ABNORMAL_DEP_APPLY_FEE_PAGE.value,
        weeks_needed=4,
    )
    send_notice_email(
        email=email,
        email_type="abnormal_deposit_application_passed",
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_abnormal_deposit_application_finished_email(application_id: int):
    """ 发送 充值未到账找回申请-找回成功 的邮件 """
    row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
    if row.status != AbnormalDepositApplication.Status.FINISHED:
        return
    user = User.query.get(row.user_id)
    if (email := user.main_user_email) is None:
        return

    pref = UserPreferences(row.user_id)
    template_args = dict(
        name=user.name_displayed,
        time=datetime_to_str(row.created_at, pref.timezone_offset),
        amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
        asset=row.asset,
        refund_tx_id=row.refund_tx_id,
        refund_tx_url=WalletClient().get_explorer_tx_url(row.chain, row.refund_tx_id) if row.refund_tx_id else "",
        is_to_spot_account=row.type not in AbnormalDepositApplication.REFUND_TYPES,
        is_show_fee=bool(row.is_new and row.is_need_fee and row.fee_asset),
        fee_amount=amount_to_str(row.fee_amount, PrecisionEnum.COIN_PLACES),
        fee_asset=row.fee_asset,
        url=WebPagePath.ABNORMAL_DEP_APPLY_FEE_PAGE.value,
    )
    send_notice_email(
        email=email,
        email_type="abnormal_deposit_application_finished",
        template_args=template_args,
        lang=pref.language.value,
    )


@celery_task
def send_spot_grid_notice_email(user_id: int, template: str, params: dict):
    """ 现货网格相关 通知邮件 """
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    send_notice_email(
        email=user.email,
        email_type=template,
        template_args=dict(
            name=user.name_displayed,
            **params,
        ),
        lang=lang,
    )


@celery_task
def send_pledge_notice_email(user_id: int, template: str, params: dict):
    """ 借贷相关 通知邮件 """
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    send_notice_email(
        email=user.email,
        email_type=template,
        template_args=dict(
            name=user.name_displayed,
            **params,
        ),
        lang=lang,
    )


@celery_task
def send_pre_trading_notice_email(user_id: int, template: str, params: dict):
    """ 盘前交易相关 通知邮件 """
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    send_notice_email(
        email=user.email,
        email_type=template,
        template_args=dict(
            name=user.name_displayed,
            **params,
        ),
        lang=lang,
    )


@celery_task(queue=CeleryQueues.EMAIL_PUSH)
def send_equity_center_notice_email(user_id: int, template: str, params: dict):
    """ 权益中心相关 通知邮件 """
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    send_notice_email(
        email=user.email,
        email_type=f'equity_center/{template}',
        template_args=dict(
            name=user.name_displayed,
            **params,
        ),
        lang=lang,
    )


@celery_task
def send_asset_exchange_order_notice_email(exchange_order_id: int):
    """ 发送 兑换订单结束 的邮件（完全兑换、部分兑换、兑换失败） """
    order: AssetExchangeOrder = AssetExchangeOrder.query.get(exchange_order_id)
    user = User.query.get(order.user_id)

    show_fail_tip = False
    source_asset_remain_amount = 0
    if order.result == AssetExchangeOrder.Result.ALL:
        email_type = "exchange_order_finished"
    elif order.result == AssetExchangeOrder.Result.FAILED:
        # 兑换失败时，剩余数=退回数（已兑数为0）
        email_type = "exchange_order_failed"
        transfer = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_REMAIN_SOURCE_ASSET,
        ).first()
        if transfer:
            source_asset_remain_amount = transfer.amount
            if transfer.amount != order.source_asset_amount:
                # 兑换数量和返还数量不一致
                show_fail_tip = True
        else:
            source_asset_remain_amount = order.source_asset_amount - order.source_asset_exchanged_amount
    elif order.result == AssetExchangeOrder.Result.PARTIAL:
        # 部分兑换，剩余数=兑换数-已兑数
        email_type = "exchange_order_partial"
        source_asset_remain_amount = order.source_asset_amount - order.source_asset_exchanged_amount
    else:
        return

    template_args = dict(
        name=user.name_displayed,
        source_asset=order.source_asset,
        source_asset_exchanged_amount=amount_to_str(order.source_asset_exchanged_amount, 8),
        source_asset_remain_amount=amount_to_str(source_asset_remain_amount, 8),
        target_asset=order.target_asset,
        target_asset_exchanged_amount=amount_to_str(order.target_asset_exchanged_amount, 8),
        show_fail_tip=show_fail_tip,
        market=f"{order.source_asset}->{order.target_asset}",
    )
    notice_users = get_notice_users(user)
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        lang = pref.language.value
        template_args.update(
            dict(
                time=datetime_to_str(order.created_at, pref.timezone_offset),
                site_url=WebPagePath.EXCHANGE_RECORD.format_page(status='FINISHED', lang=lang),
            )
        )
        send_notice_email(
            email=notice_user.email,
            email_type=email_type,
            template_args=template_args,
            lang=lang,
        )


@celery_task
def send_auto_invest_order_notice_email(plan_id: int, user_id: int, target_asset: str,
                                        source_asset: str, time: str, source_asset_traded_amount: str,
                                        source_asset_amount: str, source_asset_remain_amount: str,
                                        target_asset_traded_amount: str, result: str,
                                        ):
    """ 发送 定投订单结束 的邮件（完全成交、部分成交、成交失败） """
    user = User.query.get(user_id)
    if result == AutoInvestTask.Result.ALL.name:
        email_type = "auto_invest_deal_all"
    elif result == AutoInvestTask.Result.FAILED.name:
        email_type = "auto_invest_deal_failed"
    elif result == AutoInvestTask.Result.PARTIAL.name:
        email_type = "auto_invest_deal_partial"

    template_args = dict(
        name=user.name_displayed,
        source_asset=source_asset,
        source_asset_traded_amount=source_asset_traded_amount,
        source_asset_remain_amount=source_asset_remain_amount,
        source_asset_amount=source_asset_amount,
        target_asset=target_asset,
        target_asset_traded_amount=target_asset_traded_amount,
        time=time,
    )
    pref = UserPreferences(user.id)
    lang = pref.language.value
    template_args.update(
        dict(
            site_url=WebPagePath.STRATEGY_AUTO_INVEST.format_page(path=f'/{plan_id}'),
        )
    )
    send_notice_email(
        email=user.email,
        email_type=email_type,
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_auto_invest_failed_email(plan_id: int, user_id: int, target_asset: str):
    user = User.query.get(user_id)

    template_args = dict(
        name=user.name_displayed,
        target_asset=target_asset,
    )
    pref = UserPreferences(user.id)
    lang = pref.language.value
    template_args.update(
        dict(
            site_url=WebPagePath.STRATEGY_AUTO_INVEST.format_page(path=f'/{plan_id}'),
        )
    )
    send_notice_email(
        email=user.email,
        email_type='auto_invest_failed',
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_auto_invest_paused_email(plan_id: int, user_id: int, target_asset: str):
    user = User.query.get(user_id)

    template_args = dict(
        name=user.name_displayed,
        target_asset=target_asset,
    )
    pref = UserPreferences(user.id)
    lang = pref.language.value
    template_args.update(
        dict(
            site_url=WebPagePath.STRATEGY_AUTO_INVEST.format_page(path=f'/{plan_id}'),
        )
    )
    send_notice_email(
        email=user.email,
        email_type='auto_invest_paused',
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_auto_invest_profit_email(
        plan_id: int, user_id: int, source_asset: str, target_asset: str, profit_amount: str, profit_rate: str, notice_type: str
):
    user = User.query.get(user_id)

    if notice_type == AutoInvestPlanNoticeConfig.NoticeType.PROFIT_AMOUNT.name:
        email_type = 'auto_invest_profit_amount'
    else:
        email_type = 'auto_invest_profit_rate'

    template_args = dict(
        name=user.name_displayed,
        target_asset=target_asset,
        source_asset=source_asset,
        profit_amount=profit_amount,
        profit_rate=profit_rate,
        notice_type=notice_type,
    )
    pref = UserPreferences(user.id)
    lang = pref.language.value
    template_args.update(
        dict(
            site_url=WebPagePath.STRATEGY_AUTO_INVEST.format_page(path=f'/{plan_id}'),
        )
    )
    send_notice_email(
        email=user.email,
        email_type=email_type,
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_auto_invest_closed_email(plan_id: int, user_id: int, target_asset: str,
                                  source_asset: str, total_source_amount: str):
    user = User.query.get(user_id)

    template_args = dict(
        name=user.name_displayed,
        target_asset=target_asset,
        source_asset=source_asset,
        total_source_amount=total_source_amount,
    )
    pref = UserPreferences(user.id)
    lang = pref.language.value
    template_args.update(
        dict(
            site_url=WebPagePath.STRATEGY_AUTO_INVEST.format_page(path=f'/{plan_id}'),
        )
    )
    send_notice_email(
        email=user.email,
        email_type='auto_invest_closed',
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_coupon_notice_email_task(
        email: str,
        send_timing: str,
        coupon_type: str,
        value: str,
        value_type: str,
        lang: str,
        url: str,
        expired_at: str,
        is_random: str,
        qualified_trade_amount: str,
        trade_type: str
):
    if trade_type:
        trade_type = translate_(lang, trade_type)
    template_name = f"{send_timing.lower()}_notice"
    if is_random:
        template_name = f"random_{send_timing.lower()}_notice"
    EmailSender.send_from_template(
        f"notice",
        f"{coupon_type.lower()}/{template_name}",
        dict(
            name=email,
            value=value,
            value_type=value_type,
            expired_at=expired_at,
            is_random=is_random,
            qualified_trade_amount=qualified_trade_amount,
            trade_type=trade_type,
            url=url
        ),
        email,
        lang
    )


@celery_task(queue=CeleryQueues.VERIFICATION_EMAIL)
def send_api_expiration_extend_email(api_auth_id: int):
    """发送延长API有效期邮件"""
    record: ApiAuth = ApiAuth.query.get(api_auth_id)
    if not record:
        raise ValueError(f'invalid api auth id: {api_auth_id}')

    user = record.user
    if UserSettings(user.id).login_disabled_by_admin:
        return
    remark = record.remark
    expired_at = '{}(UTC)'.format(datetime_to_str(record.expired_at))
    record_user_id = user.id
    to_send_users: List[User] = []
    if user.is_sub_account:
        # 子账号续期邮件同时发送给 主账号 和 创建API的托管账号
        main_user = user.main_user
        sub_user_name = user.name_displayed
        token_email = main_user.email
        to_send_users.append(main_user)
        if record.creator_id and record.creator_id != record_user_id and record.creator_id != main_user.id:
            # 此API是托管账号创建
            manage_user = User.query.get(record.creator_id)
            to_send_users.append(manage_user)
    else:
        sub_user_name = None
        token_email = user.email
        to_send_users.append(user)

    email_token = EmailToken.new(
        record_user_id,
        token_email,
        EmailToken.EmailType.API_EXPIRATION_EXTEND,
        dict(api_auth_id=api_auth_id),
        ttl=1800,
    )
    token = email_token.token
    api_url = WebPagePath.APIKEY.value
    email_type = EmailToken.EmailType.API_EXPIRATION_EXTEND.value
    for to_send_user in to_send_users:
        if not to_send_user.email:
            continue
        if UserSettings(to_send_user.id).login_disabled_by_admin:
            continue
        pref = UserPreferences(to_send_user.id)
        lang = pref.language.value
        url = WebPagePath.APIKEY_RENEWAL.format_page(token=token, lang=lang)
        send_user_name = to_send_user.name_displayed
        template_args = {
            "name": send_user_name,
            "graph_name": sub_user_name if sub_user_name is not None else send_user_name,
            "remark": remark,
            "expired_at": expired_at,
            "api_url": api_url,
        }
        _send_confirmation_email(to_send_user.email, email_type, token, url, template_args, lang=lang)


@celery_task
def send_ambassador_application_to_agent(template: str, ambassador_id: int,
                                         agent_id: int, telegram: str,
                                         created_at: str = ''):
    pref = UserPreferences(agent_id)
    lang = pref.language.value
    user = User.query.get(ambassador_id)
    country = get_country(user.location_code)
    location = translate_(lang, country.cn_name) if country else ''
    ambassador_email = user.email
    referral_agent = AmbassadorAgentBusiness.get_agent(agent_id)
    agent_email = referral_agent.user.email
    template_args = {
        'name': agent_email,
        'ambassador_email': ambassador_email,
        'location': location,
        'telegram': telegram
    }
    if template == 'ambassador_application_approve':
        ambassador = Ambassador.query.filter(Ambassador.user_id == ambassador_id).first()
        level = translate_(lang, Ambassador.LEVEL_NAMES[ambassador.level])
        approved_at = datetime_to_str(datetime.datetime.now())
        template_args.update(
            {
                'level': level,
                'created_at': created_at,
                'approved_at': approved_at,
                "name": user.name_displayed,
            }
        )
    EmailSender.send_from_template(
        'notice',
        template,
        template_args,
        agent_email,
        lang
    )


def translate_(lang: str, string: str) -> str:
    with force_locale(lang):
        return gettext(string)


@celery_task
def send_potential_ambassador_email(template_name: str, user_id: int, user_name: str, email: str, ambassador_url: str):
    pref = UserPreferences(user_id)
    lang = pref.language.value
    template_args = {
        "name": user_name,
        "ambassador_url": ambassador_url,
    }
    send_notice_email(
        email=email,
        email_type=template_name,
        template_args=template_args,
        lang=lang,
    )


@celery_task
def send_trade_rank_actitivity_gift_email(user_id: int, trade_rank_activity_id: int,
                                          amount: Decimal, asset: str):

    lang = UserPreferences(user_id).language
    user = User.query.get(user_id)
    detail = TradeRankActivityDetail.query.filter(
        TradeRankActivityDetail.trade_activity_id == trade_rank_activity_id,
        TradeRankActivityDetail.lang == lang
    ).first()
    args = dict(
            amount=str(amount),
            asset=asset,
            title=detail.title,
            name=user.name_displayed
        )
    send_notice_email(
        email=user.email,
        email_type="trade_rank_activity_gift",
        template_args=args,
        lang=lang.value,
    )
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.TRADE_RANK_ACTIVITY_GIFT.name,
            content=MessageContent.TRADE_RANK_ACTIVITY_GIFT.name,
            params=json.dumps(args),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.SYSTEM,
        )
    )


@celery_task
def send_ambassador_level_change_pre_notify_email(
        user_id: int,
        level: str,
        cur_amount: str,
        cur_delta_amount: str,
        cur_count: int,
        cur_delta_count: int,
):
    """大使预降级提醒"""
    lang = UserPreferences(user_id).language
    user = User.query.get(user_id)
    with force_locale(lang.value):
        amount_satisfied, count_satisfied = False, False
        if Decimal(cur_delta_amount) <= 0:
            amount_satisfied = True
        if Decimal(cur_delta_count) <= 0:
            count_satisfied = True
        result = dict(
            name=user.name_displayed,
            support_url=config['SUPPORT_URL'],
            level_name=gettext(Ambassador.LEVEL_NAMES[Ambassador.Level[level]]),
            cur_amount=f'{cur_amount} USD',
            cur_delta_amount=f'{cur_delta_amount} USD',
            cur_count=cur_count,
            cur_delta_count=cur_delta_count,
            amount_satisfied=amount_satisfied,
            count_satisfied=count_satisfied,
        )
    EmailSender.send_from_template(
        'notice',
        'ambassador_level_change_pre_notify',
        result,
        user.email,
        subject=None,
        lang=str(lang.value)
    )


@celery_task
def send_margin_function_introduction_email(user_id):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    send_notice_email(
        email=user.email,
        email_type="margin_function_introduction",
        template_args=dict(
            name=user.name_displayed,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_perpetual_trading_introduction_email(user_id):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    send_notice_email(
        email=user.email,
        email_type="perpetual_trading_introduction",
        template_args=dict(
            name=user.name_displayed,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_broker_approve_email(user_id, rate):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    broker_api_url = 'https://docs.coinex.com/api/v2/'
    if pref.language == Language.ZH_HANT_HK:
        broker_api_url = 'https://docs.coinex.com/api/v2/tw/'
    send_notice_email(
        email=user.email,
        email_type="broker_approve",
        template_args=dict(
            name=user.name_displayed,
            rate=rate,
            broker_info_url=WebPagePath.BROKER.format_page(logined=1),
            broker_api_url=broker_api_url,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_broker_auth_approve_email(user_id, client_id, client_secret):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    send_notice_email(
        email=user.email,
        email_type="broker_auth_approve",
        template_args=dict(
            name=user.name_displayed,
            client_id=client_id,
            client_secret=client_secret,
        ),
        lang=pref.language.value,
    )


@celery_task
def send_novice_email(user_id, activate_title, activate_day, lang):
    user = User.query.get(user_id)
    send_notice_email(user.email, 'novice_package', dict(
        name=user.name_displayed,
        activate_title=activate_title,
        activate_day=activate_day,
        site_url=f"{config['SITE_URL']}{MessageWebLink.NOVICE_PAGE.value}"  # 暂不修改
    ), lang=lang)


@celery_task
def send_open_pos_tp_sl_fail_email(user_id: int, market: str, web_link_market: str, type_: str):
    """合约开仓止盈止损失败通知"""
    if type_ == 'stop_loss':
        template_type = 'perpetual_open_position_stop_loss_notice'
    elif type_ == 'take_profit':
        template_type = 'perpetual_open_position_take_profit_notice'
    else:
        raise InvalidArgument(message='止盈止损类型错误')
    user = User.query.get(user_id)
    notice_users = get_notice_users(user)
    is_show_sub_account_name, sub_account_name = get_show_sub_acc_info(user)
    url = WebPagePath.PERPETUAL_LIQ_RECORD.value + web_link_market
    for notice_user in notice_users:
        pref = UserPreferences(notice_user.id)
        lang = pref.language.value
        EmailSender.send_from_template(
            template_category='notice',
            template_type=template_type,
            template_args=dict(
                is_show_sub_account_name=is_show_sub_account_name,
                sub_account_name=sub_account_name,
                market=market,
                name=user.name_displayed,
                url=url,
            ),
            recipient=notice_user.email,
            lang=lang,
        )


@celery_task
def send_staking_remove_success_email(user_id: int, amount: str, asset: str):
    """发送质押赎回成功通知"""
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    title = MessageTitle.STAKING_REMOVE_SUCCESS.name
    content = MessageContent.STAKING_REMOVE_SUCCESS.name
    params = dict(
        amount=amount,
        asset=asset,
    )
    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=title,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            channel=Message.Channel.SYSTEM,
        )
    )
    lang = pref.language.value
    EmailSender.send_from_template(
        template_category='notice',
        template_type='staking_remove_success',
        template_args=dict(
            site_url=config['SITE_URL'],
            amount=amount,
            asset=asset,
        ),
        recipient=user.email,
        lang=lang,
    )


@celery_task
def send_staking_add_notice_email(user_id: int, amount: str, asset: str):
    """发送质押生效提醒通知"""
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)
    title = MessageTitle.STAKING_REMOVE_NOTICE.name
    content = MessageContent.STAKING_REMOVE_NOTICE.name
    params = dict(
        amount=amount,
        asset=asset,
    )
    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=title,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            channel=Message.Channel.SYSTEM,
        )
    )
    lang = pref.language.value
    EmailSender.send_from_template(
        template_category='notice',
        template_type='staking_remove_notice',
        template_args=dict(
            site_url=config['SITE_URL'],
            amount=amount,
            asset=asset,
        ),
        recipient=user.email,
        lang=lang,
    )


@celery_task
def send_kyc_pro_result(record_id: int):
    record: KycVerificationPro = KycVerificationPro.query.get(record_id)
    user: User = User.query.get(record.user_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value
    email = user.main_user_email
    site_url = config['SITE_URL']
    support_url = config['SUPPORT_URL']
    if record.status is KycVerificationPro.Status.PASSED:
        send_notice_email(email, 'kyc_pro_pass', dict(
            name=user.name_displayed,
            home_link=site_url,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)
        title = MessageTitle.KYC_PRO_PASS.name
        content = MessageContent.KYC_PRO_PASS.name
        params = dict()
    else:
        with force_locale(lang):
            send_notice_email(email, 'kyc_pro_fail', dict(
                name=user.name_displayed,
                verify_id_result=record.get_reject_reason(),
                account_link=_web_path.ACCOUNT_BASIC,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)
        title = MessageTitle.KYC_PRO_FAIL.name
        content = MessageContent.KYC_PRO_FAIL.name
        msg_reason_list = record.get_reject_reason(translate=False).split(';')
        params = {
            'need_translates': [
                {
                    'name': 'reject_reason',
                    'text': '{reject_reason}',
                    'params': {'reject_reason': msg_reason_list}
                }
            ]
        }

    db.session_add_and_commit(
        Message(
            user_id=user.id,
            title=title,
            content=content,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.ACCOUNT_KYC_PRO_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            channel=Message.Channel.ACCOUNT_SECURITY,
        )
    )


@celery_task
def send_sumsub_result_rechange_email_task(kyc_history_id: int):
    """
    发送邮件
    """
    history: KycVerificationHistory = KycVerificationHistory.query.get(kyc_history_id)
    if not history:
        return

    emails = config['KYC_RECHANGE_NOTIFY_EMAILS']
    reason = history.get_reject_reason(translate=False) or ""

    email_content = f"""
    <p>有用户在的初级KYC在sumsub由“通过”改为“拒绝”，请及时查看处理；</p>
    <p>流水号为：{history.transaction_id}</p>
    <p>拒绝原因为：{reason}</p>
    """
    for email in emails:
        send_internal_user_email.delay(
            email=email,
            email_content=email_content,
            subject=f'sumsub再次修改初级KYC通知',
            lang=Language.ZH_HANS_CN.value
        )


@celery_task
def send_copy_trading_email(user_id: int, lang: str, template_name: str, params: str):
    """ 跟单相关邮件 """
    user: User = User.query.get(user_id)
    json_params = json.loads(params)
    json_params['user_id'] = user_id  # 避免send_from_template替换recipient_email的值
    if 'recipient_email' not in json_params:
        json_params['recipient_email'] = user.name_displayed
    send_notice_email(
        email=user.email,
        email_type=f'copy_trading/{template_name}',
        template_args=json_params,
        lang=lang,
    )


@celery_task
def send_payment_email(user_id: int, lang: str, template_name: str, params: str):
    """ 收付款相关邮件 """
    user: User = User.query.get(user_id)
    json_params = json.loads(params)
    json_params['user_id'] = user_id
    if 'recipient_email' not in json_params:
        json_params['recipient_email'] = user.name_displayed
    send_notice_email(
        email=user.email,
        email_type=template_name,
        template_args=json_params,
        lang=lang,
    )


@celery_task
def send_p2p_email(user_id: int, lang: str, template_name: str, params: str):
    user: User = User.query.get(user_id)
    json_params = json.loads(params)
    json_params['recipient_email'] = user.name_displayed
    send_notice_email(
        email=user.email,
        email_type=f'p2p/{template_name}',
        template_args=json_params,
        lang=lang
    )


@celery_task
def send_liveness_check_email_task(history_id: int, email: str):
    """
    发送活体验证邮件
    """
    from . import LivenessCheckBusiness

    history = LivenessCheckHistory.query.get(history_id)
    if not history:
        return
    user_id = history.user_id
    user: User = User.query.get(user_id)
    if not user or user.is_sub_account:
        return

    pref = UserPreferences(user_id)
    lang = pref.language.value
    email_token = EmailToken.new(
        user_id,
        user.email,
        EmailToken.EmailType.LIVENESS_CHECK,
        dict(history_id=history_id),
        ttl=LivenessCheckBusiness.VERIFICATION_EXPIRE_TIME,
    )
    liveness_page = WebPagePath.LIVENESS_VERIFY.format_page(path=f'/{email_token.token}')
    with force_locale(lang):
        send_notice_email(email, 'liveness_check', dict(
            name=user.communication_name,
            site_url=liveness_page,
            hours=24,
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)


@celery_task
def send_kyt_audit_email(record_id: int):
    # TODO: 过渡性保留

    def _get_token():
        email_token = EmailToken.new(
            user.id,
            email,
            EmailToken.EmailType.KYT_DEPOSIT_AUDIT,
            dict(kyt_id=record_id),
            ttl=60 * 60 * 24 * 30,
        )
        return email_token.token

    record: KYTDepositAudit = KYTDepositAudit.query.get(record_id)
    user: User = User.query.get(record.user_id)
    deposit: Deposit = Deposit.query.get(record.deposit_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value
    email = user.main_user_email
    support_url = config['SUPPORT_URL']
    if record.status is KYTDepositAudit.Status.INFO_REQUIRED:
        send_notice_email(email, 'kyt_deposit_info_required', dict(
            deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
            asset=deposit.asset,
            amount=amount_to_str(deposit.amount),
            kyc_status=user.kyc_status.name,
            tx_id=(tx_id := deposit.tx_id),
            tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
            kyc_page=WebPagePath.KYC.value,
            kyt_page=WebPagePath.KYT.format_page(token=_get_token()),
            name=user.name_displayed,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        ), lang=lang)
    elif record.status in [
        KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
        KYTDepositAudit.Status.FREEZING,
    ]:
        email_type = 'kyt_deposit_extra_info_required'
        if record.status is KYTDepositAudit.Status.FREEZING:
            email_type = 'kyt_deposit_freezing'
        with force_locale(lang):
            send_notice_email(email, email_type, dict(
                deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
                asset=deposit.asset,
                amount=amount_to_str(deposit.amount),
                name=user.name_displayed,
                reject_reason=record.get_reject_reason(),
                tx_id=(tx_id := deposit.tx_id),
                tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
                kyt_page=WebPagePath.KYT.format_page(token=_get_token()),
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)
    elif record.status is KYTDepositAudit.Status.WITHDRAWAL_ONLY:
        with force_locale(lang):
            send_notice_email(email, 'kyt_deposit_withdrawal_only', dict(
                name=user.name_displayed,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)


@celery_task
def send_business_ambassador_referral_detail_email(
        email: str, title: str, lang: str, export_url: str
):
    """商务大使发邀请数据明细邮件"""
    EmailSender.send_from_template(
        'notice',
        'business_ambassador_referral_detail',
        dict(
            name=email,
            export_url=export_url,
        ),
        email,
        lang,
        subject=title
    )


@celery_task
def send_coupon_balance_warn_email(user_id: int, warn_text: str):
    user: User = User.query.get(user_id)
    pref = UserPreferences(user_id)
    lang = pref.language.value
    email = user.main_user_email
    json_params = {
        "recipient_email": user.name_displayed,
        "warn_text": warn_text
    }
    send_notice_email(
        email=email,
        email_type='coupon_balance_warn',
        template_args=json_params,
        lang=lang
    )


@celery_task
def send_deposit_bonus_reword_email(user_id, gift_type, title, rewards, page_path, template_name, lang):
    user: User = User.query.get(user_id)
    email = user.main_user_name_displayed
    EmailSender.send_from_template(
        "notice",
        template_name,
        dict(
            name=email,
            gift_type=gift_type,
            title=title,
            rewards=rewards,
            url=url_join(config["SITE_URL"], page_path),  # 透传的 path 暂时不改
        ),
        user.email,
        lang
    )


@celery_task
def batch_send_system_business_push_email(push_id: int, user_ids: list[int]):
    users = User.query.filter(User.id.in_(user_ids)).all()
    push = EmailPush.query.get(push_id)
    kwargs = push.get_system_template_kwargs()
    if not kwargs or kwargs['business'] != 'RISK_SCREEN':
        # 当前只支持风险筛查
        return

    for user in users:
        if not (email := user.email):
            continue
        if not (kw := kwargs['template_kwargs'].get(user.id)):
            continue
        template_args = dict(
            template_type='risk_screen_kyc_edd',
            from_year=kw.get('from_year', ''),
            to_year=kw.get('to_year', ''),
            date=kw.get('date', ''),
        )
        try:
            send_notice_email.delay(
                email=email,
                email_type=template_args['template_type'],
                template_args=template_args,
                lang=Language.EN_US.value,
            )
        except Exception as e:
            current_app.logger.error(f"{user.id} send_push_email_by_system_business {email} error: {e}")


def send_email_by_business(user_id: int, params: dict, business: str, type_: str):
    handler_map = {
        'COMMENT': send_comment_email,
    }
    handler = handler_map.get(business)
    if handler:
        handler.delay(user_id, params, type_)


@celery_task
def send_comment_email(user_id: int, params: dict, type_: str):
    user = User.query.get(user_id)
    pref = UserPreferences(user_id)

    lang = params.get('lang')
    if lang:
        lang = Language[lang].value
    else:
        lang = pref.language.value
    if type_ == 'interaction':

        count = params.get('tip_count', 0)
        if not count:
            return

        amount = ', '.join([f'{amount}{asset}' for asset, amount in params.get('tip_amount', {}).items()])
        if not amount:
            return

        with force_locale(lang):
            title = gettext("收到%(count)s份礼物", count=count)
            content = gettext("您发表的内容收到%(count)s份礼物(打赏)共%(amount)s，", count=count, amount=amount)

        EmailSender.send_from_template(
            'notice',
            'comment_interaction',
            dict(
                name=user.email,
                title=title,
                content=content,
                site_url=config['INTERACTION_MESSAGE_URL']
            ),
            user.email,
            lang,
            subject=f"【CoinEx】{title}"
        )


@celery_task
def send_deposit_audit_email(record_id: int):
    record: DepositAudit = DepositAudit.query.get(record_id)
    user: User = User.query.get(record.user_id)
    deposit: Deposit = Deposit.query.get(record.deposit_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value
    email = user.main_user_email
    support_url = config['SUPPORT_URL']
    if record.status == DepositAudit.Status.FREEZING:
        email_type = 'edd_freezing'
        kw = dict(
            deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
            asset=deposit.asset,
            amount=amount_to_str(deposit.amount),
            name=user.name_displayed,
            reject_reason=record.get_reject_reason(),
            tx_id=(tx_id := deposit.tx_id),
            tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        )
        with force_locale(lang):
            send_notice_email(email, email_type, kw, lang=lang)


@celery_task
def send_edd_audit_email(record_id: int):
    record: EDDAudit = EDDAudit.query.get(record_id)
    user: User = User.query.get(record.user_id)
    deposit: Deposit = Deposit.query.get(record.deposit_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value
    email = user.main_user_email
    support_url = config['SUPPORT_URL']
    edd_page = WebPagePath.EDD.format_page(path=f'/{record.id}')
    if record.status == EDDAudit.Status.INFO_REQUIRED:
        email_template = 'edd_manual_info_required'
        kw = dict(
            kyc_status=user.kyc_status.name,
            kyc_page=WebPagePath.KYC.value,
            edd_page=edd_page,
            name=user.name_displayed,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        )
        if record.source == EDDAudit.Source.DEPOSIT_AUDIT:
            email_template = 'edd_info_required'
            kw = dict(
                deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
                asset=deposit.asset,
                amount=amount_to_str(deposit.amount),
                kyc_status=user.kyc_status.name,
                tx_id=(tx_id := deposit.tx_id),
                tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
                kyc_page=WebPagePath.KYC.value,
                edd_page=edd_page,
                name=user.name_displayed,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            )
        send_notice_email(email, email_template, kw, lang=lang)
    elif record.status in [
        EDDAudit.Status.EXTRA_INFO_REQUIRED,
        EDDAudit.Status.FREEZING,
    ]:
        email_type = None
        kw = dict(
            name=user.name_displayed,
            reject_reason=record.get_reject_reason(),
            edd_page=edd_page,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        )
        if record.status == EDDAudit.Status.FREEZING:
            if record.source == EDDAudit.Source.DEPOSIT_AUDIT:
                email_type = 'edd_freezing'
                kw = dict(
                    deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
                    asset=deposit.asset,
                    amount=amount_to_str(deposit.amount),
                    name=user.name_displayed,
                    reject_reason=record.get_reject_reason(),
                    tx_id=(tx_id := deposit.tx_id),
                    tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
                    edd_page=edd_page,
                    support_url=support_url,
                    anti_phishing_code=pref.anti_phishing_code,
                )
        else:
            email_type = 'edd_manual_extra_info_required'
            if record.source == EDDAudit.Source.DEPOSIT_AUDIT:
                email_type = 'edd_extra_info_required'
                kw = dict(
                    deposit_at=datetime_to_str(deposit.created_at, pref.timezone_offset),
                    asset=deposit.asset,
                    amount=amount_to_str(deposit.amount),
                    name=user.name_displayed,
                    reject_reason=record.get_reject_reason(),
                    tx_id=(tx_id := deposit.tx_id),
                    tx_id_url=WalletClient().get_explorer_tx_url(deposit.chain, tx_id),
                    edd_page=edd_page,
                    support_url=support_url,
                    anti_phishing_code=pref.anti_phishing_code,
                )
        if not email_type:
            return
        with force_locale(lang):
            send_notice_email(email, email_type, kw, lang=lang)
    elif record.status == EDDAudit.Status.AUDITED:
        if record.source != EDDAudit.Source.MANUAL:
            return
        with force_locale(lang):
            send_notice_email(email, 'edd_manual_pass', dict(
                name=user.name_displayed,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            ), lang=lang)


def send_mission_email(user_id: int, lang: str, template_name: str, params: str):
    user: User = User.query.get(user_id)
    json_params = json.loads(params)
    json_params['recipient_email'] = user.name_displayed
    send_notice_email(
        email=user.email,
        email_type=f'mission_center/{template_name}',
        template_args=json_params,
        lang=lang
    )


def send_guide_to_rate_trustpilot_email(user_id: int, is_new_user: bool):
    """ 引导至Trustpilot评分 """
    user = User.query.get(user_id)
    email_type = 'guide_to_rate_trustpilot_new' if is_new_user else 'guide_to_rate_trustpilot_old'
    bcc_address = config['TRUSTPILOT_BCC_ADDRESS'] or None  # 密送至Trustpilot
    send_notice_email(
        email=user.email,
        email_type=email_type,
        template_args=dict(),
        lang=Language.EN_US.value,
        handlers=[AWSCommentEmail],
        bcc_address=bcc_address,
    )


@celery_task
def send_package_settlement_not_ambassador_msg(package_id: int, invalidated: bool):
    """"大使激励包考核不通过（原因：不是大使）"""
    package = UserAmbassadorPackage.query.get(package_id)
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    settle_records = PackageSettlementHistory.query.filter(
        PackageSettlementHistory.user_package_id == package_id,
    ).order_by(PackageSettlementHistory.id.desc()).all()
    asset = batch.asset
    total_release_amount, total_release_periods = Decimal(), 0
    lose_amount, lose_periods = Decimal(), 0
    for record in settle_records:
        if not record.is_settled:
            lose_amount += record.period_amount
            lose_periods += 1
        else:
            total_release_periods += 1
            total_release_amount += record.settled_amount
    user_id = package.user_id
    user = User.query.get(user_id)
    email = user.main_user_email
    lang = UserPreferences(user_id).language.value

    if invalidated:
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_suspend',
            dict(
                asset=asset,
                total_release_periods=total_release_periods,
                lose_periods=lose_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_amount=amount_to_str(lose_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
                apply_link=WebPagePath.AMBASSADOR_APPLY_PAGE.value,
            ),
            email,
            lang,
        )
    else:
        latest_settle_rec = settle_records[0]
        period_amount = latest_settle_rec.period_amount
        remaining_periods = batch.periods - len(settle_records)
        remaining_amount = batch.package_amount - (total_release_amount + lose_amount)
        package_amount = amount_to_str(batch.package_amount, 2)
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_fail_amb_invalid',
            dict(
                asset=asset,
                period_amount=amount_to_str(period_amount, 2),
                total_release_periods=total_release_periods,
                lose_periods=lose_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_amount=amount_to_str(lose_amount, 2),
                remaining_periods=remaining_periods,
                remaining_amount=amount_to_str(remaining_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
                apply_link=WebPagePath.AMBASSADOR_APPLY_PAGE.value,
                package_amount=package_amount,
            ),
            email,
            lang,
            )


@celery_task
def send_not_meet_requirement_package_msg(package_id: int, is_last_period: bool):
    """"大使激励包考核不通过（原因：未达标）"""
    package = UserAmbassadorPackage.query.get(package_id)
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    settle_records = PackageSettlementHistory.query.filter(
        PackageSettlementHistory.user_package_id == package_id,
    ).order_by(PackageSettlementHistory.id.desc()).all()
    asset = batch.asset
    total_release_amount, total_release_periods = Decimal(), 0
    total_release_amount = Decimal()
    lose_amount, lose_periods = Decimal(), 0
    for record in settle_records:
        if not record.is_settled:
            lose_amount += record.period_amount
            lose_periods += 1
        else:
            total_release_periods += 1
            total_release_amount += record.settled_amount
    latest_settle_rec = settle_records[0]
    period_amount = latest_settle_rec.period_amount
    current_refer_users = latest_settle_rec.refer_users
    current_refer_amount = latest_settle_rec.refer_amount

    user_id = package.user_id
    user = User.query.get(user_id)
    email = user.main_user_email
    lang = UserPreferences(user_id).language.value
    if is_last_period:
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_fail_final',
            dict(
                asset=asset,
                period_amount=amount_to_str(period_amount, 2),
                current_refer_users=current_refer_users,
                current_refer_amount=amount_to_str(current_refer_amount, 2),
                total_release_periods=total_release_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_periods=lose_periods,
                lose_amount=amount_to_str(lose_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
            ),
            email,
            lang,
        )
    else:
        remaining_periods = batch.periods - len(settle_records)
        remaining_amount = batch.package_amount - (total_release_amount + lose_amount)
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_fail_assessment',
            dict(
                asset=asset,
                current_refer_users=current_refer_users,
                current_refer_amount=amount_to_str(current_refer_amount, 2),
                total_release_periods=total_release_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_periods=lose_periods,
                lose_amount=amount_to_str(lose_amount, 2),
                remaining_periods=remaining_periods,
                remaining_amount=amount_to_str(remaining_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
                period_amount=amount_to_str(period_amount, 2),
            ),
            email,
            lang,
        )


@celery_task
def send_meet_requirement_package_msg(package_id: int, is_last_period: bool):
    """"大使激励包考核通过"""
    package = UserAmbassadorPackage.query.get(package_id)
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    settle_records = PackageSettlementHistory.query.filter(
        PackageSettlementHistory.user_package_id == package_id,
    ).order_by(PackageSettlementHistory.id.desc()).all()
    asset = batch.asset
    total_release_amount, total_release_periods = Decimal(), 0
    lose_amount, lose_periods = Decimal(), 0
    for record in settle_records:
        if not record.is_settled:
            lose_amount += record.period_amount
            lose_periods += 1
        else:
            total_release_periods += 1
            total_release_amount += record.settled_amount
    latest_settle_rec = settle_records[0]
    current_refer_users = latest_settle_rec.refer_users
    current_refer_amount = latest_settle_rec.refer_amount
    settled_amount = latest_settle_rec.settled_amount

    user_id = package.user_id
    user = User.query.get(user_id)
    email = user.main_user_email
    lang = UserPreferences(user_id).language.value
    if is_last_period:
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_success_final',
            dict(
                asset=asset,
                settled_amount=amount_to_str(settled_amount, 2),
                current_refer_users=current_refer_users,
                current_refer_amount=amount_to_str(current_refer_amount, 2),
                total_release_periods=total_release_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_periods=lose_periods,
                lose_amount=amount_to_str(lose_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
            ),
            email,
            lang,
        )
    else:
        remaining_periods = batch.periods - len(settle_records)
        remaining_amount = batch.package_amount - (total_release_amount + lose_amount)
        EmailSender.send_from_template(
            'notice',
            'ambassador_package_success',
            dict(
                asset=asset,
                current_refer_users=current_refer_users,
                current_refer_amount=amount_to_str(current_refer_amount, 2),
                total_release_periods=total_release_periods,
                total_release_amount=amount_to_str(total_release_amount, 2),
                lose_periods=lose_periods,
                lose_amount=amount_to_str(lose_amount, 2),
                remaining_periods=remaining_periods,
                remaining_amount=amount_to_str(remaining_amount, 2),
                support_url=config['SUPPORT_URL'],
                package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
                settled_amount=amount_to_str(settled_amount, 2),
            ),
            email,
            lang,
        )



@celery_task
def send_ambassador_package_invite(package_id: int):
    """"大使激励包邀请"""
    package = UserAmbassadorPackage.query.get(package_id)
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    asset = batch.asset
    periods = batch.periods
    package_amount = amount_to_str(batch.package_amount, 2)
    user_id = package.user_id
    user = User.query.get(user_id)
    email = user.main_user_email
    lang = UserPreferences(user_id).language.value
    deadline = batch.actural_release_time + timedelta(days=batch.valid_days)
    deadline_str = deadline.strftime('%Y-%m-%d %H:00')
    EmailSender.send_from_template(
        'notice',
        'ambassador_package_invite',
        dict(
            asset=asset,
            package_amount=package_amount,
            periods=periods,
            support_url=config['SUPPORT_URL'],
            package_url=WebPagePath.AMBASSADOR_PACKAGE_RUN_PAGE.value,
            deadline=deadline_str,
        ),
        email,
        lang,
    )


@celery_task
def send_ambassador_package_stop(package_id: int):
    """"大使激励包停止"""
    package = UserAmbassadorPackage.query.get(package_id)
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    asset = batch.asset
    user_id = package.user_id
    user = User.query.get(user_id)
    email = user.main_user_email
    lang = UserPreferences(user_id).language.value
    EmailSender.send_from_template(
        'notice',
        'ambassador_package_stop',
    dict(
        asset=asset,
        package_url=WebPagePath.AMBASSADOR_PACKAGE_FINISHED_PAGE.value,
        support_url=config['SUPPORT_URL'],
    ),
    email,
    lang,
    )


@celery_task
def send_emergency_contact_notify_email(log_id: int):
    record: UserInactiveNotifyLog = UserInactiveNotifyLog.query.get(log_id)
    user: User = User.query.get(record.user_id)
    uec: UserEmergencyContact = UserEmergencyContact.query.get(record.uec_id)
    pref = UserPreferences(user.id)
    lang = pref.language.value
    email = user.main_user_email
    support_url = config['SUPPORT_URL']
    if record.notify_type == UserInactiveNotifyLog.NotifyType.INACTIVE_NOTIFY:
        email_type = 'inactive_notice'
        kw = dict(
            days=record.days_before_sleep,
            name=user.name_displayed,
            email=email,
            support_url=support_url,
            anti_phishing_code=pref.anti_phishing_code,
        )
    else:
        email_type = 'emergency_notice'
        emergency_email = record.email
        with force_locale(lang):
            dormancy_period = gettext(uec.dormancy_period.value)
            kw = dict(
                emergency_name=uec.name,
                user_message=uec.user_message,
                emergency_email=emergency_email,
                email=email,
                dormancy_period=dormancy_period,
                support_url=support_url,
                anti_phishing_code=pref.anti_phishing_code,
            )
            email = emergency_email
    with force_locale(lang):
        email_type = f'emergency_contact/{email_type}'
        send_notice_email(email, email_type, kw, lang=lang)