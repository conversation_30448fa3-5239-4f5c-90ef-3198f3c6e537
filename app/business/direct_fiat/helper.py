from decimal import Decimal

from flask import current_app

from app.business import lock_call, CacheLock, Lock<PERSON>eys
from app.business.direct_fiat import Quote, Order
from app.business.security import check_withdraw_password_by_api
from app.business.wallet import WithdrawalHelper
from app.common import CeleryQueues
from app.common.fiat import DirectFiatPaymentMethod
from app.exceptions import InvalidArgument
from app.models import User, Withdrawal, db
from app.models.direct_fiat import DirectFiatOrder
from app.models.wallet import WithdrawalCancel
from app.utils import now, celery_task


class DirectFiatBuyOrderHelper:

    @classmethod
    def place_buy_order(cls, user, client, quote, address) -> tuple[DirectFiatOrder, Order]:
        try:
            order = client.place_order(
                user=user,
                quote=quote,
                address=address,
            )
        except Exception as e:
            current_app.logger.error(f"place order to {client.name} error: {e!r}")
            raise InvalidArgument(message=f'{client.name} Service Unavailable')

        chain = client.get_asset_chain(quote.asset)
        record = DirectFiatOrder.new_record(
            user_id=user.id,
            asset=quote.asset,
            chain=chain,
            coin_amount=quote.asset_amount,
            fiat_currency=quote.fiat,
            fiat_total_amount=quote.fiat_amount,
            order_id=order.id,
            address=address,
            third_party=client.name,
            order_type=DirectFiatOrder.OrderType(quote.support_type),
            payment_url=order.payment_url,
            payment_expired_at=order.extra.get('expired_at'),
            payment_method=DirectFiatPaymentMethod.PIX
        )
        return record, order



class DirectFiatSellOrderHelper:

    @classmethod
    def place_sell_order(
            cls,
            details: dict,
            quote: Quote,
            #
            user: User,
            asset: str,
            chain: str,
            address: str,
            memo: str,
            amount: Decimal,
            fee_asset: str,
            remark: str = '',
            withdraw_password: str = '',
    ) -> (DirectFiatOrder, Withdrawal):
        extra = {}
        # DONE: 分成两步进行事务提交，
        # 对于提现成功，但创建订单失败场景，需要手动处理
        user_id = user.id
        withdrawal = cls._do_withdrawal(
            user, asset, chain, address, memo, amount,
            fee_asset, extra, remark, withdraw_password,
        )
        order = DirectFiatOrder.new_record(
            user_id=user_id,
            asset=quote.asset,
            chain=chain,
            coin_amount=quote.asset_amount,
            fiat_currency=quote.fiat,
            fiat_total_amount=quote.fiat_amount,
            order_id=details['order_id'],
            address=address,
            third_party=details['third_party'],
            order_type=DirectFiatOrder.OrderType(quote.support_type),
            payment_account=details['payment_account'],
            payment_method=details['payment_method'],
            auto_commit=False,
        )
        order.status = DirectFiatOrder.Status.SELL_SENT
        order.biz_id = withdrawal.id
        order.sent_at = now()
        db.session.commit()
        return order, withdrawal

    @classmethod
    def _do_withdrawal(
            cls,
            user: User,
            asset: str,
            chain: str,
            address: str,
            memo: str,
            amount: Decimal,
            fee_asset: str,
            extra: dict = None,
            remark: str = '',
            withdraw_password: str = '',
    ) -> Withdrawal:
        WithdrawalHelper.validate_user_permission(user)
        check_withdraw_password_by_api(user, withdraw_password)
        WithdrawalHelper.validate_fee_asset(asset, fee_asset)
        extra = extra or {}
        # 这里产品要求：提现数量在后端进行手续费扣减，屏蔽用户感知
        fee = WithdrawalHelper.get_onchain_withdrawal_fee(asset, fee_asset, chain)
        actual_amount = amount - fee  # 实际提现数量
        WithdrawalHelper.validate_onchain_withdrawal_params(
            user, asset, chain, address, memo, actual_amount, extra
        )
        withdrawal: Withdrawal = WithdrawalHelper.do_withdrawal(
            w_type=Withdrawal.Type.ON_CHAIN,
            user=user,
            asset=asset,
            chain=chain,
            address=address,
            memo=memo,
            extra=extra,
            amount=actual_amount,
            fee=fee,
            fee_asset=fee_asset,
            recipient_id=None,
            remark=remark,
        )
        return withdrawal

    @classmethod
    def do_cancel_withdrawal(
            cls,
            user_id: int,
            withdraw_id: int,
    ):
        return WithdrawalHelper.do_cancel_withdrawal(
            user_id,
            withdraw_id,
            cancel_type=WithdrawalCancel.CancelType.USER,
            cancel_user_id=user_id,
        )


@celery_task(queue=CeleryQueues.REAL_TIME)
@lock_call(with_args=True)
def notify_direct_fiat_withdrawal_canceled(withdrawal_id: int):
    model = DirectFiatOrder
    order: DirectFiatOrder = model.query.filter(
        model.order_type == model.OrderType.SELL,
        model.biz_id == withdrawal_id
    ).first()
    if not order:
        return
    with CacheLock(LockKeys.direct_fiat(order.id)):
        db.session.rollback()
        order: DirectFiatOrder = model.query.filter(
            model.order_type == model.OrderType.SELL,
            model.biz_id == withdrawal_id
        ).first()
        if not order:
            return
        if order.status not in [
            model.Status.CREATED,
            model.Status.SELL_SENT,
        ]:
            current_app.logger.warning(f'DirectFiatOrder canceled failed: {withdrawal_id}, status: {order.status.name}')
            return
        order.status = model.Status.CANCELED
        order.reason = model.Reason.WITHDRAWAL_SYSTEM_TIMEOUT.name
        order.canceled_at = now()
        db.session.commit()