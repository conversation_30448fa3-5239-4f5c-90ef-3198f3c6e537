from __future__ import annotations

import datetime
from bisect import bisect_right
from collections import defaultdict
from decimal import Decimal
from typing import Dict, List, Set, Tuple

from flask import current_app
from sqlalchemy import func

from app.business import ReferralBusiness, TradeHistoryDB, PerpetualHistoryDB, PriceManager
from app.caches import MarketCache
from app.common import OrderSideType
from app.models import db, SubAccount, User, PopWindowDumpHistory, UserShareWindowRecord, VipUser, NewUserInvestmentSummary
from app.models.activity import LaunchMiningPoolUserInfo, LaunchMiningPool
from app.models.staking import StakingUserSummary
from app.utils import now, batch_iter, amount_to_str, today_datetime, today


class SharePopWindowRepository:
    """用户分享弹窗"""
    us_model = UserShareWindowRecord
    amount_pop_types = [
        us_model.PopType.investment_amount,
        us_model.PopType.stake_amount,
        us_model.PopType.user_anniversary,
        us_model.PopType.spot_profit_rate
    ]
    amount_field_names = [i.name for i in amount_pop_types]

    @classmethod
    def get_pop_window_detail(cls, user_id) -> Dict:
        pop_type_names = set(i.name for i in cls.us_model.PopType)

        new_model = cls.us_model
        new_rows = new_model.query.filter(
            new_model.user_id == user_id,
            new_model.status == new_model.Status.POPPED
        )

        pop_dict = dict()
        for row in new_rows:
            pop_name = row.pop_type.name
            if pop_name in cls.amount_field_names:
                pop_dict[pop_name] = row.detail.get("value", Decimal())
            else:
                pop_dict[pop_name] = True

        # 初始化未弹窗的字段
        for field in pop_type_names:
            if field not in pop_dict:
                pop_dict[field] = Decimal() if field in cls.amount_field_names else False
        return pop_dict

    @classmethod
    def get_waiting_pop_win(cls, user_id) -> List:
        now_ = now()
        expired_time = now_ - datetime.timedelta(days=1)
        records = cls.us_model.query.filter(
            cls.us_model.user_id == user_id,
            cls.us_model.status == cls.us_model.Status.PENDING,
            cls.us_model.created_at >= expired_time
        ).all()

        res = []
        for record in records:
            item = {
                'pop_type': record.pop_type.name,
                'detail': record.detail
            }
            res.append(item)
        return res

    @classmethod
    def update_pop_win_status(cls, user_id, pop_types: List[dict]):
        popped_list = [i["name"] for i in pop_types]
        exists_rows = cls.us_model.query.filter(
            cls.us_model.user_id == user_id,
            cls.us_model.pop_type.in_(popped_list)
        )
        exists_set = set()
        new_pop_map = {i["name"]: i["value"] for i in pop_types}
        for row in exists_rows:
            exists_set.add(row.pop_type)
            if row.status == cls.us_model.Status.PENDING:
                row.status = cls.us_model.Status.POPPED
            if row.pop_type in cls.amount_pop_types:
                old_val = Decimal(row.detail.get("value", 0))
                new_val = Decimal(new_pop_map[row.pop_type])
                if old_val < new_val:
                    row.detail = {"value": amount_to_str(new_val, 2)}

        new_popup = [i for i in pop_types if i["name"] in (set(popped_list) - exists_set)]
        for item in new_popup:
            type_ = item["name"]
            new_row = UserShareWindowRecord(
                user_id=user_id,
                pop_type=type_,
                status=cls.us_model.Status.POPPED,
                detail={"value": amount_to_str(item["value"], 2)} if type_ in cls.amount_pop_types else {},
            )
            db.session.add(new_row)
        db.session.commit()

    @classmethod
    def fmt_users(cls, user_ids: Set) -> Set:
        """弹窗用户排除子账户和系统账户"""
        sub_records = SubAccount.query.filter(
            SubAccount.user_id.in_(user_ids),
        ).with_entities(SubAccount.user_id).all()
        sub_users = {i.user_id for i in sub_records}
        user_records = User.query.filter(User.id.in_(user_ids)).with_entities(User.id).all()
        recorded_users = {i.id for i in user_records}
        return recorded_users - {0} - sub_users

    @classmethod
    def get_need_pop_user_ids(cls, main_ids, pop_type) -> set:
        new_rows = cls.us_model.query.filter(
            cls.us_model.user_id.in_(main_ids),
            cls.us_model.pop_type == pop_type
        ).all()
        popped_ids = {i.user_id for i in new_rows}
        new_ids = main_ids - popped_ids
        return new_ids

    @classmethod
    def create_pending_window(cls, user_id, pop_type, detail: dict = None):
        new_row = UserShareWindowRecord(
            user_id=user_id,
            pop_type=pop_type,
            status=cls.us_model.Status.PENDING,
            detail=detail if detail else {}
        )
        db.session.add(new_row)
        return new_row

    def gen_pop_window(self):
        raise NotImplementedError


class ServerTableDumpMixin:
    dump_model = PopWindowDumpHistory

    @classmethod
    def get_server_dump_history(cls, dump_type):
        records = cls.dump_model.query.filter(
            cls.dump_model.dump_type == dump_type
        ).all()
        return [(i.dump_db, i.dump_table, i.dump_id) for i in records]

    @classmethod
    def update_server_dump_history(cls, dump_res: Dict[Tuple, int], dump_type):
        records = cls.dump_model.query.filter(
            cls.dump_model.dump_type == dump_type
        ).all()
        for record in records:
            k = (record.dump_db, record.dump_table)
            new_id = dump_res.get(k)
            if new_id:
                record.dump_id = new_id
        db.session.commit()

    @classmethod
    def get_web_dump_id(cls, dump_type):
        record = cls.dump_model.query.filter(
            cls.dump_model.dump_type == dump_type
        ).first()
        return record.dump_id if record else None

    @classmethod
    def update_web_dump_history(cls, dump_type, new_id):
        if not new_id:
            return
        row = cls.dump_model.query.filter(
            cls.dump_model.dump_type == dump_type
        ).first()
        if not row:
            cls.create_web_dump_history(dump_type, new_id)
        elif row.dump_id != new_id:
            row.dump_id = new_id
            db.session.commit()

    @classmethod
    def create_web_dump_history(cls, dump_type, dump_id: int):
        if not dump_id:
            return
        row = PopWindowDumpHistory(
            dump_type=dump_type,
            dump_id=dump_id
        )
        db.session_add_and_commit(row)


class SpotPopWindow(SharePopWindowRepository, ServerTableDumpMixin):
    POP_TYPE = UserShareWindowRecord.PopType.first_spot_trade
    DumpType = PopWindowDumpHistory.Type.SPOT_ORDER

    @classmethod
    def get_spot_need_ids(cls, main_ids) -> set:
        need_ids = cls.get_need_pop_user_ids(main_ids, cls.POP_TYPE)
        return need_ids

    @classmethod
    def get_spot_trade_users(cls):
        spot_users = dict()
        dump_res = dict()
        table_dump_res = cls.get_server_dump_history(cls.DumpType)
        for db_, table_, id_ in table_dump_res:
            records = TradeHistoryDB.get_order_history_records(db_, table_, id_)
            if not records:
                dump_res.update({(db_, table_): id_})
            else:
                dump_res.update({(db_, table_): records[0][0]})
            for record in records:  # 因同一个user的数据只会存在于一个表中，因此只需添加该表内最早的一条记录即可
                _, market, side, user_id = record
                spot_users.update({user_id: (market, side)})
        return dump_res, spot_users

    @classmethod
    def new_first_spot_share_windows(cls, user_id, trade_info: Tuple):
        market, side = trade_info
        market_info = MarketCache(market).dict
        asset = market_info['quote_asset'] if side == OrderSideType.SELL else market_info[
            'base_asset']
        referral_code = ReferralBusiness.get_code_by_user(user_id)
        waiting_pop = UserShareWindowRecord(
            user_id=user_id,
            pop_type=cls.POP_TYPE,
            status=cls.us_model.Status.PENDING,
            detail={"asset": asset, "referral_code": referral_code}
        )
        db.session.add(waiting_pop)
        return waiting_pop

    @classmethod
    def update_share_windows(cls, pop_users_info):
        main_ids = cls.fmt_users(set(pop_users_info.keys()))
        need_pop_ids = cls.get_spot_need_ids(main_ids)
        for user_id in need_pop_ids:
            cls.new_first_spot_share_windows(user_id, pop_users_info[user_id])
        db.session.commit()

    @classmethod
    def gen_pop_window(cls):
        current_app.logger.info("start gen spot pop window")
        spot_dump_res, spot_users = cls.get_spot_trade_users()
        if spot_users:
            cls.update_share_windows(spot_users)
        cls.update_server_dump_history(spot_dump_res, PopWindowDumpHistory.Type.SPOT_ORDER)


class PerpetualPopWindow(SharePopWindowRepository, ServerTableDumpMixin):
    POP_TYPE = UserShareWindowRecord.PopType.first_position_finished
    DumpType = PopWindowDumpHistory.Type.PERPETUAL_POSITION
    _pop_type = UserShareWindowRecord.PopType
    LEVEL_TUPLE = (
        (_pop_type.first_position_finished, Decimal('0')),
        (_pop_type.first_perpetual_50_percent_profit, Decimal('0.5')),
        (_pop_type.first_perpetual_100_percent_profit, Decimal('1')),
        (_pop_type.first_perpetual_200_percent_profit, Decimal('2')),
        (_pop_type.first_perpetual_300_percent_profit, Decimal('3')),
        (_pop_type.first_perpetual_400_percent_profit, Decimal('4')),
        (_pop_type.first_perpetual_500_percent_profit, Decimal('5')),
        (_pop_type.first_perpetual_1000_percent_profit, Decimal('10')),
    )

    @classmethod
    def get_profit_pop_step(cls, profit_rate: Decimal):
        profit_list = [i[1] for i in cls.LEVEL_TUPLE]
        idx = bisect_right(profit_list, profit_rate)
        return cls.LEVEL_TUPLE[idx - 1] if idx >= 1 else (None, Decimal())

    @classmethod
    def get_pop_fields(cls):
        return [i[0] for i in cls.LEVEL_TUPLE]

    @classmethod
    def get_waiting_pop_map(cls) -> Dict:
        model = UserShareWindowRecord
        rows = model.query.filter(
            model.status == model.Status.PENDING,
            model.pop_type.in_(cls.get_pop_fields())
        ).all()
        return {i.user_id: i for i in rows}  # 合约相关的待弹窗记录只会有一条

    @classmethod
    def get_perpetual_trade_user_info(cls):
        perpetual_users = {}
        user_profit_dic = {}
        dump_res = {}
        table_dump_res = cls.get_server_dump_history(cls.DumpType)
        for db_, table_, id_ in table_dump_res:
            records = PerpetualHistoryDB.get_position_history_records(db_, table_, id_)
            if not records:
                dump_res.update({(db_, table_): id_})
            else:
                dump_res.update({(db_, table_): records[0][0]})
            for record in records:  # 因同一个user的数据只会存在于一个表中，因此只需添加该表内最早的一条记录即可
                _, user_id, market, leverage, side, type_, profit_real, amount_max_margin = record
                profit_rate = profit_real / amount_max_margin if amount_max_margin else 0
                perpetual_users.update({user_id: (market, leverage, side, type_, profit_rate)})
                user_previous_info = user_profit_dic.get(user_id)
                if user_previous_info:
                    previous_profit_rate = user_previous_info[-1]
                    if profit_rate >= previous_profit_rate:
                        user_profit_dic[user_id] = (market, leverage, side, type_, profit_rate)
                else:
                    user_profit_dic[user_id] = (market, leverage, side, type_, profit_rate)
        return dump_res, perpetual_users, user_profit_dic

    @classmethod
    def update_share_windows(cls, user_profit_dic):
        profit_users = set(user_profit_dic.keys())
        main_ids = cls.fmt_users(profit_users)
        waiting_map = cls.get_waiting_pop_map()
        highest_row_map = cls.get_new_highest_row_map(main_ids)

        for batch_ids in batch_iter(user_profit_dic.keys(), 1000):
            for user_id in batch_ids:
                market, leverage, side, type_, profit_rate = user_profit_dic[user_id]
                if user_id not in main_ids:
                    continue
                # 获取最高收益率阶段
                pop_type, new_step = cls.get_profit_pop_step(profit_rate)
                if not pop_type:
                    continue
                new_detail = {"value": amount_to_str(profit_rate, 4)}
                if user_id in highest_row_map:
                    old_rate = Decimal(highest_row_map[user_id].detail.get("value", 0))
                    _, old_step = cls.get_profit_pop_step(old_rate)
                    if old_step is not None and old_step >= new_step:
                        continue
                    else:
                        # 更新最高收益率，旧弹窗记录置为 POPPED
                        if new_model_row := highest_row_map.get(user_id):
                            new_model_row.detail = new_detail
                            if wait_row := waiting_map.get(user_id):
                                wait_row.status = cls.us_model.Status.POPPED
                        else:
                            cls.add_new_highest_row(user_id, new_detail)
                else:
                    cls.add_new_highest_row(user_id, new_detail)

                cls.add_new_share_windows(user_id, pop_type, leverage, market, profit_rate, side, type_)
            db.session.commit()

    @classmethod
    def add_new_share_windows(cls, user_id, pop_type, leverage, market, profit_rate, side, type_):
        detail = {
            'market': market,
            'leverage': amount_to_str(leverage, 2),
            'side': side,
            'type': type_,
            'profit_rate': amount_to_str(profit_rate, 4),
            "referral_code": ReferralBusiness.get_code_by_user(user_id)
        }
        new_row = UserShareWindowRecord(
            user_id=user_id,
            pop_type=pop_type,
            status=cls.us_model.Status.PENDING,
            detail=detail
        )
        db.session.add(new_row)

    @classmethod
    def add_new_highest_row(cls, user_id, new_detail):
        highest_row = UserShareWindowRecord(
            user_id=user_id,
            pop_type=cls.us_model.PopType.highest_perpetual_profit_rate,
            detail=new_detail
        )
        db.session.add(highest_row)

    @classmethod
    def get_new_highest_row_map(cls, main_ids):
        highest_rows = cls.us_model.query.filter(
            cls.us_model.user_id.in_(main_ids),
            cls.us_model.pop_type == cls.us_model.PopType.highest_perpetual_profit_rate
        ).all()
        highest_map = {i.user_id: i for i in highest_rows}
        return highest_map

    @classmethod
    def gen_pop_window(cls):
        current_app.logger.info("start gen perpetual pop window")
        perpetual_dump_res, perpetual_users, user_profit_dic = cls.get_perpetual_trade_user_info()
        if user_profit_dic:
            cls.update_share_windows(user_profit_dic)
        cls.update_server_dump_history(perpetual_dump_res, cls.DumpType)


class VipPopWindow(SharePopWindowRepository, ServerTableDumpMixin):
    POP_TYPE = UserShareWindowRecord.PopType.first_vip
    DumpType = PopWindowDumpHistory.Type.VIP

    @classmethod
    def get_vip_users(cls) -> (int, List):
        model = VipUser
        last_id = cls.get_web_dump_id(cls.DumpType)
        if not last_id:
            # 取最近1000条数据
            rows = model.query.filter(
                model.status == model.StatusType.PASS
            ).order_by(model.id.desc()).limit(1000).all()
            last_id = rows[0].id if rows else 0
            return last_id, rows

        records = model.query.filter(
            model.status == model.StatusType.PASS,
            model.id > last_id
        ).order_by(model.id.desc()).all()
        if not records:
            return last_id, []
        else:
            return records[0].id, records

    @classmethod
    def update_base_share_windows(cls, rows, pop_type):
        need_ids = cls.get_need_pop_user_ids({i.user_id for i in rows}, pop_type)
        for row in rows:
            user_id = row.user_id
            if user_id not in need_ids:
                continue
            need_ids.add(user_id)
            cls.create_pending_window(user_id, pop_type)
        db.session.commit()
        return need_ids

    @classmethod
    def gen_pop_window(cls):
        last_id, rows = cls.get_vip_users()
        if rows:
            cls.update_base_share_windows(rows, cls.POP_TYPE)
        cls.update_web_dump_history(cls.DumpType, last_id)


class LaunchPopWindow(SharePopWindowRepository, ServerTableDumpMixin):
    POP_TYPE = UserShareWindowRecord.PopType.first_launch_pool
    DumpType = PopWindowDumpHistory.Type.LAUNCH_POOL

    @classmethod
    def get_launch_data(cls) -> (int, List):
        model = LaunchMiningPoolUserInfo
        last_id = cls.get_web_dump_id(cls.DumpType)
        if not last_id:
            # 取今日数据
            start_time = today_datetime()
            end_time = now()
            rows = model.query.filter(
                model.reward_status == model.RewardStatus.FINISHED,
                model.created_at >= start_time,
                model.created_at < end_time
            ).order_by(model.id.desc()).all()
            last_id = rows[0].id if rows else 0
            return last_id, rows

        rows = model.query.filter(
            model.reward_status == model.RewardStatus.FINISHED,
            model.id > last_id
        ).order_by(model.id.desc()).all()
        if not rows:
            return last_id, []
        else:
            return rows[0].id, rows

    @classmethod
    def update_base_share_windows(cls, rows, pop_type):
        need_ids = cls.get_need_pop_user_ids({i.user_id for i in rows}, pop_type)
        pool_ids = {i.pool_id for i in rows}
        pool_map = {i.id: i for i in LaunchMiningPool.query.filter(
            LaunchMiningPool.id.in_(pool_ids)
        ).all()}
        for row in rows:
            user_id = row.user_id
            if user_id not in need_ids:
                continue
            need_ids.add(user_id)
            apr = pool_map[row.pool_id].apr or Decimal()
            detail = {"apr": amount_to_str(apr, 4)}
            cls.create_pending_window(user_id, pop_type, detail)
        db.session.commit()
        return need_ids

    @classmethod
    def gen_pop_window(cls):
        last_id, rows = cls.get_launch_data()
        if rows:
            cls.update_base_share_windows(rows, cls.POP_TYPE)
        cls.update_web_dump_history(cls.DumpType, last_id)


class StepPopWindowMixin:
    us_model = UserShareWindowRecord
    POP_TYPE: UserShareWindowRecord.PopType
    LEVEL_TUPLE = []

    @classmethod
    def get_popup_rows(cls, user_ids):
        rows = cls.us_model.query.filter(
            cls.us_model.user_id.in_(user_ids),
            cls.us_model.pop_type == cls.POP_TYPE,
        ).all()
        return rows

    @classmethod
    def get_level(cls, amount):
        idx = bisect_right(cls.LEVEL_TUPLE, amount)
        return cls.LEVEL_TUPLE[idx - 1] if idx >= 1 else Decimal()

    @classmethod
    def get_user_amount_map(cls, old_map):
        return old_map

    @classmethod
    def update_share_windows(cls, user_amount_map):
        if not user_amount_map:
            return
        max_level = cls.LEVEL_TUPLE[-1]
        need_ids = set()
        for batch_ids in batch_iter(user_amount_map.keys(), 2000):
            popup_map = {i.user_id: i for i in cls.get_popup_rows(batch_ids)}
            batch_asset_map = {k: v for k, v in user_amount_map.items() if k in batch_ids}

            max_user_ids = {k for k, v in popup_map.items() if Decimal(v.detail.get("value")) >= max_level}
            # 过滤掉已通知最高等级用户
            tmp_map = {k: v for k, v in batch_asset_map.items() if k not in max_user_ids}
            new_amount_map = cls.get_user_amount_map(tmp_map)
            for user_id, amount in new_amount_map.items():
                new_level = cls.get_level(amount)
                if not new_level:
                    continue
                detail = {"value": amount_to_str(new_level, 2)}
                if old_row := popup_map.get(user_id):
                    old_level = Decimal(popup_map[user_id].detail.get("value", 0))
                    if new_level <= old_level:
                        continue
                    old_row.status = cls.us_model.Status.PENDING
                    old_row.detail = detail
                else:
                    SharePopWindowRepository.create_pending_window(user_id, cls.POP_TYPE, detail)
                need_ids.add(user_id)
            db.session.commit()
        return need_ids


class InvestmentPopWindow(SharePopWindowRepository, StepPopWindowMixin):
    us_model = UserShareWindowRecord
    POP_TYPE = us_model.PopType.investment_amount
    LEVEL_TUPLE = (Decimal("500"), Decimal("3000"), Decimal("10000"))

    @classmethod
    def get_invest_data(cls):
        model = NewUserInvestmentSummary
        # 理财收益表会每日覆盖更新
        rows = model.query.filter(
            model.report_date == today()
        ).with_entities(
            model.user_id,
            model.amount,
            model.asset,
        ).all()
        if rows:
            new_dict = defaultdict(lambda: defaultdict(Decimal))
            for row in rows:
                new_dict[row.user_id][row.asset] += row.amount
            return new_dict

    @classmethod
    def get_user_amount_map(cls, old_map):
        prices = PriceManager.assets_to_usd()
        new_map = defaultdict(Decimal)
        for user_id, asset_data in old_map.items():
            for asset, amount in asset_data.items():
                new_map[user_id] += amount * prices.get(asset, Decimal())
        return new_map

    @classmethod
    def gen_pop_window(cls):
        user_asset_data = cls.get_invest_data()
        if user_asset_data:
            cls.update_share_windows(user_asset_data)


class StakingPopWindow(SharePopWindowRepository, StepPopWindowMixin, ServerTableDumpMixin):
    us_model = UserShareWindowRecord
    POP_TYPE = us_model.PopType.stake_amount
    LEVEL_TUPLE = (Decimal("500"), Decimal("3000"), Decimal("10000"))

    @classmethod
    def get_stake_data(cls) -> (int, dict):
        model = StakingUserSummary
        rows = model.query.filter(
            model.last_reward_at >= today_datetime(),
        ).group_by(
            model.user_id
        ).with_entities(
            model.user_id,
            func.sum(model.total_reward_usd).label("amount"),
        ).all()
        if rows:
            new_dict = defaultdict(Decimal)
            for row in rows:
                new_dict[row.user_id] += row.amount
            return new_dict

    @classmethod
    def gen_pop_window(cls):
        user_asset_data = cls.get_stake_data()
        if user_asset_data:
            cls.update_share_windows(user_asset_data)


def real_time_update_share_window():
    return [
        SpotPopWindow,
        PerpetualPopWindow,
        VipPopWindow,
        LaunchPopWindow,
    ]


def daily_update_share_window():
    return [
        InvestmentPopWindow,
        StakingPopWindow,
    ]
