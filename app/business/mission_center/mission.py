import copy
from collections import defaultdict
from datetime import timedelta, datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any

from flask import current_app
from flask_babel import gettext as _
from sqlalchemy import func

from app import Language, config
from app.api.common.request import get_request_platform
from app.business.equity_center.helper import EquityCenterService
from app.business.equity_center.helper import EquityContentHelper
from app.business.equity_center.inv_increase import IncreaseEquityHelper
from app.business.mission_center.utils import MissionUtils
from app.business.user import UserRepository
from app.caches.mission import MissionCache, MissionContentCache
from app.common import MessageWebLink
from app.models import db, User, ReferralHistory, Referral
from app.models.equity_center import EquityType
from app.models.mission_center import MissionScene, MonitorType, UserMission, Mission, MissionPlanUserGroup, \
    MissionPlan, SceneType, MissionCondition, LogicTemplate, UserMissionMonitor, MissionProgressMode
from app.models.referral import Ambassador, BusinessAmbassador
from app.utils import now, quantize_amount, amount_to_str, batch_iter, url_join
from app.utils.push import AppPagePath


class MissionDataFormatter:
    """任务数据格式化器"""

    @staticmethod
    def format_mission_data(mission_cache_data: Dict[str, Any], referrer_info: Dict[str, str]) -> Dict[str, Any]:
        """格式化任务数据"""
        return dict(
            id=mission_cache_data['mission_id'],
            mission_id=mission_cache_data['mission_id'],
            scene_type=mission_cache_data['scene_type'],
            deadline_days=mission_cache_data['deadline_days'],
            mission_type=mission_cache_data['mission_condition'],
            title=MissionBiz.build_title(mission_cache_data),
            status=UserMission.Status.PENDING.name,
            sequence=mission_cache_data['sequence'],
            referrer_info=referrer_info,
            reward=mission_cache_data['reward'],
            extra={}
        )

    @classmethod
    def _get_progress_unit(cls, mission_condition: MissionCondition, mission_params) -> str:
        unit = mission_params['ASSET']
        if mission_condition.progress_mode == MissionProgressMode.FISSION_COUNT:
            unit = _("人")
        if mission_condition in [MissionCondition.COPY_TRADING_ONCE, MissionCondition.DEMO_TRADING_ONCE]:
            unit = ""
        return unit

    @classmethod
    def _get_progress_label(cls, mission_condition: MissionCondition) -> str:
        """APP暂时不展示进度条的label"""
        return ""
        
    @classmethod
    def _check_user_is_ambassador(cls, user_id: int) -> bool:
        is_ambassador = Ambassador.query.filter(
            Ambassador.user_id == user_id,
            Ambassador.status == Ambassador.Status.VALID
        ).first()
        if is_ambassador:
            return True
        is_bus_ambassador = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id == user_id,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID
        ).first()
        return bool(is_bus_ambassador)
        
    @classmethod
    def _fmt_mission_extra(
        cls,
        mission_condition: MissionCondition, 
        mission_extra: Dict[str, Any],
        user_id: int
    ) -> Dict[str, Any]:
        """格式化任务额外数据"""
        if not mission_extra:
            return {}
        # 特殊逻辑处理，如果用户是 ambassador，则需要返回 ambassador 的链接
        # 移动端裂变任务跳转链接需要根据用户身份进行区分。
        # 后续APP端邀请页面重构完成后，需要删除这个逻辑。
        # 也没有几个裂变任务这里暂时不考虑性能问题。
        if not mission_condition.is_fission_mission:
            return mission_extra
        if not get_request_platform().is_mobile():
            return mission_extra
        if not cls._check_user_is_ambassador(user_id):
            return mission_extra
        ambassador_web_link = url_join(config['SITE_URL'], MessageWebLink.AMBASSADOR_PAGE.value)
        mission_extra['android_link'] = ambassador_web_link
        mission_extra['ios_link'] = ambassador_web_link
        return mission_extra

    @classmethod
    def format_user_mission_data(
            cls,
            mission_cache_data: Dict[str, Any],
            user_mission: UserMission,
            equity_data: Dict[str, Any],
            referrer_info: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """格式化用户任务数据"""
        mission_type = mission_cache_data["mission_condition"]
        mission_params = mission_cache_data['logic_params']
        current_value = user_mission.event_data[user_mission.mission_condition.name] \
            if user_mission.event_data else 0
        user_mission_data = dict(
            id=user_mission.id,
            mission_id=user_mission.mission_id,
            scene_type=mission_cache_data['scene_type'],
            title=MissionBiz.build_title(mission_cache_data),
            mission_type=mission_type,
            deadline_days=mission_cache_data["deadline_days"],
            start_at=user_mission.used_at,
            expired_at=user_mission.expired_at,
            completed_at=user_mission.completed_at,
            status=UserMissionBiz.get_user_mission_status(user_mission, equity_data.get('status')),
            reward=equity_data,
            sequence=mission_cache_data['sequence'],
            extra=cls._fmt_mission_extra(
                MissionCondition[mission_type],
                mission_cache_data.get('mission_extra', {}),
                user_mission.user_id
            ),
            progress=dict(
                total=quantize_amount(mission_params[mission_type], 2),
                current=quantize_amount(current_value, 2),
                progress=user_mission.progress,
                unit=cls._get_progress_unit(MissionCondition[mission_type], mission_params),
                label=cls._get_progress_label(MissionCondition[mission_type]),
            ),
        )
        if referrer_info:
            user_mission_data['referrer_info'] = referrer_info
        return user_mission_data


class MissionValidator:
    """任务验证器"""

    @staticmethod
    def check_mission_expired(user_mission: UserMission, current_time: datetime) -> bool:
        """检查任务是否过期"""
        return user_mission.status == UserMission.Status.PENDING and user_mission.expired_at < current_time

    @staticmethod
    def check_user_mission_status(query_status: UserMission.Status, user_mission: UserMission) -> bool:
        """检查用户任务状态"""
        if query_status == UserMission.Status.EXPIRED:
            return (user_mission.status == UserMission.Status.EXPIRED or
                    (user_mission.status == UserMission.Status.PENDING and user_mission.expired_at < now()))
        elif query_status == UserMission.Status.PENDING:
            return user_mission.status == UserMission.Status.PENDING and user_mission.expired_at > now()
        return query_status == user_mission.status


class MissionQuotaManager:
    """任务配额管理器"""

    @staticmethod
    def handle_plan_quota(plan: MissionPlan, users: List[User]) -> Tuple[List[User], bool]:
        """处理计划配额限制"""
        send_count = UserMission.query.filter(
            UserMission.plan_id == plan.id,
            UserMission.status != UserMission.Status.FAILED
        ).with_entities(
            func.count(UserMission.user_id.distinct())
        ).scalar() or 0

        can_send_count = plan.total - send_count
        if can_send_count <= 0:
            return [], True

        if can_send_count <= len(users):
            users = sorted(users, key=lambda x: x.created_at)[:can_send_count]
            quota_reached = True
        else:
            quota_reached = False

        return users, quota_reached


class MissionRewardManager:
    """任务奖励管理器"""

    @staticmethod
    def send_reward(
            user_missions: List[Tuple[UserMission, Dict[str, Any], EquityCenterService.UserEquityStatus]]
    ) -> List[UserMission]:
        """发放奖励"""
        equity_params = []
        user_mission_mapper = {}
        for user_mission, mission_data, equity_status in user_missions:
            user_mission_mapper[user_mission.id] = user_mission
            equity_params.append(dict(
                biz_id=user_mission.id,
                biz_type=EquityCenterService.BizTypes.MISSION,
                user_id=user_mission.user_id,
                equity_id=mission_data['equity_id'],
                status=equity_status
            ))
            current_app.logger.warning(f"发放奖励: 用户 {user_mission.user_id}, 权益ID {mission_data['equity_id']}")
        result_mapper = EquityCenterService.batch_get_or_create_user_equity(equity_params, is_commit=False)
        success_user_mission = []
        for eq_biz_key, equity in result_mapper.items():
            if equity.status == EquityCenterService.UserEquityStatus.FAILED:
                continue
            # 空投奖励在奖励发放之后触发奖励
            if equity.type == EquityType.AIRDROP:
                continue
            # eq_biz_key: (biz_id, biz_type, user_id)
            success_user_mission.append(user_mission_mapper[eq_biz_key[0]])
        return success_user_mission


class MissionBiz:
    """任务业务类"""

    class MissionTitleType(Enum):
        NORMAL = "normal"
        ONCE = "once"
        INITIAL_DEPOSIT_COUNT = "initial_deposit_count"
        INITIAL_TRADED_COUNT = "initial_traded_count"
        INITIAL_TRADED_AMOUNT = "initial_traded_amount"

    TITLE_TEMPLATE_MAP = {
        MissionTitleType.NORMAL: _("完成%(mission_type)s, 且累计金额≥%(amount)s %(asset)s，获得%(receive_reward_content)s"),
        MissionTitleType.ONCE: _("完成一次%(mission_type)s，获得%(receive_reward_content)s"),
        MissionTitleType.INITIAL_DEPOSIT_COUNT:
            _("邀请 %(user_count)s 位新用户，且每位入金≥%(amount)s %(asset)s，获得%(receive_reward_content)s"),
        MissionTitleType.INITIAL_TRADED_COUNT:
            _("邀请 %(user_count)s 位新用户，且每位交易量≥%(amount)s %(asset)s，获得%(receive_reward_content)s"),
        MissionTitleType.INITIAL_TRADED_AMOUNT:
            _("邀请新用户，且受邀新用户的累计交易量≥%(amount)s %(asset)s，获得%(receive_reward_content)s")
    }

    SIMPLE_TITLE_MAP = {
        MissionTitleType.NORMAL: _("完成%(mission_type)s, 获得%(reward_type)s奖励"),
        MissionTitleType.ONCE: _("完成一次%(mission_type)s，获得%(reward_type)s奖励"),
        MissionTitleType.INITIAL_DEPOSIT_COUNT: _("邀请新用户, 获得%(reward_type)s奖励"),
        MissionTitleType.INITIAL_TRADED_COUNT: _("邀请新用户, 获得%(reward_type)s奖励"),
        MissionTitleType.INITIAL_TRADED_AMOUNT: _("邀请新用户, 获得%(reward_type)s奖励")
    }

    @classmethod
    def get_mission_extra_by_condition(cls, mission_condition):
        match mission_condition:
            case MissionCondition.DEPOSIT_AMOUNT:
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.DEPOSIT_PAGE.value,
                    android_link=AppPagePath.DEPOSIT.value.format(asset=""),
                    ios_link=AppPagePath.DEPOSIT.domain_url,
                )
            case MissionCondition.SPOT_AMOUNT:
                app_link = AppPagePath.SPOT_TRADE.value.format(market="BTCUSDT", account=0, trade_type='')
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.SPOT_TRADE_PAGE.value,
                    android_link=app_link,
                    ios_link=app_link,
                )
            case MissionCondition.PERPETUAL_AMOUNT:
                app_link = AppPagePath.PERPETUAL_TRADE.value.format(market="BTCUSDT", trade_type='')
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.PERPETUAL_TRADE_PAGE.value,
                    android_link=app_link,
                    ios_link=app_link,
                )
            case MissionCondition.COPY_TRADING_ONCE:
                return dict(
                    is_progress=False,
                    web_link=MessageWebLink.COPY_TRADER_INDEX_PAGE.value,
                    android_link=AppPagePath.COPY_TRADING.value,
                    ios_link=AppPagePath.COPY_TRADING.value,
                )
            case MissionCondition.DEMO_TRADING_ONCE:
                return dict(
                    is_progress=False,
                    web_link=MessageWebLink.DEMO_TRADING_PAGE.value,
                    android_link=AppPagePath.DEMO_TRADING.value,
                    ios_link=AppPagePath.DEMO_TRADING.value,
                )
            case MissionCondition.INVITE_NEWBIE_DEPOSIT_COUNT:
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.REFER_PAGE.value,
                    android_link=AppPagePath.REFER_PAGE.value,
                    ios_link=AppPagePath.REFER_PAGE.value,
                )
            case MissionCondition.INVITE_NEWBIE_TRADED_COUNT:
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.REFER_PAGE.value,
                    android_link=AppPagePath.REFER_PAGE.value,
                    ios_link=AppPagePath.REFER_PAGE.value,
                )
            case MissionCondition.INVITE_NEWBIE_TRADED_AMOUNT:
                return dict(
                    is_progress=True,
                    web_link=MessageWebLink.REFER_PAGE.value,
                    android_link=AppPagePath.REFER_PAGE.value,
                    ios_link=AppPagePath.REFER_PAGE.value,
                )
            case _:
                return dict()

    @classmethod
    def get_mission_title_type(cls, mission_type: MissionCondition) -> MissionTitleType:
        if mission_type in [MissionCondition.COPY_TRADING_ONCE, MissionCondition.DEMO_TRADING_ONCE]:
            return cls.MissionTitleType.ONCE
        elif mission_type == MissionCondition.INVITE_NEWBIE_DEPOSIT_COUNT:
            return cls.MissionTitleType.INITIAL_DEPOSIT_COUNT
        elif mission_type == MissionCondition.INVITE_NEWBIE_TRADED_COUNT:
            return cls.MissionTitleType.INITIAL_TRADED_COUNT
        elif mission_type == MissionCondition.INVITE_NEWBIE_TRADED_AMOUNT:
            return cls.MissionTitleType.INITIAL_TRADED_AMOUNT
        return cls.MissionTitleType.NORMAL

    @classmethod
    def get_title_template(cls, mission_type: MissionCondition | str, is_simple: bool = False) -> str:
        if isinstance(mission_type, str):
            mission_type = MissionCondition[mission_type]
        title_type = cls.get_mission_title_type(mission_type)
        return cls.SIMPLE_TITLE_MAP[title_type] if is_simple else cls.TITLE_TEMPLATE_MAP[title_type]

    @classmethod
    def format_monitor_config(cls, user_missions: list[UserMission]) -> Dict[int, Dict[str, Any]]:
        """格式化监控配置"""

        mission_user_mappers = defaultdict(dict)
        for user_mission in user_missions:
            # 只存储进行中的任务
            if user_mission.status != UserMission.Status.PENDING:
                continue
            is_new = user_mission.used_at == UserMission.MIN_UTC_DATETIME
            mission_user_mappers.update({
                user_mission.id: {
                    "user_id": user_mission.user_id,
                    "used_at": user_mission.created_at if is_new else user_mission.used_at,
                    "created_at": user_mission.created_at,
                    "expired_at": user_mission.expired_at,
                    "mission_condition": user_mission.mission_condition.name
                }
            })
        return mission_user_mappers

    @classmethod
    def get_monitor_config(cls, plan_id) -> Dict[int, Dict[str, Any]]:
        """获取监控配置"""
        mission_query = UserMission.query.filter(
            UserMission.plan_id == plan_id,
            UserMission.status == UserMission.Status.PENDING
        ).with_entities(
            UserMission.id,
            UserMission.user_id,
            UserMission.mission_id,
            UserMission.status,
            UserMission.mission_condition,
            UserMission.used_at,
            UserMission.expired_at,
            UserMission.created_at
        ).all()
        return cls.format_monitor_config(mission_query)

    @classmethod
    def get_build_title_params(cls, mission_data: Dict[str, Any]) -> Dict[str, Any]:
        mission_params = mission_data['logic_params']
        mission_type = mission_data["mission_condition"]
        equity_params = mission_data["reward"]
        reward_type = equity_params['reward_type']
        value = equity_params["value"]
        value_type = equity_params["value_type"]
        if equity_params["reward_type"] == EquityType.INVEST_INCREASE.name:
            value, value_type = IncreaseEquityHelper.format_value_and_type(equity_params["increase_rate"])
        title_params = {
            "mission_type": MissionCondition[mission_type].value,
            "asset": mission_params["ASSET"],
            "amount": mission_params.get(mission_type, ""),
            "value": value,
            "value_type": value_type,
            "reward_type": EquityType[reward_type].value,
            "receive_reward_content": EquityContentHelper.get_reward_content(EquityType[reward_type], value, value_type),
        }
        match mission_type:
            case MissionCondition.INVITE_NEWBIE_DEPOSIT_COUNT.name:
                title_params['amount'] = mission_params[MonitorType.DEPOSIT_AMOUNT.name]
                title_params['user_count'] = mission_params[mission_type]
            case MissionCondition.INVITE_NEWBIE_TRADED_COUNT.name:
                title_params['amount'] = mission_params[MonitorType.SPOT_PERPETUAL_AMOUNT.name]
                title_params['user_count'] = mission_params[mission_type]
        return title_params

    @classmethod
    def build_title(cls, mission_data: Dict[str, Any], is_simple: bool = False) -> str:
        """构建任务标题"""
        title_params = cls.get_build_title_params(mission_data)
        params = {k: _(v) for k, v in title_params.items()}
        title_template = cls.get_title_template(mission_data["mission_condition"], is_simple)
        return _(title_template, **params)

    @classmethod
    def create_missions(cls, plan_id: int, mission_params: List[Dict[str, Any]]) -> None:
        """创建任务"""
        missions = [
            Mission(
                plan_id=plan_id,
                sequence=param['sequence'],
                deadline_days=param['deadline_days'],
                mission_condition=param['mission_condition'],
                equity_id=param['equity_id'],
                logic_params=param['logic_params']
            ) for param in mission_params
        ]
        db.session.bulk_save_objects(missions)

    @classmethod
    def update_missions(cls, plan_id: int, mission_params: List[Dict[str, Any]]) -> None:
        """更新任务"""
        mission_mapper = {
            m.id: m for m in Mission.query.filter(
                Mission.plan_id == plan_id
            ).all()
        }
        params_ids = set()
        for params in mission_params:
            id_ = params.get('id')
            if not id_:
                db.session.add(Mission(
                    plan_id=plan_id,
                    sequence=params['sequence'],
                    deadline_days=params['deadline_days'],
                    mission_condition=params['mission_condition'],
                    equity_id=params['equity_id'],
                    logic_params=params['logic_params']
                ))
                continue
            mission = mission_mapper[id_]
            mission.sequence = params['sequence']
            mission.deadline_days = params['deadline_days']
            mission.mission_condition = params['mission_condition']
            mission.equity_id = params['equity_id']
            mission.logic_params = params['logic_params']
            params_ids.add(id_)
        delete_ids = set(mission_mapper.keys()) - params_ids
        Mission.query.filter(
            Mission.id.in_(delete_ids)
        ).delete()
        db.session.flush()

    @classmethod
    def get_plan_mission_data(cls, plan_id: int) -> List[Dict[str, Any]]:
        """获取计划任务数据"""
        plan_missions = Mission.query.filter(
            Mission.plan_id == plan_id
        ).order_by(
            Mission.sequence
        ).all()
        equity_ids = {m.equity_id for m in plan_missions}
        equity_data_mapper = EquityCenterService.batch_query_equity_basic_info(equity_ids)
        data = []
        for m in plan_missions:
            equity_data = equity_data_mapper[m.equity_id]
            reward_data = {
                "reward_type": equity_data["type"].name,
                "asset": equity_data["cost_asset"],
                "amount": equity_data["cost_amount"],
                "equity_id": m.equity_id
            }
            base_dict = m.to_dict(enum_to_name=True)
            base_dict.update(reward_data)
            data.append(base_dict)
        return data

    @classmethod
    def get_mission_info(cls, mission_id: int) -> Dict[str, Any]:
        """获取任务信息"""
        mission_data = MissionCache.get_data_by_id(mission_id)
        if not mission_data:
            return {}
        info_data = MissionDataFormatter.format_mission_data(mission_data, {})
        mission_params = mission_data['logic_params']
        mission_type = mission_data["mission_condition"]
        progress = dict(
            total=quantize_amount(mission_params[mission_type], 2),
            current=Decimal(),
            progress=Decimal(),
            unit=mission_params["ASSET"]
        )
        info_data['progress'] = progress
        return info_data

    @classmethod
    def get_group_by_user_data(
            cls,
            user_data: dict,
    ) -> MissionPlanUserGroup | None:
        from .group import UserParamMatcher, MissionGroupBiz
        matcher = UserParamMatcher(SceneType.NEWBIE, user_data)
        groups = MissionGroupBiz.query_display_content_groups(SceneType.NEWBIE)
        return matcher.find_matching_group_by_groups(groups)

    @classmethod
    def get_mission_content(cls, req_params: Dict[str, Any], lang: Language, tz_offset: str, is_web: bool) -> Optional[Dict[str, Any]]:
        """获取任务内容"""
        if not lang:
            return None
        if MissionUtils.check_device_registered(req_params.get("device_id")):
            return None
        user_params = MissionUtils.get_user_params(req_params, tz_offset)
        group = cls.get_group_by_user_data(user_params)
        if not group:
            return None
        lang_content = MissionContentCache.get_lang_data(group.plan_id, lang)
        if not lang_content:
            return None
        if isinstance(lang_content, dict):
            mission_content = dict(
                content=lang_content['web_content'] if is_web else lang_content['app_content'],
                promotion_amount=lang_content['promotion_amount'],
            )
        else:
            mission_content = dict(
                content=lang_content
            )
        if (group.logic_template != LogicTemplate.REFERER_ID_IN) or not (refer_code := req_params.get("refer_code")):
            return mission_content
        referral = Referral.query.filter(Referral.code == refer_code).first()
        if not referral:
            return mission_content
        return dict(
            **mission_content,
            referrer_info=MissionUtils.get_referrer_info(referral.user_id),
        )

    @classmethod
    def query_user_mission_info(cls, biz_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """查询用户任务信息"""
        query = UserMission.query.filter(
            UserMission.id.in_(biz_ids)
        ).with_entities(
            UserMission.id,
            UserMission.mission_id,
            UserMission.plan_id,
            UserMission.scene_type
        ).all()
        return {u.id: dict(
            mission_id=u.mission_id,
            plan_id=u.plan_id,
            scene_type=u.scene_type.name
        ) for u in query}

    @classmethod
    def trigger_mission_monitor_completed(
            cls,
            monitor_type: MonitorType,
            mission_data: Dict[str, Any],
            monitor_data: Dict[str, Any]
    ) -> bool:
        """触发用户任务监控完成"""
        mode = MissionCondition[mission_data['mission_condition']].progress_mode
        # 累积类的任务不完成监控，在监控有效时间内一直监控
        if mode == MissionProgressMode.FISSION_SUM:
            return False
        progress = MissionUtils.calculate_progress(mission_data, monitor_data, monitor_type.name)
        return progress >= Decimal('100')


class AuthenticatedUserMissionRetriever:
    """已认证用户任务获取器"""

    @classmethod
    def get_missions(cls, user: User, status: Optional[UserMission.Status] = None) -> List[Dict[str, Any]]:
        """获取已认证用户的任务"""
        user_missions = UserMission.query.filter(
            UserMission.user_id == user.id,
            UserMission.used_at > UserMission.MIN_UTC_DATETIME
        ).all()
        mission_ids = [um.mission_id for um in user_missions]
        cache_data = MissionCache.get_cache_data_by_ids(mission_ids)
        biz_ids = {um.id for um in user_missions}
        equity_data_mapper = EquityCenterService.batch_query_user_eq_info(
            EquityCenterService.BizTypes.MISSION,
            biz_ids
        )
        referrer_info = UserMissionBiz.get_referrer_info_by_plan_ids(
            [i.plan_id for i in user_missions],
            user.id
        )
        return cls._format_missions(user_missions, cache_data, equity_data_mapper, referrer_info, status)

    @classmethod
    def _format_missions(
            cls,
            user_missions: List[UserMission],
            cache_data: Dict[int, Dict[str, Any]],
            equity_data_mapper: Dict[int, Dict[str, Any]],
            referrer_info: Dict[int, Dict[str, str]],
            status: Optional[UserMission.Status]
    ) -> List[Dict[str, Any]]:
        """格式化任务数据"""
        data = []
        for um in user_missions:
            if um.status == UserMission.Status.FAILED:
                continue
            if status and not MissionValidator.check_user_mission_status(status, um):
                continue
            mission_cache_data = cache_data[um.mission_id]
            equity_data = UserMissionBiz.fmt_equity_data(
                mission_cache_data['reward'],
                equity_data_mapper.get(um.id)
            )
            um_data = MissionDataFormatter.format_user_mission_data(
                cache_data[um.mission_id],
                um,
                equity_data,
                referrer_info.get(um.plan_id, {})
            )
            data.append(um_data)
        return data


class UnauthenticatedUserMissionRetriever:
    """未认证用户任务获取器"""

    @classmethod
    def get_missions(cls, user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取未认证用户的任务"""
        from .group import LogicGroupValidator
        group = LogicGroupValidator(SceneType.NEWBIE).find_matching_group(user_data)
        if not group:
            return []

        plan_id = group.plan_id
        mission_query = Mission.query.filter(
            Mission.plan_id == plan_id
        ).with_entities(
            Mission.id
        ).all()
        mission_ids = [i.id for i in mission_query]
        referrer_info = UserMissionBiz.get_mission_referrer_info(user_data, group)
        cache_data_mapper = MissionCache.get_cache_data_by_ids(mission_ids)

        return [
            MissionDataFormatter.format_mission_data(cache_data, referrer_info)
            for cache_data in cache_data_mapper.values()
        ]


class UserMissionBiz:
    """用户任务业务类"""
    UPDATE_LIMIT = 1000

    @classmethod
    def get_user_mission_status(cls, user_mission: UserMission, equity_status: Optional[str]) -> str:
        """获取用户任务状态"""
        if user_mission.status == UserMission.Status.PENDING and user_mission.expired_at < now():
            return UserMission.Status.EXPIRED.name
        if (user_mission.status == UserMission.Status.FINISHED and
                equity_status and equity_status == EquityCenterService.UserEquityStatus.CREATED.name):
            return UserMission.Status.SETTLING.name
        return user_mission.status.name

    @classmethod
    def _query_total_traded_amount(cls, user_mission_id: int) -> Decimal:
        query = UserMissionMonitor.query.filter(
            UserMissionMonitor.user_mission_id == user_mission_id
        ).with_entities(
            UserMissionMonitor.monitor_data,
            UserMissionMonitor.monitor_type
        ).all()
        total_traded_amount = Decimal()
        for monitor_data, monitor_type in query:
            total_traded_amount += Decimal(monitor_data[monitor_type.name])
        return total_traded_amount

    @classmethod
    def build_event_data(
            cls,
            mission_condition: MissionCondition,
            user_mission_monitors: List[UserMissionMonitor]
    ) -> Dict[str, Decimal]:
        """构建事件数据"""
        event_value = Decimal()
        for monitor in user_mission_monitors:
            monitor_finished = monitor.status == UserMissionMonitor.Status.INVALID
            monitor_data = monitor.monitor_data
            match mission_condition:
                case MissionCondition.INVITE_NEWBIE_DEPOSIT_COUNT:
                    event_value += Decimal(1) if monitor_finished else Decimal()
                case MissionCondition.INVITE_NEWBIE_TRADED_COUNT:
                    event_value += Decimal(1) if monitor_finished else Decimal()
                case _:
                    event_value += Decimal(monitor_data[monitor.monitor_type.name])

        return {mission_condition.name: event_value}

    @classmethod
    def update_event_data(
            cls,
            event_data: Dict[str, Decimal],
            user_mission_data: Dict[str, Decimal]
    ) -> Dict[str, Decimal]:
        """更新事件数据"""
        if not event_data:
            return user_mission_data
        new_data = {}
        for k, v in user_mission_data.items():
            # 直接赋值不用计算
            new_data[k] = Decimal(v)
        return new_data

    @classmethod
    def format_event_data(cls, event_data: Dict[str, Decimal]) -> Dict[str, str]:
        """格式化事件数据"""
        copy_event_data = copy.deepcopy(event_data)
        for k, v in copy_event_data.items():
            if isinstance(v, Decimal):
                copy_event_data[k] = amount_to_str(v)
        return copy_event_data

    @classmethod
    def init_user_mission(
            cls,
            user_missions: list[UserMission]
    ) -> List[UserMissionMonitor]:
        """初始化用户任务"""
        from app.business.mission_center.monitor import UserMissionMonitorBiz
        now_ = now()
        # 获取用户任务ID
        if not user_missions:
            return []

        mission_ids = [i.mission_id for i in user_missions]
        mission_data_mapper = MissionCache.get_cache_data_by_ids(mission_ids)

        init_user_missions = []
        for user_mission in user_missions:
            deadline_days = int(mission_data_mapper[user_mission.mission_id]['deadline_days'])
            user_mission.used_at = now_
            user_mission.expired_at = now_ + timedelta(days=deadline_days)
            init_user_missions.append(user_mission)
        db.session.flush()
        user_mission_monitors = UserMissionMonitorBiz.bulk_create_monitors(init_user_missions)
        return user_mission_monitors

    @classmethod
    def update_user_mission(cls, user_mission_mapper: Dict[int, Dict[str, Decimal]]) -> bool:
        """更新用户任务"""
        current_time = now()
        biz_ids = set(user_mission_mapper.keys())
        query = UserMission.query.filter(
            UserMission.id.in_(biz_ids),
            UserMission.status == UserMission.Status.PENDING,
            UserMission.used_at > UserMission.MIN_UTC_DATETIME,
        ).with_for_update()
        mission_ids = [i.mission_id for i in query]
        mission_data_mapper = MissionCache.get_cache_data_by_ids(mission_ids)
        to_settle_user_missions = []
        for user_mission in query.yield_per(cls.UPDATE_LIMIT):
            updated_user_mission = cls._update_single_mission(
                user_mission,
                user_mission_mapper[user_mission.id],
                mission_data_mapper[user_mission.mission_id],
                current_time
            )
            if updated_user_mission.status == UserMission.Status.SETTLING:
                to_settle_user_missions.append(updated_user_mission)
        db.session.flush()
        return bool(to_settle_user_missions)

    @classmethod
    def _update_single_mission(
            cls,
            user_mission: UserMission,
            user_mission_data: Dict[str, Decimal],
            mission_data: Dict[str, Any],
            current_time: datetime
    ) -> UserMission:
        """更新单个用户任务"""
        new_event_data = cls.update_event_data(user_mission.event_data, user_mission_data)
        user_mission.event_data = cls.format_event_data(new_event_data)
        progress = MissionUtils.calculate_progress(mission_data, new_event_data, user_mission.mission_condition.name)
        user_mission.progress = progress
        if progress >= Decimal('100'):
            user_mission.completed_at = current_time
            user_mission.status = UserMission.Status.SETTLING
        if MissionValidator.check_mission_expired(user_mission, current_time):
            user_mission.status = UserMission.Status.EXPIRED
        user_mission.last_updated_at = current_time
        return user_mission

    @classmethod
    def _create_user_missions(
            cls,
            scene_type: SceneType,
            mission: Mission,
            users: List[User],
            status: UserMission.Status = UserMission.Status.PENDING
    ) -> List[UserMission]:
        """创建用户任务"""
        ums = []
        for user in users:
            um = UserMission(
                mission_id=mission.id,
                user_id=user.id,
                plan_id=mission.plan_id,
                scene_type=scene_type,
                mission_condition=mission.mission_condition,
                status=status,
                used_at=UserMission.MIN_UTC_DATETIME,  # 手动设置使用时间
                expired_at=UserMission.MAX_UTC_DATETIME,  # 手动设置过期时间
            )
            ums.append(um)
        return ums

    @classmethod
    def bulk_save_user_mission(
            cls,
            scene_type: SceneType,
            missions: List[Mission],
            to_add_users: List[User],
            failed_users: List[User]
    ) -> List[UserMission]:
        """批量保存用户任务"""
        user_missions = []
        for mission in missions:
            user_missions += cls._create_user_missions(scene_type, mission, to_add_users)
            user_missions += cls._create_user_missions(scene_type, mission, failed_users, UserMission.Status.FAILED)
        return user_missions

    @classmethod
    def _get_plan_missions(cls, plan_id: int) -> List[Mission]:
        """获取计划任务"""
        return Mission.query.filter(
            Mission.plan_id == plan_id
        ).with_entities(
            Mission.plan_id,
            Mission.id,
            Mission.deadline_days,
            Mission.mission_condition
        ).all()

    @classmethod
    def _filter_valid_users(
            cls,
            users: List[User],
            scene_type: SceneType,
            plan_id: int,
            scene_id: int

    ) -> Tuple[List[User], List[User], List[Dict[int, Any]]]:
        """筛选有效用户"""
        user_ids = {u.id for u in users}
        scene_handler = get_scene_handler(scene_type)
        device_risk_users_mapper = scene_handler.get_device_risk_users_mapper(plan_id, user_ids)
        # 增加二次检查排除用户，防止部分用户任务已经派发执行失败。(二次保证)
        excluded_users = scene_handler.get_excluded_users(user_ids, scene_id=scene_id)
        device_risk_users = set(device_risk_users_mapper.keys())
        add_users, failed_users = [], []
        for user in users:
            if user.id in excluded_users:
                continue
            if user.id in device_risk_users:
                failed_users.append(user)
                continue
            add_users.append(user)
        failed_group_data = scene_handler.format_failed_group_data(failed_users, device_risk_users_mapper)
        return add_users, failed_users, failed_group_data

    @classmethod
    def add_user_mission(
            cls,
            plan: MissionPlan,
            users: List[User]
    ) -> Tuple[List[UserMission], bool, List[Dict[int, Any]]]:
        """添加用户任务"""
        if not plan or plan.status != MissionPlan.Status.EFFECTIVE:
            return [], False, []

        missions = cls._get_plan_missions(plan.id)
        if not missions:
            return [], False, []

        add_users, failed_users, failed_users_data = cls._filter_valid_users(
            users, plan.scene_type, plan.id, plan.scene_id
        )
        quota_reached = False
        if plan.total > 0:
            add_users, quota_reached = MissionQuotaManager.handle_plan_quota(plan, add_users)

        user_missions = cls.bulk_save_user_mission(plan.scene_type, missions, add_users, failed_users)
        return user_missions, quota_reached, failed_users_data

    @classmethod
    def get_referrer_info_by_plan_ids(cls, plan_ids: List[int], user_id: int) -> Dict[int, Dict[str, str]]:
        """获取推荐人信息"""
        has_referrer_plan_ids = {
            mg.plan_id for mg in MissionPlanUserGroup.query.filter(
                MissionPlanUserGroup.plan_id.in_(plan_ids),
                MissionPlanUserGroup.logic_template == LogicTemplate.REFERER_ID_IN
            ).with_entities(
                MissionPlanUserGroup.plan_id
            ).all()
        }
        if not has_referrer_plan_ids:
            return {}
        referrer = ReferralHistory.query.filter(
            ReferralHistory.referree_id == user_id,
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).with_entities(
            ReferralHistory.referrer_id
        ).first()
        if not referrer:
            return {}
        info = MissionUtils.get_referrer_info(referrer.referrer_id)
        return {i: info for i in has_referrer_plan_ids}

    @classmethod
    def _format_equity_status(
            cls,
            equity_status: EquityCenterService.UserEquityStatus
    ) -> EquityCenterService.UserEquityStatus:
        """格式化权益状态"""
        # 6.11 任务中心 奖励发放失败，权益状态改为使用中 不给用户展示失败状态。
        if equity_status == EquityCenterService.UserEquityStatus.FAILED:
            return EquityCenterService.UserEquityStatus.FAILED
        return equity_status

    @classmethod
    def fmt_equity_data(
            cls,
            equity_cache_data: Dict[str, Any],
            equity_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """格式化权益数据"""
        if not equity_data:
            return equity_cache_data
        equity_status = cls._format_equity_status(equity_data['status'])
        equity_cache_data['status'] = equity_status.name
        return equity_cache_data

    @classmethod
    def get_user_missions(
            cls,
            user: Optional[User] = None,
            user_data: Optional[Dict[str, Any]] = None,
            status: Optional[UserMission.Status] = None
    ) -> List[Dict[str, Any]]:
        """获取用户任务

        Args:
            user: 已认证用户对象
            user_data: 未认证用户数据
            status: 任务状态过滤

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        if user:
            return AuthenticatedUserMissionRetriever.get_missions(user, status)
        return UnauthenticatedUserMissionRetriever.get_missions(user_data)

    @classmethod
    def get_user_mission_info(cls, user: Optional[User], id_: int) -> Dict[str, Any]:
        """获取用户任务信息"""
        if user:
            return cls.get_user_mission_by_id(user.id, id_)
        return MissionBiz.get_mission_info(id_)

    @classmethod
    def get_user_mission_by_id(cls, user_id: int, user_mission_id: int) -> Dict[str, Any]:
        """通过ID获取用户任务"""
        user_mission = UserMission.query.filter(
            UserMission.user_id == user_id,
            UserMission.id == user_mission_id
        ).first()
        if not user_mission:
            return {}
        mission_cache_data = MissionCache.get_data_by_id(user_mission.mission_id)
        equity_mapper_data = EquityCenterService.batch_query_user_eq_info(
            EquityCenterService.BizTypes.MISSION, {user_mission_id}
        )
        equity_data = cls.fmt_equity_data(mission_cache_data['reward'], equity_mapper_data.get(user_mission_id))
        user_mission_data = MissionDataFormatter.format_user_mission_data(mission_cache_data, user_mission, equity_data)
        if "referrer_info" in user_mission_data:
            user_mission_data.pop("referrer_info")
        return user_mission_data

    @classmethod
    def get_mission_referrer_info(cls, user_data: Dict[str, Any], group: MissionPlanUserGroup) -> Dict[str, str]:
        """获取任务推荐人信息"""
        if group.logic_template != LogicTemplate.REFERER_ID_IN or group.logic_template.name not in user_data:
            return {}
        referrer_user_id = user_data[group.logic_template.name]
        return MissionUtils.get_referrer_info(referrer_user_id)


class SceneHandler:
    """场景处理器"""
    scene_type: SceneType

    @classmethod
    def get_scene_mapper(cls, scene_type: SceneType) -> Dict[int, str]:
        """获取场景ID"""
        return {
            i.id: i.name for i in MissionScene.query.filter(
                MissionScene.scene_type == scene_type
            ).with_entities(
                MissionScene.id,
                MissionScene.name
            ).order_by(
                MissionScene.id.desc()
            ).all()
        }

    @classmethod
    def get_had_scene_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取已经完成场景的用户"""
        raise NotImplementedError

    @classmethod
    def get_excluded_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取排除的用户(新手用户增加了风控，羊毛党用户由风控逻辑处理)"""
        raise NotImplementedError

    @classmethod
    def get_device_risk_users_mapper(cls, plan_id: int, check_users: Set[int]) -> Dict[int, str]:
        """获取设备风险用户"""
        raise NotImplementedError

    @classmethod
    def get_plan_send_count_mapper(cls, plan_ids: List[int]) -> Dict[int, int]:
        """获取计划发送计数映射"""
        return {
            pid: count for pid, count in UserMission.query.filter(
                UserMission.plan_id.in_(plan_ids)
            ).group_by(
                UserMission.plan_id
            ).with_entities(
                UserMission.plan_id,
                func.count(UserMission.id)
            ).all()
        }

    @classmethod
    def format_failed_group_data(cls, failed_users: List[User], device_risk_users_mapper: Dict[int, str]) -> list:
        """格式化失败用户数据"""
        failed_group_data = []
        for user in failed_users:
            failed_group_data.append({
                "user_id": user.id,
                "fail_reason": UserMission.FailReason.RISK_USER.name,
                "fail_data": {
                    "device_id": device_risk_users_mapper.get(user.id, "")
                }
            })
        return failed_group_data


class NewbieSceneHandler(SceneHandler):
    """新手场景处理器"""
    scene_type = SceneType.NEWBIE
    
    @classmethod
    def get_excluded_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取排除的用户"""
        had_scene_users = cls.get_had_scene_users(check_users, scene_id)
        return had_scene_users

    @classmethod
    def get_had_scene_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取已经完成场景的用户"""
        users = set()
        for batch_ids in batch_iter(check_users, 10000):
            user_ids = {
                um.user_id for um in
                UserMission.query.filter(
                    UserMission.scene_type == cls.scene_type,
                    UserMission.user_id.in_(batch_ids)
                ).with_entities(
                    UserMission.user_id
                ).all()
            }
            users |= user_ids
        return users

    @classmethod
    def get_device_risk_users_mapper(cls, plan_id: int, check_users: Set[int]) -> Dict[int, str]:
        """获取设备风险用户(统一由新手任务风控来处理)"""
        return {}


class RoutineSceneHandler(SceneHandler):
    """常规场景处理器"""

    scene_type = SceneType.ROUTINE
    
    @classmethod
    def get_excluded_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取排除的用户"""
        abnormal_users = UserRepository.get_abnormal_users()
        had_scene_users = cls.get_had_scene_users(check_users, scene_id)
        return (abnormal_users & check_users) | had_scene_users

    @classmethod
    def get_had_scene_users(cls, check_users: Set[int], scene_id: int = None) -> Set[int]:
        """获取已经完成场景的用户"""
        from app.business.mission_center.plan import MissionPlanBiz
        # 老用户必须要有scene_id
        if not scene_id:
            raise ValueError('RoutineSceneHandler scene_id is required')
        plan_ids = MissionPlanBiz.query_scene_plan_ids(cls.scene_type, scene_id)
        users = set()
        for batch_ids in batch_iter(check_users, 10000):
            user_ids = {
                um.user_id for um in
                UserMission.query.filter(
                    UserMission.scene_type == cls.scene_type,
                    UserMission.user_id.in_(batch_ids),
                    UserMission.plan_id.in_(plan_ids)
                ).with_entities(
                    UserMission.user_id
                ).all()
            }
            users |= user_ids
        return users

    @classmethod
    def get_device_risk_users_mapper(cls, plan_id: int, check_users: Set[int]) -> Dict[int, str]:
        """获取设备风险用户"""
        from app.caches.mission import MissionDeviceSnapshotCache
        last_user_device_mapper = MissionDeviceSnapshotCache.get_snapshot(plan_id)
        device_user_mapper = defaultdict(list)
        for user_id, device_id in last_user_device_mapper.items():
            if not device_id:
                continue
            device_user_mapper[device_id].append(user_id)
        return {u: d for d, users in device_user_mapper.items() if len(users) > 1 for u in users if u in check_users}


def get_scene_handler(scene_type: SceneType) -> 'SceneHandler':
    """获取场景处理器"""
    if scene_type == SceneType.NEWBIE:
        return NewbieSceneHandler
    elif scene_type == SceneType.ROUTINE:
        return RoutineSceneHandler
    raise ValueError(f"Invalid scene type: {scene_type}")
