# -*- coding: utf-8 -*-

import binascii
import json
import os
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from math import ceil
from typing import Optional, Set

from flask import g, current_app
from flask_babel import gettext as _, gettext
from flask_api.exceptions import AuthenticationFailed
from sqlalchemy import func
from app.api.common.decorators import verify_user_permission
from app.business import send_red_packet_registration_email, BusinessSettings
from app.exceptions.denied import OperationDenied
from app.models import User, Message, RedPacketHistory, RedPacket, RedPacketReturnHistory, \
    CBoxCode, CBoxTheme, LoginRelationHistory
from app.models import db
from app.utils import amount_to_str, now, \
    datetime_to_str
from app.utils import hide_email
from .constants import COIN_PLACES, RED_PACKET_SEND_BUSINESS, RED_PACKET_GRAB_BUSINESS, GRAB_EXPIRED_TIME
from .encryption import A<PERSON><PERSON>enerator, RsaGenerator, AesTool
from .. import ServerClient, User<PERSON>re<PERSON>s, CacheLock, LockKeys
from ..clients.biz_monitor import biz_monitor
from ...caches.red_packet import <PERSON><PERSON><PERSON>et<PERSON>rab<PERSON>ache, CBoxUnregisterEmailInfoCache
from ...caches.flow_control import CBoxCodeFailureCache
from ...common import BalanceBusiness, MessageTitle, MessageContent, MessageWebLink, \
    PrecisionEnum, C_BOX_CODE_FAILURE_TIMES_LIMIT, C_BOX_CODE_FAILURE_INTERVAL, IncreaseEvent
from ...exceptions import (
    InternalServerError,
    InvalidArgument, CBoxHasExpired, CBoxUserHasReceived, CBoxOnlyNewUser,
    CBoxHasFinished,
    CBoxStatusError, CBoxNotFoundOrNotExists, FrequencyExceeded, InvalidCBoxCode, CBoxCheatedUser
)


def send_red_packet_grabbed_notice(history: RedPacketHistory):
    """ 发送抢到红包的通知（红包到账站内信） """
    user_id = history.user_id
    pref = UserPreferences(user_id)
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.C_BOX_TO_ACCOUNT.name,
            content=MessageContent.C_BOX_TO_ACCOUNT.name,
            params=json.dumps(
                dict(
                    amount=amount_to_str(history.amount, PrecisionEnum.COIN_PLACES),
                    coin_type=history.asset,
                    time=datetime_to_str(history.grab_at, pref.timezone_offset),
                )
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.TEXT,
            channel=Message.Channel.ACTIVITY,
        )
    )


def send_red_packet_refund_notice(red_packet: RedPacket, refund_history: RedPacketReturnHistory):
    """ 发送红包退还的通知（红包退还站内信） """
    user_id = red_packet.user_id
    pref = UserPreferences(user_id)
    if refund_history.reason == RedPacketReturnHistory.Reason.EXPIRE:
        content = MessageContent.C_BOX_EXPIRE_REFUND_NOTICE_NEW.name
    else:
        content = MessageContent.C_BOX_NOT_REGISTER_REFUND_NOTICE.name
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.C_BOX_REFUND_NOTICE.name,
            content=content,
            params=json.dumps(
                dict(
                    amount=amount_to_str(refund_history.return_amount,
                                         PrecisionEnum.COIN_PLACES),
                    coin_type=refund_history.asset,
                    time=datetime_to_str(red_packet.created_at, pref.timezone_offset),
                    valid_days=red_packet.valid_days
                )
            ),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.SPOT_ASSET_HISTORY_PAGE.value,
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.ACTIVITY,
        )
    )


def get_red_packet(encrypt_packet_id, status_tuple=(RedPacket.Status.PASSED,)
                   ) -> RedPacket:
    gen = AESGenerator(RED_PACKET_SEND_BUSINESS)
    if red_packet_id := gen.decrypt(str(encrypt_packet_id)):
        if red_packet := RedPacket.query.get(red_packet_id):
            if red_packet.status in status_tuple:
                return red_packet
            raise CBoxStatusError
    raise CBoxNotFoundOrNotExists


def get_red_packet_by_code(code_id, status_tuple=(RedPacket.Status.PASSED,)) -> RedPacket:
    red_packet = RedPacket.query.filter(RedPacket.code_id == code_id).first()
    if not red_packet:
        raise CBoxNotFoundOrNotExists
    if red_packet.status in status_tuple:
        return red_packet
    raise CBoxStatusError


def get_red_packet_info(encrypt_packet_id, page: int, limit: int):
    red_packet = get_red_packet(
        encrypt_packet_id,
        status_tuple=(RedPacket.Status.CREATED,
                      RedPacket.Status.CANCELLED,
                      RedPacket.Status.RISK,
                      RedPacket.Status.PASSED,
                      RedPacket.Status.EXPIRED,
                      RedPacket.Status.RISK_CANCELLED,
                      RedPacket.Status.FINISHED))
    return_amount = get_return_amount(red_packet)
    theme = get_c_box_theme(red_packet)
    total_count = red_packet.count
    total_amount = red_packet.total_amount
    c_box_info = {
        'total_amount': amount_to_str(total_amount, COIN_PLACES),
        'coin_type': red_packet.asset,
        'count': total_count,
        'email': hide_email(red_packet.user_name),
        'greeting': red_packet.greet,
        'theme': CBoxTheme.img_src(theme.cover_file_key),
        'receive_theme': CBoxTheme.img_src(theme.receive_file_key
                                           ) if theme.receive_file_key else CBoxTheme.img_src(theme.cover_file_key),
        'theme_style': theme.style.name,
        "status": red_packet.status.value,
        'return_amount': amount_to_str(return_amount, COIN_PLACES),
        'receive_type': red_packet.receive_type.value,
        'code': red_packet.code,
        'expired_time': red_packet.expired_time
    }
    received_query = RedPacketHistory.query.filter(
        RedPacketHistory.red_packet_id == red_packet.id,
        RedPacketHistory.grab_at.isnot(None),
    ).order_by(RedPacketHistory.grab_at.desc())
    ret = received_query.paginate(page, limit, error_out=False)
    items = [dict(
        email=hide_email(v.email),
        receive_time=v.grab_time,
        amount=amount_to_str(v.amount, COIN_PLACES),
        coin_type=v.asset,
    ) for v in ret.items]
    if (red_packet.status in (RedPacket.Status.PASSED, RedPacket.Status.FINISHED) and
            red_packet.expired_at > now()):
        # 进行中和已抢完的红包可能因为高并发导致数据落库不及时，统计数据取从缓存中取
        receive_total_count, receive_total_amount = receive_info_from_cache(red_packet.id, total_count, total_amount)
    else:
        receive_total_amount = receive_info_from_db(received_query)
        receive_total_count = ret.total
    received_info = dict(
        items=items,
        total=receive_total_count,
        total_amount=receive_total_amount,
        has_next=ret.has_next
    )
    return dict(c_box_info=c_box_info,
                received_info=received_info,
                )


def receive_info_from_cache(red_packet_id, total_count, total_amount):
    remain_count, remain_amount = get_remain_c_box_info(red_packet_id)
    return total_count-remain_count, total_amount-remain_amount


def get_remain_c_box_info(red_packet_id):
    cache = RedPacketGrabCache(red_packet_id)
    ret = cache.read()
    remain_count = len(ret)
    if not remain_count:
        return remain_count, Decimal(0)
    remain_amount = sum(map(lambda x: Decimal(json.loads(x)['amount']), ret))
    return remain_count, remain_amount


def receive_info_from_db(received_query):
    return Decimal(sum([i.amount for i in received_query]))


def get_c_box_theme(red_packet):
    if red_packet.theme_id:
        theme = CBoxTheme.query.get(red_packet.theme_id)
    else:
        theme = CBoxTheme.query.filter(
            CBoxTheme.status == CBoxTheme.Status.VALID).first()  # 说明是历史数据，返回一条主题保证接口不报错即可
    return theme


def get_return_amount(red_packet: RedPacket):
    if not red_packet.is_return:
        return Decimal()
    return_rec = RedPacketReturnHistory.query.filter(
        RedPacketReturnHistory.red_packet_id == red_packet.id,
        RedPacketReturnHistory.status == RedPacketReturnHistory.Status.FINISHED
    ).with_entities(
        func.sum(RedPacketReturnHistory.return_amount).label('return_amount')
    ).first()
    return_amount = return_rec.return_amount or Decimal()
    return return_amount


def get_user_receive_info(user_id, coin_type, start_time, end_time, page, limit):
    query = RedPacketHistory.query.filter(
        RedPacketHistory.user_id == user_id,
        RedPacketHistory.status == RedPacketHistory.Status.FINISHED
    ).order_by(RedPacketHistory.grab_at.desc())
    if coin_type:
        query = query.filter(
            RedPacketHistory.asset == coin_type
        )
    if start_time:
        query = query.filter(RedPacketHistory.grab_at >= start_time)
    if end_time:
        query = query.filter(RedPacketHistory.grab_at <= end_time)

    pagination = query.paginate(page, limit, error_out=False)

    page_data = [{
        "receive_time": v.grab_time,
        "amount": amount_to_str(v.amount, COIN_PLACES),
        "coin_type": v.asset,
        "red_packet_id": v.red_packet_id,
    } for v in pagination.items]
    red_packet_info = RedPacket.query.filter(
        RedPacket.id.in_([v.red_packet_id for v in pagination.items])
    ).with_entities(
        RedPacket.id,
        RedPacket.receive_type,
        RedPacket.user_name
    )
    red_packet_dict = {v.id: {
        "email": v.user_name, 'receive_type': v.receive_type.value
    } for v in red_packet_info}
    for item in page_data:
        red_packet_id = item.pop('red_packet_id')
        red_packet_info = red_packet_dict[red_packet_id]
        item["receive_type"] = red_packet_info['receive_type']
        item['sender_email'] = hide_email(red_packet_info['email'])
    return dict(
        page=page,
        limit=limit,
        total=pagination.total,
        has_next=pagination.has_next,
        has_prev=pagination.has_prev,
        total_pages=pagination.pages,
        data=page_data
    )


def get_grab_pub_sign_data(encrypt_packet_id):
    red_packet = get_red_packet(
        encrypt_packet_id,
        status_tuple=(RedPacket.Status.PASSED, RedPacket.Status.FINISHED, RedPacket.Status.EXPIRED))
    tool = RsaGenerator(f"{RED_PACKET_GRAB_BUSINESS}:{red_packet.id}")
    return dict(sign=tool.get_pubkey_hex())


def verify_grab_data(encrypt_packet_id, sign, body):
    red_packet = get_red_packet(encrypt_packet_id,
                                status_tuple=(
                                    RedPacket.Status.PASSED, RedPacket.Status.FINISHED,
                                    RedPacket.Status.EXPIRED))

    data = decrypt_data(encrypt_packet_id, sign, body)
    email = data['email']
    if history := RedPacketHistory.query.filter(
        RedPacketHistory.red_packet_id == red_packet.id,
        RedPacketHistory.email == email
    ).first():
        receive_data = {'amount': history.amount, 'coin_type': history.asset, 'email': email}
        raise CBoxUserHasReceived(receive_data)


def encrypt_data(email, encrypt_packet_id):
    red_packet = get_red_packet(encrypt_packet_id,
                                status_tuple=(
                                    RedPacket.Status.PASSED, RedPacket.Status.FINISHED,
                                    RedPacket.Status.EXPIRED)
    )
    tool = RsaGenerator(f"{RED_PACKET_GRAB_BUSINESS}:{red_packet.id}")
    aes = AESGenerator(os.urandom(16))
    sign = tool.encrypt_base64(aes.key)
    dict_data = dict(email=email, encrypt_packet_id=encrypt_packet_id)
    sign_body = aes.encrypt_base64(json.dumps(dict_data, sort_keys=True))
    return dict(sign=sign, body=sign_body)


def decrypt_data(encrypt_packet_id, sign, body):
    red_packet = get_red_packet(encrypt_packet_id,
                                status_tuple=(
                                    RedPacket.Status.PASSED, RedPacket.Status.FINISHED,
                                    RedPacket.Status.EXPIRED)
                                )
    tool = RsaGenerator(f"{RED_PACKET_GRAB_BUSINESS}:{red_packet.id}")
    try:
        decrypted_sign = tool.decrypt(sign)
    except ValueError:
        raise InvalidArgument
    aes = binascii.unhexlify(decrypted_sign)
    aes_tool = AesTool(aes)
    body = aes_tool.decrypt(body)
    try:
        data = json.loads(body)
    except ValueError:
        raise InvalidArgument
    if data['id'] != encrypt_packet_id:
        raise CBoxUserHasReceived
    return data


class ReceiveCBoxMixin:

    @classmethod
    def verify_red_packet(cls, red_packet: RedPacket):
        if red_packet.status == RedPacket.Status.EXPIRED:
            raise CBoxHasExpired
        if red_packet.status == RedPacket.Status.FINISHED:
            raise CBoxHasFinished

    @classmethod
    def verify_has_expired(cls, red_packet: RedPacket):
        if red_packet.expired_at < now():
            raise CBoxHasExpired

    @classmethod
    def verify_has_received(cls, red_packet: RedPacket, email):
        history = RedPacketHistory.query.filter(
                RedPacketHistory.red_packet_id == red_packet.id,
                RedPacketHistory.email == email
        ).first()
        if history:
            data = {'amount': history.amount, 'coin_type': history.asset, 'email': email}
            raise CBoxUserHasReceived(data)

    @classmethod
    def update_red_packet_history(cls, history_id, amount, email):
        history: RedPacketHistory = RedPacketHistory.query.filter(
            RedPacketHistory.id == history_id,
            RedPacketHistory.status == RedPacketHistory.Status.CREATED,
            RedPacketHistory.amount == Decimal(amount)
        ).first()
        if not history:
            raise CBoxHasFinished
        history.email = email
        history.grab_at = now()
        history.status = RedPacketHistory.Status.PENDING
        db.session.commit()
        return history

    @classmethod
    def verify_user_cheated(cls, red_packet, user):
        limit_users = BusinessSettings.c_box_limit_users
        send_user = User.query.get(red_packet.user_id)
        email = send_user.email
        if email in limit_users:
            red_packet_users = cls.get_other_receive_red_packet_users(red_packet.id, user.id)
            if not red_packet_users:
                return
            same_ip_users = cls.get_same_ip_users(red_packet_users, user.registration_ip)
            if not same_ip_users:
                return
            same_device_id_users = cls.get_same_device_users(red_packet_users, user.id)
            if not same_device_id_users:
                return
            if same_ip_users & same_device_id_users:
                raise CBoxCheatedUser

    @classmethod
    def get_other_receive_red_packet_users(cls, red_packet_id, user_id):
        histories = RedPacketHistory.query.filter(
            RedPacketHistory.red_packet_id == red_packet_id,
            RedPacketHistory.user_id.isnot(None),
            RedPacketHistory.user_id != user_id
        ).with_entities(
            RedPacketHistory.user_id
        ).all()
        return {i.user_id for i in histories}

    @classmethod
    def get_same_ip_users(cls, user_ids: Set, ip: str):
        records = User.query.filter(
            User.id.in_(user_ids),
            User.registration_ip == ip
        ).with_entities(
            User.id
        )
        return {i.id for i in records}

    @classmethod
    def get_same_device_users(cls, red_packet_users: Set, user_id: int):
        record = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id == user_id
        ).with_entities(
            LoginRelationHistory.device_id
        ).order_by(LoginRelationHistory.id.desc()).first()
        if not record:
            return set()
        records = LoginRelationHistory.query.filter(
            LoginRelationHistory.user_id.in_(red_packet_users),
            LoginRelationHistory.device_id == record.device_id
        ).with_entities(
            LoginRelationHistory.user_id
        ).all()
        return {i.user_id for i in records}

    @classmethod
    def process_register_balance(cls, history: RedPacketHistory, user_id: int):
        balance_server = ServerClient()
        try:
            balance_server.add_user_balance(
                user_id,
                history.asset,
                str(history.amount),
                BalanceBusiness.RED_PACKET_GRABBING.value,
                history.id,
                {'remark': 'grab red packet'}
            )
            history.user_id = user_id
            history.status = RedPacketHistory.Status.FINISHED
            db.session.commit()
            send_red_packet_grabbed_notice(history)
        # noinspection PyBroadException
        except Exception:
            error_msg = f'receive c-box {history.red_packet_id} ' \
                        f'receive id {history.id} user {user_id} ' \
                        f'amount {history.amount} coin {history.asset} ' \
                        f'deducted error'
            current_app.logger.error(error_msg)
            history.status = RedPacketHistory.Status.FAILED
            db.session.commit()
            raise InternalServerError


class ReceiveEmailCBoxManager(ReceiveCBoxMixin):

    @classmethod
    def decrypt_email(cls, encrypt_red_packet_id, sign, body):
        # check c-box status, then decrypt data
        data = decrypt_data(encrypt_red_packet_id, sign, body)
        return data['email']

    @classmethod
    def receive(cls, encrypt_red_packet_id, sign, body):
        email = cls.decrypt_email(encrypt_red_packet_id, sign, body)
        with CacheLock(LockKeys.grab_red_packet(email), wait=False):
            db.session.rollback()
            red_packet, is_registered, user = cls.verify_and_get_red_packet(encrypt_red_packet_id, email)
            cache = RedPacketGrabCache(red_packet.id)
            if not (data := cache.lpop()):
                raise CBoxHasFinished
            if cache.llen() == 0:
                # 如果c-box领完则修改c-box状态
                red_packet.status = RedPacket.Status.FINISHED
                db.session.commit()
            amount_data = json.loads(data)
            history = cls.update_red_packet_history(
                amount_data['history_id'], amount_data['amount'], email)
            if not is_registered:
                from ...api.common import get_request_host_url
                history.status = RedPacketHistory.Status.UNREGISTER
                db.session.commit()
                lang = g.lang
                host_url = get_request_host_url()
                send_red_packet_registration_email.delay(
                    lang, red_packet.user_id, history.id, email, host_url)
                CBoxUnregisterEmailInfoCache(email).save_info(lang=lang, host_url=host_url, ttl=GRAB_EXPIRED_TIME)
            else:
                cls.process_register_balance(history, user.id)
            biz_monitor.increase_counter(IncreaseEvent.RECEIVE_C_BOX_COUNT, with_source=True)
            return dict(
                status=history.status.value,
                amount=amount_to_str(history.amount, COIN_PLACES),
                coin_type=history.asset,
                email=email,
                msg=_('领取成功')
            )

    @classmethod
    def check_user_registered(cls, email) -> [bool, Optional[User]]:
        user = User.query.filter(
            User.email == email,
            User.user_type != User.UserType.SUB_ACCOUNT
        ).first()
        if user:
            return True, user
        else:
            return False, None

    @classmethod
    def verify_and_get_red_packet(cls, encrypt_red_packet_id, email):
        red_packet = get_red_packet(
            encrypt_red_packet_id,
            status_tuple=(RedPacket.Status.PASSED,
                          RedPacket.Status.EXPIRED,
                          RedPacket.Status.FINISHED))
        cls.verify_has_received(red_packet, email)
        is_registered, user = cls.verify_user(red_packet, email)
        cls.verify_red_packet(red_packet)
        cls.verify_has_expired(red_packet)
        if user:
            cls.verify_user_cheated(red_packet, user)
        return red_packet, is_registered, user

    @classmethod
    def verify_user(cls, red_packet, email):
        from ...api.common.request import require_user_request_permission, RequestPermissionCheck

        is_registered, user = cls.check_user_registered(email)
        if user:
            try:
                verify_user_permission(user.id)
            except AuthenticationFailed:
                raise OperationDenied
            require_user_request_permission(user, [RequestPermissionCheck.USER_NOT_ONLY_WITHDRAWAL, RequestPermissionCheck.KYC])
            if red_packet.only_new_user:
                raise CBoxOnlyNewUser
        return is_registered, user


class ReceiveCodeCBoxManager(ReceiveCBoxMixin):

    @classmethod
    def receive(cls, user, code):
        with CacheLock(
                LockKeys.grab_red_packet(user.email),
                wait=False
        ):
            db.session.rollback()
            red_packet = cls.verify_and_get_red_packet(user, code)
            cache = RedPacketGrabCache(red_packet.id)
            if not (data := cache.lpop()):
                raise CBoxHasFinished
            if cache.llen() == 0:
                # 如果c-box领完则修改c-box状态
                red_packet.status = RedPacket.Status.FINISHED
                db.session.commit()
            amount_data = json.loads(data)
            history = cls.update_red_packet_history(
                amount_data['history_id'], amount_data['amount'], user.email)
            cls.process_register_balance(history, user.id)
            biz_monitor.increase_counter(IncreaseEvent.RECEIVE_C_BOX_COUNT, with_source=True)
            return dict(
                status=history.status.value,
                amount=amount_to_str(history.amount, COIN_PLACES),
                coin_type=history.asset,
                email=user.email,
                msg=_('领取成功')
            )

    @classmethod
    def verify_and_get_red_packet(cls, user, code):
        c_code_id = cls.verify_code(code, user.id)
        red_packet = get_red_packet_by_code(c_code_id,
                                            status_tuple=(RedPacket.Status.PASSED,
                                                          RedPacket.Status.EXPIRED,
                                                          RedPacket.Status.FINISHED))
        cls.verify_has_received(red_packet, user.email)
        cls.verify_red_packet(red_packet)
        cls.verify_has_expired(red_packet)
        cls.verify_user_cheated(red_packet, user)
        return red_packet

    @classmethod
    def verify_code(cls, code, user_id):
        cls.check_has_locked(user_id)
        c_code = CBoxCode.query.filter(CBoxCode.code == code.upper()).first()
        if not c_code or c_code.status != CBoxCode.Status.USED:
            cls.handle_code_failure(user_id)
        return c_code.id

    @classmethod
    def check_has_locked(cls, user_id):
        failure_cache = CBoxCodeFailureCache(user_id, ttl=C_BOX_CODE_FAILURE_INTERVAL)
        has_failed_times = failure_cache.count()
        if has_failed_times >= C_BOX_CODE_FAILURE_TIMES_LIMIT:
            retry_in = int(ceil(failure_cache.ttl() / (60 * 60)))
            raise FrequencyExceeded(
                message=gettext(
                    '口令C-Box领取功能已锁定，请%(hours)s小时后再尝试。',
                    hours=max(retry_in, 1)))

    @classmethod
    def handle_code_failure(cls, user_id):
        failure_cache = CBoxCodeFailureCache(user_id, ttl=C_BOX_CODE_FAILURE_INTERVAL)
        has_failed_times = failure_cache.count()
        curr_remain_times = C_BOX_CODE_FAILURE_TIMES_LIMIT - has_failed_times
        retry_in = int(ceil(failure_cache.ttl() / (60 * 60)))
        if curr_remain_times > 1:
            failure_cache.add_value()
            raise InvalidCBoxCode(
                fail_times=has_failed_times + 1, remain_times=curr_remain_times - 1,
                failure_ttl_hour=int(C_BOX_CODE_FAILURE_INTERVAL / 60 / 60))
        elif curr_remain_times == 1:   # 最后一次输入code的机会也输错了
            failure_cache.add_value()
            raise FrequencyExceeded(
                message=gettext(
                    '口令C-Box领取功能已锁定，请%(hours)s小时后再尝试。',
                    hours=max(retry_in, 1)))
        else:
            raise FrequencyExceeded(
                message=gettext(
                    '口令C-Box领取功能已锁定，请%(hours)s小时后再尝试。',
                    hours=max(retry_in, 1)))

    @classmethod
    def verify_user(cls, user):
        from ...api.common.request import require_user_request_permission, RequestPermissionCheck
        require_user_request_permission(user, [
            RequestPermissionCheck.USER_NOT_ONLY_WITHDRAWAL, RequestPermissionCheck.KYC
        ])

    @classmethod
    def get_red_packet_status(cls, user: User, code: str):
        c_code_id = cls.verify_code(code, user.id)
        red_packet = RedPacket.query.filter(RedPacket.code_id == c_code_id).first()
        if not red_packet:
            raise CBoxNotFoundOrNotExists
        elif red_packet.status == RedPacket.Status.EXPIRED:
            raise CBoxHasExpired
        elif red_packet.status in (RedPacket.Status.PASSED, RedPacket.Status.FINISHED):
            return dict(
                status=red_packet.status.value,
                c_box_id=AESGenerator(
                    RED_PACKET_SEND_BUSINESS).encrypt(str(red_packet.id))
            )
        else:
            raise CBoxNotFoundOrNotExists
