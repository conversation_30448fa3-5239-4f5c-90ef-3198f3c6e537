# -*- coding: utf-8 -*-
from datetime import datetime
from decimal import Decimal

from flask import current_app

from . import QueryBusinessFlag, ignore_duplicate_error, CacheLock, LockKeys, ServerResponseCode
from .clients import ServerClient
from ..assets import get_asset
from ..common import BalanceBusiness
from ..exceptions import InvalidArgument, OperationNotAllowed, InsufficientAvailableBalance, \
    DuplicateLockBalanceUpdate
from ..models import User, LockedAssetBalance, db
from ..utils import amount_to_str, validate_email, now


def get_user_id_from_email(email_userid):
    user = None
    if isinstance(email_userid, int) or email_userid.isdigit():
        user = User.query.filter(User.id == email_userid).first()
    elif validate_email(email_userid):
        user = User.query.filter(User.email == email_userid).first()
    if not user:
        raise InvalidArgument(message=f'{email_userid} 不是CoinEx用户')
    return user.id


def get_user_balance(user_id, asset):
    """
    返回用户指定币种的可用和冻结数量
    """
    client = ServerClient()
    data = client.get_user_balances(user_id, asset)
    return Decimal(data[asset]['available']), Decimal(data[asset]['frozen'])


class LockAssetHelper:
    model = LockedAssetBalance
    LOCKABLE_STATUSES = [model.Status.CREATED, model.Status.LOCK_FAIL]
    UNLOCKABLE_STATUSES = [model.Status.LOCKED, model.Status.UNLOCK_FAIL]
    INFINITE_DATETIME = datetime(9999, 12, 31)

    @classmethod
    def _get_or_create_row(
            cls,
            business: model.Business,
            business_id: int,
            user_id: int,
            asset: str,
            amount: Decimal,
            lock_business: BalanceBusiness,
            lock_type: model.OpType,
            *,
            created_by: int = None,
            unlocked_at: datetime = None,
            remark: str = None,
            retry_type: model.RetryType = model.RetryType.NO_RETRY,
            commit: bool = True,
    ) -> model:
        def _check_row_status(r: cls.model):
            if r.status not in cls.LOCKABLE_STATUSES:
                raise DuplicateLockBalanceUpdate

        row = cls.get_uni_row(business, business_id, user_id)
        if row:
            _check_row_status(row)
            return row

        with CacheLock(LockKeys.create_locked_asset_balance(business.name, business_id, user_id)):
            row = cls.get_uni_row(business, business_id, user_id)
            if row:
                _check_row_status(row)
                return row

            row = LockedAssetBalance(
                business=business,
                business_id=business_id,
                user_id=user_id,
                asset=asset,
                amount=amount,
                status=cls.model.Status.CREATED,
                created_by=created_by or user_id,
                locked_at=now(),
                unlocked_at=unlocked_at,
                remark=remark,
                lock_type=lock_type,
                lock_business=lock_business,
                retry_type=retry_type,
            )
            db.session.add(row)
            if business == cls.model.Business.ADMIN and business_id == 0:
                db.session.flush()
                row.business_id = row.id
            if commit:
                db.session.commit()
            return row

    @classmethod
    def _get_lock_business(cls, business: model.Business, op_type: model.OpType):
        return cls.model.BUSINESS_MAPPING[op_type][business]

    @classmethod
    def lock(
            cls, business: model.Business, business_id: int, user_id: int, asset: str, amount: Decimal,
            *,
            retry_type: model.RetryType = model.RetryType.NO_RETRY,
            created_by: int = None,
            remark: str = None,
            unlocked_at: datetime = None,
            callback: bool = False,
    ):
        """
        may raise
            * InsufficientAvailableBalance before create row
            * DuplicateLockBalanceUpdate before create row
            * ServerClient.BadResponse after create row
        """
        cls._check_lock_amount(user_id, asset, amount)
        lock_type = cls.model.OpType.LOCK
        lock_business = cls._get_lock_business(business, lock_type)
        row = cls._get_or_create_row(
            business=business,
            business_id=business_id,
            user_id=user_id,
            asset=asset,
            amount=amount,
            lock_type=lock_type,
            lock_business=lock_business,
            retry_type=retry_type,
            created_by=created_by,
            remark=remark,
            unlocked_at=unlocked_at,
        )
        cls._lock(row, callback)

    @classmethod
    def _check_lock_amount(cls, user_id: int, asset: str, amount: Decimal):
        cls._check_asset_and_amount(asset, amount)
        available, _ = get_user_balance(user_id, asset)
        if available < amount:
            raise InsufficientAvailableBalance

    @classmethod
    def _check_asset_and_amount(cls, asset: str, amount: Decimal):
        amount = Decimal(amount)
        if amount <= Decimal():
            raise InvalidArgument
        get_asset(asset)  # check asset

    @classmethod
    def add_and_lock(
            cls, business: model.Business, business_id: int, user_id: int, asset: str, amount: Decimal,
            *,
            retry_type: model.RetryType = model.RetryType.NO_RETRY,
            created_by: int = None,
            remark: str = None,
            unlocked_at: datetime = None,
            callback: bool = False,
    ):
        cls._check_asset_and_amount(asset, amount)
        lock_type = cls.model.OpType.ADD_AND_LOCK
        lock_business = cls._get_lock_business(business, lock_type)
        row = cls._get_or_create_row(
            business=business,
            business_id=business_id,
            user_id=user_id,
            asset=asset,
            amount=amount,
            lock_type=lock_type,
            lock_business=lock_business,
            retry_type=retry_type,
            created_by=created_by,
            remark=remark,
            unlocked_at=unlocked_at,
        )
        cls._lock(row, callback)

    @classmethod
    def _lock(cls, row: model, callback: bool):
        if row.status not in cls.LOCKABLE_STATUSES:
            return
        with CacheLock(LockKeys.locked_asset_balance(row.id)):
            db.session.rollback()
            row: cls.model = cls.model.query.get(row.id)
            if row.status not in cls.LOCKABLE_STATUSES:
                return

            try:
                # 注意 _check_already_lock 会根据步骤获取不同的 lock_type
                is_locked, new_lock_type = cls._check_already_lock(row)
                if is_locked:
                    row.locked_at = now()
                    row.status = cls.model.Status.LOCKED
                    db.session.commit()
                    return

                with ignore_duplicate_error():
                    cls._do_asset_business(row, new_lock_type)
                row.locked_at = now()
                row.status = cls.model.Status.LOCKED
                db.session.commit()
            except Exception as e:
                current_app.logger.error('LockAssetHelper _lock failed, e', e)
                if row.retry_type == cls.model.RetryType.NO_RETRY and e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                    row.status = cls.model.Status.LOCK_FAIL
                    db.session.commit()
                raise e
            finally:
                if callback:
                    cls.callback_business(row)

    @classmethod
    def unlock(
            cls, business: model.Business, business_id: int, user_id: int,
            retry_type: model.RetryType = None,
            remark: str = None,
            callback: bool = False,
    ):
        def _check_status(r: cls.model):
            if r.status == cls.model.Status.UNLOCKED:
                raise DuplicateLockBalanceUpdate
            if r.status not in cls.UNLOCKABLE_STATUSES:
                raise OperationNotAllowed(
                    message=f'LockAssetHelper can not unlock for invalid status {r.status.value}')

        row = cls.get_uni_row(business, business_id, user_id)
        _check_status(row)

        unlock_type = cls.model.OpType.UNLOCK
        unlock_business = cls._get_lock_business(business, unlock_type)
        with CacheLock(LockKeys.locked_asset_balance(row.id)):
            db.session.rollback()
            row = cls.get_uni_row(business, business_id, user_id)
            _check_status(row)

            row.unlock_type = unlock_type
            row.unlock_business = unlock_business
            if retry_type:
                row.retry_type = retry_type
            if remark:
                row.remark = remark
            db.session.commit()
            cls._unlock(row, callback)
            return row

    @classmethod
    def unlock_and_sub(
            cls, business: model.Business, business_id: int, user_id: int,
            sub_amount: Decimal = None,
            retry_type: model.RetryType = None,
            remark: str = None,
            callback: bool = False,
    ):
        """
        !!!
        如果 lock 的 lock_type == add_and_lock，且 unlock 的 unlock_type == unlock_and_sub 时，
        lock 的 balance_business 不能等于 unlock 的 balance_business，
        虽然 asset.lock 有 lock_flag 能区分业务，
        但是因为 add_and_lock 和  unlock_and_sub 都会调用 server 的 asset.update ，
        如果 balance_business 相同，则无法判断是否已经执行过。
        """
        def _check_status(r: cls.model):
            if r.status == cls.model.Status.UNLOCKED:
                raise DuplicateLockBalanceUpdate
            if r.status not in cls.UNLOCKABLE_STATUSES:
                raise OperationNotAllowed(
                    message=f'LockAssetHelper can not unlock for invalid status {r.status.value}')

        row = cls.get_uni_row(business, business_id, user_id)
        _check_status(row)
        unlock_type = cls.model.OpType.UNLOCK_AND_SUB
        unlock_business = cls._get_lock_business(business, unlock_type)
        if row.lock_type == cls.model.OpType.ADD_AND_LOCK and row.lock_business == unlock_business:
            raise OperationNotAllowed(
                message=f'unlock_and_sub failed for lock_type is {row.lock_type} and unlock_type is {unlock_type} '
                        f'and lock_business is {row.lock_business} and unlock_business is {row.unlock_business}')

        with CacheLock(LockKeys.locked_asset_balance(row.id)):
            db.session.rollback()
            row = cls.get_uni_row(business, business_id, user_id)
            _check_status(row)

            row.unlock_type = unlock_type
            row.unlock_business = unlock_business
            row.sub_amount = sub_amount or row.sub_amount or row.amount
            if retry_type:
                row.retry_type = retry_type
            if remark:
                row.remark = remark
            db.session.commit()
            cls._unlock(row, callback)

    @classmethod
    def _unlock(cls, row: model, callback: bool):
        try:
            is_unlocked = cls._check_already_unlock(row)
            if is_unlocked:
                row.unlocked_at = now()
                row.status = cls.model.Status.UNLOCKED
                db.session.commit()
                return

            with ignore_duplicate_error():
                cls._do_asset_business(row, row.unlock_type)
            row.unlocked_at = now()
            row.status = cls.model.Status.UNLOCKED
            db.session.commit()
        except Exception as e:
            current_app.logger.error('LockAssetHelper _unlock failed, e', e)
            if row.retry_type == cls.model.RetryType.NO_RETRY and e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                row.status = cls.model.Status.UNLOCK_FAIL
                db.session.commit()
            raise e
        finally:
            if callback:
                cls.callback_business(row)

    @classmethod
    def set_unlock_at(
            cls, business: model.Business, business_id: int, user_id: int, unlock_at: datetime,
            unlock_type: model.OpType = model.OpType.UNLOCK,
            retry_type: model.RetryType = None,
            is_commit=True,
    ):
        """ 设置解锁时间 """
        row = cls.get_uni_row(business, business_id, user_id)
        if row.status not in cls.UNLOCKABLE_STATUSES:
            raise OperationNotAllowed(
                message=f'LockAssetHelper can not set_unlock_at for invalid status {row.status.value}')
        with CacheLock(LockKeys.locked_asset_balance(row.id)):
            db.session.rollback()
            row: cls.model = cls.model.query.get(row.id)
            if row.status not in cls.UNLOCKABLE_STATUSES:
                raise OperationNotAllowed(
                    message=f'LockAssetHelper can not set_unlock_at for invalid status {row.status.value}')

            row.unlocked_at = unlock_at
            row.unlock_type = unlock_type
            row.unlock_business = cls._get_lock_business(business, row.unlock_type)
            if retry_type:
                row.retry_type = retry_type
            if is_commit:
                db.session.commit()

    @classmethod
    def get_uni_row(cls, business: model.Business, business_id: int, user_id: int) -> model:
        return cls.model.query.filter(
            cls.model.business == business,
            cls.model.business_id == business_id,
            cls.model.user_id == user_id
        ).first()

    @classmethod
    def _check_already_lock(cls, row):
        if row.lock_type == cls.model.OpType.ADD_AND_LOCK:
            add_ret = cls._check_already(row, row.lock_business)
            if not add_ret:
                return False, cls.model.OpType.ADD_AND_LOCK
        lock_ret = cls._check_already(row, row.lock_business, lock_flag=QueryBusinessFlag.LOCK)
        if not lock_ret:
            return False, cls.model.OpType.LOCK
        return True, None

    @classmethod
    def _check_already_unlock(cls, row):
        unlock_ret = cls._check_already(row, row.unlock_business, lock_flag=QueryBusinessFlag.UNLOCK)
        if not unlock_ret:
            return False
        if row.unlock_type == cls.model.OpType.UNLOCK_AND_SUB:
            sub_ret = cls._check_already(row, row.unlock_business)
            if not sub_ret:
                raise OperationNotAllowed(message=f'当前资金已解锁但是未扣减，请联系开发人员处理')
        return True

    @classmethod
    def _do_asset_business(cls, row, op_type):
        client = ServerClient()
        if op_type == cls.model.OpType.ADD_AND_LOCK:
            client.add_and_lock_user_balance(
                row.user_id, row.asset, amount_to_str(row.amount, 8), row.lock_business, row.id
            )
        elif op_type == cls.model.OpType.LOCK:
            client.lock_user_balance(
                row.user_id, row.asset, amount_to_str(row.amount, 8), row.lock_business, row.id
            )
        elif op_type == cls.model.OpType.UNLOCK_AND_SUB:
            client.unlock_and_sub_user_balance(
                row.user_id, row.asset, amount_to_str(row.amount, 8), row.id,
                sub_amount=row.sub_amount, unlock_bus=row.unlock_business, sub_bus=row.unlock_business
            )
        elif op_type == cls.model.OpType.UNLOCK:
            client.unlock_user_balance(
                row.user_id, row.asset, amount_to_str(row.amount, 8), row.unlock_business, row.id
            )

    @classmethod
    def _check_already(cls, row, balance_business, lock_flag=None):
        client = ServerClient()
        if lock_flag:
            add_ret = client.asset_query_business(
                row.user_id, row.asset, balance_business, row.id, lock_flag=lock_flag)
        else:
            add_ret = client.asset_query_business(
                row.user_id, row.asset, balance_business, row.id)
        return bool(add_ret)

    @classmethod
    def callback_business(cls, row):
        from app.business.onchain.order import OnchainTransBiz

        # 只处理终结状态
        if row.status not in [cls.model.Status.LOCKED, cls.model.Status.LOCK_FAIL,
                              cls.model.Status.UNLOCKED, cls.model.Status.UNLOCK_FAIL]:
            return
        callback_mapping = {
            cls.model.Business.ONCHAIN: {
                cls.model.Status.UNLOCKED: {
                    cls.model.OpType.UNLOCK: OnchainTransBiz.unlock,
                    cls.model.OpType.UNLOCK_AND_SUB: OnchainTransBiz.unlock_and_sub,
                },
                cls.model.Status.UNLOCK_FAIL: {
                    cls.model.OpType.UNLOCK: OnchainTransBiz.unlock,
                    cls.model.OpType.UNLOCK_AND_SUB: OnchainTransBiz.unlock_and_sub,
                },
            },
        }
        business_callbacks = callback_mapping.get(row.business)
        if not business_callbacks:
            current_app.logger.error(f"未找到业务类型 {row.business} 的回调映射")
            return
        status_callbacks = business_callbacks.get(row.status)
        if not status_callbacks:
            current_app.logger.warning(f"业务 {row.business} 未定义状态 {row.status} 的回调处理")
            return

        if row.status in [cls.model.Status.LOCKED, cls.model.Status.LOCK_FAIL]:
            lock_type = row.lock_type
        else:
            lock_type = row.unlock_type
        callback_method = status_callbacks.get(lock_type)
        if not callback_method:
            current_app.logger.warning(f"业务 {row.business} 未定义状态 {row.status} 操作类型 {lock_type} 的回调处理")
            return
        callback_method(row.business_id)

