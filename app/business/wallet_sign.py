# -*- coding: utf-8 -*-

from collections import defaultdict
from decimal import Decimal
import time
from enum import Enum

from flask import current_app

from app.common.onchain import OrderSide, CHAIN_MONEY_MAPPING, Chain
from app.models import Withdrawal, WithdrawalSignature, OnchainSwapSignature, AssetChainSignConfig, db
from app.models.onchain import OnchainToken, OnchainOrder
from app.exceptions import WalletSignFailed
from app.business import WalletManagerClient, LockKeys, CacheLock, PriceManager
from app.caches.wallets import <PERSON><PERSON><PERSON>ress<PERSON><PERSON><PERSON>ache, TmpAddressAmountCache, GasAddressThresholdCache
from app.utils import <PERSON>sonRPC2Client
from app.utils.onchain import decimal_div, decimal_sub
from app.config import config


class SignType(Enum):
    WITHDRAWAL = 'WITHDRAWAL'
    ONCHAIN_TRADING_SWAP = 'ONCHAIN_TRADING:SWAP'
    ONCHAIN_TRADING_APPROVE = 'ONCHAIN_TRADING:APPROVE'


class WalletSigner:

    @classmethod
    def tss_sign_pre(cls, chain: str, sender: str, message: str, commitment: str, group: int) -> dict:
        start = time.time()
        c = WalletManagerClient(group, chain)
        try:
            return c.tss_sign_pre(dict(chain=chain, sender=sender, message=message, commitment=commitment))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager pre-sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager pre-sign error: {e.message}")
        finally:
            current_app.logger.warning(f"tss_sign_pre {chain} {time.time() - start}")

    @classmethod
    def sign_tx(cls, type: SignType, wwid: list[int], chain: str, sender: str, recipients: list[dict],
                raw_data: dict, signer: str, message: str, sign_func: str, group: int):
        match type:
            case SignType.WITHDRAWAL:
                return WithdrawalSigner.sign_tx(wwid, chain, sender, recipients, raw_data, signer, message, sign_func, group)
            case SignType.ONCHAIN_TRADING_APPROVE:
                return OnchainSwapSigner.sign_approve_tx(chain, sender, recipients, raw_data, signer, message, sign_func, group)
            case SignType.ONCHAIN_TRADING_SWAP:
                return OnchainSwapSigner.sign_swap_tx(wwid, chain, sender, recipients, raw_data, signer, message, sign_func, group)
            case _:
                raise WalletSignFailed(message=f"invalid args 'type': {type}")


class WithdrawalSigner:

    @classmethod
    def sign_tx(cls, wwid: list[int], chain: str, sender: str, recipients: list[dict],
                raw_data: dict, signer: str, message: str, sign_func: str, group: int):
        start = time.time()
        with CacheLock(LockKeys.wallet_sign(chain), wait=30, ttl=300):
            db.session.rollback()
            # 打给gas地址、冷钱包情况下，没有提现单据
            sum_of_ws = {}
            if wwid:
                ws = cls.check_withdrawals_signed(chain, wwid)
                sum_of_ws = cls.sum_withdrawals(ws)
            # wallet_manager会检查recipients和raw_data(待签名数据)是否匹配
            # 因此在backend只检查recipients是安全的
            sum_of_rs = cls.sum_recipients(chain, recipients)
            # recipients不应该为空，可能存在风险
            if not sum_of_rs:
                raise WalletSignFailed(message="empty recipients")
            cls.check_recipients_with_withdrawals(chain, sum_of_ws, sum_of_rs)

        c = WalletManagerClient(group, chain)
        try:
            sig = c.sign_tx(sign_func, dict(
                chain=chain,
                sender=sender,
                recipients=recipients,
                raw_data=raw_data,
                signer=signer,
                message=message
            ))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: {e.message}")

        if wwid:
            with CacheLock(LockKeys.wallet_sign(chain), wait=30, ttl=300):
                db.session.rollback()
                row = WithdrawalSignature.query.filter(WithdrawalSignature.withdrawal_id.in_(wwid),
                                                       WithdrawalSignature.status == WithdrawalSignature.Status.FINISHED).first()
                if row:
                    raise WalletSignFailed(message=f"duplicate sign")
                cls.set_withdrawals_signed(wwid)

        current_app.logger.warning(f"tss_sign {chain} {time.time() - start}")
        return {'signature': sig}

    @classmethod
    def check_withdrawals_signed(cls, chain, wwid):
        ss = WithdrawalSignature.query.filter(WithdrawalSignature.withdrawal_id.in_(wwid)).all()
        if any(x.status == WithdrawalSignature.Status.FINISHED for x in ss):
            raise WalletSignFailed(message="wwid already signed")
        ws = Withdrawal.query.filter(Withdrawal.id.in_(wwid)).all()
        if len(ws) != len(wwid):
            raise WalletSignFailed(message="wwid not found")
        if any(w.status != Withdrawal.Status.PROCESSING or w.chain != chain for w in ws):
            raise WalletSignFailed(message="invalid wwid status")
        return ws

    @classmethod
    def set_withdrawals_signed(cls, wwid):
        for wid in wwid:
            db.session.add(WithdrawalSignature(
                withdrawal_id=wid,
                status=WithdrawalSignature.Status.FINISHED,
            ))
        db.session.commit()

    @classmethod
    def sum_withdrawals(cls, ws):
        sum_of_ws = defaultdict(Decimal)
        for w in ws:
            key = (w.address, w.asset)
            sum_of_ws[key] += w.amount
        return sum_of_ws

    @classmethod
    def sum_recipients(cls, chain, recipients):
        identities = {x["amount"]["identity"] for x in recipients}
        rows = AssetChainSignConfig.query.filter(
            AssetChainSignConfig.chain == chain, AssetChainSignConfig.identity.in_(identities)
        ).all()
        id_to_asset = {x.identity: x for x in rows}
        sum_of_recipients = defaultdict(Decimal)
        for recipient in recipients:
            amount = recipient["amount"]
            if not (asset_info := id_to_asset.get(amount["identity"])):
                raise WalletSignFailed(message="identity not found")
            key = (recipient["address"], asset_info.asset)
            value = Decimal(amount["value"]) / (10**asset_info.precision)
            sum_of_recipients[key] += value
        return sum_of_recipients

    @classmethod
    def check_recipients_with_withdrawals(cls, chain, ws, rs):
        # 检查提现金额跟交易输出金额相等
        for key, value in ws.items():
            other = rs.get(key)
            _, asset = key
            if not other or not cls.amount_equal(chain, asset, value, other):
                raise WalletSignFailed(message="recipient amount not euqal")
        # 检查剩余的交易输出，是否是白名单地址或gas地址
        whitelist = cls.get_whitelist_addresses(chain)
        gas_address = cls.get_gas_address(chain)
        tmp_address = cls.get_temp_address(chain)
        to_gas = defaultdict(Decimal)
        to_tmp = defaultdict(Decimal)
        for key, amount in rs.items():
            if key in ws:
                continue
            address, asset = key
            if address in whitelist:
                continue
            if (isinstance(gas_address, str) and address == gas_address) \
                or (isinstance(gas_address, list) and address in gas_address):
                to_gas[asset] += amount
            elif address in tmp_address:
                to_tmp[asset] += amount
            else:
                raise WalletSignFailed(message="unrecognized recipient address")
        # 对gas地址转账限额，以防gas地址被盗的情况
        if to_gas:
            cls.check_gas_address_amount(chain, to_gas)
        # 对转账到临时地址的，检查限额
        if to_tmp:
            cls.check_tmp_address_amount(chain, to_tmp)

    @classmethod
    def amount_equal(cls, chain, asset, amount, other):
        if amount == other:
            return True
        # 对于链上精度小于数据库精度的情况，金额相差不能超过链上最小精度
        c = AssetChainSignConfig.query.filter(AssetChainSignConfig.chain == chain,
                                              AssetChainSignConfig.asset == asset).first()
        return abs(amount - other) <= Decimal(10) ** -c.precision

    @classmethod
    def get_whitelist_addresses(cls, chain):
        addrs = config["WALLET_WHITELIST_ADDRESS"]
        return addrs.get(chain, [])

    @classmethod
    def get_gas_address(cls, chain) -> str | list[str] | None:
        addrs = config["WALLET_GAS_ADDRESS"]
        return addrs.get(chain)

    @classmethod
    def get_temp_address(cls, chain) -> list:
        conf = config["WALLET_TEMP_ADDRESS"]
        data = conf.get(chain)
        if not data:
            return []
        # 临时地址带有过期时间，这样可以防止忘记删除
        result = []
        n = int(time.time())
        for addr, ttl in data['address']:
            if ttl < n:
                continue
            result.append(addr)
        return result

    @classmethod
    def check_gas_address_amount(cls, chain, assets):
        prices = PriceManager.assets_to_usd()
        amount = sum([prices.get(asset, 0) * v for asset, v in assets.items()])
        cache = GasAddressAmountCache()
        sent = cache.get_amount(chain)
        if sent + amount > GasAddressThresholdCache(chain).get_limit():
            raise WalletSignFailed(message="too many amount to gas address")
        cache.add_amount(chain, amount)

    @classmethod
    def check_tmp_address_amount(cls, chain, assets):
        limit = config["WALLET_TEMP_ADDRESS"][chain]['threshold']
        prices = PriceManager.assets_to_usd()
        amount = sum([prices.get(asset, 0) * v for asset, v in assets.items()])
        cache = TmpAddressAmountCache()
        sent = cache.get_amount(chain)
        if sent + amount > limit:
            raise WalletSignFailed(message="too many amount to tmp address")
        cache.add_amount(chain, amount)


class OnchainSwapSigner:

    @classmethod
    def get_token(cls, chain, address):
        return OnchainToken.query.filter(OnchainToken.chain == chain, OnchainToken.contract == address).first()

    @classmethod
    def sign_approve_tx(cls, chain: str, sender: str, recipients: list[dict],
                        raw_data: dict, signer: str, message: str, sign_func: str, group: int):
        """ERC20代币授权交易签名"""
        from_addr = cls.get_swap_from_address(chain)
        if not from_addr or sender != from_addr:
            raise WalletSignFailed(message="invalid sender address")
        whitelist = cls.get_whitelist_addresses(chain)
        for recipient in recipients:
            contract = recipient['address']
            if contract not in whitelist:
                raise WalletSignFailed(message="unrecognized recipient address")
            # 对token或money币种进行授权
            token_addr = recipient["amount"]['identity']
            token = cls.get_token(chain, token_addr)
            if not token:
                money_assets = list(CHAIN_MONEY_MAPPING[Chain[chain]].keys())
                cf = AssetChainSignConfig.query.filter(AssetChainSignConfig.chain == chain,
                                                       AssetChainSignConfig.identity == token_addr).first()
                if not cf or cf.asset not in money_assets:
                    raise WalletSignFailed(message="identity not found")

        c = WalletManagerClient(group, chain)
        try:
            sig = c.sign_tx(sign_func, dict(
                chain=chain,
                sender=sender,
                recipients=recipients,
                raw_data=raw_data,
                signer=signer,
                message=message
            ))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: {e.message}")
        return {'signature': sig}

    @classmethod
    def sign_swap_tx(cls, wwid: list[int], chain: str, sender: str, recipients: list[dict],
                     raw_data: dict, signer: str, message: str, sign_func: str, group: int):
        """dex合约调用交易签名"""
        from_addr = cls.get_swap_from_address(chain)
        if not from_addr or sender != from_addr:
            raise WalletSignFailed(message="invalid sender address")
        if len(wwid) != 1:  # 单笔tx只能执行一笔兑换
            raise WalletSignFailed(message=f"invalid wwid count")
        if len(recipients) != 1: 
            raise WalletSignFailed(message=f"invalid recipients count")

        order = cls.check_order_signed(chain, wwid[0], raw_data.get('nonce'))
        cls.check_recipient(chain, order, recipients[0])

        c = WalletManagerClient(group, chain)
        try:
            sig = c.sign_tx(sign_func, dict(
                chain=chain,
                sender=sender,
                recipients=recipients,
                raw_data=raw_data,
                signer=signer,
                message=message
            ))
        except JsonRPC2Client.BadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: http {e.code}")
        except JsonRPC2Client.RPCBadResponse as e:
            raise WalletSignFailed(message=f"wallet manager sign error: {e.message}")
        
        with CacheLock(LockKeys.wallet_sign(chain), wait=30, ttl=300):
            db.session.rollback()
            row = OnchainSwapSignature.query.filter(OnchainSwapSignature.order_id == wwid[0], 
                                                    OnchainSwapSignature.status == OnchainSwapSignature.Status.FINISHED).first()
            if row and (not row.nonce or row.nonce != raw_data.get('nonce')):
                raise WalletSignFailed(message=f"duplicate sign")
            cls.set_order_signed(order.id, raw_data.get('nonce'))
        return {'signature': sig}

    @classmethod
    def check_order_signed(cls, chain, order_id, nonce=None):
        order = OnchainOrder.query.get(order_id)
        if not order or order.chain.name != chain:
            raise WalletSignFailed(message=f"order not found")
        # 极端情况下还未变更状态钱包就请求签名了，加个延迟再检查一次
        if order.status != OnchainOrder.Status.PROCESSING:
            db.session.rollback()
            time.sleep(1)
            order = OnchainOrder.query.get(order_id)
            if order.status != OnchainOrder.Status.PROCESSING:
                raise WalletSignFailed(message="invalid order status")
        row = OnchainSwapSignature.query.filter(OnchainSwapSignature.order_id == order_id).first()
        if row and row.status == OnchainSwapSignature.Status.FINISHED and (not row.nonce or row.nonce != nonce):
            raise WalletSignFailed(message="order already signed")
        return order

    @classmethod
    def set_order_signed(cls, order_id, nonce=None):
        db.session_add_and_commit(OnchainSwapSignature(
            order_id=order_id,
            nonce=nonce or '',
            status=OnchainSwapSignature.Status.FINISHED,
        ))

    @classmethod
    def check_recipient(cls, chain, order, recipient):
        amount = recipient["amount"]
        if order.side == OrderSide.BUY:
            money_identity = amount["identity"]
            token_identity = recipient['to_identity']
        else:
            money_identity = recipient['to_identity']
            token_identity = amount["identity"]
        # 检查money币种是否一致
        cf = AssetChainSignConfig.query.filter(AssetChainSignConfig.chain == chain,
                                               AssetChainSignConfig.identity == money_identity).first()
        if not cf:
            raise WalletSignFailed(message="identity not found")
        money_asset = cf.asset
        if money_asset != order.money_asset:
            raise WalletSignFailed(message="money asset not match")
        # 检查token是否一致
        token = cls.get_token(chain, token_identity)
        if not token:
            raise WalletSignFailed(message="token not found")
        if token.id != order.token_id:
            raise WalletSignFailed(message="token not match")
        # 检查花费的金额是否一致
        if order.side == OrderSide.BUY:
            decimals = cf.precision
            order_amount = order.money_amount
        else:
            decimals = token.decimals
            order_amount = order.token_amount
        value = decimal_div(Decimal(amount["value"]), 10**decimals)
        if not cls.amount_equal(order_amount, value, decimals):
            raise WalletSignFailed(message="amount not euqal")
        # 检查滑点
        if order.slippage_limit != Decimal(recipient['slippage']):
            raise WalletSignFailed(message="slippage not match")
        # 确保调用已知合约
        whitelist = cls.get_whitelist_addresses(chain)
        if recipient['address'] not in whitelist:
            raise WalletSignFailed(message="unrecognized contract address")
        # 确保兑换的资产是给交易所兑换钱包的地址
        to_addr = cls.get_swap_to_address(chain)
        if not to_addr or recipient['to_address'] != to_addr:
            raise WalletSignFailed(message="unrecognized to address")

    @classmethod
    def amount_equal(cls, amount, other, decimals):
        if amount == other:
            return True
        # 对于链上精度小于数据库精度的情况，金额相差不能超过链上最小精度
        return abs(decimal_sub(amount, other)) <= decimal_div(10, 10**decimals)

    @classmethod
    def get_whitelist_addresses(cls, chain):
        addrs = config["ONCHAIN_WHITELIST_ADDRESS"]
        return addrs.get(chain, [])
    
    @classmethod
    def get_swap_from_address(cls, chain) -> str | None:
        conf = config["ONCHAIN_CONFIGS"]['chain_config']
        if chain not in conf:
            return None
        return conf[chain]['swap_from_address']

    @classmethod
    def get_swap_to_address(cls, chain) -> str | None:
        conf = config["ONCHAIN_CONFIGS"]['chain_config']
        if chain not in conf:
            return None
        return conf[chain]['swap_to_address']
