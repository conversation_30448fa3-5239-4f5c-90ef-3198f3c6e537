# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal, ROUND_UP
from datetime import timedelta, datetime, date
from functools import cached_property
from typing import NamedTuple, Optional

from flask import current_app
from sqlalchemy import and_, or_, func

from app.models import db, AmmMarket, SubAccount
from app.models.equity_center import (
    EquityType,
    UserEquity,
    UserCashbackEquity,
    UserCashbackEquityHistory,
    UserCashbackSettlementHistory,
    UserCashbackTradeFeeHistory,
    UserCashbackEquityTransferHistory,
)
from app.models.exchange import AssetExchangeOrder, SysAssetExchangeOrder
from app.models.user import UserBizTag
from app import config
from app.common import PrecisionEnum, CeleryQueues
from app.business import (
    PriceManager, CacheLock, LockKeys, lock_call, ServerClient, BalanceBusiness,
    SPOT_ACCOUNT_ID, TradeHistoryDB, PerpetualHistoryDB,
)
from app.business.utils import yield_query_records_by_time_range
from app.caches.perpetual import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.caches.user import UserActivenessCache
from app.utils import now, quantize_amount, timestamp_to_datetime, batch_iter, route_module_to_celery_queue, celery_task, today
from app.utils.logs import log_func_consume


route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


class SettlementAmounts(NamedTuple):
    """ 结算记录的金额 """
    cashback_amount: Decimal      # 返现币种数量
    settle_cost_amount: Decimal   # 结算记录成本币种数量
    equity_used_cost: Decimal     # 权益使用的成本币种数量
    settle_used_cost: Decimal      # 结算记录实际消耗量


class CashbackEquityHelper:

    @classmethod
    def update_equities_to_finished_status(cls):
        """ 将过期的返现权益状态改为结束态 """
        now_ = now()
        rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                ]
            ),
            UserCashbackEquity.end_time < now_,
        ).with_entities(
            UserCashbackEquity.user_id,
        ).all()
        user_ids = {i.user_id for i in rows}
        has_settle_user_ids = set()
        for ch_user_ids in batch_iter(user_ids, 5000):
            ch_settling_rows = UserCashbackSettlementHistory.query.filter(
                UserCashbackSettlementHistory.user_id.in_(ch_user_ids),
                UserCashbackSettlementHistory.status == UserCashbackSettlementHistory.Status.SETTLING,
            ).with_entities(
                UserCashbackSettlementHistory.user_id,
            ).all()
            ch_settling_user_ids: set[int] = {i.user_id for i in ch_settling_rows}
            has_settle_user_ids.update(ch_settling_user_ids)

        no_settle_user_ids = user_ids - has_settle_user_ids
        if no_settle_user_ids:
            cls.batch_finish_no_settle_equity(no_settle_user_ids)

        for user_id in has_settle_user_ids:
            try:
                with CacheLock(key=LockKeys.cashback_equity_settle(user_id)):
                    db.session.rollback()
                    cls.finish_equity_by_user(user_id)
            except Exception as _e:
                db.session.rollback()
                current_app.logger.exception(f"finish_equity_by_user {user_id} error: {_e!r}")

    @classmethod
    @log_func_consume
    def batch_finish_no_settle_equity(cls, user_ids: set[int]):
        """批量过期-没有结算记录的权益；不加锁"""
        now_ = now()
        for ch_user_ids in batch_iter(user_ids, 5000):
            fin_rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
                UserCashbackEquity.status == UserCashbackEquity.Status.USING,
                UserCashbackEquity.end_time < now_,
                UserCashbackEquity.user_id.in_(ch_user_ids),
            ).all()
            user_eq_ids = [i.user_equity_id for i in fin_rows]
            user_eq_rows: list[UserEquity] = UserEquity.query.filter(UserEquity.id.in_(user_eq_ids)).all()
            user_eq_row_map = {i.id: i for i in user_eq_rows}
            for fin_row in fin_rows:
                user_eq = user_eq_row_map[fin_row.user_equity_id]
                CashbackEquityHelper.finish_one_equity(user_eq, fin_row, is_commit=False)
            db.session.commit()
            current_app.logger.warning(f"batch_finish_no_settle_equity chunk {len(user_ids)} user_ids {len(fin_rows)} equity")
        current_app.logger.warning(f"batch_finish_no_settle_equity {len(user_ids)} user_ids")

    @classmethod
    def finish_equity_by_user(cls, user_id: int):
        """用户维度过期-有结算记录的权益"""
        now_ = now()
        fin_rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user_id,
            UserCashbackEquity.end_time < now_,
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                ]
            ),
        ).all()
        if not fin_rows:
            return

        user_eq_ids = [i.user_equity_id for i in fin_rows]
        user_eq_rows: list[UserEquity] = UserEquity.query.filter(UserEquity.id.in_(user_eq_ids)).all()
        user_eq_row_map = {i.id: i for i in user_eq_rows}
        for fin_row in fin_rows:
            user_eq = user_eq_row_map[fin_row.user_equity_id]
            CashbackEquityHelper.finish_one_equity(user_eq, fin_row, is_commit=False)
        db.session.commit()

    @classmethod
    def finish_one_equity(cls, user_eq: UserEquity, cb_eq: UserCashbackEquity, is_commit: bool):
        if cb_eq.cashback_amount > 0:
            status = UserCashbackEquity.Status.FINISHED
        else:
            status = UserCashbackEquity.Status.EXPIRED
        cb_eq.status = status
        user_eq.status = UserEquity.Status.FINISHED
        user_eq.finished_at = now()
        if is_commit:
            db.session.commit()

    @classmethod
    def update_biz_tag(cls, max_finish_dt: date = None):
        """ 更新返佣标签：对于今日有已使用｜已过期 的权益，在次日也不返佣。后续有其他标签再移走 """
        if not max_finish_dt:
            max_finish_dt = today() - timedelta(days=1)

        tag_user_ids = UserBizTag.query_tag_user_ids(biz_tag=UserBizTag.BizTag.EE_NOT_REFERRAL)
        if not tag_user_ids:
            return

        has_using_eq_user_ids = CashbackSettleHelper.query_has_using_equity_user_ids()
        check_user_ids = tag_user_ids - has_using_eq_user_ids  # 无使用中权益 & 有标签 的用户
        user_max_finished_at_map = {}
        for ch_user_ids in batch_iter(check_user_ids, 5000):
            ch_rows = UserEquity.query.filter(
                UserEquity.user_id.in_(ch_user_ids),
                UserEquity.type == EquityType.CASHBACK,
            ).group_by(
                UserEquity.user_id,
            ).with_entities(
                UserEquity.user_id,
                func.max(UserEquity.finished_at).label('max_finished_at'),
            ).all()
            user_max_finished_at_map.update(dict(ch_rows))

        del_user_ids = set()
        for user_id, _max_finished_at in user_max_finished_at_map.items():
            if _max_finished_at and _max_finished_at.date() < max_finish_dt:
                del_user_ids.add(user_id)
        if del_user_ids:
            for ch_del_user_ids in batch_iter(del_user_ids, 5000):
                UserBizTag.batch_del_user_tag(
                    biz_tag=UserBizTag.BizTag.EE_NOT_REFERRAL,
                    source=UserBizTag.Source.CASHBACK_EQUITY,
                    user_ids=set(ch_del_user_ids),
                )
                current_app.logger.warning(f"batch_del_user_tag EE_NOT_REFERRAL {len(ch_del_user_ids)} {ch_del_user_ids[:10]}")
            db.session.commit()


class CashbackSettleHelper:

    TOLERANCE_AMOUNT = Decimal('0.0001')

    SPOT_FEE_TRADE_TYPES = [
        UserCashbackTradeFeeHistory.TradeType.EXCHANGE,
        UserCashbackTradeFeeHistory.TradeType.SPOT,
    ]
    PERPETUAL_FEE_TRADE_TYPES = [
        UserCashbackTradeFeeHistory.TradeType.PERPETUAL,
    ]

    # 权益-返现范围：[结算记录-交易类型]
    CASHBACK_SCOPE_SETTLE_TRADE_TYPE_MAP = {
        UserCashbackEquity.CashbackScope.SPOT: [UserCashbackSettlementHistory.TradeType.SPOT],
        UserCashbackEquity.CashbackScope.PERPETUAL: [UserCashbackSettlementHistory.TradeType.PERPETUAL],
        UserCashbackEquity.CashbackScope.ALL: [
            UserCashbackSettlementHistory.TradeType.SPOT,
            UserCashbackSettlementHistory.TradeType.PERPETUAL,
        ],
    }

    @classmethod
    def get_amm_markets(cls) -> set[str]:
        amm_market_rows = AmmMarket.query.filter(
            AmmMarket.status == AmmMarket.Status.ONLINE,
        ).with_entities(
            AmmMarket.name,
        ).all()
        amm_markets = {i.name for i in amm_market_rows}
        return amm_markets

    @classmethod
    def user_is_need_collect_fee(cls, user_id: int, ago_minutes: int = 30) -> tuple[bool, bool]:
        """判断用户是否需要统计手续费"""

        sub: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == user_id,
        ).with_entities(SubAccount.main_user_id).first()
        main_uid = sub.main_user_id if sub else user_id

        now_ = now()
        rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == main_uid,
            UserCashbackEquity.status.in_(
                [UserCashbackEquity.Status.USING]
            ),
            or_(
                # 开始时间在N分钟内
                UserCashbackEquity.start_time <= now_ + timedelta(minutes=ago_minutes),
                and_(
                    # 生效中
                    UserCashbackEquity.start_time <= now_,
                    UserCashbackEquity.end_time >= now_,
                )
            ),
        ).with_entities(
            UserCashbackEquity.cashback_scope,
        ).all()

        need_spot = False
        need_per = False
        scopes = {r.cashback_scope for r in rows}
        if UserCashbackEquity.CashbackScope.ALL in scopes:
            need_spot = True
            need_per = True
        if UserCashbackEquity.CashbackScope.SPOT in scopes:
            need_spot = True
        if UserCashbackEquity.CashbackScope.PERPETUAL in scopes:
            need_per = True
        return need_spot, need_per

    @classmethod
    def get_need_collect_fee_users(cls, ago_minutes: int = 30) -> tuple[dict, dict]:
        """需要统计手续费的user_id，包括子帐号"""
        now_ = now()
        rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.status.in_(
                [UserCashbackEquity.Status.USING]
            ),
            or_(
                # 开始时间在N分钟内
                UserCashbackEquity.start_time <= now_ + timedelta(minutes=ago_minutes),
                and_(
                    # 生效中
                    UserCashbackEquity.start_time <= now_,
                    UserCashbackEquity.end_time >= now_,
                )
            ),
        ).with_entities(
            UserCashbackEquity.user_id,
            UserCashbackEquity.cashback_scope,
            UserCashbackEquity.end_time,
        ).all()
        if not rows:
            return dict(), dict()

        return cls.group_by_scope_users(rows)

    @classmethod
    def get_time_range_need_collect_fee_users(cls, start_time: datetime, end_time: datetime) -> tuple[dict, dict]:
        now_ = now()
        rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.status.in_(
                [UserCashbackEquity.Status.USING]
            ),
            UserCashbackEquity.start_time >= start_time,
            UserCashbackEquity.start_time <= end_time,
            UserCashbackEquity.end_time >= now_,
        ).with_entities(
            UserCashbackEquity.user_id,
            UserCashbackEquity.cashback_scope,
            UserCashbackEquity.end_time,
        ).all()
        if not rows:
            return dict(), dict()

        return cls.group_by_scope_users(rows)

    @classmethod
    def group_by_scope_users(cls, rows: list[UserCashbackEquity]) -> tuple[dict, dict]:
        from app.business.sub_account import get_all_sub_main_info

        spot_user_map = dict()
        per_user_map = dict()
        for r in rows:
            if r.cashback_scope in [
                UserCashbackEquity.CashbackScope.ALL,
                UserCashbackEquity.CashbackScope.SPOT,
            ]:
                if r.user_id not in spot_user_map:
                    spot_user_map[r.user_id] = r.end_time
                else:
                    spot_user_map[r.user_id] = max(r.end_time, spot_user_map[r.user_id])
            if r.cashback_scope in [
                UserCashbackEquity.CashbackScope.ALL,
                UserCashbackEquity.CashbackScope.PERPETUAL,
            ]:
                if r.user_id not in per_user_map:
                    per_user_map[r.user_id] = r.end_time
                else:
                    per_user_map[r.user_id] = max(r.end_time, per_user_map[r.user_id])

        res_spot, res_per = dict(), dict()
        sub_main_map, main_subs_map = get_all_sub_main_info()
        for user_id, dt in spot_user_map.items():
            res_spot[user_id] = dt
            subs = main_subs_map.get(user_id, [])
            for s in subs:
                res_spot[s] = dt
        for user_id, dt in per_user_map.items():
            res_per[user_id] = dt
            subs = main_subs_map.get(user_id, [])
            for s in subs:
                res_per[s] = dt

        return res_spot, res_per

    @classmethod
    def query_has_using_equity_user_ids(cls) -> set[int]:
        """有使用中权益的user_ids"""
        now_ = now()
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.start_time <= now_,
            UserCashbackEquity.end_time >= now_,
        ).with_entities(
            UserCashbackEquity.user_id,
        )
        using_equity_rows = q.all()
        user_ids = {i.user_id for i in using_equity_rows}
        return user_ids

    @classmethod
    def query_using_equities(cls, user_ids: list[int] = None) -> list[UserCashbackEquity]:
        """查询 使用中的权益"""
        now_ = now()
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.start_time <= now_,
            UserCashbackEquity.end_time >= now_,
        )
        if user_ids:
            q = q.filter(UserCashbackEquity.user_id.in_(user_ids))
        rows = q.all()
        return rows

    @classmethod
    def equity_sort_func_for_using(cls, equity: UserCashbackEquity) -> tuple:
        """权益使用顺序
        优先使用过期时间更早的权益、其次返现比例更高的，再就是优先使用返现价值更大的权益，如果权益相同则随机使用其一
        """
        v1 = int(equity.end_time.timestamp()) if equity.end_time else 0  # 按秒
        v2 = -equity.cashback_ratio
        v3 = -equity.cost_amount
        v4 = equity.id
        return v1, v2, v3, v4

    @classmethod
    def query_user_usable_settle_rows(cls, user_id: int) -> list[UserCashbackSettlementHistory]:
        """查询 可用的结算记录"""
        settle_rows = UserCashbackSettlementHistory.query.filter(
            UserCashbackSettlementHistory.user_id == user_id,
            UserCashbackSettlementHistory.status == UserCashbackSettlementHistory.Status.SETTLING,
        ).all()
        return settle_rows

    @classmethod
    @log_func_consume
    def query_can_settle_fees(cls) -> list[UserCashbackTradeFeeHistory]:
        """查询 可结算的手续费统计"""
        fee_rows = UserCashbackTradeFeeHistory.query.filter(
            UserCashbackTradeFeeHistory.settle_his_id.is_(None),
        ).all()
        return fee_rows

    @classmethod
    def merge_settle_fees_by_main(
        cls,
        fee_rows: list[UserCashbackTradeFeeHistory],
        sub_main_map: dict[int, int],
    ) -> dict[int, list[UserCashbackTradeFeeHistory]]:
        main_fee_his_rows_map = defaultdict(list)
        for f in fee_rows:
            mid = sub_main_map.get(f.trade_user_id, f.trade_user_id)
            main_fee_his_rows_map[mid].append(f)
        return main_fee_his_rows_map

    @log_func_consume
    def generate_settle_history(self):
        from app.business.sub_account import get_all_sub_main_info

        can_settle_fee_rows = self.query_can_settle_fees()
        if not can_settle_fee_rows:
            return

        price_map = PriceManager.assets_to_usd()
        sub_main_map, main_subs_map = get_all_sub_main_info()
        main_fee_rows_map = self.merge_settle_fees_by_main(can_settle_fee_rows, sub_main_map)
        main_user_ids = set(main_fee_rows_map)

        using_equity_rows = []
        for ch_main_ids in batch_iter(main_user_ids, 5000):
            ch_using_equity_rows = self.query_using_equities(ch_main_ids)
            using_equity_rows.extend(ch_using_equity_rows)
        if not using_equity_rows:
            return

        user_equity_rows_map = defaultdict(list)
        for equity in using_equity_rows:
            user_equity_rows_map[equity.user_id].append(equity)

        current_app.logger.warning(f"generate_settle_history {len(user_equity_rows_map)} users")
        for user_id, equity_rows in user_equity_rows_map.items():
            fee_rows = main_fee_rows_map[user_id]
            if not fee_rows:
                continue
            try:
                self.gen_user_settle_history(equity_rows, fee_rows, price_map)
            except Exception as _e:
                db.session.rollback()
                current_app.logger.exception(f"gen_user_settle_history {user_id} error: {_e!r}")

    def gen_user_settle_history(
        self,
        equity_rows: list[UserCashbackEquity],
        fee_rows: list[UserCashbackTradeFeeHistory],
        price_map: dict[str, Decimal],
    ):
        """ 生成结算记录：如果某个交易类型同时有2个或以上的权益，则不进行Fee的合并 """
        if not equity_rows:
            return

        # 默认结算生成结算记录的币种为USDT, 后续返现时，会根据用户的成本币种进行转换计算返现价值
        default_settle_cost_asset = "USDT"

        min_start_time = min([i.start_time for i in equity_rows])
        max_end_time = max([i.end_time for i in equity_rows])
        available_fee_rows = [
            i for i in fee_rows if min_start_time < i.trade_time < max_end_time and i.settle_his_id is None
        ]
        if not available_fee_rows:
            return

        cost_assets = {i.cost_asset for i in equity_rows}

        equity_rows.sort(key=self.equity_sort_func_for_using)
        available_fee_rows.sort(key=lambda x: x.trade_time)

        user_id = equity_rows[0].user_id
        cost_asset = default_settle_cost_asset
        cost_asset_price = price_map[cost_asset]

        spot_eqs = []
        per_eqs = []
        for eq in equity_rows:
            if eq.cashback_scope in eq.SPOT_SCOPES:
                spot_eqs.append(eq)
            if eq.cashback_scope in eq.PERPETUAL_SCOPES:
                per_eqs.append(eq)
        can_merge_spot_fee = len(spot_eqs) <= 1
        can_merge_per_fee = len(per_eqs) <= 1

        spot_fees = []
        per_fees = []
        for fee in available_fee_rows:
            if fee.trade_type in self.SPOT_FEE_TRADE_TYPES:
                spot_fees.append(fee)
            elif fee.trade_type in self.PERPETUAL_FEE_TRADE_TYPES:
                per_fees.append(fee)

        cashback_assets = {i.cashback_asset for i in equity_rows}
        # 添加默认结算币种，用于计算结算记录的币种, 成本和返现可能会不包含默认结算币种
        save_assets = cost_assets | cashback_assets | {default_settle_cost_asset}
        save_asset_prices = {k: price_map[k] for k in save_assets}

        def _new_settle_his(_fees: list[UserCashbackTradeFeeHistory], _trade_type):
            total_cost_amount = Decimal()
            item_asset_rates = dict(save_asset_prices)
            fee_his_ids = []
            trade_times = []
            for _fee in _fees:
                for market, fee_asset, fee_amount in _fee.fees:
                    fee_amount = Decimal(fee_amount)
                    fee_asset_price = price_map[fee_asset]
                    fee_cost_amount = quantize_amount(fee_asset_price * fee_amount / cost_asset_price, PrecisionEnum.COIN_PLACES)
                    total_cost_amount += fee_cost_amount
                    item_asset_rates[fee_asset] = fee_asset_price
                fee_his_ids.append(_fee.id)
                trade_times.append(_fee.trade_time)
            settle_time = max(trade_times)
            settle_his = UserCashbackSettlementHistory(
                user_id=user_id,
                trade_type=_trade_type,
                settle_time=settle_time,
                cost_asset=cost_asset,
                total_cost_amount=total_cost_amount,
                used_cost_amount=0,
                asset_rates=item_asset_rates,
                fee_his_ids=fee_his_ids,
            )
            db.session.add(settle_his)
            db.session.flush()
            for _fee in _fees:
                _fee.settle_his_id = settle_his.id
                db.session.add(_fee)

        if spot_fees:
            to_add_spot_fees_list = [spot_fees] if can_merge_spot_fee else [[f] for f in spot_fees]
        else:
            to_add_spot_fees_list = []
        if per_fees:
            to_add_per_fees_list = [per_fees] if can_merge_per_fee else [[f] for f in per_fees]
        else:
            to_add_per_fees_list = []
        for to_add_spot_fees in to_add_spot_fees_list:
            _new_settle_his(to_add_spot_fees, UserCashbackSettlementHistory.TradeType.SPOT)
        for to_add_per_fees in to_add_per_fees_list:
            _new_settle_his(to_add_per_fees, UserCashbackSettlementHistory.TradeType.PERPETUAL)
        db.session.commit()

    @log_func_consume
    def execute_cashback(self):
        """通过settle_his，执行返现"""
        settling_rows = UserCashbackSettlementHistory.query.filter(
            UserCashbackSettlementHistory.status == UserCashbackSettlementHistory.Status.SETTLING,
        ).with_entities(
            UserCashbackSettlementHistory.user_id,
        ).all()
        user_ids = {i.user_id for i in settling_rows}

        # 先统一执行结算&生成划转记录
        current_app.logger.warning(f"execute_cashback execute_start {len(user_ids)} users")
        new_tran_rows = []
        for user_id in user_ids:
            try:
                with CacheLock(key=LockKeys.cashback_equity_settle(user_id)):
                    db.session.rollback()
                    _user_tran_rows = self.execute_cashback_by_user(user_id)
                    new_tran_rows.extend(_user_tran_rows)
            except Exception as _e:
                db.session.rollback()
                current_app.logger.exception(f"execute_cashback_by_user {user_id} error: {_e!r}")

        # 划转最后进行
        current_app.logger.warning(f"execute_cashback transfer_start {len(new_tran_rows)} new_tran_rows")
        tran_row_ids = [i.id for i in new_tran_rows]
        for tran_row_id in tran_row_ids:
            tran_row: UserCashbackEquityTransferHistory = UserCashbackEquityTransferHistory.query.get(tran_row_id)
            try:
                self.transfer_by_tran_history(tran_row)
            except Exception as _e:
                db.session.rollback()
                current_app.logger.exception(f"execute_cashback.transfer_by_tran_history {tran_row.user_id} {tran_row.id} error: {_e!r}")

    def execute_cashback_by_user(self, user_id: int) -> list[UserCashbackEquityTransferHistory]:
        equity_rows = self.query_using_equities(user_ids=[user_id])
        if not equity_rows:
            return []

        equity_rows.sort(key=self.equity_sort_func_for_using)
        usable_settle_rows = self.query_user_usable_settle_rows(user_id)
        new_tran_rows = []
        for equity in equity_rows:
            sorted_settle_rows = self.filter_and_sort_settle_rows(equity, usable_settle_rows)
            _eq_his_rows, _tran_row = self.execute_one_equity_settle(equity, sorted_settle_rows)
            if _tran_row:
                new_tran_rows.append(_tran_row)

        return new_tran_rows

    @classmethod
    def filter_and_sort_settle_rows(
        cls,
        equity: UserCashbackEquity,
        settle_rows: list[UserCashbackSettlementHistory],
    ) -> list[UserCashbackSettlementHistory]:
        trade_types = cls.CASHBACK_SCOPE_SETTLE_TRADE_TYPE_MAP[equity.cashback_scope]
        res = []
        for settle in settle_rows:
            if (
                settle.trade_type in trade_types
                and settle.status == UserCashbackSettlementHistory.Status.SETTLING
                and equity.start_time <= settle.settle_time <= equity.end_time
            ):
                res.append(settle)
        res.sort(key=lambda x: x.settle_time)
        return res

    @classmethod
    def is_tolerance_equal(cls, computation_amount, comparison_amount) -> bool:
        """
        :param computation_amount: 计算结果
        :param comparison_amount: 比较结果
        :return:
        """
        return abs(computation_amount - comparison_amount) <= cls.TOLERANCE_AMOUNT
    
    @classmethod
    def _calculate_settlement_amounts(
        cls,
        equity_remain_cost: Decimal,      # 权益剩余成本 (单位 equity.cost_asset)
        settle_remain_cost: Decimal,      # 结算剩余成本 (单位 settle_row.cost_asset, 通常是USDT)
        cashback_ratio: Decimal,          # 返现比例 (equity.cashback_ratio)
        equity_cost_asset: str,           # 权益成本币种 (equity.cost_asset)
        settle_cost_asset: str,           # 结算成本币种 (settle_row.cost_asset)
        cashback_asset: str,              # 返现币种 (equity.cashback_asset)
        usd_rates: dict[str, Decimal]     # 各币种对USD汇率
    ):
        """
        计算结算金额 - 核心汇率换算逻辑
        
        换算步骤（以USD为中间货币统一计算）：
        1. 权益剩余成本 -> USD
        2. 结算可返现成本（考虑返现比例）-> USD
        3. 取较小值作为本次结算的USD价值
        4. 本次结算USD -> 返现币种数量
        5. 本次结算USD -> 结算记录使用的成本币种数量  
        6. 本次结算USD -> 权益使用的成本币种数量
        7. 计算结算记录实际消耗量（逆向计算返现比例）
        """
        assert Decimal() < cashback_ratio <= Decimal(1)
        
        # 1. 权益剩余成本转为USD
        equity_remain_usd = equity_remain_cost * usd_rates[equity_cost_asset]
        
        # 2. 结算记录可返现金额转为USD
        settle_cashback_cost = settle_remain_cost * cashback_ratio
        settle_cashback_usd = settle_cashback_cost * usd_rates[settle_cost_asset]
        
        # 3. 本次实际结算的USD金额（取较小值，避免超限）
        current_settle_usd = min(equity_remain_usd, settle_cashback_usd)
        
        # 4. USD转换为返现币种数量
        cashback_amount = quantize_amount(current_settle_usd / usd_rates[cashback_asset], 8)
        
        # 5. USD转换为结算记录使用的成本币种数量
        settle_cost_amount = quantize_amount(current_settle_usd / usd_rates[settle_cost_asset], 8)
        
        # 6. USD转换为权益使用的成本币种数量
        equity_used_cost = quantize_amount(current_settle_usd / usd_rates[equity_cost_asset], 8)
        
        # 7. 计算结算记录实际消耗量（考虑返现比例的逆向计算）
        settle_used_cost = min(
            quantize_amount(settle_cost_amount / cashback_ratio, 8, rounding=ROUND_UP),
            settle_remain_cost
        )
        assert cashback_amount >= Decimal()
        return SettlementAmounts(
            cashback_amount=cashback_amount,
            settle_cost_amount=settle_cost_amount,
            equity_used_cost=equity_used_cost,
            settle_used_cost=settle_used_cost,
        )

    @classmethod
    def execute_one_equity_settle(
        cls,
        equity: UserCashbackEquity,
        settle_rows: list[UserCashbackSettlementHistory],
    ) -> tuple[list[UserCashbackEquityHistory], Optional[UserCashbackEquityTransferHistory]]:
        """
        执行单个权益的结算
        1. 根据结算记录，生成权益明细
        2. 根据权益明细，生成划转记录

        币种单位：
        - 成本币种：equity.cost_asset (USDT, USDC)
        - 返现币种：equity.cashback_asset  (USDT, CET, USDC, ...)
        - 结算记录币种：settle_row.cost_asset  USDT
        - 划转记录币种：equity.cashback_asset
        
        计算过程中单位转换：
        1. 计算返现币种数量:
            - 根据 结算记录币种：settle_row.cost_asset  USDT 和 返现币种：equity.cashback_asset 的汇率，计算返现币种数量
        2. 计算使用待结算币种数量
            - 根据 返现权益的剩余成本币种数量，和 结算记录币种：settle_row.cost_asset  USDT
              和 权益成本币种：equity.cost_asset 的汇率，计算使用待结算币种数量 需要保持单位统一。
        3. 计算已返现成本币种数量和已结算币种数量
            - 根据计算结果，更新权益和结算记录的字段
        
        """
        now_ = now()
        eq_his_rows = []
        for settle_row in settle_rows:
            remain_cost_amount = equity.remain_cost_amount
            if remain_cost_amount <= Decimal():
                break
            if settle_row.remain_cost_amount <= Decimal():
                continue
            
            # 计算本次的结算数目
            asset_rates = {k: Decimal(v) for k, v in settle_row.asset_rates.items()}
            
            settlement_amounts = cls._calculate_settlement_amounts(
                equity_remain_cost=remain_cost_amount,
                settle_remain_cost=settle_row.remain_cost_amount,
                cashback_ratio=equity.cashback_ratio,
                equity_cost_asset=equity.cost_asset,
                settle_cost_asset=settle_row.cost_asset,
                cashback_asset=equity.cashback_asset,
                usd_rates=asset_rates,
            )            

            # 更新权益的字段
            equity.cashback_amount += settlement_amounts.cashback_amount
            equity.used_cost_amount += settlement_amounts.equity_used_cost
            assert equity.used_cost_amount <= equity.cost_amount
            equity.last_cashback_at = now_
            eq_is_finished = False
            if cls.is_tolerance_equal(equity.used_cost_amount, equity.cost_amount):
                user_eq: UserEquity = UserEquity.query.get(equity.user_equity_id)
                CashbackEquityHelper.finish_one_equity(user_eq, equity, is_commit=False)
                eq_is_finished = True

            # 更新结算记录的字段
            settle_row.used_cost_amount += settlement_amounts.settle_used_cost
            assert settle_row.used_cost_amount <= settle_row.total_cost_amount
            settle_is_finished = False
            if cls.is_tolerance_equal(settle_row.used_cost_amount, settle_row.total_cost_amount):
                settle_row.status = UserCashbackSettlementHistory.Status.FINISHED
                settle_is_finished = True

            # 权益和结算记录 至少有一个用完了
            assert int(eq_is_finished) + int(settle_is_finished) >= 1

            # 写入权益明细
            eq_his = UserCashbackEquityHistory(
                user_id=equity.user_id,
                user_equity_id=equity.user_equity_id,
                settle_his_id=settle_row.id,
                cost_asset=equity.cost_asset,
                cashback_asset=equity.cashback_asset,
                delta_cost_amount=settlement_amounts.equity_used_cost,
                delta_cashback_amount=settlement_amounts.cashback_amount,
                total_used_cost_amount=equity.used_cost_amount,
                total_cashback_amount=equity.cashback_amount,
                settle_at=now_,
            )
            db.session.add(eq_his)
            db.session.flush()
            eq_his_rows.append(eq_his)

        # 合并多笔权益明细，为一笔划转
        tran_his = None
        total_tran_amount = sum([i.delta_cashback_amount for i in eq_his_rows])
        if total_tran_amount > 0:
            tran_his = UserCashbackEquityTransferHistory(
                user_id=equity.user_id,
                user_equity_id=equity.user_equity_id,
                asset=equity.cashback_asset,
                amount=total_tran_amount,
                his_ids=[i.id for i in eq_his_rows],
            )
            db.session.add(tran_his)

        db.session.commit()
        return eq_his_rows, tran_his

    @classmethod
    def transfer_by_tran_history(cls, row: UserCashbackEquityTransferHistory):
        from_user_id = config["EQUITY_CENTER_ADMIN_USER_ID"]
        to_user_id = row.user_id
        from_business = to_business = BalanceBusiness.EQUITY_CASHBACK
        bus_id = row.id
        asset_ = row.asset
        amount_ = row.amount

        client = ServerClient()
        remark = f"cashback_equity_his for {row.id}"
        if row.status == UserCashbackEquityTransferHistory.Status.CREATED:
            if amount_ > Decimal():
                try:
                    result = client.add_user_balance(
                        user_id=from_user_id,
                        asset=asset_,
                        amount=str(-amount_),
                        business=from_business,
                        business_id=bus_id,
                        detail={"remark": remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                    if not result:
                        current_app.logger.error(
                            f"equity_cashback_transfer_by_equity_history {row.id} {asset_} {amount_} "
                            f"from {from_user_id} to {to_user_id} deduct DUPLICATE_BALANCE_UPDATE"
                        )
                except Exception as e:
                    current_app.logger.error(
                        f"equity_cashback_transfer_by_equity_history {row.id} {asset_} {amount_} "
                        f"from {from_user_id} to {to_user_id} failed {e!r}"
                    )
                    # 不处理余额不足的情况
                    raise
            row.status = UserCashbackEquityTransferHistory.Status.DEDUCTED
            row.deducted_at = now()
            db.session.commit()

        if row.status == UserCashbackEquityTransferHistory.Status.DEDUCTED:
            if amount_ > Decimal():
                result = client.add_user_balance(
                    user_id=to_user_id,
                    asset=asset_,
                    amount=str(amount_),
                    business=to_business,
                    business_id=bus_id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
                if not result:
                    current_app.logger.error(
                        f"equity_cashback_transfer_by_equity_history {row.id} {asset_} {amount_} "
                        f"from {from_user_id} to {to_user_id} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            row.status = UserCashbackEquityTransferHistory.Status.FINISHED
            row.finished_at = now()
            db.session.commit()

    @classmethod
    def update_settle_fees_to_invalid(cls, ago_days: int = 3):
        """更新无效的手续费记录"""
        from app.business.sub_account import get_all_sub_main_info

        min_trade_time = now() - timedelta(days=ago_days)
        fee_rows: list[UserCashbackTradeFeeHistory] = UserCashbackTradeFeeHistory.query.filter(
            UserCashbackTradeFeeHistory.settle_his_id.is_(None),
            UserCashbackTradeFeeHistory.trade_time <= min_trade_time,
        ).with_entities(
            UserCashbackTradeFeeHistory.id,
            UserCashbackTradeFeeHistory.trade_user_id,
            UserCashbackTradeFeeHistory.trade_type,
            UserCashbackTradeFeeHistory.trade_time,
        ).all()
        if not fee_rows:
            return

        eq_rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                ]
            ),
        ).with_entities(
            UserCashbackEquity.user_id,
            UserCashbackEquity.cashback_scope,
        ).all()
        spot_user_ids = set()
        per_user_ids = set()
        for r in eq_rows:
            if r.cashback_scope in UserCashbackEquity.SPOT_SCOPES:
                spot_user_ids.add(r.user_id)
            if r.cashback_scope in UserCashbackEquity.PERPETUAL_SCOPES:
                per_user_ids.add(r.user_id)

        sub_main_map, main_subs_map = get_all_sub_main_info()
        invalid_fee_ids = []
        for fee_row in fee_rows:
            mid = sub_main_map.get(fee_row.trade_user_id, fee_row.trade_user_id)
            if fee_row.trade_type in cls.SPOT_FEE_TRADE_TYPES and mid not in spot_user_ids:
                invalid_fee_ids.append(fee_row.id)
            elif fee_row.trade_type in cls.PERPETUAL_FEE_TRADE_TYPES and mid not in per_user_ids:
                invalid_fee_ids.append(fee_row.id)

        for ch_ids in batch_iter(invalid_fee_ids, 5000):
            UserCashbackTradeFeeHistory.query.filter(
                UserCashbackTradeFeeHistory.id.in_(ch_ids),
            ).update(
                {UserCashbackTradeFeeHistory.settle_his_id: UserCashbackTradeFeeHistory.INVALID_SETTLE_HIS_ID},
                synchronize_session=False,
            )
            db.session.commit()

    @classmethod
    def update_settle_his_to_expired(cls, ago_days: int = 7):
        """结算记录过期"""
        min_time = now() - timedelta(days=ago_days)
        settle_rows: list[UserCashbackSettlementHistory] = UserCashbackSettlementHistory.query.filter(
            UserCashbackSettlementHistory.status == UserCashbackSettlementHistory.Status.SETTLING,
            UserCashbackSettlementHistory.settle_time <= min_time,
        ).with_entities(
            UserCashbackSettlementHistory.id,
            UserCashbackSettlementHistory.user_id,
            UserCashbackSettlementHistory.trade_type,
            UserCashbackSettlementHistory.settle_time,
        ).all()
        if not settle_rows:
            return

        eq_rows: list[UserCashbackEquity] = UserCashbackEquity.query.filter(
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                ]
            ),
        ).with_entities(
            UserCashbackEquity.user_id,
            UserCashbackEquity.cashback_scope,
        ).all()
        eq_spot_user_ids = set()
        eq_per_user_ids = set()
        for r in eq_rows:
            if r.cashback_scope in UserCashbackEquity.SPOT_SCOPES:
                eq_spot_user_ids.add(r.user_id)
            if r.cashback_scope in UserCashbackEquity.PERPETUAL_SCOPES:
                eq_per_user_ids.add(r.user_id)

        to_exp_settle_ids = []
        for settle_row in settle_rows:
            user_id = settle_row.user_id
            if settle_row.trade_type == UserCashbackSettlementHistory.TradeType.SPOT and user_id not in eq_spot_user_ids:
                to_exp_settle_ids.append(settle_row.id)
            elif settle_row.trade_type == UserCashbackSettlementHistory.TradeType.PERPETUAL and user_id not in eq_per_user_ids:
                to_exp_settle_ids.append(settle_row.id)

        for ch_ids in batch_iter(to_exp_settle_ids, 5000):
            UserCashbackSettlementHistory.query.filter(
                UserCashbackSettlementHistory.id.in_(ch_ids),
            ).update(
                {UserCashbackSettlementHistory.status: UserCashbackSettlementHistory.Status.EXPIRED},
                synchronize_session=False,
            )
            db.session.commit()


class CashbackTradeFeeCollector:
    """ 成交记录手续费-手动查表收集 """

    CHUNK_SIZE = 5000

    def __init__(self, start_time: datetime, end_time: datetime):
        self.start_time = start_time
        self.end_time = end_time

    @cached_property
    def amm_markets(self) -> set[str]:
        return CashbackSettleHelper.get_amm_markets()

    @cached_property
    def per_market_info_map(self) -> tuple[dict, dict]:
        online_market_info_map = PerpetualMarketCache().read_aside()
        offline_market_info_map = PerpetualOfflineMarketCache().read_aside()
        return online_market_info_map, offline_market_info_map

    def get_per_market_info(self, market: str) -> dict:
        online_market_info_map, offline_market_info_map = self.per_market_info_map
        info = online_market_info_map.get(market) or offline_market_info_map[market]
        return info

    @classmethod
    def log(cls, *args):
        current_app.logger.warning(f"CashbackTradeFeeCollector {args}")

    def query_users_spot_deal_fee(self, deal_table, user_ids: list[int], start_time: datetime, end_time: datetime) -> list[dict]:
        start_ts = int(start_time.timestamp())
        end_ts = int(end_time.timestamp())
        deal_user_id_str = ','.join(map(str, user_ids))
        deal_where = f'user_id in ({deal_user_id_str}) and time >= {start_ts} and time <= {end_ts} and fee>0 '
        deal_columns = ['user_id', 'deal_id', 'time', 'market', 'fee_asset', 'fee']
        deal_rows = deal_table.select(
            *deal_columns,
            where=deal_where,
        )
        result_map = dict()
        for user_id, deal_id, deal_time, market, fee_asset, fee_amount in deal_rows:
            if market in self.amm_markets:
                continue
            fee_amount = quantize_amount(fee_amount, 8)
            if fee_amount <= 0:
                continue

            fee_info = [market, fee_asset, fee_amount]

            res_key = (user_id, deal_id)
            if res_key not in result_map:
                item = {
                    'trade_user_id': user_id,
                    'trade_business_id': deal_id,
                    'trade_time': timestamp_to_datetime(deal_time),
                    'fees': [fee_info],
                }
                result_map[res_key] = item
            else:
                result_map[res_key]['fees'].append(fee_info)
        return list(result_map.values())

    def query_users_per_deal_fee(self, deal_table, user_ids: list[int], start_time: datetime, end_time: datetime) -> list[dict]:
        start_ts = int(start_time.timestamp())
        end_ts = int(end_time.timestamp())
        deal_user_id_str = ','.join(map(str, user_ids))
        deal_where = f'user_id in ({deal_user_id_str}) and time >= {start_ts} and time <= {end_ts} and deal_fee>0 '
        deal_columns = ['user_id', 'deal_id', 'time', 'market', 'fee_asset', 'deal_fee']
        deal_rows = deal_table.select(
            *deal_columns,
            where=deal_where,
        )
        result_map = dict()
        for user_id, deal_id, deal_time, market, fee_asset, fee_amount in deal_rows:
            fee_amount = quantize_amount(fee_amount, 8)
            if fee_amount <= 0:
                continue

            # 合约成交记录的fee_asset可能是空字符串
            market_info = self.get_per_market_info(market)
            fee_asset = fee_asset or PerpetualMarketCache.get_fee_asset(market_info)
            fee_info = [market, fee_asset, fee_amount]

            res_key = (user_id, deal_id)
            if res_key not in result_map:
                item = {
                    'trade_user_id': user_id,
                    'trade_business_id': deal_id,
                    'trade_time': timestamp_to_datetime(deal_time),
                    'fees': [fee_info],
                }
                result_map[res_key] = item
            else:
                result_map[res_key]['fees'].append(fee_info)
        return list(result_map.values())

    @classmethod
    def filter_by_user_trade_type_bus_id(cls, trade_keys: list[dict], trade_type: UserCashbackTradeFeeHistory.TradeType) -> list[dict]:
        """ 按user_id、trade_type、trade_business_id过滤已有Fee的记录 """
        trade_business_ids = [f['trade_business_id'] for f in trade_keys]
        exist_fee_keys = set()
        for ch_bus_ids in batch_iter(trade_business_ids, cls.CHUNK_SIZE):
            ch_exist_fee_rows = UserCashbackTradeFeeHistory.query.filter(
                UserCashbackTradeFeeHistory.trade_business_id.in_(ch_bus_ids),
                UserCashbackTradeFeeHistory.trade_type == trade_type,
            ).with_entities(
                UserCashbackTradeFeeHistory.trade_user_id,
                UserCashbackTradeFeeHistory.trade_business_id,
            ).all()
            exist_fee_keys.update([(i.trade_user_id, i.trade_business_id) for i in ch_exist_fee_rows])
        filtered_trade_keys = [i for i in trade_keys if (i['trade_user_id'], i['trade_business_id']) not in exist_fee_keys]
        return filtered_trade_keys

    @classmethod
    def save_trade_fees(cls, trade_fees: list[dict], trade_type: UserCashbackTradeFeeHistory.TradeType):
        new_fee_rows = []
        for fee_info in trade_fees:
            fee_row = UserCashbackTradeFeeHistory(
                **fee_info,
                trade_type=trade_type,
            )
            new_fee_rows.append(fee_row)
        db.session.bulk_save_objects(new_fee_rows)
        db.session.commit()
        return new_fee_rows

    @log_func_consume
    def collect_spot_deal(self, user_ids: set[int], start_time: datetime, end_time: datetime):
        all_deal_fees = []
        dbs_tables = TradeHistoryDB.users_to_dbs_and_tables(user_ids, table_name='user_deal_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table_name, _table_user_ids in db_tables[1].items():
                self.log(f"query_users_spot_deal_fee {_table_name} {len(_table_user_ids)} users")
                _deal_table = _db.table(_table_name)
                for _ch_table_user_ids in batch_iter(_table_user_ids, self.CHUNK_SIZE):
                    try:
                        ch_deal_fee = self.query_users_spot_deal_fee(
                            _deal_table, _ch_table_user_ids, start_time, end_time
                        )
                        all_deal_fees.extend(ch_deal_fee)
                    except Exception as __e:
                        self.log(f"query_users_spot_deal_fee {_table_name} error {__e!r}")

        trade_type = UserCashbackTradeFeeHistory.TradeType.SPOT
        miss_deal_fees = self.filter_by_user_trade_type_bus_id(all_deal_fees, trade_type)
        if miss_deal_fees:
            self.save_trade_fees(miss_deal_fees, trade_type)

    @log_func_consume
    def collect_perpetual_deal(self, user_ids: set[int], start_time: datetime, end_time: datetime):
        all_deal_fees = []
        dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, table_name='deal_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table_name, _table_user_ids in db_tables[1].items():
                self.log(f"query_users_per_deal_fee {_table_name} {len(_table_user_ids)} users")
                _deal_table = _db.table(_table_name)
                for _ch_table_user_ids in batch_iter(_table_user_ids, self.CHUNK_SIZE):
                    try:
                        ch_deal_fee = self.query_users_per_deal_fee(
                            _deal_table, _ch_table_user_ids, start_time, end_time
                        )
                        all_deal_fees.extend(ch_deal_fee)
                    except Exception as __e:
                        self.log(f"query_users_per_deal_fee {_table_name} error {__e!r}")

        trade_type = UserCashbackTradeFeeHistory.TradeType.PERPETUAL
        miss_deal_fees = self.filter_by_user_trade_type_bus_id(all_deal_fees, trade_type)
        if miss_deal_fees:
            self.save_trade_fees(miss_deal_fees, trade_type)

    @log_func_consume
    def collect_exchange_order(self, user_ids: set[int], start_time: datetime, end_time: datetime):
        exc_fees = []
        for order in yield_query_records_by_time_range(
                table=AssetExchangeOrder,
                start_time=start_time,
                end_time=end_time,
                select_fields=[
                    AssetExchangeOrder.id,
                    AssetExchangeOrder.created_at,
                    AssetExchangeOrder.user_id,
                    AssetExchangeOrder.status,
                ],
                limit=5000,
        ):
            if order.status != AssetExchangeOrder.Status.FINISHED:
                continue
            if order.user_id not in user_ids:
                continue
            exc_fees.append(
                {
                    'trade_user_id': order.user_id,
                    'trade_business_id': order.id,
                    'trade_time': order.created_at,
                    'fees': [],  # 占位
                }
            )

        trade_type = UserCashbackTradeFeeHistory.TradeType.EXCHANGE
        miss_exc_fees = self.filter_by_user_trade_type_bus_id(exc_fees, trade_type)
        if miss_exc_fees:
            exc_id_fee_info_map = {i['trade_business_id']: i for i in miss_exc_fees}
            for ch_exc_ids in batch_iter(exc_id_fee_info_map, self.CHUNK_SIZE):
                sys_rows = SysAssetExchangeOrder.query.filter(
                    SysAssetExchangeOrder.exchange_order_id.in_(ch_exc_ids),
                    SysAssetExchangeOrder.fee_amount > 0,
                ).with_entities(
                    SysAssetExchangeOrder.exchange_order_id,
                    SysAssetExchangeOrder.market,
                    SysAssetExchangeOrder.fee_asset,
                    SysAssetExchangeOrder.fee_amount,
                ).all()
                for sys in sys_rows:
                    if sys.market in self.amm_markets:
                        continue
                    fee_info = exc_id_fee_info_map[sys.exchange_order_id]
                    fee_info['fees'].append([sys.market, sys.fee_asset, sys.fee_amount])
            to_save_fees = [v for v in exc_id_fee_info_map.values() if v['fees']]
            self.save_trade_fees(to_save_fees, trade_type)

    @log_func_consume
    def collect(self):
        self.log(f"start_collect: {self.start_time} -> {self.end_time}")
        spot_user_map, per_user_map = CashbackSettleHelper.get_need_collect_fee_users()
        self._collect_by_users(per_user_map, spot_user_map)

    @log_func_consume
    def new_equity_user_collect(self):
        """ 补偿新权益用户的手续费，忽略不活跃的用户 """
        from app.business.user import filter_active_users

        spot_user_map, per_user_map = CashbackSettleHelper.get_time_range_need_collect_fee_users(self.start_time, self.end_time)
        self.log(f"collect_by_new_equity_user: {self.start_time} -> {self.end_time} spot: {len(spot_user_map)} per: {len(per_user_map)}")

        start_date, end_date = self.start_time.date(), self.end_time.date()
        active_user_ids = filter_active_users(start_date, end_date)
        active_user_ids.update(UserActivenessCache(end_date).get_users())
        day_before_end = end_date - timedelta(days=1)
        if day_before_end >= start_date:
            active_user_ids.update(UserActivenessCache(day_before_end).get_users())
        spot_user_map = {k: v for k, v in spot_user_map.items() if k in active_user_ids}
        per_user_map = {k: v for k, v in per_user_map.items() if k in active_user_ids}

        self.log(f"collect_by_new_equity_user: {self.start_time} -> {self.end_time} spot: {len(spot_user_map)} per: {len(per_user_map)}")
        self._collect_by_users(per_user_map, spot_user_map)

    def _collect_by_users(self, per_user_map: dict, spot_user_map: dict):
        spot_user_ids = set(spot_user_map)
        per_user_ids = set(per_user_map)
        if spot_user_ids:
            self.collect_exchange_order(spot_user_ids, self.start_time, self.end_time)
            self.collect_spot_deal(spot_user_ids, self.start_time, self.end_time)
        if per_user_ids:
            self.collect_perpetual_deal(per_user_ids, self.start_time, self.end_time)

    @classmethod
    def collect_one_exchange_order(cls, order: AssetExchangeOrder) -> bool:
        if order.status != AssetExchangeOrder.Status.FINISHED:
            return False
        need_spot, need_per = CashbackSettleHelper.user_is_need_collect_fee(order.user_id)
        if not need_spot:
            return False

        trade_type = UserCashbackTradeFeeHistory.TradeType.EXCHANGE
        trade_business_id = order.id
        exist_fee_row = UserCashbackTradeFeeHistory.query.filter(
            UserCashbackTradeFeeHistory.trade_business_id == trade_business_id,
            UserCashbackTradeFeeHistory.trade_type == trade_type,
        ).with_entities(
            UserCashbackTradeFeeHistory.trade_business_id,
        ).first()
        if exist_fee_row:
            return False

        sys_rows = SysAssetExchangeOrder.query.filter(
            SysAssetExchangeOrder.exchange_order_id == order.id,
            SysAssetExchangeOrder.fee_amount > 0,
        ).with_entities(
            SysAssetExchangeOrder.exchange_order_id,
            SysAssetExchangeOrder.market,
            SysAssetExchangeOrder.fee_asset,
            SysAssetExchangeOrder.fee_amount,
        ).all()
        if not sys_rows:
            return False

        fees = []
        amm_markets = CashbackSettleHelper.get_amm_markets()
        for sys in sys_rows:
            if sys.market in amm_markets:
                continue
            fees.append([sys.market, sys.fee_asset, sys.fee_amount])
        if fees:
            to_save_fees = [
                {
                    'trade_user_id': order.user_id,
                    'trade_business_id': trade_business_id,
                    'trade_time': order.created_at,
                    'fees': fees,
                }
            ]
            cls.save_trade_fees(to_save_fees, trade_type)
            return True
        return False


@celery_task
@lock_call()
def cashback_collect_exchange_order_fee_task(exchange_order_id: int):
    """ 收集一笔兑换订单的手续费 """
    exchange_order = AssetExchangeOrder.query.get(exchange_order_id)
    CashbackTradeFeeCollector.collect_one_exchange_order(order=exchange_order)


def retry_cashback_equity_settle_transfer():
    """ 返现划转重试 """
    now_ = now()
    start_dt = now_ - timedelta(hours=24)
    end_dt = now_ - timedelta(minutes=5)
    pending_statues = [UserCashbackEquityTransferHistory.Status.CREATED, UserCashbackEquityTransferHistory.Status.DEDUCTED]
    for row in yield_query_records_by_time_range(
        table=UserCashbackEquityTransferHistory,
        start_time=start_dt,
        end_time=end_dt,
        select_fields=[
            UserCashbackEquityTransferHistory.id,
            UserCashbackEquityTransferHistory.user_id,
            UserCashbackEquityTransferHistory.status,
        ],
        limit=5000,
    ):
        if row.status not in pending_statues:
            continue
        try:
            with CacheLock(key=LockKeys.cashback_equity_settle(row.user_id)):
                db.session.rollback()
                eq_tran_his: UserCashbackEquityTransferHistory = UserCashbackEquityTransferHistory.query.get(row.id)
                CashbackSettleHelper.transfer_by_tran_history(eq_tran_his)
        except Exception as _e:
            db.session.rollback()
            current_app.logger.exception(f"retry_cashback_equity_settle_transfer {row.id} {row.user_id} error: {_e!r}")
