# -*- coding: utf-8 -*-
from decimal import Decimal
from enum import Enum
from typing import Dict, Set

from app.common.constants import BalanceBusiness

from .base import ModelBase, db

    
class InterestType(Enum):
    BASE = "基础利息"
    LADDER = "阶梯利息"
    FIXED = "固定利息"
    
class InvStatisticTimeRange(Enum):
    DAYS_7 = 7
    DAYS_30 = 30
    DAYS_90 = 90
    DAYS_180 = 180
    DAYS_365 = 365
        
class AssetInvestmentConfig(ModelBase):
    """理财币种配置"""
    __tablename__ = "investment_account"

    # 单位 USD
    DEFAULT_MIN_AMOUNT = Decimal("10")
    ASSET_MIN_AMOUNT_MAP = {
        "BTC": Decimal("100"),
    }
    
    class ConfigType(Enum):
        LADDER = "阶梯年化补贴"
        FIXED = "固定年化补贴"
        
    class StatusType(Enum):
        OPEN = "开启"
        CLOSE = "关闭"

    __table_args__ = (
        db.UniqueConstraint('asset', name='uni_asset'),
    )
    ACCOUNT_ID = 20000

    account_id = db.Column(db.Integer, nullable=False, default=ACCOUNT_ID)  # 已废弃，线上都是 ACCOUNT_ID

    asset = db.Column(db.String(32), nullable=False, unique=True, comment="币种")
    """
    rule_ = {
        ConfigType.LADDER.name: {
            "limit": 0, # 年化补贴金额
            "rate": 0, # 年化补贴利率
        },
        ConfigType.FIXED.name: {
            "rate": 0, # 年化补贴利率
        },
    }
    """
    rule_map = db.Column(db.JSON, default=dict(), nullable=False, comment="配置")
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.CLOSE, comment="状态")
    remark = db.Column(db.Text, nullable=True, comment="备注")

    # todo 将这2个更新频繁的数据放到新表
    min_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal(), comment="最小申购数量")
    base_rate = db.Column(db.MYSQL_DECIMAL_26_8, default=Decimal(), comment="最新小时基础利率")

    @classmethod
    def get_valid_assets(self) -> Set[str]:
        return {v.asset for v in self.query.filter(
            self.status == self.StatusType.OPEN
        ).with_entities(self.asset).all()}

    @classmethod
    def ladder_rule_show(cls, ladder_rule: Dict, is_sub_account: bool = False) -> Dict | None:
        if not is_sub_account and ladder_rule and Decimal(ladder_rule.get("limit", 0)) > 0 and Decimal(ladder_rule.get("rate", 0)) > 0:
            return ladder_rule
        # app 希望返回 None
        return None
    
    @classmethod
    def get_open_configs(cls):
        return cls.query.filter(cls.status == cls.StatusType.OPEN).all()


class InvestmentTransferHistory(ModelBase):
    """
    用户的理财划转记录
    """
    __tablename__ = "investment_balance_history"
    
    class StatusType(Enum):
        CREATE = 'create'
        SUCCESS = "success"
        DEDUCTED = 'deducted'
        FAIL = "fail"

    class OptType(Enum):
        IN = "in"
        OUT = "out"  # amount为负
        INTEREST = "interest" # 已废弃，使用 UserDayInterestHistory 记录每日收益    # todo 带 ID 迁移

    __table_args__ = (
        db.UniqueConstraint('user_id', 'asset', 'report_date', name='user_id_asset_report_date'),
        db.Index("idx_created_at", "created_at"),
        db.Index("idx_user_id_opt_type_status", "user_id", "opt_type", "status"),
        db.Index("idx_user_id_opt_type_status_created_at", "user_id", "opt_type", "status", "created_at"),
    )

    success_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    # 用户发放利息的日息，做mysql数据库的唯一约束，保证不重复发
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))

    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    opt_type = db.Column(db.Enum(OptType), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)

    # 已废弃
    day_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())
    report_date = db.Column(db.Date, nullable=True, index=True)
    investment_account_id = db.Column(db.Integer, nullable=False)

    
    def get_trans_business(self):
        return BalanceBusiness.INVESTMENT_IN if self.opt_type == self.OptType.IN else BalanceBusiness.INVESTMENT_OUT
    
    
class UserHourInterestHistory(ModelBase):
    """每小时用户利息记录"""
    
    __table_args__ = (
        db.UniqueConstraint('report_hour', 'user_id', 'asset', name='uni_report_hour_user_id_asset'),
    )
    
    id = db.Column(db.BigInteger, primary_key=True)
    report_hour = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="计息小时")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    asset = db.Column(db.String(32), nullable=False, index=True, comment="理财币种")
    interest_asset = db.Column(db.String(32), nullable=False, comment="利息币种")
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="利息数量")
    
    
class UserHourInterestDetail(ModelBase):
    """每小时用户利息详情"""

    __table_args__ = (
        db.UniqueConstraint("report_hour", "user_id", "asset", "interest_type", name="uni_report_hour_user_id_asset_interest_type"),
    )
        
    id = db.Column(db.BigInteger, primary_key=True)
    report_hour = db.Column(db.MYSQL_DATETIME_6, nullable=False, comment="计息小时")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    asset = db.Column(db.String(32), nullable=False, index=True, comment="理财币种")
    
    interest_type = db.Column(db.Enum(InterestType), nullable=False, comment="利息类型")
    interest_asset = db.Column(db.String(32), nullable=False, comment="利息币种")
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="利息金额")
    
    rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="规则利率")
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="规则余额")
    
class UserDayInterestHistory(ModelBase):
    """用户每日利息记录"""

    class Status(Enum):
        PENDING = "待发放"      # 待发放
        SUCCESS = "发放成功"      # 发放成功
        
    __table_args__ = (
        db.UniqueConstraint('report_date', 'user_id', 'asset', name='uni_report_date_user_id_asset'),
    )
    
    id = db.Column(db.BigInteger, primary_key=True)
    report_date = db.Column(db.Date, nullable=False, comment="计息日期")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    asset = db.Column(db.String(32), nullable=False, index=True, comment="理财币种")
    interest_asset = db.Column(db.String(32), nullable=False, comment="利息币种")
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="利息数量")
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PENDING, comment="发放状态")
    payout_at = db.Column(db.MYSQL_DATETIME_6, nullable=True, comment="实际发放时间")

    
class UserDayInterestDetail(ModelBase):
    """用户每日利息详情"""
        
    __table_args__ = (
        db.UniqueConstraint('report_date', 'user_id', 'asset', 'interest_type', name='uni_report_date_user_id_asset_interest_type'),
    )
    
    id = db.Column(db.BigInteger, primary_key=True)
    report_date = db.Column(db.Date, nullable=False, index=True, comment="计息日期")
    user_id = db.Column(db.Integer, nullable=False, index=True, comment="用户ID")
    asset = db.Column(db.String(32), nullable=False, index=True, comment="理财币种")

    interest_type = db.Column(db.Enum(InterestType), nullable=False, comment="利息类型")
    interest_asset = db.Column(db.String(32), nullable=False, comment="利息币种")
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="利息数量")


class InterestStatisticTime(ModelBase):
    """理财任务时间表，记录当前利息任务进度"""

    class BusType(Enum):
        SITE = "站内理财"
        INCREASE = "理财加息权益"

    hour_interest_time = db.Column(db.MYSQL_DATETIME_6, comment="小时利息统计时间")
    day_interest_date = db.Column(db.Date, comment="日利息统计时间")
    day_payout_date = db.Column(db.Date, comment="日利息发放时间")
    bus_type = db.Column(db.StringEnum(BusType), nullable=False, default=BusType.SITE, comment="业务类型")


class UserInvestmentSummary(ModelBase):
    """
    !!!已废弃，使用 NewUserInvestmentSummary 代替
    """

    __table_args__ = (db.UniqueConstraint("user_id", "asset", name="user_id_asset_uniq"),)

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))

    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class NewUserInvestmentSummary(ModelBase):
    """
    用户累计理财收益汇总
    """

    class InvType(Enum):
        # 理财类型
        CURRENT = "活期理财"
        COUPON = "理财加息卷"
        EQUITY = "活期加息权益"

    __table_args__ = (db.UniqueConstraint("user_id", "asset", "inv_type", name="user_id_asset_inv_type_uniq"),)

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)

    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    inv_type = db.Column(db.StringEnum(InvType), nullable=False, comment="理财类型")


class InvestmentUserAMMSlice(ModelBase):
    """
    投资账号AMM持仓统计
    """

    __table_args__ = (db.UniqueConstraint("report_date", "market", name="report_date_market_uniq"),)

    report_date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    base_asset = db.Column(db.String(32), nullable=False)
    quote_asset = db.Column(db.String(32), nullable=False)
    online_days = db.Column(db.Integer, nullable=False, default=0)
    base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_cost = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    liquidity_usd_profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_rate_7d = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    profit_rate_30d = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentUserSpotSlice(ModelBase):
    """
    投资账号现货持仓统计
    """

    __table_args__ = (db.UniqueConstraint("report_date", "asset", name="report_date_asset_uniq"),)

    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)
    online_days = db.Column(db.Integer, nullable=False, default=0)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    avg_price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 购买均价
    profit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class InvestmentUserAMMMarketRemark(ModelBase):
    """投资账号AMM市场备注"""

    market = db.Column(db.String(32), nullable=False, unique=True)
    remark = db.Column(db.Text, nullable=False)


class InvestmentUserSpotAssetRemark(ModelBase):
    """投资账号现货资产备注"""

    asset = db.Column(db.String(32), nullable=False, unique=True)
    remark = db.Column(db.Text, nullable=False)
