# -*- coding: utf-8 -*-
import json
import random
import re
from enum import Enum
from functools import wraps
from logging import getLogger
from typing import Type, TypeVar, Any, Literal, NamedTuple

from dateutil.tz import UTC
from bson import ObjectId
from flask import g
from flask_sqlalchemy import BaseQuery, Model
from flask_sqlalchemy import SQLAlchemy as _SQLAlchemy
from pydantic import BaseModel
from sqlalchemy import TypeDecorator, func, types, column, event
from sqlalchemy.dialects.mysql import (DATETIME, DECIMAL, JSON, MEDIUMBLOB,
                                       MEDIUMTEXT, TINYINT)
from sqlalchemy.inspection import inspect as sql_inspect
from sqlalchemy.orm import Session, scoped_session, sessionmaker
from contextlib import contextmanager

from sqlalchemy.sql import visitors
from sqlalchemy.sql.elements import BinaryExpression
from sqlalchemy.sql.operators import in_op

from ..utils import now, batch_iter
from app.utils.parser import JsonEncoder

_logger = getLogger(__name__)


# noinspection PyAbstractClass
class DateTimeUTC(TypeDecorator):

    impl = DATETIME
    cache_ok = True

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return value.replace(tzinfo=UTC)


# noinspection PyAbstractClass
class StringEnum(TypeDecorator):
    impl = types.Enum

    cache_ok = True

    limit_length = 64

    def __init__(self, *args, **kwargs):
        kwargs.update(native_enum=False, length=self.limit_length)
        super(StringEnum, self).__init__(*args, **kwargs)

    def process_bind_param(self, value, dialect):
        if value is None:
            # Keep the same as Enum._enum_init._valid_lookup
            return value

        if isinstance(value, str):
            enum_names = [v.name for v in self.enum_class]
            if value not in enum_names:
                raise ValueError
        elif isinstance(value, self.enum_class):
            value = value.name
        else:
            raise ValueError

        self._validate_length(value)
        return value

    def _validate_length(self, value):
        if len(value) > self.limit_length:
            raise ValueError(f'Expected length is {self.limit_length}')


class RichJSON(TypeDecorator):
    impl = JSON
    cache_ok = True

    model = None
    def __init__(self, *args, **kwargs):
        """
        初始化 RichJSON
        
        Args:
            *args: 第一个参数可以是 Pydantic 模型类，用于序列化和反序列化
            **kwargs: 传递给父类的关键字参数
        """
        # 检查第一个参数是否是 Pydantic 模型类
        if args and issubclass(args[0], BaseModel):
            self.model = args[0]
            args = args[1:]

        super().__init__(*args, **kwargs)

    def process_bind_param(self, value, dialect):
        """将 Python 对象处理为 python内置类型（在保存到数据库之前）"""
        if value is None:
            return None

        # 如果指定了存储的子文档类型，将之处理为 Python 内置类型
        if self.model is not None:
            if isinstance(value, list):
                # 如果是列表，对每个元素使用模型处理
                value = [
                    item.model_dump() if isinstance(item, self.model)
                    else self.model(**item).model_dump()
                    for item in value
                ]
            else:
                # 如果是单个对象
                value = (
                    value.model_dump() if isinstance(value, self.model)
                    else self.model(**value).model_dump()
                )
        # JSON 类型会再进行一次 json 序列化，因此，这里只需要处理成内置类型即可
        return json.loads(json.dumps(value, cls=JsonEncoder))

    def process_result_value(self, value, dialect):
        """将 JSON 格式的数据反序列化为 Python 对象（从数据库读取之后）"""
        if value is None:
            return None

        if self.model is not None:
            if isinstance(value, list):
                # 如果是列表，对每个元素使用模型处理
                return [self.model(**item) for item in value]
            else:
                # 如果是单个对象
                return self.model(**value)
        return value


T = TypeVar('T', bound=Model)


class SQLAlchemy(_SQLAlchemy):

    MYSQL_DATETIME_6 = DateTimeUTC(fsp=6)
    MYSQL_DECIMAL_26_8 = DECIMAL(precision=26, scale=8)  # 此类型用于加密货币(资产、交易量、手续费等)
    MYSQL_DECIMAL_PRICE = DECIMAL(precision=26, scale=12)  # 只用于价格
    MYSQL_DECIMAL_PRICE_NEW = DECIMAL(precision=26, scale=16)  # 增加精度只用于价格
    MYSQL_BOOL = TINYINT(display_width=1)
    MYSQL_MEDIUM_TEXT = MEDIUMTEXT
    MYSQL_MEDIUM_BLOB = MEDIUMBLOB
    MYSQL_JSON = RichJSON
    StringEnum = StringEnum

    session: Session

    def _wrap_get_engine_read_only(self, get_engine_vanilla):
        """
        Read-Write separation:
        For read-only operations, we use the slave mysql as additional binds.
        We use the flask app scope as the holder of the application context, so we can
        inspecify how we want the engine selected by sqlalchemy as follows:
        - bind_key(str): if we want to specify a bind, set it with current_app.g.bind_key
        - read_only(bool): if we want to enable read-only mode, set it with current_app.g.read_only
        """
        @wraps(get_engine_vanilla)
        def _inner_wrapped(app=None, bind_key=None):
            # get app if it's not provided when use db.engine forcely
            if not app:
                app = self.get_app(app)
            assert app is not None

            # list all registered binds
            binds = app.config.get("SQLALCHEMY_BINDS") or {}
            bind_keys = list(binds.keys())

            if bind_key is None:
                bind_key = getattr(g, "bind_key", None)
            # if bind_key is not specified, pick a random bind
            if (bind_key not in bind_keys) and (getattr(g, "read_only", False)):
                bind_key = random.choice(bind_keys)
            return get_engine_vanilla(app, bind_key)

        return _inner_wrapped

    def init_app(self, app):
        super().init_app(app)

        # ensure the SQLAlchemy extension is initialized
        assert hasattr(app, "extensions")
        assert "sqlalchemy" in app.extensions

        # rewrite the engine to use the read-only engine
        db = app.extensions["sqlalchemy"].db
        db.get_engine = self._wrap_get_engine_read_only(db.get_engine)

    def session_add_and_commit(self, obj: T) -> T:
        self.session.add(obj)
        self.session.commit()
        return obj

    def session_add_and_flush(self, obj: T) -> T:
        self.session.add(obj)
        self.session.flush()
        return obj


class BatchQuery(BaseQuery):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.batch_size = 500  # 默认分批大小

    def set_batch_size(self, batch_size: int) -> 'BatchQuery':
        """设置 IN 语句的分批大小"""
        self.batch_size = batch_size
        return self

    @classmethod
    def find_largest_condition(cls, where_clause):
        """找到最大的 in_ 条件"""
        max_length = 0
        largest_condition = None

        # 使用 iterate 遍历所有节点
        for element in visitors.iterate(where_clause):
            if isinstance(element, BinaryExpression) and element.operator is in_op:
                values = element.right.value
                if len(values) > max_length:
                    max_length = len(values)
                    largest_condition = element
        return largest_condition

    def batch_all(self) -> list[Any]:
        """分批处理 IN 语句, 这里只支持简单查询，sum，group by等聚合语句，请勿使用"""
        largest_condition = self.find_largest_condition(self.whereclause)

        if largest_condition is None:
            raise ValueError

        target_element = largest_condition
        in_values = target_element.right.value

        for batch_values in batch_iter(in_values, self.batch_size):

            def replace_func(element):
                if element is target_element:  # 直接对象比较
                    return target_element.left.in_(batch_values)
                return None  # 不替换其他元素

            new_where_clause = visitors.replacement_traverse(
                self.whereclause,
                {},  # options
                replace_func
            )
            cloned_query = self._clone()
            cloned_query._where_criteria = (new_where_clause,)
            yield from cloned_query.all()


class Query(BaseQuery):
    RE_GROUP_BY = re.compile(r'\bGROUP BY\b')
    RE_HAVING = re.compile(r'\bHAVING\b')

    class Pagination(NamedTuple):
        items: list
        has_next: bool
        cursor: Any | None

    def count(self):
        r"""
        获取查询结果的数量。
        这里覆盖了flask_sqlalchemy的实现，因为原实现用嵌套子查询，会导致性能问题，
        所以通过分析原查询的条件，构造一个新的查询来实现。
        （当查询条件过于复杂，无法试用 func.count 构造时，再退化到原实现。）
        """
        query = self
        # 如果查询包含distinct，暂时不支持，退化到 flask-sqlalchemy 的原始版本（子查询嵌套）
        state_str = str(query.statement).upper()
        if ' DISTINCT ' in state_str:
            return super().count()

        # 如果查询包含 group by，暂时直接退化到 flask-sqlalchemy 的原始版本（子查询嵌套）
        # 这里本来是可以把 group_by 字句对应的 count_query，改写成 count(distinct A, B, C)
        # 模式的，但 func.distinct() 的实现，会把生成的 distinct 语句，加上括号，
        # 变成 count(distinct(A, B, C))，导致  Mysql 报错，暂时退化
        if self.RE_GROUP_BY.search(state_str):
            return super().count()

        count_query = query.with_entities(func.count('*'))

        count_query = count_query.order_by(None)

        if len(count_query.selectable.froms) == 0:
            # 如果没有 self._query 没有指定任何 filter，那么设置 entities 为 count(*)，会导致 sum_query 失去来源表信息
            count_query = query.with_entities(func.count(query.selectable.froms[0].columns['id'])).order_by(None)
        return count_query.scalar()


    def cursor_paginate(self, cursor: Any | None, limit: int,
                          cursor_field: str = "id",
                          sort: Literal["asc", "desc"] = "desc") -> Pagination:
        if limit < 1:
            raise ValueError
        if cursor is None:
            query = self
        else:
            if sort == "desc":
                query = self.filter(column(cursor_field) < cursor)
            else:
                query = self.filter(column(cursor_field) > cursor)
        if not query._order_by_clauses:
            if sort == "desc":
                query = query.order_by(column(cursor_field).desc())
            else:
                query = query.order_by(column(cursor_field).asc())

        items = query.limit(limit + 1).all()
        has_next = len(items) > limit
        items = items[:limit]
        if items:
            next_cursor = getattr(items[-1], cursor_field)
        else:
            next_cursor = cursor
        return self.Pagination(items=items, has_next=has_next, cursor=next_cursor)


    def infinite_paginate(self, page: int, limit: int) -> Pagination:
        if page < 1 or limit < 1:
            raise ValueError
        items = self.limit(limit).offset((page - 1) * limit).all()
        has_next = len(items) == limit
        return self.Pagination(items=items, has_next=has_next, cursor=None)


# 自定义模型基类，添加 batch_query 属性
class BatchQueryModel(Model):
    @classmethod
    def batch_query(cls):
        """返回 BatchQuery 实例"""
        return BatchQuery(cls, session=cls.query.session)


db = SQLAlchemy(query_class=Query, model_class=BatchQueryModel)


class _Session(Session):

    def __del__(self):
        self.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def new_session() -> _Session:
    return scoped_session(sessionmaker(class_=_Session, bind=db.engine))()


def read_only_inspector_session(session):
    @event.listens_for(session, "persistent_to_detached")
    @event.listens_for(session, "persistent_to_deleted")
    @event.listens_for(session, "persistent_to_transient")
    def deref_object(sess, instance):
        if "refs" not in sess.info:
            return
        sess.info["refs"].discard(instance)

    @event.listens_for(session, "before_commit")
    def check_read_only_objects_changes(sess):
        if "refs" not in sess.info:
            return
            
        # 检查只读数据实例是否有变动，如果修改了只读实例，则直接报错
        for instance in sess.info["refs"]:
            if sql_inspect(instance).modified:
                raise RuntimeError(f"read_only ins {type(instance).__name__} (id={getattr(instance, 'id', None)}) changed! commit denied!")


def _register_read_only_instance(sess, instance):
    if "refs" not in sess.info:
        sess.info["refs"] = refs = set()
    else:
        refs = sess.info["refs"]

    refs.add(instance)


@contextmanager
def read_only_session(allow_update=False):
    """
    使用读库的会话上下文管理器
    需要注意以下几点：
    1. 必须使用创建的 session 来执行查询，不能用 model.query 这种方式，具体见以下示例代码；
    2. 在上下文管理器外，不用特别关注 session 管理，以及 数据实例的手动迁移；
    3. 可以在分散的逻辑中使用多次上下文管理器，不用担心额外的 session 管理开销；
    4. 查询到的只读数据实例不能修改，如果需要修改，需要通过参数 allow_update 参数来指定；

    Example:
        # 获得可写的数据实例，默认不可修改
        with read_only_session(allow_update=True) as ro_session:
            test_user = ro_session.query(User).first()
            print(test_user.name)
        
        # 上述获取了可修改实例才允许 commit
        test_user.name = "new_name"
        db.session.commit()
    """
    read_write_session = db.session
    read_write_session.connection()  # 如果没有创建链接的话，触发立即建立连接，避免在 read_only 启用后才触发建立
    # 1.1 创建只读 session
    read_only_session = getattr(g, "read_only_session", None)
    if not read_only_session:
        g.read_only = True
        read_only_session = g.read_only_session = db.create_session({"query_cls": db.Query})()
    # 1.2 注册时间监听，当 只读实例修改时根据参数决定是否抛出异常
    read_only_inspector_session(read_write_session)

    try:
        yield read_only_session
    finally:
        # 2.1 将 只读session 的数据实例转移到 读写 session
        for ins in list(read_only_session):
            # 从 只读session 移除查询到的实例，并添加到 读写session 中
            read_only_session.expunge(ins)
            read_write_session.add(ins)
            # 记录只读实例，以备检查是否修改只读实例
            if not allow_update:
                _register_read_only_instance(read_write_session, ins)
        # 2.2 还原到读写模式
        g.read_only = False


def row_to_dict(row, *,
                with_hook: bool = True,
                enum_to_name: bool = False):
    result = {(name := col.name):
              (v.name
               if isinstance(v := getattr(row, name), Enum) and enum_to_name
               else v)
              for col in type(row).__table__.columns}

    if with_hook:
        hook = getattr(row, '_row_to_dict_hook_', None)
        if hook is not None:
            # noinspection PyBroadException
            try:
                hook(result)
            except Exception:
                pass

    return result


def get_primary_key(model: Type[db.Model]) -> str:
    return sql_inspect(model).primary_key[0].name


class ModelBase(db.Model):
    __abstract__ = True

    id = db.Column(db.Integer, primary_key=True)

    created_at = db.Column(db.MYSQL_DATETIME_6, default=now)
    updated_at = db.Column(db.MYSQL_DATETIME_6, default=now, onupdate=now)

    def to_dict(self, *, with_hook: bool = True, enum_to_name: bool = False):
        return row_to_dict(self, with_hook=with_hook,
                           enum_to_name=enum_to_name)

    @classmethod
    def get_or_create(cls, auto_commit=False, **kwargs) -> T:
        filters = [getattr(cls, k) == v for k, v in kwargs.items()]
        if record := cls.query.filter(*filters).first():
            return record
        record = cls()
        for k, v in kwargs.items():
            setattr(record, k, v)
        if auto_commit:
            return db.session_add_and_commit(record)
        return record


class M2MModelBase(ModelBase):
    __abstract__ = True
    
    mongo_id = db.Column(db.String(32), unique=True, nullable=False)

    def __init__(self, *args, **kwargs):
        # 由于新的mysql数据记录是不会有 mongo_id 的，但之前的逻辑都是基于 mongo_id 的
        # 所以，这里在初始化实例的时候自动生成一个 mongo_id，用于兼容旧的数据表
        if 'mongo_id' not in kwargs:
            kwargs["mongo_id"] = str(ObjectId())
        return super().__init__(*args, **kwargs)

    def save(self):
        db.session.add(self)
        db.session.commit()
        return self

__all__ = 'db', 'new_session', 'row_to_dict', 'get_primary_key', 'ModelBase'
