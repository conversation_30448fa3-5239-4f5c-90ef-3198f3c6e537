# -*- coding: utf-8 -*-

from enum import Enum
from .base import ModelBase, db
from app.common.constants import Language


class Video(ModelBase):
    """用于视频管理"""
    class Platform(Enum):
        ALL = "all"
        WEB = "web"
        APP = "app"

    class Usage(Enum):
        TUTORIAL = 'tutorial'   # 教程（用户教育）

    class Business(Enum):
        # 视频展示位置
        PERPETUAL_TRADE = "perpetual_trade"
        SPOT_TRADE = "spot_trade"
        MARGIN_TRADE = "margin_trade"
        THIRD_PARTY_BUY = "third_party_buy"
        THIRD_PARTY_SELL = "third_party_sell"
        WITHDRAWAL = "withdrawal"
        DEPOSIT = "deposit"
        SWAP = "swap"
        FINANCIAL = "financial"
        AMM = "amm"
        AMM_KNOW_HOW = "amm_know_how"
        AMBASSADOR_INFO = "ambassador_info"
        AMBASSADOR_AGENT = "ambassador_agent"
        P2P = "p2p"
        PRE_MARKET = "pre_market"
        SPOT_GRID = "spot_grid"
        AUTO_INVEST = "auto_invest"
        STAKING = "staking"
        LEAD_TRADER = "lead_trader"
        COPY_TRADER = "copy_trader"
        PLEDGE_TOPIC_TOP = "pledge_topic_top"
        PLEDGE_ASSET_SIDE = "pledge_asset_side"
        INVESTMENT_TOPIC_TOP = "investment_topic_top"
        FUTURES_GUIDE_WEB_NEWBIE = "futures_guide_web_newbie"
        MARGIN_GUIDE_WEB_NEWBIE = "margin_guide_web_newbie"

        # 新增的交易指引类型
        SPOT_GUIDE_APP = "spot_guide_app"          # 现货-App功能区-交易指引-现货
        MARGIN_GUIDE_APP = "margin_guide_app"      # 现货-App功能区-交易指引-杠杆
        FUTURES_GUIDE_APP = "futures_guide_app"    # 合约-App功能区-交易指引

    user_id = db.Column(db.Integer)
    file_id = db.Column(db.Integer, nullable=False)
    file_key = db.Column(db.String(128), nullable=False)
    cover_key = db.Column(db.String(128), nullable=True)   # 封面图文件
    name = db.Column(db.String(128), nullable=False)  # 视频名称
    usage = db.Column(db.StringEnum(Usage), nullable=True, default=None)
    platform = db.Column(db.StringEnum(Platform), nullable=True, default=None)
    business = db.Column(db.StringEnum(Business), nullable=True, default=None)  # 视频展示位置
    lang = db.Column(db.StringEnum(Language), nullable=False, default=Language.EN_US)


class VideoSubtitle(ModelBase):
    """视频字幕"""
    __table_args__ = (
        db.UniqueConstraint(
            'video_id', 'lang',
            name='vidio_subtitle_lang_unique'),
    )
    video_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False, default=Language.EN_US)  # 语言代码，如 'en_US', 'zh_Hans_CN'
    file_id = db.Column(db.Integer, nullable=False)
    file_key = db.Column(db.String(128), nullable=False)  # 字幕文件的存储键
    is_translated = db.Column(db.Boolean, nullable=False, default=False)  # 是否为自动生成的字幕

