from flask import g
from webargs import fields

from app.api.common.request import require_user_request_permission
from app.business import <PERSON><PERSON><PERSON><PERSON>, Lock<PERSON><PERSON><PERSON>
from app.business.security import update_security_statistics, SecuritySettingType
from app.exceptions import InvalidArgument, RecordNotFound

from app.api.common import Resource, require_2fa, require_login
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common.fields import EnumField

from app.common.constants import MobileCodeType
from app.models import db, UserEmergencyContact
from app.utils import max_length_validator, validate_email

ns = Namespace('Emergency Contact')


@ns.route('')
@respond_with_code
class EmergencyContactResource(Resource):
    model = UserEmergencyContact

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """紧急联系人-获取"""
        model = cls.model
        row = model.query.filter(
            model.user_id == g.user.id,
            model.status == model.Status.VALID
        ).first()
        if not row:
            return {}
        return {
            'name': row.name,
            'email': row.email,
            'dormancy_period': row.dormancy_period.name,
            'user_message': row.user_message,
        }

    @classmethod
    @require_2fa(MobileCodeType.EMERGENCY_CONTACT, allow_sub_account=False)
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        email=fields.String(required=True),
        user_message=fields.String(required=False, validate=max_length_validator(600)),
        dormancy_period=EnumField(UserEmergencyContact.DormancyPeriod, required=True),
    ))
    def post(cls, **kwargs):
        """紧急联系人-添加"""
        email = kwargs['email']
        if not validate_email(email):
            raise InvalidArgument
        require_user_request_permission(g.user)
        model = cls.model
        user_id = g.user.id
        with CacheLock(LockKeys.emergency_contact(user_id)):
            db.session.rollback()
            row = model.query.filter(
                model.user_id == user_id,
                model.status == model.Status.VALID
            ).first()
            if row:
                raise InvalidArgument
            row = model(
                user_id=user_id,
                name=kwargs['name'],
                email=email,
                dormancy_period=kwargs['dormancy_period'],
                user_message=kwargs.get('user_message', ''),
            )
            db.session_add_and_commit(row)
            update_security_statistics([user_id], SecuritySettingType.EMERGENCY_CONTACT)
            return {}

    @classmethod
    @require_2fa(MobileCodeType.EMERGENCY_CONTACT, allow_sub_account=False)
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        email=fields.String(required=True),
        user_message=fields.String(required=True, valdate=max_length_validator(600)),
        dormancy_period=EnumField(UserEmergencyContact.DormancyPeriod, required=True),
    ))
    def put(cls, **kwargs):
        """紧急联系人-修改"""
        email = kwargs['email']
        if not validate_email(email):
            raise InvalidArgument
        require_user_request_permission(g.user)
        with CacheLock(LockKeys.emergency_contact(g.user.id)):
            db.session.rollback()
            row = cls._get_row(g.user.id)
            row.name = kwargs['name']
            row.email = email
            row.user_message = kwargs['user_message']
            row.dormancy_period = kwargs['dormancy_period']
            db.session.commit()
            update_security_statistics([row.user_id], SecuritySettingType.EMERGENCY_CONTACT)


    @classmethod
    @require_2fa(MobileCodeType.EMERGENCY_CONTACT, allow_sub_account=False)
    def delete(cls):
        """紧急联系人-删除"""
        model = cls.model
        with CacheLock(LockKeys.emergency_contact(g.user.id)):
            db.session.rollback()
            row = cls._get_row(g.user.id)
            row.status = model.Status.DELETED
            db.session.commit()
            update_security_statistics([row.user_id], SecuritySettingType.EMERGENCY_CONTACT)

    @classmethod
    def _get_row(cls, user_id: int) -> UserEmergencyContact:
        model = cls.model
        row = model.query.filter(
            model.user_id == user_id,
            model.status == model.Status.VALID
        ).first()
        if not row:
            raise RecordNotFound
        return row
