import json
import uuid
from decimal import ROUND_UP, Decimal

from flask import g, current_app
from marshmallow import fields as mm_fields
from flask_babel import gettext as _

from ..common import Resource, Namespace, respond_with_code, require_login, require_2fa
from ..common.fields import PositiveDecimalField, AssetField, EnumField
from ..common.request import require_user_request_permission
from ...business import WalletClient, PriceManager, CacheLock, LockKeys
from ...business.direct_fiat import (get_direct_fiat_partners, get_direct_fiat_partner_client, Quote)
from ...business.direct_fiat.base import SupportType
from ...business.direct_fiat.helper import <PERSON><PERSON><PERSON><PERSON><PERSON>ell<PERSON>rder<PERSON>elper, DirectFiatBuyOrderHelper
from ...caches.direct_fiat import DirectFiatQuoteCache
from ...common import MobileCodeType
from ...common.fiat import DirectFiatPaymentMethod
from ...exceptions import InvalidArgument, OperationNotAllowed
from ...models import db, User, Withdrawal
from ...models.direct_fiat import DirectFiatOrder
from ...utils import quantize_amount, now, amount_to_str
from ...utils.parser import JsonEncoder

ns = Namespace('Direct Fiat')


@ns.route('/<partner>/validation')
@respond_with_code
class DirectFiatValidationResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        type=EnumField(SupportType, missing=SupportType.BUY)
    ))
    def get(cls, partner, **kwargs):
        """目前是用于获取 swapix 所需的校验信息"""
        client = get_direct_fiat_partner_client(partner, kwargs['type'])
        if not client:
            raise InvalidArgument
        match client.name:
            case 'Swapix':
                return cls._swapix_info(client)
            case _:
                raise InvalidArgument

    @classmethod
    def _swapix_info(cls, client):
        user = g.user
        cpf_status = cpf_loc = None
        cpf_info = client.get_cpf_status(user)
        if cpf_info:
            cpf_status, cpf_loc = cpf_info['status'], cpf_info['location_code']
        return {
            'kyc_status': user.kyc_status.name,
            'kyc_type': user.kyc_type.name,
            'has_mobile': bool(user.mobile),
            'cpf_status': cpf_status,
            'cpf_loc': cpf_loc,
        }


@ns.route('/partners')
@respond_with_code
class DirectFiatPartners(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(SupportType, missing=SupportType.BUY)
    ))
    def get(cls, **kwargs):
        support_type = SupportType(kwargs['type'])
        result = []
        for partner in get_direct_fiat_partners():
            c = get_direct_fiat_partner_client(partner, support_type)
            if not c:
                continue
            item = dict(
                name=c.name,
                assets=getattr(c, f"{support_type.value}_assets"),
                fiats=getattr(c, f"{support_type.value}_fiats"),
                payment_methods=[{'name': x.value, 'logo': x.logo}
                                 for x in getattr(c, f"{support_type.value}_payment_methods")],
                quote_methods=getattr(c, f'{support_type.value}_quote_methods'),
                order_limit_min=getattr(c, f"{support_type.value}_order_limit_min"),
                order_limit_max=getattr(c, f"{support_type.value}_order_limit_max"),
            )
            result.append(item)

        return dict(
            partners=result
        )


@ns.route('/<partner>/quotes')
@respond_with_code
class DirectFiatQuoteResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs({
        'from': mm_fields.String(required=True),
        'to': mm_fields.String(required=True),
        'amount': PositiveDecimalField(required=True),
        'type': EnumField(SupportType, missing=SupportType.BUY)
    })
    def post(cls, partner, **kwargs):
        require_user_request_permission(g.user)
        from_ = kwargs['from']
        to = kwargs['to']
        support_type = SupportType(kwargs['type'])
        amount = quantize_amount(kwargs['amount'], 8)
        if amount <= 0:
            raise InvalidArgument

        if not (client := get_direct_fiat_partner_client(partner, support_type)):
            raise InvalidArgument
        assets = getattr(client, f"{support_type.value}_assets")
        fiats = getattr(client, f"{support_type.value}_fiats")
        order_limit_min = getattr(client, f"{support_type.value}_order_limit_min")
        order_limit_max = getattr(client, f"{support_type.value}_order_limit_max")
        if from_ in assets and to in fiats:
            price = client.asset_to_fiat(from_, to, support_type)
            rate = PriceManager.fiat_to_usd(to)
            amount_usd = price * amount * rate
        elif from_ in fiats and to in assets:
            amount_usd = PriceManager.fiat_to_usd(from_) * amount
        else:
            raise InvalidArgument

        if quantize_amount(amount_usd, 0, ROUND_UP) < order_limit_min:
            raise InvalidArgument(message=_("输入的资产数量过小"))
        if quantize_amount(amount_usd, 0) > order_limit_max:
            raise InvalidArgument(message=_("输入的资产数量过大"))
        try:
            quote: Quote = client.quote(g.user, from_, to, amount, support_type)
        except Exception as e:
            current_app.logger.error(f"quote to {partner} error: {e!r}")
            raise InvalidArgument(message=_('%(partner)s异常，请稍后重试', partner=partner))
        cache = DirectFiatQuoteCache(client.name, g.user.id, quote.id)
        ttl = quote.ttl or cache.ttl
        cache.set(json.dumps(quote.to_dict(), cls=JsonEncoder), ex=ttl)
        return quote


class OrderMixin:

    @classmethod
    def check_general(cls, partner: str, quote_id: str) -> str:
        cache = DirectFiatQuoteCache(partner, g.user.id, quote_id)
        quote = cache.read()
        if not quote:
            raise InvalidArgument(message=_('价格已失效，请重新购买'))
        return quote

    @classmethod
    def check_business(cls, client, support_type: SupportType, business_data: dict = None):
        business_data = business_data or {}
        match client.name:
            case 'Swapix':
                cls._check_swapix(client, support_type, business_data)
            case _:
                raise InvalidArgument

    @classmethod
    def _check_swapix(cls, client, support_type: SupportType, business_data: dict):
        if support_type == SupportType.BUY:
            return cls._validate_swapix_buy(client, support_type)
        return cls._validate_swapix_sell(client, support_type, business_data)

    @classmethod
    def _validate_swapix_buy(cls, client, support_type: SupportType):
        if not g.user.mobile:
            raise InvalidArgument(message='mobile phone unbound')
        cls._validate_order_counts(client.name, g.user, support_type)
        client.validate_cpf(g.user)

    @classmethod
    def _validate_swapix_sell(cls, client, support_type: SupportType, business_data: dict):
        if not g.user.mobile:
            raise InvalidArgument(message='mobile phone unbound')
        cls._validate_order_counts(client.name, g.user, support_type)
        client.validate_cpf(g.user)
        payment_account = business_data.get('payment_account')
        if not payment_account:
            raise InvalidArgument
        try:
            uuid_obj = uuid.UUID(payment_account)
            is_valid = str(uuid_obj) == payment_account.lower()
        except ValueError:
            raise InvalidArgument(message=_('PIX Key 格式错误，请重新输入'))
        else:
            if not is_valid:
                raise InvalidArgument(message=_('PIX Key 格式错误，请重新输入'))

    @classmethod
    def _validate_order_counts(cls, third_party: str, user, support_type: SupportType):
        model = DirectFiatOrder
        query = model.query.filter(
            model.user_id == user.id,
            model.third_party == third_party,
            model.status.in_(model.PENDING_STATUS_TUPLE)
        )
        if support_type == SupportType.SELL:
            order_type = model.OrderType.SELL
        else:
            order_type = model.OrderType.BUY
        pending_count = query.filter(
            model.order_type == order_type
        ).count()
        order_limit = 2
        if pending_count >= order_limit:
            raise InvalidArgument(message=_("当前存在未完成的订单，请处理后再创建新订单"))


@ns.route('/<partner>/buy-orders')
@respond_with_code
class DirectFiatBuyOrdersResource(OrderMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        quote_id=mm_fields.String(required=True),
    ))
    def post(cls, partner, **kwargs):
        require_user_request_permission(g.user)
        quote_str = cls.check_general(partner, kwargs['quote_id'])
        quote = Quote(**json.loads(quote_str))
        support_type = SupportType(quote.support_type)
        if support_type != SupportType.BUY:
            raise OperationNotAllowed
        if not (client := get_direct_fiat_partner_client(partner, support_type)):
            raise InvalidArgument

        asset = quote.asset
        chain = client.get_asset_chain(asset)
        address, memo = WalletClient().get_or_create_deposit_address(chain, g.user.id)
        if memo:
            raise InvalidArgument(message=f'Purchasing {asset} is not supported')

        cls.check_business(client, support_type)
        ret = cls.place_buy_order(client, quote, address)
        DirectFiatQuoteCache(partner, g.user.id, kwargs['quote_id']).delete()
        return ret

    @classmethod
    def place_buy_order(cls, client, quote, address):
        record, order = DirectFiatBuyOrderHelper.place_buy_order(g.user, client, quote, address)
        return {
            'id': record.id,
            'partner': client.name,
            'payment_url': order.payment_url,
            'timeout': order.timeout,
            'address': address,  # TODO: check it if necessary
            'extra': order.extra,  # TODO: check it if necessary
        }


@ns.route('/<partner>/sell-orders')
@respond_with_code
class DirectFiatSellOrdersResource(OrderMixin, Resource):

    @classmethod
    @require_2fa(MobileCodeType.DIRECT_FIAT, allow_sub_account=False)
    @ns.use_kwargs(dict(
        quote_id=mm_fields.String(required=True),
        business_data=mm_fields.Dict(),
        withdraw_password=mm_fields.String(),  # 提现密码
    ))
    def post(cls, partner, **kwargs):
        require_user_request_permission(g.user)
        quote_str = cls.check_general(partner, kwargs['quote_id'])
        quote = Quote(**json.loads(quote_str))
        support_type = SupportType(quote.support_type)
        if support_type != SupportType.SELL:
            raise OperationNotAllowed
        if not (client := get_direct_fiat_partner_client(partner, support_type)):
            raise InvalidArgument
        business_data = kwargs.get('business_data') or {}
        cls.check_business(client, support_type, business_data)

        ret = cls.place_sell_order(
            client,
            quote,
            business_data,
            kwargs.get('withdraw_password'),
        )
        DirectFiatQuoteCache(partner, g.user.id, kwargs['quote_id']).delete()
        return ret

    @classmethod
    def place_sell_order(cls, client, quote, business_data: dict, withdraw_password='', ):
        match client.name:
            case 'Swapix':
                return cls._place_swapix_sell_order(client, quote, business_data, withdraw_password)
            case _:
                raise InvalidArgument

    @classmethod
    def _place_swapix_sell_order(cls, client, quote, business_data: dict, withdraw_password='', ):
        user: User = g.user
        asset = fee_asset = quote.asset
        order_id = str(uuid.uuid4())
        asset_amount = Decimal(quote.asset_amount)

        chain = client.get_asset_chain(asset)
        address = client.get_withdrawal_address(asset, chain)
        pix_key = payment_account = business_data['payment_account']
        deposit_address, _ = WalletClient().get_or_create_deposit_address(chain, user.id)
        memo = f'{pix_key}!{deposit_address}'
        remark = f'{client.name}(Direct Fiat)'

        details = {
            'order_id': order_id,
            'third_party': client.name,
            'payment_account': payment_account,
            'payment_method': DirectFiatPaymentMethod.PIX,
        }
        order, _ = DirectFiatSellOrderHelper.place_sell_order(
            details, quote,
            #
            user,
            asset,
            chain,
            address,
            memo,
            asset_amount,
            fee_asset,
            remark,
            withdraw_password,
        )
        return {
            'id': order.id,
            'partner': client.name,
        }


@ns.route('/order-list')
@respond_with_code
class DirectFiatOrderListResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        order_type=EnumField(DirectFiatOrder.OrderType),
        status=EnumField(DirectFiatOrder.Status),
        asset=AssetField(),
        fiat=mm_fields.String(),
        page=mm_fields.Integer(missing=1),
        limit=mm_fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        page, limit = kwargs['page'], kwargs['limit']
        model = DirectFiatOrder
        query = model.query.filter(
            model.user_id == g.user.id
        )
        if order_type := kwargs.get('order_type'):
            query = query.filter(
                model.order_type == order_type
            )
        if status := kwargs.get('status'):
            query_status = [status]
            if status == model.Status.PENDING:
                query_status = list(model.PENDING_STATUS_TUPLE)
            query = query.filter(
                model.status.in_(query_status)
            )
        if asset := kwargs.get('asset'):
            query = query.filter(
                model.asset == asset
            )
        if fiat := kwargs.get('fiat'):
            query = query.filter(
                model.fiat_currency == fiat
            )

        pagination = query.order_by(model.id.desc()).paginate(page, limit, error_out=False)
        data = []
        for v in pagination.items:
            status = model.DISPLAY_STATUSES[v.status]
            data.append(dict(
                id=v.id,
                payment_id=v.payment_id,
                created_at=v.created_at,
                asset=v.asset,
                coin_amount=cls.format_asset_amount(v.asset, v.coin_amount),
                fiat_currency=v.fiat_currency,
                fiat_total_amount=v.fiat_total_amount,
                third_party=v.third_party,
                status=status.name,
                order_type=v.order_type.name,
                price=quantize_amount(v.fiat_total_amount / v.coin_amount, 2)
            ))
        return dict(
            items=data,
            total=pagination.total,
            page=pagination.page
        )

    @classmethod
    def format_asset_amount(cls, asset, amount):
        precision = 4 if asset in ['USDT'] else 8
        return amount_to_str(amount, precision)


@ns.route('/orders/<int:order_id>')
@respond_with_code
class DirectFiatOrderDetailResource(Resource):
    model = DirectFiatOrder

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, order_id):
        row = cls._get_row(order_id, g.user.id)
        status = cls.model.DISPLAY_STATUSES[row.status].name
        if row.status == cls.model.Status.CREATED:
            status = row.status.name
        return dict(
            created_at=row.created_at,
            finished_at=row.finished_at,
            canceled_at=row.canceled_at,
            payment_url=row.payment_url,
            payment_id=row.payment_id,
            expired_at=row.payment_expired_at,
            payment_account=row.payment_account,
            payment_method=row.payment_method,
            asset=row.asset,
            coin_amount=DirectFiatOrderListResource.format_asset_amount(row.asset, row.coin_amount),
            fiat_currency=row.fiat_currency,
            fiat_total_amount=row.fiat_total_amount,
            third_party=row.third_party,
            status=status,
            order_type=row.order_type.name,
            price=quantize_amount(row.fiat_total_amount / row.coin_amount, 2),
            tx_id=row.tx_id,
            biz_id=row.biz_id,
            reason=row.get_reason(),
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        is_timeout=mm_fields.Boolean(missing=False),
    ))
    def patch(cls, order_id, **kwargs):
        """手动取消订单"""
        model = cls.model
        row = cls._get_row(order_id, g.user.id)
        if row.order_type == model.OrderType.SELL:
            return cls._cancel_sell_order(row.id, g.user.id)
        else:
            return cls._cancel_buy_order(row.id, g.user.id, kwargs['is_timeout'])

    @classmethod
    def _cancel_buy_order(cls, order_id: int, user_id: int, is_timeout: bool):
        model = cls.model
        with CacheLock(LockKeys.direct_fiat(order_id), wait=10):
            db.session.rollback()
            row = cls._get_row(order_id, user_id)
            allowed_statuses = [
                model.Status.CREATED,
                model.Status.PENDING,
            ]
            if row.status not in allowed_statuses:
                raise OperationNotAllowed
            reason = model.Reason.MANUAL_CANCELED
            if is_timeout:
                reason = model.Reason.SYSTEM_TIMEOUT
            row.status = model.Status.CANCELED
            row.reason = reason.name
            row.canceled_at = now()
            db.session.commit()

    @classmethod
    def _cancel_sell_order(cls, order_id: int, user_id: int):
        model = cls.model
        row = cls._get_row(order_id, user_id)
        allowed_statuses = [
            model.Status.CREATED,
            model.Status.SELL_SENT,
        ]
        if row.status not in allowed_statuses:
            raise OperationNotAllowed
        withdrawal_id = row.biz_id
        if not withdrawal_id:
            raise InvalidArgument
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if not withdrawal:
            raise InvalidArgument
        if withdrawal.status not in (
                Withdrawal.Status.CREATED,
                Withdrawal.Status.AUDIT_REQUIRED,
                Withdrawal.Status.AUDITED
        ):
            raise InvalidArgument

        DirectFiatSellOrderHelper.do_cancel_withdrawal(user_id, withdrawal_id)

        with CacheLock(LockKeys.direct_fiat(order_id), wait=10):
            db.session.rollback()
            row = cls._get_row(order_id, user_id)
            allowed_statuses = [
                model.Status.CREATED,
                model.Status.SELL_SENT,
            ]
            if row.status not in allowed_statuses:
                raise OperationNotAllowed

            if withdrawal.status not in (
                    Withdrawal.Status.CANCELLED,
                    Withdrawal.Status.CANCELLATION_FAILED,
            ):
                raise InvalidArgument

            reason = model.Reason.MANUAL_CANCELED
            row.status = model.Status.CANCELED
            row.reason = reason.name
            row.canceled_at = now()
            db.session.commit()

    @classmethod
    def _get_row(cls, order_id: int, user_id: int):
        model = cls.model
        row = model.query.filter(
            model.user_id == user_id,
            model.id == order_id
        ).first()
        if not row:
            raise InvalidArgument
        return row
