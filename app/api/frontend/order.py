# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import datetime
from decimal import Decimal, ROUND_UP
from enum import Enum
from typing import Optional, NamedTuple, Callable

from flask import current_app, g
from flask_babel import gettext
from flask_restx import fields as fx_fields, marshal
from marshmallow import fields as mm_fields
from webargs import fields as wa_fields

from ..common import (
    Namespace, respond_with_code, require_login, Resource, fields, success
)
from ..common.decorators import require_trade_password, trade_permission_validate
from ..common.request import RequestPlatform, require_user_request_permission
from ...business import (
    SPOT_ACCOUNT_ID,
    MAX_ORDER_ACCOUNT_ID, ALL_RECORD_ACCOUNT_ID, CacheLock, LockKeys,
)
from ...business.clients.biz_monitor import biz_monitor
from ...business.clients.server import ServerClient, validate_order_account_id, ORDER_BOTH_SIDE
from ...business.margin.helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BUY_TYP<PERSON>, SELL_TYPE
from ...business.margin.loan import MarginOrderLoanOperation
from ...business.order import Verify<PERSON>riceTool, OrderFeeOption, Order
from ...business.fee import FeeFetcher
from ...business.user import UserPreferences
from ...business.pledge.helper import is_pledge_account
from ...assets import is_pre_asset
from ...caches import MarketCache
from ...caches.pre_trading import PreTradingMarketCache, check_pre_trading_price
from ...common import (
    OrderSideType, TradeType, OrderBusinessType, OrderType, Language,
    OrderOption,
    TradeBusinessType, PrecisionEnum, OrderIntType, StopEvent, MarginEvent
)
from ...config import config
from ...exceptions import (
    TwoFactorAuthenticationRequired,
    OrderPlacementForbidden, OrderCancellationForbidden, OrderExceptionMap,
    OrderException, InvalidArgument, StopOrderAmountLimitExceeded,
)
from ...models import Market, db
from ...utils import amount_to_str, now, export_xlsx, batch_iter, \
    quantize_amount
from ...utils.helper import Struct

ns = Namespace('Order')


def validate_query_account_id(account_id: int) -> bool:
    """ 效验 查询接口中的 account_id参数 """
    return SPOT_ACCOUNT_ID <= account_id < MAX_ORDER_ACCOUNT_ID or is_pledge_account(account_id)


def _report_stop_order_event(order_type: OrderType, user_id: int, account_id: int):
    """现货下单上报"""
    tag = biz_monitor.get_source_tag()
    base_event = StopEvent
    if account_id != SPOT_ACCOUNT_ID:
        base_event = MarginEvent

    biz_monitor.increase_counter(
        base_event.ORDER_COUNT,
        tag=tag
    )
    biz_monitor.increase_uniq_counter(
        base_event.ORDER_NUM,
        value=[user_id],
        tag=tag
    )
    if order_type == OrderType.MARKET_ORDER_TYPE:
        event = base_event.MARKET_ORDER_COUNT
    elif order_type == OrderType.LIMIT_ORDER_TYPE:
        event = base_event.LIMIT_ORDER_COUNT
    elif order_type == OrderType.STOP_MARKET_ORDER_TYPE:
        event = base_event.STOP_MARKET_ORDER_COUNT
    else:
        event = base_event.STOP_LIMIT_ORDER_COUNT

    biz_monitor.increase_counter(event, tag=tag)


class OrderTypeToBeCancelled(Enum):
    LIMIT = 'LIMIT'
    STOP = 'STOP'


class CancelRequestCollection(NamedTuple):
    list_method: Callable
    cancel_batch_method: Callable
    cancel_one_method: Callable


class CancelOrderTool(object):
    MAX_CANCEL_ORDER_COUNT = 1000
    MESSAGE = gettext("部分订单不可撤销")

    @classmethod
    def build_order_type_request_collection(cls, _cancel_order_type: OrderTypeToBeCancelled) -> CancelRequestCollection:
        client = ServerClient()
        match _cancel_order_type:
            case OrderTypeToBeCancelled.LIMIT:
                return CancelRequestCollection(
                    list_method=client.user_pending_orders,
                    cancel_batch_method=client.cancel_batch_user_order,
                    cancel_one_method=client.cancel_user_order,
                )
            case OrderTypeToBeCancelled.STOP:
                return CancelRequestCollection(
                    list_method=client.user_pending_stop_orders,
                    cancel_batch_method=client.cancel_batch_user_stop_order,
                    cancel_one_method=client.cancel_user_stop_order,
                )
        raise InvalidArgument

    def __init__(self, order_type: OrderTypeToBeCancelled, user_id: int, market: Optional[str], account_id: int,
                 side: Optional[OrderSideType]):
        self.order_type = order_type
        self.collection = self.build_order_type_request_collection(order_type)
        self.user_id = user_id
        self.market = market
        self.validate_market_name(self.market)
        if self.market is None and not validate_order_account_id(account_id):
            raise InvalidArgument(f'{account_id}')
        self.account_id = account_id
        self.client = ServerClient()
        self.side = side or ORDER_BOTH_SIDE

    @classmethod
    def validate_market_name(cls, name):
        if name is not None and name not in MarketCache.list_online_markets():
            raise InvalidArgument

    def get_all_orders_data(self):
        result = self.collection.list_method(
            user_id=self.user_id,
            market=self.market,
            page=1,
            limit=self.MAX_CANCEL_ORDER_COUNT,
            side=self.side,
            account_id=self.account_id)
        return [(v['market'], v['id']) for v in result]

    @classmethod
    def check_market_can_cancel_order(cls, market_name, raise_exception=True):
        market_cache = MarketCache(market_name).dict
        if market_cache["status"] not in (Market.Status.ONLINE,
                                          Market.Status.BIDDING,
                                          Market.Status.COUNTING_DOWN):
            if raise_exception:
                raise OrderCancellationForbidden
            return False

        if market_cache["status"] == Market.Status.BIDDING and \
                market_cache["bidding_matching_started_at"] < now():
            if raise_exception:
                raise OrderExceptionMap[OrderException.BIDDING_STATUS]
            return False
        return True

    def cancel_all(self):
        all_orders_data = self.get_all_orders_data()
        all_markets = {v[0] for v in all_orders_data}
        disable_markets = set()
        for market_name in all_markets:
            if not self.check_market_can_cancel_order(market_name, raise_exception=False):
                disable_markets.add(market_name)
        filter_order_data = [v for v in all_orders_data if v[0] not in disable_markets]
        notice_message = False
        if len(all_markets & disable_markets) > 0:
            notice_message = True
        cancel_order_details = defaultdict(list)
        for (market_name, order_id) in filter_order_data:
            cancel_order_details[market_name].append(order_id)

        for _market_name, _market_order_ids in cancel_order_details.items():
            for _ids in batch_iter(_market_order_ids, 100):
                self.collection.cancel_batch_method(self.user_id, _market_name, _ids)
        return success(message=self.MESSAGE) if notice_message else success()

    def cancel_one(self, order_id: int):
        if not self.market:
            raise InvalidArgument
        self.check_market_can_cancel_order(self.market)
        self.collection.cancel_one_method(user_id=self.user_id, market=self.market, order_id=order_id)


def _put_margin_loan_order(market_cache: dict, account_id: int,
                           side: OrderSideType,
                           amount: Decimal, price: Decimal,
                           client: ServerClient, type_: OrderIntType) -> bool:
    """
    借币，返回值表示是否发生借币
    """
    user_id = g.user.id
    with CacheLock(LockKeys.margin_loan_or_flat(user_id), wait=False),\
         CacheLock(LockKeys.user_margin_account(user_id, account_id), wait=False):
        db.session.rollback()
        if side == OrderSideType.SELL:
            asset = market_cache['base_asset']
        else:
            asset = market_cache['quote_asset']
        balance_map = client.get_user_balances(user_id, asset=asset,
                                               account_id=account_id)
        available_amount = Decimal()
        if asset in balance_map:
            available_amount = balance_map[asset]['available']
        if side == OrderSideType.SELL:
            order_amount = amount
        else:
            if type_ == OrderIntType.LIMIT:
                order_amount = amount * price
                order_amount = quantize_amount(order_amount, PrecisionEnum.COIN_PLACES, rounding=ROUND_UP)
            else:
                order_amount = amount

        # 可用数量 < 下单数量
        if available_amount < order_amount:
            margin_account_utils = MarginAccountHelper(user_id, account_id)
            loan_max_amount = margin_account_utils.calculate_loan_max_amount()
            if side == OrderSideType.SELL:
                max_loan_amount = loan_max_amount[SELL_TYPE]
            else:
                max_loan_amount = loan_max_amount[BUY_TYPE]
            # 下单数量 < 可用数量 + 最大可借数量，触发借币
            if order_amount <= available_amount + max_loan_amount:
                operation = MarginOrderLoanOperation(user_id,
                                                     margin_identity=account_id)
                loan_amount = quantize_amount(order_amount - available_amount,
                                              PrecisionEnum.COIN_PLACES)
                operation.add_new_loan_order(asset, loan_amount, False)

                biz_monitor.increase_counter(
                    MarginEvent.AUTO_LOAN_COUNT,
                    with_source=True
                )
                biz_monitor.increase_uniq_counter(
                    MarginEvent.AUTO_LOAN_NUM,
                    value=[user_id],
                    with_source=True
                )
                biz_monitor.increase_counter(
                    MarginEvent.LOAN_COUNT,
                    with_source=True
                )
                biz_monitor.increase_uniq_counter(
                    MarginEvent.LOAN_NUM,
                    value=[user_id],
                    with_source=True
                )
                return True
        return False


def _is_order_asset_reversed(order_asset: Optional[str], market_info: dict,
                             sell_or_buy: OrderSideType) -> bool:
    base_asset, quote_asset = market_info['base_asset'], market_info['quote_asset']
    if not order_asset:
        return False
    if sell_or_buy == OrderSideType.BUY:
        return order_asset == base_asset
    else:
        return order_asset == quote_asset


@ns.route('/limit')
class LimitOrderResource(Resource):
    POST_MODEL_SCHEMA = dict(
        amount=fields.PositiveDecimalField(description='order amount', example='1', required=True),
        price=fields.PositiveDecimalField(description='order price', example='10.34',
                                          required=True),
        type=fields.IntEnumField(enum=OrderSideType,
                                 example='sell',
                                 description='order type, "sell" or "buy"',
                                 required=True),
        market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        account_id=mm_fields.Integer(
            missing=SPOT_ACCOUNT_ID,
            validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
        ),
        option=fields.IntEnumField(
            enum=OrderOption,
            example='普通: NORMAL, 立即成交或取消: IOC, 全部成交或取消: FOK, 只做maker: MAKER_ONLY',
            default=OrderOption.NORMAL,
            missing=OrderOption.NORMAL,
            description='订单选项'
        ),
        hide=fields.BoolField(missing=False, default=False, description='是否隐藏委托'),
    )

    @classmethod
    @ns.use_kwargs(
        POST_MODEL_SCHEMA,
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    @require_trade_password
    def post(cls, **kwargs):
        body = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        require_user_request_permission(g.user)
        market = body.market
        side = body.type
        amount = body.amount
        price = body.price
        account_id = body.account_id

        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden

        if not check_pre_trading_price(market, price):
            raise OrderPlacementForbidden

        market_cache = c.dict
        tool = VerifyPriceTool(market=market,
                               business_type=OrderBusinessType.NORMAL_BUSINESS_TYPE,
                               order_type=OrderType.LIMIT_ORDER_TYPE,
                               sell_or_buy=side,
                               amount=amount,
                               price=price,
                               stop_price=Decimal('0'),
                               account_id=account_id
                               )
        tool.validate(g.user.main_user_type)
        param_side = int(side)
        param_price = amount_to_str(price, market_cache['quote_asset_precision'])
        param_amount = amount_to_str(amount, market_cache['base_asset_precision'])

        source = RequestPlatform.from_request()
        client = ServerClient(current_app.logger)
        op = OrderFeeOption(market_cache, g.user.id)
        fee_asset, fee_discount = op.fee_asset, op.fee_discount
        option = op.with_option(body.option, body.hide)

        t = FeeFetcher(g.user.id)
        fee_result = t.fetch(TradeBusinessType.SPOT, market)
        taker_fee_rate, maker_fee_rate = fee_result[TradeType.TAKER], fee_result[TradeType.MAKER]
        # 如果开启了自动借币，判断下单是否需要借币
        if account_id != 0 and UserPreferences(g.user.id).auto_put_margin_loan_order:
            _put_margin_loan_order(market_cache, account_id, side,
                                   Decimal(param_amount), Decimal(param_price), client, OrderIntType.LIMIT)

        client.put_limit_order(
            user_id=g.user.id,
            market=market,
            side=param_side,
            amount=param_amount,
            price=param_price,
            taker_fee_rate=str(taker_fee_rate),
            maker_fee_rate=str(maker_fee_rate),
            source=source.value,
            fee_asset=fee_asset,
            fee_discount=str(fee_discount),
            account_id=account_id,
            option=option
        )

        _report_stop_order_event(
            OrderType.LIMIT_ORDER_TYPE,
            user_id=g.user.id,
            account_id=account_id
        )
        return success(message=gettext("下单成功"))


@ns.route('/modify')
class OrderModifyResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            amount=fields.PositiveDecimalField(required=True),
            price=fields.PositiveDecimalField(required=True),
            market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
            order_id=mm_fields.Integer(
                required=True,
                validate=lambda x: x > 0,
            ),
            account_id=mm_fields.Integer(
                missing=SPOT_ACCOUNT_ID,
                validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
            ),
        )
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    def post(cls, **kwargs):
        body = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        market = body.market
        amount = body.amount
        price = body.price
        order_id = body.order_id

        if not check_pre_trading_price(market, price):
            raise OrderPlacementForbidden

        if not CancelOrderTool.check_market_can_cancel_order(market, False):
            raise InvalidArgument(message=gettext('集合竞价第二阶段，暂不支持修改'))

        client = ServerClient(current_app.logger)
        detail_result = client.pending_order_detail(
            market=market,
            order_id=order_id,
        )

        if not detail_result:
            raise OrderExceptionMap[OrderException.ORDER_NOT_FOUND]
        if int(detail_result.get('user', 0)) != g.user.id:
            raise OrderExceptionMap[OrderException.NOT_YOUR_ORDER]
        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden
        tool = VerifyPriceTool(market=market,
                               business_type=OrderBusinessType.NORMAL_BUSINESS_TYPE,
                               order_type=OrderType.LIMIT_ORDER_TYPE,
                               sell_or_buy=OrderSideType(int(detail_result['side'])),
                               amount=amount,
                               price=price,
                               stop_price=Decimal('0'),
                               account_id=kwargs['account_id']
                               )
        tool.validate(g.user.main_user_type)
        market_cache = c.dict
        param_price = amount_to_str(price, market_cache['quote_asset_precision'])
        param_amount = amount_to_str(amount, market_cache['base_asset_precision'])
        client.order_modify(
            user_id=g.user.id,
            market=market,
            order_id=order_id,
            amount=param_amount,
            price=param_price,
        )
        return success(message=gettext("下单成功"))


@ns.route('/stop/modify')
class OrderModifyStopResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            amount=fields.PositiveDecimalField(required=True),
            price=fields.PositiveDecimalField(required=True, allow_zero=True),
            stop_price=fields.PositiveDecimalField(required=True),
            market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
            order_id=mm_fields.Integer(
                required=True,
                validate=lambda x: x > 0,
            ),
            account_id=mm_fields.Integer(
                missing=SPOT_ACCOUNT_ID,
                validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
            ),
        )
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    def post(cls, **kwargs):
        body = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        market = body.market
        amount = body.amount
        price = body.price
        stop_price = body.stop_price
        order_id = body.order_id

        if not check_pre_trading_price(market, price):
            raise OrderPlacementForbidden

        if not check_pre_trading_price(market, stop_price):
            raise OrderPlacementForbidden

        if not CancelOrderTool.check_market_can_cancel_order(market, False):
            raise InvalidArgument(message=gettext('集合竞价第二阶段，暂不支持修改'))

        client = ServerClient(current_app.logger)
        detail_result = client.pending_order_stop_detail(
            market=market,
            order_id=order_id,
        )
        if not detail_result:
            raise OrderExceptionMap[OrderException.ORDER_NOT_FOUND]
        if int(detail_result.get('user', 0)) != g.user.id:
            raise OrderExceptionMap[OrderException.NOT_YOUR_ORDER]
        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden
        order_type = OrderType.STOP_LIMIT_ORDER_TYPE if int(detail_result['type']) == OrderIntType.LIMIT.value \
            else OrderType.STOP_MARKET_ORDER_TYPE
        if order_type == OrderType.STOP_LIMIT_ORDER_TYPE:
            tool = VerifyPriceTool(market=market,
                                   business_type=OrderBusinessType.NORMAL_BUSINESS_TYPE,
                                   order_type=order_type,
                                   sell_or_buy=OrderSideType(int(detail_result['side'])),
                                   amount=amount,
                                   price=price,
                                   stop_price=stop_price,
                                   account_id=kwargs['account_id']
                                   )
            tool.validate(g.user.main_user_type)
        market_cache = c.dict
        param_price = amount_to_str(price, market_cache['quote_asset_precision']) if price else ''
        param_stop_price = amount_to_str(stop_price, market_cache['quote_asset_precision'])
        param_amount = amount_to_str(amount, market_cache['base_asset_precision'])
        client.order_modify_stop(
            user_id=g.user.id,
            market=market,
            order_id=order_id,
            amount=param_amount,
            price=param_price,
            stop_price=param_stop_price,
        )
        return success(message=gettext("下单成功"))


@ns.route('/market')
class MarketOrderResource(Resource):
    POST_MODEL_SCHEMA = dict(
        amount=fields.PositiveDecimalField(description='order amount', example='1', required=True),
        type=fields.IntEnumField(enum=OrderSideType,
                                 example='sell',
                                 description='order type, "sell" or "buy"',
                                 required=True),
        market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        account_id=mm_fields.Integer(
            missing=SPOT_ACCOUNT_ID,
            validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
        ),
        asset=mm_fields.String
    )

    @classmethod
    @ns.use_kwargs(
        POST_MODEL_SCHEMA,
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    @require_trade_password
    def post(cls, **kwargs):
        payload = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        require_user_request_permission(g.user)

        market = payload.market
        side = payload.type
        amount = payload.amount
        account_id = payload.account_id
        asset = payload.asset

        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden

        market_cache = c.dict
        if asset and asset not in (market_cache['base_asset'], market_cache['quote_asset']):
            raise InvalidArgument
        is_asset_reversed = _is_order_asset_reversed(asset, market_cache, side)
        if side == OrderSideType.BUY:
            if is_asset_reversed:
                asset_type_str = 'base_asset'
            else:
                asset_type_str = 'quote_asset'
        else:
            if is_asset_reversed:
                asset_type_str = 'quote_asset'
            else:
                asset_type_str = 'base_asset'
        param_amount = amount_to_str(amount, market_cache[f'{asset_type_str}_precision'])
        param_side = int(side)
        tool = VerifyPriceTool(market=market,
                               business_type=OrderBusinessType.NORMAL_BUSINESS_TYPE,
                               order_type=OrderType.MARKET_ORDER_TYPE,
                               sell_or_buy=side,
                               amount=amount,
                               price=Decimal('0'),
                               stop_price=Decimal('0'),
                               account_id=account_id,
                               order_asset=asset
                               )
        tool.validate(g.user.main_user_type)
        source = RequestPlatform.from_request()
        client = ServerClient(current_app.logger)
        op = OrderFeeOption(market_cache, g.user.id)
        fee_asset, fee_discount, option = op.fee_asset, op.fee_discount, op.option

        if is_asset_reversed:
            option = op.with_option(OrderOption.REVERSE_AMOUNT)

        t = FeeFetcher(g.user.id)
        fee_result = t.fetch(TradeBusinessType.SPOT, market)
        taker_fee_rate = fee_result[TradeType.TAKER]
        has_put_loan_order = False
        if account_id != 0 and UserPreferences(g.user.id).auto_put_margin_loan_order:
            if not is_asset_reversed:
                amount = Decimal(param_amount)
            else:
                last_price = client.market_last(market)
                if side == OrderSideType.BUY:
                    amount = last_price * Decimal(param_amount)
                else:
                    amount = Decimal(param_amount) / last_price
                amount = quantize_amount(amount, market_cache[f'{asset_type_str}_precision'])
            has_put_loan_order = _put_margin_loan_order(market_cache, account_id, side,
                                                        amount, Decimal(),
                                                        client, OrderIntType.MARKET)
        try:
            client.put_market_order(
                user_id=g.user.id,
                market=market,
                side=param_side,
                amount=param_amount,
                taker_fee_rate=str(taker_fee_rate),
                source=source.value,
                fee_asset=fee_asset,
                fee_discount=str(fee_discount),
                account_id=account_id,
                option=option,
            )
        except Exception as e:
            if has_put_loan_order:
                raise OrderExceptionMap[OrderException.ORDER_FAILED_BUT_LOAN_ORDER_PLACED]
            raise e

        _report_stop_order_event(
            OrderType.MARKET_ORDER_TYPE,
            user_id=g.user.id,
            account_id=account_id
        )
        return success(message=gettext("下单成功"))


@ns.route('/stop/limit')
class StopLimitOrderResource(Resource):
    POST_MODEL_SCHEMA = dict(
        amount=fields.PositiveDecimalField(description='order amount', example='1', required=True),
        price=fields.PositiveDecimalField(description='order price', example='10.34',
                                          required=True),
        stop_price=fields.PositiveDecimalField(description='order price', example='10.34',
                                               required=True),
        type=fields.IntEnumField(enum=OrderSideType,
                                 example='sell',
                                 description='order type, "sell" or "buy"',
                                 required=True),
        market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        account_id=mm_fields.Integer(
            missing=SPOT_ACCOUNT_ID,
            validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
        ),
        option=fields.IntEnumField(
            enum=OrderOption,
            example='普通: NORMAL, 立即成交或取消: IOC, 全部成交或取消: FOK, 只做maker: MAKER_ONLY',
            default=OrderOption.NORMAL,
            missing=OrderOption.NORMAL,
            description='订单选项'
        ),
        hide=fields.BoolField(missing=False, default=False, description='是否隐藏委托'),
    )

    @classmethod
    @ns.use_kwargs(
        POST_MODEL_SCHEMA,
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    @require_trade_password
    def post(cls, **kwargs):
        pay_load = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        require_user_request_permission(g.user)

        market = pay_load.market
        side = pay_load.type
        amount = pay_load.amount
        price = pay_load.price
        stop_price = pay_load.stop_price
        account_id = pay_load.account_id
        if not check_pre_trading_price(market, price):
            raise OrderPlacementForbidden
        if not check_pre_trading_price(market, stop_price):
            raise OrderPlacementForbidden

        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden

        if not check_pre_trading_price(market, price):
            raise OrderPlacementForbidden
        if not check_pre_trading_price(market, stop_price):
            raise OrderPlacementForbidden

        market_cache = c.dict
        if not MarketCache.check_stop_order_count(g.user.id, market=market, account_id=account_id):
            raise StopOrderAmountLimitExceeded(max_count=MarketCache.MAX_STOP_ORDER_COUNT)
        tool = VerifyPriceTool(market=market,
                               business_type=OrderBusinessType.NORMAL_BUSINESS_TYPE,
                               order_type=OrderType.STOP_LIMIT_ORDER_TYPE,
                               sell_or_buy=side,
                               amount=amount,
                               price=price,
                               stop_price=stop_price,
                               account_id=account_id
                               )
        tool.validate(g.user.main_user_type)
        param_side = int(side)
        param_price = amount_to_str(price, market_cache['quote_asset_precision'])
        param_stop_price = amount_to_str(stop_price, market_cache['quote_asset_precision'])
        param_amount = amount_to_str(amount, market_cache['base_asset_precision'])
        source = RequestPlatform.from_request()
        client = ServerClient(current_app.logger)
        op = OrderFeeOption(market_cache, g.user.id)
        fee_asset, fee_discount = op.fee_asset, op.fee_discount
        option = op.with_option(pay_load.option, pay_load.hide)

        t = FeeFetcher(g.user.id)
        fee_result = t.fetch(TradeBusinessType.SPOT, market)
        taker_fee_rate, maker_fee_rate = fee_result[TradeType.TAKER], fee_result[TradeType.MAKER]
        if pay_load.option & OrderOption.MAKER_ONLY:
            raise InvalidArgument

        client.put_stop_limit_order(
            user_id=g.user.id,
            market=market,
            side=param_side,
            amount=param_amount,
            stop_price=param_stop_price,
            price=param_price,
            taker_fee_rate=str(taker_fee_rate),
            maker_fee_rate=str(maker_fee_rate),
            source=source.value,
            fee_asset=fee_asset,
            fee_discount=str(fee_discount),
            account_id=account_id,
            option=option
        )
        _report_stop_order_event(
            OrderType.STOP_LIMIT_ORDER_TYPE,
            user_id=g.user.id,
            account_id=account_id
        )
        return success(message=gettext("下单成功"))


@ns.route('/stop/market')
class StopMarketOrderResource(Resource):
    POST_MODEL_SCHEMA = dict(
        amount=fields.PositiveDecimalField(description='order amount', example='1', required=True),
        stop_price=fields.PositiveDecimalField(description='order price', example='10.34',
                                               required=True),
        type=fields.IntEnumField(enum=OrderSideType,
                                 example='sell',
                                 description='order type, "sell" or "buy"',
                                 required=True),
        market=mm_fields.String(required=True, validate=lambda x: x.isupper()),
        account_id=mm_fields.Integer(
            missing=SPOT_ACCOUNT_ID,
            validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID
        ),
        asset=mm_fields.String
    )

    @classmethod
    @ns.use_kwargs(
        POST_MODEL_SCHEMA,
    )
    @require_login
    @trade_permission_validate(is_spot=True)
    @require_trade_password
    def post(cls, **kwargs):
        pay_load = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        require_user_request_permission(g.user)

        market = pay_load.market
        side = pay_load.type
        amount = pay_load.amount
        stop_price = pay_load.stop_price
        account_id = pay_load.account_id
        asset = pay_load.asset

        c = MarketCache(market)
        if not c.check_order_permission(g.user.main_user_type):
            raise OrderPlacementForbidden

        market_cache = c.dict
        if PreTradingMarketCache.has(market) or is_pre_asset(market_cache['base_asset']):
            raise InvalidArgument(message=gettext("预测市场暂不支持计划市价的订单"))
        if asset and asset not in (market_cache['base_asset'], market_cache['quote_asset']):
            raise InvalidArgument
        is_asset_reversed = _is_order_asset_reversed(asset, market_cache, side)

        if not MarketCache.check_stop_order_count(g.user.id, market=market, account_id=account_id):
            raise StopOrderAmountLimitExceeded(max_count=MarketCache.MAX_STOP_ORDER_COUNT)
        param_side = int(side)
        param_stop_price = amount_to_str(stop_price, market_cache['quote_asset_precision'])
        param_amount = amount_to_str(amount, market_cache['base_asset_precision'])
        source = RequestPlatform.from_request()
        client = ServerClient(current_app.logger)
        op = OrderFeeOption(market_cache, g.user.id, use_in_asset=True)
        fee_asset, fee_discount, option = op.fee_asset, op.fee_discount, op.option

        if is_asset_reversed:
            option = op.with_option(OrderOption.REVERSE_AMOUNT)

        t = FeeFetcher(g.user.id)
        fee_result = t.fetch(TradeBusinessType.SPOT, market)
        taker_fee_rate = fee_result[TradeType.TAKER]
        maker_fee_rate = fee_result[TradeType.MAKER]

        client.put_stop_market_order(
            user_id=g.user.id,
            market=market,
            side=param_side,
            amount=param_amount,
            stop_price=param_stop_price,
            taker_fee_rate=str(taker_fee_rate),
            maker_fee_rate=str(maker_fee_rate),
            source=source.value,
            fee_asset=fee_asset,
            fee_discount=str(fee_discount),
            account_id=account_id,
            option=option
        )
        _report_stop_order_event(
            OrderType.STOP_MARKET_ORDER_TYPE,
            user_id=g.user.id,
            account_id=account_id
        )
        return success(message=gettext("下单成功"))


@ns.route('/finished')
@respond_with_code
class FinishedOrderListResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=wa_fields.Integer(required=True),
        limit=wa_fields.Integer(required=True),
        start_time=wa_fields.Integer(default=0, missing=0),
        end_time=wa_fields.Integer(default=0, missing=0),
        market=wa_fields.String(validate=lambda x: x.isupper() or not x, default='', missing='',
                                description='market name'),
        type=fields.IntEnumField(enum=OrderSideType, default='', missing=''),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_order_account_id),
        stop_order_id=wa_fields.Integer(),
        export=wa_fields.Bool(default=False, missing=False, description='export or not')
    )

    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': fx_fields.String,
        'deal_amount': fx_fields.String(attribute='deal_stock'),
        'stock_fee': fx_fields.String,
        'money_fee': fx_fields.String,
        'deal_money': fx_fields.String,
        'asset_fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'avg_price': Order.AvgPriceFields(
            attribute=lambda x: (Decimal(x['deal_stock']), Decimal(x['deal_money']), x['market'])),
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: Order.NormalOrderType(int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
        'status': Order.StatusFields(attribute=lambda x: (x, x['type'], False)),
        'effect_type_name': Order.SpotOrderEffectField(attribute='option'),
        'amount_asset': Order.AmountAssetFields(attribute=lambda x: (x['market'], x['side'], x['type'], x['option'])),
        'option': fx_fields.Integer,
        'is_system': Order.SystemOrderFields(
            attribute=lambda x: x['source']
        )
    }

    export_marshal_fields = dict(
        marshal_fields,
        deal_money=fields.AmountField(attribute='deal_money'),
        price=Order.MarketPriceFields(
            attribute=lambda x: (Decimal(x['price']), x['market'], int(x['type']))),
        order_type=Order.MarketMarginOrderTypeFields(
            attribute=lambda x: (int(x['type']), x['account'])),
        create_time=fields.UTCTimeStr(attribute=lambda x: int(x['ctime'])),
        amount=Order.AmountFields(attribute=lambda x: (x['amount'], x['type'], x['side'])),
    )

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "市场", Language.EN_US: "Market"},
        {"field": "order_type", Language.ZH_HANS_CN: "类型", Language.EN_US: "Type"},
        {"field": "type", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "price", Language.ZH_HANS_CN: "委托价", Language.EN_US: "Price"},
        {"field": "amount", Language.ZH_HANS_CN: "委托量", Language.EN_US: "Amount"},
        {"field": "avg_price", Language.ZH_HANS_CN: "成交均价", Language.EN_US: "Avg.Price"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "成交量", Language.EN_US: "Executed"},
        {"field": "deal_money", Language.ZH_HANS_CN: "成交额", Language.EN_US: "Exec.Value"},
        # done (done, 全部成交-All Executed）（!done, 部分成交-Party Executed）
        {"field": "status", Language.ZH_HANS_CN: "成交状态", Language.EN_US: "Execution Status"},
    )

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        user_id = g.user.id
        client = ServerClient()
        stop_order_id = kwargs.get("stop_order_id", None)
        result = client.user_finished_orders(
            user_id,
            kwargs['market'],
            start_time=kwargs['start_time'],
            end_time=kwargs['end_time'],
            page=kwargs['page'] if not kwargs['export'] else 1,
            limit=(kwargs['limit'] if not kwargs['export']
                   else config['EXPORT_ITEM_MAX_COUNT']),
            side=int(OrderSideType(kwargs['type'])) if kwargs['type'] != '' else 0,
            account_id=kwargs['account_id'],
            stop_order_id=stop_order_id
        )
        if not kwargs['export']:
            data = result.as_dict()
            data['data'] = marshal(data['data'], cls.marshal_fields)
            return data
        export_data = marshal(result, cls.export_marshal_fields)
        file_name = datetime.utcnow().strftime('%Y%m%d-order-history')
        return export_xlsx(
            filename=file_name,
            data_list=export_data,
            export_headers=cls.export_headers
        )


@ns.route('/finished/<int:order_id>')
@respond_with_code
class FinishedOrderResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=fields.PageField(),
        limit=fields.LimitField(),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_query_account_id)
    )

    marshal_deal_fields = {
        'create_time': fx_fields.Integer(attribute='time'),
        'price': fx_fields.String,
        'amount': fx_fields.String,
        'fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'role': fx_fields.String(attribute=lambda x: Order.TradeRoleType(int(x['role'])).name.lower()),
        'deal_money': fx_fields.String(attribute='deal'),
        'amount_asset': fx_fields.String,
        # 添加option,手续费币种展示相关
        'option': fx_fields.Integer
    }

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, order_id, **kwargs):
        user_id = g.user.id
        client = ServerClient()
        detail_result = client.finished_order_detail(
            user_id=user_id,
            order_id=order_id,
        )
        if not detail_result:
            raise OrderExceptionMap[OrderException.ORDER_NOT_FOUND]

        if int(detail_result.get('user', 0)) != user_id:
            raise OrderExceptionMap[OrderException.NOT_YOUR_ORDER]
        deals_result = client.user_order_deals(user_id=user_id,
                                               order_id=order_id,
                                               page=kwargs['page'],
                                               limit=kwargs['limit'],
                                               account_id=kwargs['account_id'])
        data = dict()
        amount_asset = Order.AmountAssetFields().format((detail_result['market'],
                                                         detail_result['side'],
                                                         detail_result['type'], detail_result['option']))
        detail_result['amount_asset'] = amount_asset
        data['order'] = marshal(detail_result, FinishedOrderListResource.marshal_fields)
        deals_data = deals_result.as_dict()
        for i, _ in enumerate(deals_data['data']):
            deals_data['data'][i]['market'] = detail_result['market']
            deals_data['data'][i]['amount_asset'] = amount_asset
        deals_data['data'] = marshal(deals_data['data'], cls.marshal_deal_fields)
        data.update(deals_data)

        return data


@ns.route('/pending')
class PendingOrderListResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=wa_fields.Integer(required=True),
        limit=wa_fields.Integer(required=True),
        market=wa_fields.String(validate=lambda x: x.isupper() or '', default=None, missing=None,
                                description='market name'),
        type=fields.IntEnumField(enum=OrderSideType, default='', missing=''),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_order_account_id)
    )

    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': fx_fields.String,
        'deal_amount': fx_fields.String(attribute='deal_stock'),
        'stock_fee': fx_fields.String,
        'money_fee': fx_fields.String,
        'deal_money': fx_fields.String,
        'asset_fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'is_system': Order.SystemOrderFields(
            attribute=lambda x: x['source']
        ),
        'avg_price': Order.AvgPriceFields(
            attribute=lambda x: (Decimal(x['deal_stock']), Decimal(x['deal_money']), x['market'])),
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: Order.NormalOrderType(int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
        'status': Order.StatusFields(attribute=lambda x: (x, x['type'], True)),
        'option': fx_fields.Integer,
        'amount_asset': Order.AmountAssetFields(attribute=lambda x: (x['market'], x['side'], x['type'], x['option'])),
    }

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        user_id = g.user.id
        client = ServerClient(current_app.logger)
        if params.market and params.market not in MarketCache.list_online_markets():
            return success(dict(
                data=[],
                curr_page=1,
                has_next=False,
                count=0,
                total=0,
            ))
        result = client.user_pending_orders(
            user_id,
            market=params.market,
            page=params.page,
            limit=params.limit,
            side=int(OrderSideType(params.type)) if kwargs['type'] != '' else 0,
            account_id=params.account_id)
        data = result.as_dict()
        data['data'] = marshal(data['data'], cls.marshal_fields)
        return success(data)

    DELETE_MODEL_SCHEMA = dict(
        market=wa_fields.String(
            validate=lambda x: x.isupper(),
            description='market name',
            missing=None,
            allow_none=True),
        account_id=wa_fields.Integer(
            default=SPOT_ACCOUNT_ID,
            missing=ALL_RECORD_ACCOUNT_ID,
            validate=validate_order_account_id),
        type=fields.IntEnumField(enum=OrderSideType,
                                 example='sell',
                                 description='order type, "sell" or "buy"',
                                 missing=ORDER_BOTH_SIDE
                                 )
    )

    @classmethod
    @ns.use_kwargs(DELETE_MODEL_SCHEMA)
    @require_login
    def delete(cls, **kwargs):
        user_id = g.user.id
        market = kwargs.get('market', None)
        account_id = kwargs.get('account_id', SPOT_ACCOUNT_ID)
        side = kwargs.get('type', None)
        t = CancelOrderTool(OrderTypeToBeCancelled.LIMIT, user_id, market, account_id, side)
        return t.cancel_all()


@ns.route('/pending/<int:order_id>')
@respond_with_code
class PendingOrderResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=wa_fields.Integer(required=True),
        limit=wa_fields.Integer(required=True),
        market=wa_fields.String(validate=lambda x: x.isupper(), required=True,
                                description='market name'),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_query_account_id)
    )

    DELETE_MODEL_SCHEMA = dict(
        market=wa_fields.String(
            validate=lambda x: x.isupper(),
            description='market name',
            required=True
            ),
        account_id=wa_fields.Integer(
            default=SPOT_ACCOUNT_ID,
            missing=SPOT_ACCOUNT_ID,
            validate=lambda x:
            SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID)
    )

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, order_id, **kwargs):
        params = Struct(**kwargs)
        user_id = g.user.id
        client = ServerClient()
        detail_result = client.pending_order_detail(
            market=params.market,
            order_id=order_id,
        )
        if not detail_result:
            raise OrderExceptionMap[OrderException.ORDER_NOT_FOUND]

        if int(detail_result.get('user', 0)) != user_id:
            raise OrderExceptionMap[OrderException.NOT_YOUR_ORDER]

        deals_result = client.user_order_deals(user_id=user_id,
                                               order_id=order_id,
                                               page=kwargs['page'],
                                               limit=kwargs['limit'],
                                               account_id=kwargs['account_id'])
        data = dict()
        data['order'] = marshal(detail_result, PendingOrderListResource.marshal_fields)
        deals_data = deals_result.as_dict()
        deals_data['data'] = marshal(deals_data['data'], FinishedOrderResource.marshal_deal_fields)
        data.update(deals_data)

        return data

    @classmethod
    @ns.use_kwargs(DELETE_MODEL_SCHEMA)
    @require_login
    def delete(cls, order_id, **kwargs):
        user_id = g.user.id
        market = kwargs['market']
        account_id = kwargs.get('account_id', SPOT_ACCOUNT_ID)
        t = CancelOrderTool(OrderTypeToBeCancelled.LIMIT, user_id, market, account_id, None)
        return t.cancel_one(order_id)


@ns.route('/deals')
@respond_with_code
class OrderDealListResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=wa_fields.Integer(required=True),
        limit=wa_fields.Integer(required=True),
        start_time=wa_fields.Integer(default=0, missing=0),
        end_time=wa_fields.Integer(default=0, missing=0),
        market=wa_fields.String(validate=lambda x: x.isupper() or not x, default='', missing='',
                                description='market name'),
        type=fields.IntEnumField(enum=OrderSideType, default='', missing=''),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_order_account_id),
        export=wa_fields.Bool(default=False, missing=False, description='export or not')
    )

    marshal_fields = {
        'id': fx_fields.Integer,
        'create_time': fx_fields.Integer(attribute='time'),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
        'role': fx_fields.String(attribute=lambda x: Order.TradeRoleType(int(x['role'])).name.lower()),
        'price': fx_fields.String,
        'amount': fx_fields.String,
        'fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'market': fx_fields.String,
        'deal_money': fx_fields.String(attribute='deal'),
        'account_id': fx_fields.Integer(attribute='account')
    }

    export_marshal_fields = dict(
        marshal_fields,
        create_time=fields.UTCTimeStr(attribute=lambda x: int(x['time']))
    )

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "成交时间", Language.EN_US: "Execution Time"},
        {"field": "market", Language.ZH_HANS_CN: "交易对/合约名",
         Language.EN_US: "Trading pair/Contract name"},
        {"field": "type", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "price", Language.ZH_HANS_CN: "成交价格", Language.EN_US: "Executed Price"},
        {"field": "amount", Language.ZH_HANS_CN: "成交量", Language.EN_US: "Executed Amount"},
        {"field": "deal_money", Language.ZH_HANS_CN: "成交金额", Language.EN_US: "Executed Value"},
        {"field": "fee", Language.ZH_HANS_CN: "手续费", Language.EN_US: "Fees"},
        {"field": "fee_asset", Language.ZH_HANS_CN: "手续费币种", Language.EN_US: "Fees Coin Type"},
        {"field": "role", Language.ZH_HANS_CN: "成交类型", Language.EN_US: "Executed Type"},
    )

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        user_id = g.user.id
        client = ServerClient()
        params = Struct(**kwargs)
        result = client.market_user_deals(
            user_id=user_id,
            market=params.market,
            start_time=params.start_time,
            end_time=params.end_time,
            page=params.page if not params.export else 1,
            limit=(params.limit
                   if not params.export
                   else config['EXPORT_ITEM_MAX_COUNT']),
            side=int(OrderSideType(kwargs['type'])) if kwargs['type'] != '' else 0,
            account_id=params.account_id
        )
        if not params.export:
            data = result.as_dict()
            data['data'] = marshal(data['data'], cls.marshal_fields)
            return data
        else:
            export_data = marshal(result, cls.export_marshal_fields)
            file_name = datetime.utcnow().strftime('%Y%m%d-execution-history')
            return export_xlsx(
                filename=file_name,
                data_list=export_data,
                export_headers=cls.export_headers
            )


@ns.route('/stop/finished')
@respond_with_code
class StopFinishedOrderListResource(Resource):
    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': fx_fields.String,
        'stop_price': Order.PriceFields(attribute=lambda x: (x['stop_price'], x['market'])),
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: Order.NormalOrderType(int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
        'status': Order.StopStatusFields(attribute=lambda x: x['status']),
        'direction': fx_fields.Integer(attribute='direction'),
        'amount_asset': Order.AmountAssetFields(attribute=lambda x: (x['market'], x['side'], x['type'], x['option'])),
    }

    export_marshal_fields = dict(
        marshal_fields,
        price=Order.MarketPriceFields(
            attribute=lambda x: (Decimal(x['price']), x['market'], int(x['type']))),
        order_type=Order.MarketMarginOrderTypeFields(
            attribute=lambda x: (int(x['type']), x['account'])),
        create_time=fields.UTCTimeStr(attribute=lambda x: int(x['ctime'])),
        amount=Order.AmountFields(attribute=lambda x: (x['amount'], x['type'], x['side'])),
        order_stoptype=Order.OrderStopTypeFields(attribute=lambda x: int(x['type'])),
    )

    export_headers = (
        {"field": "create_time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "市场", Language.EN_US: "Market"},
        {"field": "order_stoptype", Language.ZH_HANS_CN: "类型", Language.EN_US: "Type"},
        {"field": "type", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "price", Language.ZH_HANS_CN: "委托价", Language.EN_US: "Price"},
        {"field": "amount", Language.ZH_HANS_CN: "委托量", Language.EN_US: "Amount"},
        {"field": "stop_price", Language.ZH_HANS_CN: "触发价", Language.EN_US: "Stop"},
        # done: Submitted, Failed to place order
        {"field": "status", Language.ZH_HANS_CN: "委托状态", Language.EN_US: "Order Status"},
    )

    @classmethod
    @ns.use_kwargs(FinishedOrderListResource.GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        user_id = g.user.id
        params = Struct(**kwargs)
        client = ServerClient()
        result = client.user_finished_stop_orders(
            user_id=user_id,
            market=params.market,
            start_time=params.start_time,
            end_time=params.end_time,
            page=params.page if not params.export else 1,
            limit=(params.limit
                   if not params.export
                   else config['EXPORT_ITEM_MAX_COUNT']),
            side=int(OrderSideType(kwargs['type'])) if kwargs['type'] != '' else 0,
            account_id=params.account_id
        )
        if not params.export:
            data = result.as_dict()
            data['data'] = marshal(data['data'], cls.marshal_fields)
            return data
        export_data = marshal(result, cls.export_marshal_fields)
        file_name = datetime.utcnow().strftime('%Y%m%d-order-history')
        return export_xlsx(
            filename=file_name,
            data_list=export_data,
            export_headers=cls.export_headers
        )


@ns.route('/stop/pending')
class StopPendingOrderListResource(Resource):
    GET_PARAMS_SCHEMA = dict(
        page=wa_fields.Integer(required=True),
        limit=wa_fields.Integer(required=True),
        market=wa_fields.String(validate=lambda x: x.isupper() or '', default=None, missing=None,
                                description='market name'),
        type=fields.IntEnumField(enum=OrderSideType, default='', missing=''),
        account_id=wa_fields.Integer(default=SPOT_ACCOUNT_ID,
                                     missing=SPOT_ACCOUNT_ID,
                                     validate=validate_order_account_id)
    )

    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'price': fx_fields.String,
        'stop_price': fx_fields.String,
        'taker_fee': fx_fields.String,
        'maker_fee': fx_fields.String,
        'direction': fx_fields.Integer,
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'market': fx_fields.String,
        'order_type': fx_fields.String(attribute=lambda x: Order.NormalOrderType(int(x['type'])).name.lower()),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
        'amount_asset': Order.AmountAssetFields(attribute=lambda x: (x['market'], x['side'], x['type'], x['option'])),
    }

    @classmethod
    @ns.use_kwargs(GET_PARAMS_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        user_id = g.user.id
        if params.market and params.market not in MarketCache.list_online_markets():
            return success(dict(
                data=[],
                curr_page=1,
                has_next=False,
                count=0,
                total=0,
            ))
        client = ServerClient()
        result = client.user_pending_stop_orders(user_id=user_id,
                                                 market=params.market,
                                                 page=params.page,
                                                 limit=params.limit,
                                                 side=int(OrderSideType(params.type))
                                                 if kwargs['type'] != '' else 0,
                                                 account_id=params.account_id)
        data = result.as_dict()
        data['data'] = marshal(data['data'], cls.marshal_fields)
        return success(data)

    @classmethod
    @ns.use_kwargs(PendingOrderListResource.DELETE_MODEL_SCHEMA)
    @require_login
    def delete(cls, **kwargs):
        user_id = g.user.id
        market = kwargs.get('market', None)
        account_id = kwargs.get('account_id', SPOT_ACCOUNT_ID)
        side = kwargs.get('type', None)
        t = CancelOrderTool(OrderTypeToBeCancelled.STOP, user_id, market, account_id, side)
        return t.cancel_all()


@ns.route('/stop/pending/<int:order_id>')
@respond_with_code
class StopPendingOrderResource(Resource):

    @classmethod
    @ns.use_kwargs(PendingOrderResource.DELETE_MODEL_SCHEMA)
    @require_login
    def delete(cls, order_id, **kwargs):
        user_id = g.user.id
        market = kwargs.get('market', None)
        account_id = kwargs.get('account_id', SPOT_ACCOUNT_ID)
        t = CancelOrderTool(OrderTypeToBeCancelled.STOP, user_id, market, account_id, None)
        return t.cancel_one(order_id)


@ns.route('/stop-order-summary/<int:order_id>')
@respond_with_code
class StopOrderSummaryResource(Resource):

    marshal_fields = {
        'account_id': fx_fields.Integer(attribute='account'),
        'order_id': fx_fields.Integer(attribute='id'),
        'create_time': fx_fields.Integer(attribute='ctime'),
        'amount': fx_fields.String,
        'total_deal_amount': fx_fields.String,
        'total_deal_money': fx_fields.String,
        'asset_fee': fx_fields.String,
        'fee_asset': fx_fields.String,
        'fee_discount': fx_fields.String,
        'avg_limit_price': Order.PriceFields(attribute=lambda x: (x['avg_limit_price'], x['market'])),
        'avg_deal_price': Order.AvgPriceFields(
            attribute=lambda x: (Decimal(x['total_deal_amount']), Decimal(x['total_deal_money']),
                                 x['market'])),
        'market': fx_fields.String,
        'amount_asset': Order.AmountAssetFields(attribute=lambda x: (x['market'], x['side'], x['type'], x['option'])),
        'type': fx_fields.String(attribute=lambda x: Order.OrderSideType(int(x['side'])).name.lower()),
    }

    @classmethod
    @require_login
    def get(cls, order_id):
        user_id = g.user.id
        client = ServerClient()
        detail_result = client.finished_stop_order_detail(
            user_id=user_id,
            order_id=order_id,
        )
        market, account_id = detail_result["market"], detail_result["account"]
        orders = client.user_finished_orders(
            user_id,
            market,
            start_time=0,
            end_time=0,
            page=1,
            limit=50,
            side=0,
            account_id=account_id,
            stop_order_id=order_id
        )
        total_deal_amount = sum([Decimal(v["deal_stock"]) for v in orders])
        total_deal_money = sum([Decimal(v["deal_money"]) for v in orders])
        detail_result.update(
            dict(
                total_deal_amount=total_deal_amount,
                total_deal_money=total_deal_money,
                avg_limit_price=sum([Decimal(v["price"]) for v in orders]) / len(orders) if len(orders) > 0
                else Decimal()
            )
        )
        return marshal(detail_result, cls.marshal_fields)
