# -*- coding: utf-8 -*-
import json
from decimal import ROUND_UP

from flask import g, current_app
from marshmallow import fields as mm_fields
from flask_babel import gettext as _

from ..common import Resource, Namespace, respond_with_code, require_login
from ..common.request import get_request_ip, get_request_user, require_user_request_permission
from ..common.fields import PositiveDecimalField, AssetField, EnumField
from ...business.fiat import (get_fiat_partners, get_fiat_partner_client,
                              get_fiat_assets, get_fiat_currencies, Quote)
from ...business import WalletClient, PriceManager
from ...business.fiat.base import SupportType
from ...caches.fiat import FiatQuoteCache, FiatActivityCache, FiatPriceCache
from ...exceptions import FiatOrderExpire, InvalidArgument
from ...models.fiat import FiatOrder
from ...utils import quantize_amount, RESTClient
from ...utils.parser import JsonEncoder

ns = Namespace('Fiat')


@ns.route('/partners')
@respond_with_code
class FiatPartners(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(SupportType, missing=SupportType.BUY)
    ))
    def get(cls, **kwargs):
        support_type = SupportType(kwargs['type'])
        result = []
        activities = FiatActivityCache.get_data()
        for partner in get_fiat_partners():
            c = get_fiat_partner_client(partner, support_type)
            if not c:
                continue
            item = dict(
                name=c.name,
                logo=c.logo,
                assets=getattr(c, f"{support_type.value}_assets"),
                fiats=getattr(c, f"{support_type.value}_fiats"),
                payment_methods=[{'name': x.value, 'logo': x.logo}
                                 for x in getattr(c, f"{support_type.value}_payment_methods")],
                quote_methods=getattr(c, f'{support_type.value}_quote_methods'),
                fee_rate=getattr(c, f"{support_type.value}_fee_rate"),
                min_fee=c.min_fee,
                order_limit_min=getattr(c, f"{support_type.value}_order_limit_min"),
                order_limit_max=getattr(c, f"{support_type.value}_order_limit_max"),
                daily_limit=c.daily_limit,
                monthly_limit=c.monthly_limit,
                help_url=c.help_url
            )
            if activity := activities.get(partner):
                item.update(activity=activity)
            result.append(item)

        return dict(
            assets=get_fiat_assets(support_type),
            fiats=get_fiat_currencies(support_type),
            partners=result
        )


@ns.route('/prices')
@respond_with_code
class FiatPricesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        fiat=mm_fields.String(required=True),
        type=EnumField(SupportType, missing=SupportType.BUY)
    ))
    def get(cls, **kwargs):
        g.request_ip = get_request_ip()
        asset = kwargs['asset']
        fiat = kwargs['fiat']
        support_type = SupportType(kwargs['type'])
        result = {}
        for partner in get_fiat_partners():
            client = get_fiat_partner_client(partner, support_type)
            if not client:
                continue

            if asset not in getattr(client, f"{support_type.value}_assets") or \
                    fiat not in getattr(client, f"{support_type.value}_fiats"):
                continue
            pair = f'{asset}-{fiat}'
            exclude_pairs = getattr(client, f"exclude_{support_type.value}_pairs")
            if pair in exclude_pairs:
                continue
            # 优先从缓存获取价格, 如果没有获取到则回退到通过接口获取，以提升性能
            try:
                cache = FiatPriceCache(partner)
                price = cache.get_price(asset, fiat, support_type)
            except (KeyError, Exception):
                try:
                    price = client.asset_to_fiat(asset, fiat, support_type)
                except Exception as e:
                    current_app.logger.warning(f'get {client.name} {asset}-{fiat} price failed: {e!r}')
                    continue
            user = None
            if client.name == 'Banxa':
                user = get_request_user()
            chain = client.get_asset_chain(asset, user)
            result[client.name] = {
                'asset': asset,
                'chain': chain,
                'fiat': fiat,
                'price': price
            }
        return result


@ns.route('/amounts')
@respond_with_code
class FiatAmountsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        fiat=mm_fields.String(required=True),
        fiat_amount=PositiveDecimalField(required=True),
        type=EnumField(SupportType, missing=SupportType.BUY)
    ))
    def get(cls, **kwargs):
        """仅兼容 app，不再他用"""
        g.request_ip = get_request_ip()
        asset = kwargs['asset']
        fiat = kwargs['fiat']
        support_type = SupportType(kwargs['type'])
        fiat_amount = quantize_amount(kwargs['fiat_amount'], 2)
        if fiat_amount <= 0:
            raise InvalidArgument
        result = {}
        for partner in get_fiat_partners():
            client = get_fiat_partner_client(partner, support_type)
            if not client:
                continue
            if asset not in getattr(client, f"{support_type.value}_assets") or \
                    fiat not in getattr(client, f"{support_type.value}_fiats"):
                continue
            try:
                asset_amount = client.fiat_to_asset_amount(fiat, asset, fiat_amount)
            except Exception as e:
                current_app.logger.error(f'{client.name} {fiat} to {asset} amount failed: {e!r}')
                continue
            result[client.name] = {
                'asset': asset,
                'asset_amount': asset_amount,
                'fiat': fiat,
                'fiat_amount': fiat_amount
            }
        return result


@ns.route('/<partner>/quotes')
@respond_with_code
class FiatQuoteResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs({
        'from': mm_fields.String(required=True),
        'to': mm_fields.String(required=True),
        'amount': PositiveDecimalField(required=True),
        'type': EnumField(SupportType, missing=SupportType.BUY)
    })
    def post(cls, partner, **kwargs):
        require_user_request_permission(g.user)
        g.request_ip = get_request_ip()
        from_ = kwargs['from']
        to = kwargs['to']
        support_type = SupportType(kwargs['type'])
        amount = quantize_amount(kwargs['amount'], 8)
        if amount <= 0:
            raise InvalidArgument

        if not (client := get_fiat_partner_client(partner, support_type)):
            raise InvalidArgument
        assets = getattr(client, f"{support_type.value}_assets")
        fiats = getattr(client, f"{support_type.value}_fiats")
        order_limit_min = getattr(client, f"{support_type.value}_order_limit_min")
        order_limit_max = getattr(client, f"{support_type.value}_order_limit_max")
        if from_ in assets and to in fiats:
            price = client.asset_to_fiat(from_, to, support_type)
            rate = PriceManager.fiat_to_usd(to)
            amount_usd = price * amount * rate
        elif from_ in fiats and to in assets:
            amount_usd = PriceManager.fiat_to_usd(from_) * amount
        else:
            raise InvalidArgument

        if quantize_amount(amount_usd, 0) > order_limit_max or \
                quantize_amount(amount_usd, 0, ROUND_UP) < order_limit_min:
            raise InvalidArgument(message=_("输入的资产数量过大或过小"))
        try:
            quote = client.quote(g.user, from_, to, amount, support_type)
        except RESTClient.BadResponse as e:
            current_app.logger.error(f"quote to {partner} error: {e!r}")
            raise InvalidArgument(message=_('%(partner)s异常，请稍后重试', partner=partner))
        cache = FiatQuoteCache(client.name, g.user.id, quote.id)
        quote = quote._asdict()
        cache.set(json.dumps(quote, cls=JsonEncoder), ex=cache.ttl)
        return quote


@ns.route('/<partner>/orders')
@respond_with_code
class FiatOrdersResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        quote_id=mm_fields.String(required=True)
    ))
    def post(cls, partner, **kwargs):
        require_user_request_permission(g.user)
        g.request_ip = get_request_ip()
        quote_id = kwargs['quote_id']
        cache = FiatQuoteCache(partner, g.user.id, quote_id)
        quote = cache.read()
        if not quote:
            raise FiatOrderExpire
        quote = Quote(**json.loads(quote))

        if not (client := get_fiat_partner_client(partner, SupportType(quote.support_type))):
            raise InvalidArgument
        chain = client.get_asset_chain(quote.asset, g.user)
        address, memo = WalletClient().get_or_create_deposit_address(chain, g.user.id)
        if memo and SupportType(quote.support_type) == SupportType.BUY:
            raise InvalidArgument(message=f"Purchasing {quote.asset} is not supported")
        try:
            order = client.place_order(
                user=g.user,
                quote=quote,
                address=address,
            )
        except RESTClient.BadResponse as e:
            current_app.logger.error(f"place order to {partner} error: {e!r}")
            raise InvalidArgument(message=f'{partner} Service Unavailable')
        cache.delete()
        FiatOrder.new_record(
            user_id=g.user.id,
            asset=quote.asset,
            coin_amount=quote.asset_amount,
            fiat_currency=quote.fiat,
            fiat_total_amount=quote.fiat_amount,
            payment_id=order.extra.get('payment_id') or order.id,
            order_id=order.id,
            deposit_address=address,
            third_party=client.name,
            order_type=FiatOrder.OrderType(quote.support_type)
        )
        return {
            'partner': client.name,
            'payment_url': order.payment_url,
            'address': address,
            'extra': order.extra,
            # 暂时兼容
            'action': {},
            'can_place_order': True,
        }


@ns.route('/order-list')
@respond_with_code
class FiatOrderListResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        order_type=EnumField(FiatOrder.OrderType),
        status=EnumField(FiatOrder.StatusType, enum_by_value=True),
        asset=AssetField(),
        fiat=mm_fields.String(),
        page=mm_fields.Integer(missing=1),
        limit=mm_fields.Integer(missing=50),
    ))
    def get(cls, **kwargs):
        page, limit = kwargs['page'], kwargs['limit']
        query = FiatOrder.query.filter(
            FiatOrder.user_id == g.user.id
        )
        if order_type := kwargs.get('order_type'):
            query = query.filter(
                FiatOrder.order_type == order_type
            )
        if status := kwargs.get('status'):
            query_status = [status]
            if status in [FiatOrder.StatusType.PENDING, FiatOrder.StatusType.CREATE]:
                query_status = [FiatOrder.StatusType.CREATE, FiatOrder.StatusType.PENDING]
            query = query.filter(
                FiatOrder.status.in_(query_status)
            )
        if asset := kwargs.get('asset'):
            query = query.filter(
                FiatOrder.asset == asset
            )
        if fiat := kwargs.get('fiat'):
            query = query.filter(
                FiatOrder.fiat_currency == fiat
            )

        pagination = query.order_by(FiatOrder.id.desc()).paginate(page, limit, error_out=False)
        data = []
        for v in pagination.items:
            status = FiatOrder.StatusType.PENDING.value if v.status == FiatOrder.StatusType.CREATE else v.status.value
            data.append(dict(
                created_at=v.created_at,
                asset=v.asset,
                coin_amount=v.coin_amount,
                fiat_currency=v.fiat_currency,
                fiat_total_amount=v.fiat_total_amount,
                third_party=v.third_party,
                status=status,
                order_type=v.order_type,
                price=quantize_amount(v.fiat_total_amount / v.coin_amount, 8)
            ))
        return dict(
            items=data,
            total=pagination.total,
            page=pagination.page
        )

