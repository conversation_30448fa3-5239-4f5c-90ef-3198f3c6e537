from urllib import parse

import requests
from flask import make_response
from flask import request

from app import config
from app.models import db
from app.models import User
from app.exceptions import ApiPermissionNotAllowed
from app.api.common import Namespace, Resource
from app.api.common import get_request_user, get_api_request_user

ns = Namespace('Demo Trading')
url_prefix = '/demo_trading'


@ns.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
class RelayResource(Resource):
    HOP_BY_HOP_HEADERS = (
        'Host', 'Connection', 'Transfer-Encoding', 'Content-Encoding',
        'Content-Length', 'Authorization', 'Cookie', 'Set-Cookie',
    )

    session = requests.Session()

    def dispatch_request(self, path, *args, **kwargs):
        meth = getattr(self.session, request.method.lower())
        headers = self.remove_hop_by_hop_headers(request.headers)
        user = get_request_user()
        if user:
            headers['User-Id'] = str(user.id)
        else:
            headers['User-Id'] = ''
        url = parse.urljoin(config["CLIENT_CONFIGS"]["demo_trading"]["frontend_url"], path)
        if query_string := request.full_path.split('?', 1)[1]:
            url = f'{url}?{query_string}'
        db.session.close()
        r = meth(url, headers=headers, data=request.data, timeout=5)
        return make_response(r.content, r.status_code, self.remove_hop_by_hop_headers(r.headers))

    @classmethod
    def remove_hop_by_hop_headers(cls, headers):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in cls.HOP_BY_HOP_HEADERS:
                build_new_headers[name] = value
        return build_new_headers


@ns.route('/api/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
class APIRelayResource(Resource):
    HOP_BY_HOP_HEADERS = (
        'Host', 'Connection', 'Transfer-Encoding', 'Content-Encoding',
        'Content-Length', 'Authorization', 'Cookie', 'Set-Cookie',
    )

    session = requests.Session()

    def dispatch_request(self, path, *args, **kwargs):
        meth = getattr(self.session, request.method.lower())
        headers = self.remove_hop_by_hop_headers(request.headers)
        user = get_api_request_user()
        if user:
            # only internal makers can access this route
            if user.user_type == User.UserType.SUB_ACCOUNT:
                main_user = user.main_user
            else:
                main_user = user            

            if main_user.user_type != User.UserType.INTERNAL_MAKER:
                raise ApiPermissionNotAllowed
        if user:
            headers['User-Id'] = str(user.id)
        else:
            headers['User-Id'] = ''
        url = parse.urljoin(config["CLIENT_CONFIGS"]["demo_trading"]["api_url"], path)
        if query_string := request.full_path.split('?', 1)[1]:
            url = f'{url}?{query_string}'
        db.session.close()
        r = meth(url, headers=headers, data=request.data, timeout=5)
        return make_response(r.content, r.status_code, self.remove_hop_by_hop_headers(r.headers))

    @classmethod
    def remove_hop_by_hop_headers(cls, headers):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in cls.HOP_BY_HOP_HEADERS:
                build_new_headers[name] = value
        return build_new_headers
