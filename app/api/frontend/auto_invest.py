import json
from decimal import Decimal, ROUND_DOWN

from flask import g
from flask_babel import gettext
from webargs import fields

from app.api.common.request import require_user_request_permission
from app.business.auto_invest import process_auto_invest_order_task, create_auto_invest_plan_log, \
    auto_invest_plan_terminated_task, auto_invest_plan_running_task, auto_invest_plan_paused_task
from app.business.strategy.base import BaseStrategyManager
from app.caches.spot import AutoInvestMarketCache
from app.caches.system import MarketMaintainCache
from app.caches.user import SubMainUserCache
from app.common import PrecisionEnum
from app.models import db, Market
from app.api.common import Namespace, Resource, require_login, respond_with_code
from app.api.common.decorators import trade_permission_validate
from app.api.common.fields import PositiveDecimalField, EnumField, MarketField
from app.business import ServerClient, LockKeys, CacheLock
from app.caches import MarketCache
from app.exceptions import InvalidArgument, InsufficientBalance
from app.models.auto_invest import AutoInvestTask, AutoInvestSlice, AutoInvestPlanTimeConfig, \
    AutoInvestPlanNoticeConfig, AutoInvestPlan, AutoInvestPlanStatistic, AutoInvestMarket
from app.models.strategy import UserStrategy
from app.utils import quantize_amount, now

ns = Namespace("Auto-Invest")
url_prefix = '/auto_invest'


@ns.route("/plans")
@respond_with_code
class AutoInvestPlanResource(Resource):

    NOT_SUPPORTED_MSG = gettext("暂不支持定投")
    NOT_SUPPORTED_MARKET_MSG = gettext("该市场暂不支持定投")
    DEEP_NOT_ENOUGH_MSG = gettext("该币对深度不足，暂不支持定投")
    MAX_AMOUNT_NOT_ENOUGH_MSG = gettext("该币对定投金额不支持定投")

    @classmethod
    def validate_cycle_params(cls,
                              cycle_type: AutoInvestPlanTimeConfig.CycleType,
                              invest_day: int,
                              invest_time: int,
                              hour_interval: int,
                              ):
        if not (0 <= invest_time < AutoInvestPlanTimeConfig.MAX_HOUR):
            raise InvalidArgument
        if cycle_type == AutoInvestPlanTimeConfig.CycleType.DAILY:
            if hour_interval not in AutoInvestPlanTimeConfig.HOUR_INTERNALS:
                raise InvalidArgument
        elif cycle_type in [AutoInvestPlanTimeConfig.CycleType.WEEKLY,
                            AutoInvestPlanTimeConfig.CycleType.BI_WEEKLY]:
            if not (0 <= invest_day <= AutoInvestPlanTimeConfig.MAX_WEEKDAY):
                raise InvalidArgument
        elif cycle_type == AutoInvestPlanTimeConfig.CycleType.MONTHLY:
            if not (0 <= invest_day <= AutoInvestPlanTimeConfig.MAX_DAY):
                raise InvalidArgument

    @classmethod
    def validate_market_params(cls, market: str, amount: Decimal):
        market_data = Market.query.filter(
            Market.name == market
        ).first()
        if not market_data or not market_data.status == Market.Status.ONLINE or market_data.trading_disabled:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MARKET_MSG)
        market_config = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == market
        ).first()
        if not market_config or market_config.status != AutoInvestMarket.Status.OPEN:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MARKET_MSG)
        market_cache = AutoInvestMarketCache().hget(market)
        if not market_cache:
            raise InvalidArgument(message=cls.DEEP_NOT_ENOUGH_MSG)
        market_cache = json.loads(market_cache)
        if not AutoInvestPlan.MIN_ORDER_AMOUNT <= amount <= Decimal(market_cache['max_usd']):
            raise InvalidArgument(message=cls.MAX_AMOUNT_NOT_ENOUGH_MSG)
        if ServerClient().get_protect_status()['status']:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)
        market_maintains = MarketMaintainCache.get_market_maintains()
        if market in market_maintains:
            raise InvalidArgument(message=cls.NOT_SUPPORTED_MSG)

    @classmethod
    @require_login(allow_sub_account=False)
    @trade_permission_validate(is_spot=True, account_id=0)
    @ns.use_kwargs(
        dict(
            source_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
            source_stop_amount=PositiveDecimalField(allow_zero=True, places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
            market=MarketField(required=True),
            cycle_type=EnumField(AutoInvestPlanTimeConfig.CycleType, required=True),
            invest_day=fields.Integer(required=True),
            invest_time=fields.Integer(required=True),
            hour_interval=fields.Integer(required=True),
            notice_type=EnumField(AutoInvestPlanNoticeConfig.NoticeType, required=True),
            notice_value=PositiveDecimalField(allow_zero=True, required=True),
            timezone_offset=fields.Integer(missing=0),
            is_manual=fields.Boolean(missing=False),
        )
    )
    def post(cls, **kwargs):
        """ 提交定投计划 """
        require_user_request_permission(g.user)

        market = kwargs["market"]
        cycle_type = kwargs["cycle_type"]
        source_amount = kwargs["source_amount"]
        invest_day = kwargs["invest_day"]
        invest_time = kwargs["invest_time"]
        hour_interval = kwargs["hour_interval"]
        notice_type = kwargs["notice_type"]
        notice_value = kwargs["notice_value"]
        source_stop_amount = kwargs["source_stop_amount"]
        timezone_offset = kwargs["timezone_offset"]
        is_manual = kwargs["is_manual"]

        cls.validate_cycle_params(cycle_type, invest_day, invest_time, hour_interval)
        cls.validate_market_params(market, source_amount)
        market_info = MarketCache(market).dict
        source_amount = quantize_amount(source_amount, market_info["quote_asset_precision"])
        source_asset = market_info["quote_asset"]
        target_asset = market_info["base_asset"]
        user_id = g.user.id

        with CacheLock(key=LockKeys.create_strategy(user_id), wait=False):
            db.session.rollback()
            strategy = BaseStrategyManager(user_id)
            basic_sty = strategy.new_strategy(UserStrategy.Type.AUTO_INVEST)
            now_ = now()
            plan = AutoInvestPlan(
                user_id=user_id,
                strategy_id=basic_sty.id,
                sys_user_id=basic_sty.run_user_id,
                target_asset=target_asset,
                source_asset=source_asset,
                source_amount=source_amount,
                market=market,
                next_effected_at=now_,
            )
            db.session.add(plan)
            db.session.flush()
            time_config = AutoInvestPlanTimeConfig(
                plan_id=plan.id,
                cycle_type=cycle_type,
                invest_day=invest_day,
                invest_time=invest_time,
                hour_interval=hour_interval,
                last_edited_at=now_,
                timezone_offset=timezone_offset,
                is_manual=is_manual,
            )
            db.session.add(time_config)
            notice_config = AutoInvestPlanNoticeConfig(
                plan_id=plan.id,
                notice_type=notice_type,
                notice_value=notice_value,
                source_stop_amount=source_stop_amount,
            )
            db.session.add(notice_config)
            statistic = AutoInvestPlanStatistic(plan_id=plan.id)
            db.session.add(statistic)
            plan.next_effected_at = now_ if is_manual else time_config.get_next_effective_time(is_first=True)
            if is_manual:
                order = AutoInvestTask(
                    plan_id=plan.id,
                    user_id=plan.user_id,
                    sys_user_id=plan.sys_user_id,
                    market=plan.market,
                    source_asset=plan.source_asset,
                    source_asset_amount=source_amount,
                    target_asset=plan.target_asset,
                    effected_at=now_,
                )
                db.session.add(order)
            db.session.commit()
        if is_manual:
            process_auto_invest_order_task.delay(order.id)

        SubMainUserCache.add_sub_user(basic_sty.run_user_id, basic_sty.user_id)
        create_auto_invest_plan_log.delay(plan.id)
        return {"id": plan.id}

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 定投计划列表 """
        user_id = g.user.id
        plan_query = AutoInvestPlan.query.filter(
            AutoInvestPlan.user_id == user_id
        )
        plan_rows = plan_query.all()
        plan_ids = [plan.id for plan in plan_rows]
        statistic_query = AutoInvestPlanStatistic.query.filter(
            AutoInvestPlanStatistic.plan_id.in_(plan_ids)
        ).all()
        statistic_map = {statistic.plan_id: statistic for statistic in statistic_query}
        plan_list = []
        tickers = ServerClient().get_all_market_tickers()
        for plan in plan_rows:
            plan_data = dict(
                id=plan.id,
                created_at=plan.created_at,
                target_asset=plan.target_asset,
                source_amount=plan.source_amount,
                source_asset=plan.source_asset,
                market=plan.market,
                status=plan.status.name,
                stop_at=plan.stop_at,
            )
            plan_statistic = statistic_map[plan.id]
            plan_data['target_avg_price'] = plan_statistic.target_avg_price
            plan_data['target_end_price'] = plan_statistic.target_end_price
            plan_data['order_count'] = plan_statistic.order_count
            plan_data['total_source_amount'] = plan_statistic.total_source_amount
            plan_data['total_target_amount'] = plan_statistic.total_target_amount
            ticker = tickers.get(plan.market)
            last_price = Decimal(ticker['last']) if ticker else 0
            plan_data['end_price'] = plan_data['target_end_price'] if plan_data['target_end_price'] else last_price
            plan_data['last_price'] = last_price
            plan_data['profit_amount'] = plan_data['total_target_amount'] * plan_data['end_price'] - plan_data['total_source_amount']
            plan_data['profit_rate'] = (
                plan_data['profit_amount'] / plan_data['total_source_amount'] if plan_data['total_source_amount'] else 0
            )
            plan_list.append(plan_data)

        return plan_list


@ns.route('/plans/orders/<int:id_>')
@respond_with_code
class AutoInvestPlansOrdersResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=fields.Integer,
        limit=fields.Integer,
    ))
    def get(cls, id_, **kwargs):
        limit = kwargs['limit']
        page = kwargs['page']
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument
        pagination = AutoInvestTask.query.filter(
            AutoInvestTask.plan_id == id_,
            AutoInvestTask.status == AutoInvestTask.Status.FINISHED,
        ).order_by(
            AutoInvestTask.id.desc()
        ).paginate(page, limit)

        order_list = []
        for item in pagination.items:
            order = dict(
                id=item.id,
                created_at=item.created_at,
                effected_at=item.effected_at,
                source_asset=item.source_asset,
                target_asset=item.target_asset,
                source_asset_traded_amount=item.source_asset_traded_amount,
                target_asset_traded_amount=item.target_asset_traded_amount,
                fee_asset=item.fee_asset,
                fee_amount=item.fee_amount,
                target_asset_avg_price=item.target_asset_avg_price,
            )
            order_list.append(order)

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=pagination.total,
            data=order_list,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/plans/<int:id_>')
@respond_with_code
class AutoInvestPlanResourceDetail(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument
        market = plan.market
        client = ServerClient()
        last_price = client.market_last(market)
        time_config = plan.time_config
        next_effected_at = plan.next_effected_at
        if plan.status == AutoInvestPlan.Status.PAUSED:
            next_effected_at = time_config.get_next_effective_time()
        plan_data = dict(
            id=plan.id,
            created_at=plan.created_at,
            target_asset=plan.target_asset,
            source_amount=plan.source_amount,
            source_asset=plan.source_asset,
            market=plan.market,
            stop_at=plan.stop_at,
            status=plan.status.name,
            next_effected_at=next_effected_at,
            cycle_type=time_config.cycle_type.name,
            invest_day=time_config.invest_day,
            invest_time=time_config.invest_time,
            hour_interval=time_config.hour_interval,
        )
        notice_config = plan.notice_config
        plan_data['source_stop_amount'] = notice_config.source_stop_amount
        plan_data['notice_value'] = notice_config.notice_value
        plan_data['notice_type'] = notice_config.notice_type.name
        statistic = plan.statistic
        plan_data['total_target_amount'] = statistic.total_target_amount
        plan_data['total_source_amount'] = statistic.total_source_amount
        plan_data['target_avg_price'] = statistic.target_avg_price
        plan_data['target_end_price'] = statistic.target_end_price
        plan_data['order_count'] = statistic.order_count
        plan_data['last_price'] = last_price
        plan_data['end_price'] = plan_data['target_end_price'] if plan_data['target_end_price'] else last_price
        plan_data['profit_amount'] = plan_data['total_target_amount'] * plan_data['end_price'] - plan_data['total_source_amount']
        plan_data['profit_rate'] = plan_data['profit_amount'] / plan_data['total_source_amount'] if plan_data['total_source_amount'] else 0
        plan_data['is_processing'] = plan.is_order_processing
        return plan_data

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            source_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
            cycle_type=EnumField(AutoInvestPlanTimeConfig.CycleType, required=True),
            invest_day=fields.Integer(required=True),
            invest_time=fields.Integer(required=True),
            hour_interval=fields.Integer(required=True),
            is_time_changed=fields.Boolean(required=True),
            source_stop_amount=PositiveDecimalField(allow_zero=True, places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
            notice_type=EnumField(AutoInvestPlanNoticeConfig.NoticeType, required=True),
            notice_value=PositiveDecimalField(allow_zero=True, required=True),
            timezone_offset=fields.Integer(missing=0),
            is_manual=fields.Boolean(missing=False),
        )
    )
    def put(cls, id_, **kwargs):
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument

        source_amount = kwargs['source_amount']
        cycle_type = kwargs['cycle_type']
        invest_day = kwargs['invest_day']
        invest_time = kwargs['invest_time']
        hour_interval = kwargs['hour_interval']
        is_time_changed = kwargs['is_time_changed']
        source_stop_amount = kwargs['source_stop_amount']
        notice_type = kwargs['notice_type']
        notice_value = kwargs['notice_value']
        timezone_offset = kwargs['timezone_offset']
        is_manual = kwargs['is_manual']
        AutoInvestPlanResource.validate_cycle_params(cycle_type, invest_day, invest_time, hour_interval)
        AutoInvestPlanResource.validate_market_params(plan.market, source_amount)
        now_ = now()
        need_create_order = False
        if is_time_changed and is_manual:
            need_create_order = True
        with CacheLock(key=LockKeys.auto_invest_order(plan_id=plan.id), wait=False):
            db.session.rollback()
            if plan.is_order_processing:
                raise InvalidArgument(message=gettext('本次定投正在购买，请稍后再试'))
            if plan.status != AutoInvestPlan.Status.RUNNING:
                raise InvalidArgument
            if need_create_order:
                order = AutoInvestTask(
                    plan_id=plan.id,
                    user_id=plan.user_id,
                    sys_user_id=plan.sys_user_id,
                    market=plan.market,
                    source_asset=plan.source_asset,
                    source_asset_amount=source_amount,
                    target_asset=plan.target_asset,
                    effected_at=now_,
                )
                db.session.add(order)

            time_config = plan.time_config
            time_config.cycle_type = cycle_type
            time_config.hour_interval = hour_interval
            time_config.invest_day = invest_day
            time_config.invest_time = invest_time
            time_config.timezone_offset = timezone_offset
            time_config.is_manual = is_manual
            if is_time_changed:
                time_config.last_edited_at = now_

            notice_config = plan.notice_config
            notice_config.source_stop_amount = source_stop_amount
            notice_config.notice_type = notice_type
            notice_config.notice_value = notice_value
            notice_config.last_noticed_at = None

            if is_time_changed:
                plan.next_effected_at = now_ if is_manual else time_config.get_next_effective_time(is_first=True)
            plan.source_amount = source_amount
            db.session.commit()

        if need_create_order:
            process_auto_invest_order_task.delay(order.id)
        create_auto_invest_plan_log.delay(plan.id)


@ns.route("/markets/config")
@respond_with_code
class AutoInvestMarketsResource(Resource):
    @classmethod
    def get(cls):
        """ 返回支持定投市场的最大定投市值，最小下单量（固定USDT：1） """
        market_dict = AutoInvestMarketCache().read()
        result = []
        for market, depth_data in market_dict.items():
            result.append(dict(max_usd=json.loads(depth_data)['max_usd'],
                               market=market,
                               ))
        return dict(
            markets=result,
            min_order_amount=AutoInvestPlan.MIN_ORDER_AMOUNT
        )


@ns.route('/plans/status/<int:id_>')
@respond_with_code
class AutoInvestPlanStatusResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            status=EnumField(AutoInvestPlan.Status, required=True),
        )
    )
    def put(cls, id_, **kwargs):
        status = kwargs['status']
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument
        if plan.status == status:
            raise InvalidArgument
        if plan.is_order_processing:
            raise InvalidArgument(message=gettext('本次定投正在购买，请稍后再试'))

        if status == AutoInvestPlan.Status.TERMINATED:
            auto_invest_plan_terminated_task(plan.id)
        elif status == AutoInvestPlan.Status.PAUSED:
            auto_invest_plan_paused_task(plan.id)
        elif status == AutoInvestPlan.Status.RUNNING:
            auto_invest_plan_running_task(plan.id)


@ns.route('/plans/slice/orders/<int:id_>')
@respond_with_code
class AutoInvestPlanSliceOrderResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument

        slices = AutoInvestTask.query.filter(
            AutoInvestTask.plan_id == id_,
            AutoInvestTask.status == AutoInvestTask.Status.FINISHED,
        ).order_by(
            AutoInvestTask.id.desc()
        ).with_entities(
            AutoInvestTask.effected_at,
            AutoInvestTask.target_asset_traded_amount,
            AutoInvestTask.source_asset_traded_amount,
            AutoInvestTask.target_asset_avg_price,
        ).limit(365).all()
        return [(i.effected_at,
                 i.source_asset_traded_amount,
                 i.target_asset_traded_amount, i.target_asset_avg_price
                 ) for i in slices]


@ns.route('/plans/slice/<int:id_>')
@respond_with_code
class AutoInvestPlanSliceResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument
        slices = AutoInvestSlice.query.filter(
            AutoInvestSlice.plan_id == id_,
        ).order_by(AutoInvestSlice.date.desc()).limit(180).all()
        return [(i.date, i.profit_amount) for i in slices][::-1]  # 时间早的放最前面


@ns.route('/plans/manual/<int:id_>')
@respond_with_code
class PlanInvestManualResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            source_amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, id_, **kwargs):
        plan = AutoInvestPlan.query.filter(
            AutoInvestPlan.id == id_,
            AutoInvestPlan.user_id == g.user.id,
        ).first()
        if not plan:
            raise InvalidArgument
        if plan.status == AutoInvestPlan.Status.TERMINATED:
            raise InvalidArgument(message=gettext('策略已终止，无法立即定投'))
        source_amount = kwargs['source_amount']
        AutoInvestPlanResource.validate_market_params(plan.market, source_amount)

        balance = ServerClient().get_user_balances(plan.user_id, plan.source_asset)[plan.source_asset]
        if balance["available"] < source_amount:
            raise InsufficientBalance

        with CacheLock(key=LockKeys.auto_invest_order(plan_id=plan.id), wait=False):
            db.session.rollback()
            if plan.is_order_processing:
                raise InvalidArgument(message=gettext('本次定投正在购买，请稍后再试'))
            order = AutoInvestTask(
                plan_id=plan.id,
                user_id=plan.user_id,
                sys_user_id=plan.sys_user_id,
                market=plan.market,
                source_asset=plan.source_asset,
                source_asset_amount=source_amount,
                target_asset=plan.target_asset,
                op_type=AutoInvestTask.OpType.MANUAL,
                effected_at=now(),
            )
            db.session.add(order)
            db.session.commit()
            process_auto_invest_order_task.delay(order.id)
