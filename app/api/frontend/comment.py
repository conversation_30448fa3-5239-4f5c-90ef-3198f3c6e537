import json
import re

import requests

from urllib import parse

from flask import request, g
from flask import make_response
from flask_api.exceptions import AuthenticationFailed
from webargs import fields
from werkzeug.routing import Map, Rule
from werkzeug.exceptions import NotFound

from app import config

from app.api.common import Resource, get_request_user, respond_with_code
from app.api.common import Namespace
from app.api.common.decorators import require_login
from app.api.common.fields import AssetField, PositiveDecimalField
from app.api.common.request import is_old_app_request
from app.business.fee import UserVipLevelLocalCache
from app.business.user import UserRepository, UserPreferences
from app.common.constants import PrecisionEnum
from app.exceptions import ErrorWithResponseCode, SubAccountNotAllowed, UserNotAgreeCommentTOS, InvalidArgument
from app.models import UserExtra, User, db
from app.utils.amount import quantize_amount
from app.utils.net import get_url_base
from app.models.asset import AssetIdentity
from app.models.quotes import CoinInformation
from app.business.comment import CommentTipHandler
from logging import getLogger


_logger = getLogger(__name__)

ns = Namespace('Comment')

url_base = get_url_base(config['CLIENT_CONFIGS']['comment_internal']['url'])


@ns.route('/comment/at-user/check')
@respond_with_code
class CommentAtUserCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        account_name=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        account_name = kwargs['account_name']
        extra = UserExtra.query.filter(UserExtra.account_name == account_name).first()
        if not extra:
            return {}

        user = User.query.filter(User.id == extra.user_id).first()
        sign_off_users = UserRepository.get_sign_off_users([user.id])
        if user.id in sign_off_users:
            # 注销用户不要匹配到
            return {}

        return {
            "user_id": extra.user_id,
            "account_name": extra.display_account_name,
            "user_name": user.nickname,
            "avatar": extra.avatar or "",
            "avatar_url": extra.avatar_url,
        }


@ns.route('/comment-tip/tip-limit')
@respond_with_code
class CommentTipLimitResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        asset=AssetField(required=True)
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        asset = kwargs['asset']
        r = CommentTipHandler.get_tip_limit_amount(user_id, asset)
        amount = r['amount']
        type_ = r['type']
        return {
            'amount': quantize_amount(amount, 0),  # 整数
            'type': type_.name
        }


@ns.route('/comment-tip/<int:comment_id>/tip')
@respond_with_code
class CommentTipTransferResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        receive_user_id=fields.Integer(required=True),
        amount=PositiveDecimalField(required=True, places=PrecisionEnum.COIN_PLACES)
    ))
    def post(cls, comment_id, **kwargs):
        send_user_id = g.user.id
        receive_user_id = kwargs['receive_user_id']
        amount = kwargs['amount']

        handler = CommentTipHandler
        handler.check_tip_available(send_user_id, receive_user_id, comment_id, amount)
        handler.send_tip(
            send_user_id=send_user_id,
            receive_user_id=receive_user_id,
            comment_id=comment_id,
            amount=amount)


def encode_utf8_to_latin(utf8_str):
    # 请求头中的参数只能以latin编码传输，这里将utf8的字节流编码成latin编码传输，对端反向操作即可获取到原文
    return utf8_str.encode().decode("latin-1")


@ns.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
class CommentRelayResource(Resource):
    HOP_BY_HOP_HEADERS = (
        'Host', 'Connection', 'Transfer-Encoding', 'Content-Encoding',
        'Content-Length', 'Authorization', 'Cookie', 'Set-Cookie',
    )

    session = requests.Session()

    def dispatch_request(self, path, *args, **kwargs):
        meth = getattr(self.session, request.method.lower())
        headers = self.remove_hop_by_hop_headers(request.headers)
        user = get_request_user()
        headers['User-Id'] = str(user.id) if user else ""
        headers['User-Name'] = encode_utf8_to_latin(user.nickname) if user else ""
        db.session.close()
        with CommentRelayHook(path, meth, headers, request.data, request.full_path, user) as hook:
            r = hook.meth(hook.url, headers=hook.headers, data=hook.data, timeout=5)
            hook.response = r

        return make_response(
            hook.response_content if hook.response_content is not None else r.content,
            hook.response_status_code if hook.response_status_code is not None else r.status_code,
            self.remove_hop_by_hop_headers(
                hook.response_headers if hook.response_headers is not None else r.headers
            )
        )

    @classmethod
    def remove_hop_by_hop_headers(cls, headers):
        build_new_headers = {}
        for name, value in headers.items():
            if name not in cls.HOP_BY_HOP_HEADERS:
                build_new_headers[name] = value
        return build_new_headers


class CommentRelayHookMeta(type):
    path_hook_map = Map().bind("")  # 给一个绑定空服务的map，用于做path匹配
    hook_cls_map = {}

    def __new__(mcs, name, bases, dct):
        cls = super().__new__(mcs, name, bases, dct)
        path = getattr(cls, 'PATH', None)
        if path is None:  # base class
            return cls
        mcs.path_hook_map.map.add(Rule(path, endpoint=cls.__name__))
        mcs.hook_cls_map[cls.__name__] = cls
        return cls


class CommentRelayHook(metaclass=CommentRelayHookMeta):
    PATH = None

    def __init__(self, path, meth, headers, data, full_path, user):
        self.path = path
        self.meth = meth
        self.headers = headers
        self.data = data
        self.full_path = full_path

        self.url = parse.urljoin(url_base, '/res/' + self.path)
        if query_string := full_path.split('?', 1)[1]:
            self.url = f'{self.url}?{query_string}'

        self.user = user
        self.response = None
        self.response_content = None
        self.response_status_code = None
        self.response_headers = None
        if not self.PATH:
            self.instance = self.route_to_subclass(path)

    def route_to_subclass(self, url):
        try:
            endpoint, _ = self.__class__.path_hook_map.match(url)
        except NotFound:
            return self

        hook_cls = self.__class__.hook_cls_map.get(endpoint)  # 默认使用自己

        if hook_cls is not None:
            return hook_cls(self.path, self.meth, self.headers, self.data, self.full_path, self.user)
        else:
            return self

    def __enter__(self):
        self.instance.before_request()
        return self.instance

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 业务错误直接返回
        try:
            content = self.instance.response.json()
        except Exception:
            return

        if (code := content.get('code')) != 0:
            if code is None:
                return

            # 尝试使用code相同的backend侧err_msg
            if (err_cls := ErrorWithResponseCode.response_code_to_class(code)) is not None:
                data = content.get('data', {})
                _kwargs = {}
                if isinstance(data, dict):
                    _kwargs = data.pop('_kwargs', {})
                else:
                    # 其他类型报错不应该透传给用户，是错误写法，记录日志，清空data
                    _logger.error(f'raise err data invalid {err_cls} {data}')
                    data = {}
                content["message"] = err_cls(data, **_kwargs).message
                self.instance.response_content = json.dumps(content)

            return

        self.instance.after_request()

    def before_request(self):
        pass

    def after_request(self):
        pass


class CommentRelayHookMixin:
    """ 一些用于CommentRelayHook 的公用业务方法集 """

    @staticmethod
    def full_user_avatar_url(user_info):
        if user_info:
            user_info['avatar_url'] = UserRepository.get_user_avatar_url(user_info.get('avatar'))

    @staticmethod
    def full_user_vip_level(user_info, req_user):
        if user_info:
            user_id = user_info.get('user_id')
            if not user_id:
                return

            if int(user_info.get('show_vip', 0)) or (req_user and req_user.id == user_id):
                user_info['vip'] = UserVipLevelLocalCache.get(user_id)
            else:
                user_info['vip'] = None

    @classmethod
    def full_comment_user_info(cls, comment, req_user):
        user_info = comment['created_by']
        parent_user = comment.get('parent_user')

        cls.full_user_avatar_url(user_info)
        cls.full_user_avatar_url(parent_user)
        cls.full_user_vip_level(user_info, req_user)
        cls.full_user_vip_level(parent_user, req_user)

        at_users = comment.get('at_users') or []
        for at_user_info in at_users:
            cls.full_user_avatar_url(at_user_info)
            cls.full_user_vip_level(at_user_info, req_user)

        comment['created_by'] = user_info
        comment['parent_user'] = parent_user
        comment['at_users'] = at_users

        return comment

    @classmethod
    def full_comments_user_info(cls, comments, req_user):
        for comment in comments:
            if comment.get('children'):
                for children in comment['children']:
                    cls.full_comment_user_info(children, req_user)
            cls.full_comment_user_info(comment, req_user)

    @classmethod
    def match_at_user(cls, comment, at_users):
        comment = comment.replace('<br>', '\n')

        # 匹配出全部可能的 account_name
        account_name_pattern = r'@([A-Za-z0-9]{1,20})'
        account_names = re.findall(account_name_pattern, comment)
        if not account_names:
            return comment, at_users

        account_names = account_names[:10]  # 只查询前10个
        user_account_id_map = {
            ue.account_name: ue
            for ue in UserExtra.query.filter(
                UserExtra.account_name.in_(account_names),
            ).all()
        }
        user_ids = list(ue.user_id for ue in user_account_id_map.values())
        user_name_map = {
            user.id: user.nickname
            for user in User.query.filter(User.id.in_(user_ids)).all()
        }
        sign_off_users = set()
        if user_account_id_map:
            sign_off_users = UserRepository.get_sign_off_users(user_ids)
        at_user_ids = set(at_user.get('user_id') for at_user in at_users)

        parts = []
        last_end = 0
        replaced_count = 0
        for match in re.finditer(account_name_pattern, comment):
            account_name = match.group(0)  # 完整的用户名，如 @Alice
            no_at_account_name = account_name.strip("@")
            start, end = match.span()  # 匹配的用户名位置

            if replaced_count < 10:  # 只替换前10个
                parts.append(comment[last_end:start])
                if no_at_account_name in user_account_id_map:
                    extra = user_account_id_map[no_at_account_name]
                    user_id = extra.user_id
                    if user_id not in sign_off_users:
                        parts.append(f"{{{{mention:{no_at_account_name}}}}}")
                        if user_id not in at_user_ids:
                            at_users.append({
                                "name": user_name_map.get(user_id, ""),
                                "account_name": no_at_account_name,
                                "avatar": extra.avatar,
                                "user_id": user_id,
                            })
                            at_user_ids.add(user_id)
                    else:
                        parts.append(account_name)
                else:
                    parts.append(account_name)
                replaced_count += 1
            else:
                break

            last_end = end

        # 拼接剩余部分
        parts.append(comment[last_end:])
        comment = ''.join(parts).replace('\n', '<br>')
        return comment, at_users

    def fill_business_info(self):
        # 对于 GET 请求，需要从查询参数中获取数据
        query_params = {}
        if '?' in self.full_path:
            query_string = self.full_path.split('?', 1)[1]
            # 解析查询字符串
            query_params = dict(parse.parse_qsl(query_string))

        business_code = query_params.get('business_code', '')
        business_id = query_params.get('business_id', None)

        if business_code:
            business_id = AssetIdentity.get_id(business_code)
            if not business_id:
                _logger.error(f"Invalid AssetIdentity code: {business_code}")
                raise InvalidArgument
        elif business_id:
            # 旧版本 app 端传递的参数
            coin_info = CoinInformation.query.get(business_id)
            if not coin_info:
                _logger.error(f"Invalid CoinInformation id: {business_id}")
                raise InvalidArgument
            business_code = coin_info.code
            business_id = AssetIdentity.get_id(business_code)
        else:
            _logger.info("Both business_code and business_id are missing")
            # 如果没有提供 business_code 或 business_id，则不进行任何处理
            return

        # 更新查询参数
        query_params['business_id'] = business_id
        query_params['business_code'] = business_code

        # 重建查询字符串和 URL
        new_query_string = parse.urlencode(query_params)
        self.url = f"{self.url.split('?')[0]}?{new_query_string}"


class CommentAddAccountInfoHook(CommentRelayHook):
    # 用于检查用户account_info是否完整，并给请求添加account_info
    def before_request(self):
        if not self.user:
            raise AuthenticationFailed

        if self.user.is_sub_account:
            raise SubAccountNotAllowed

        # 避免用户跳过协议签署异常请求
        user_preferences = UserPreferences(self.user.id)
        if is_old_app_request(3480, 91):
            viewed_tos = user_preferences.view_comment_user_tos \
                or user_preferences.view_comment_user_agreement \
                or user_preferences.user_info_confirmed_for_comment
        else:
            viewed_tos = user_preferences.view_comment_user_agreement
        if not viewed_tos:
            raise UserNotAgreeCommentTOS

        # 添加account_info
        extra = self.user.extra
        self.headers['Account-Name'] = encode_utf8_to_latin(extra.display_account_name)
        self.headers['Avatar'] = extra.avatar


class CreateCommentHook(CommentAddAccountInfoHook, CommentRelayHookMixin):
    PATH = "/comment"

    def before_request(self):
        super().before_request()

        try:
            data = json.loads(self.data)
            comment: str = data.get('content', "")
            business_code: str = data.get('business_code', None)
            at_users = data.get('at_users', [])
        except KeyError:
            raise InvalidArgument

        if not business_code:
            raise InvalidArgument

        comment, at_users = self.match_at_user(comment, at_users)
        data['content'] = comment

        business_id = AssetIdentity.get_id(business_code)
        data['business_id'] = str(business_id)

        self.data = json.dumps(data)

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        comment = data['data']
        self.full_comments_user_info([comment], self.user)
        data['data'] = comment
        self.response_content = json.dumps(data)


class ReplyCommentHook(CommentAddAccountInfoHook, CommentRelayHookMixin):
    PATH = "/comment/<comment_id>/reply"

    def before_request(self):
        super().before_request()

        try:
            data = json.loads(self.data)
            comment: str = data.get('content', "")
            at_users = data.get('at_users', [])
        except KeyError:
            raise InvalidArgument

        comment, at_users = self.match_at_user(comment, at_users)

        data['content'] = comment
        self.data = json.dumps(data)

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        comment = data['data']
        self.full_comments_user_info([comment], self.user)
        data['data'] = comment
        self.response_content = json.dumps(data)


class CommentVoteHook(CommentAddAccountInfoHook):
    PATH = "/comment/<comment_id>/vote"


class CommentReportHook(CommentAddAccountInfoHook):
    PATH = "/comment/<comment_id>/report"


class CommentListHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/comment-list"

    def before_request(self):
        super().before_request()
        self.fill_business_info()

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        comments = data['data']
        self.full_comments_user_info(comments, self.user)
        data['data'] = comments
        self.response_content = json.dumps(data)


class CommentInfoHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/comment-info"

    def before_request(self):
        super().before_request()
        self.fill_business_info()


class CommentTipsHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/<comment_id>/tips"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return
        items = data['data']['items']
        for item in items:
            self.full_user_avatar_url(item['send_user_info'])
        self.response_content = json.dumps(data)


class CommentTipUsersHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/<comment_id>/tip-users"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return
        
        items = data['data']['items']
        for item in items:
            self.full_user_avatar_url(item)
        self.response_content = json.dumps(data)


class CommentReplyListHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/<comment_id>/reply-list"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        comments = data['data']['replies']
        self.full_comments_user_info(comments, self.user)
        self.response_content = json.dumps(data)


class CommentRootHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/comment/root"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        comment = data['data']
        self.full_comments_user_info([comment], self.user)
        data['data'] = comment
        self.response_content = json.dumps(data)


class CommentMessageHook(CommentRelayHook):
    PATH = "/comment-message/message-list"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        messages = data['data']['messages']
        for msg in messages:
            user_info = msg['user_info']
            if user_info:
                user_info['avatar_url'] = UserRepository.get_user_avatar_url(user_info.get('avatar'))

            msg['user_info'] = user_info

        self.response_content = json.dumps(data)


class CommentATUserListHook(CommentRelayHook):
    PATH = "/comment/at-user-list"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        user_ids = set()
        for user_info in data['data']:
            if user_info and user_info.get('user_id'):
                user_ids.add(user_info.get('user_id'))

        # 查询最新的用户信息
        user_info_map = UserRepository.get_user_account_info_mapper(user_ids)

        user_infos = []
        for user_info in data['data']:
            user_id = user_info.get('user_id')

            new_user_info = user_info_map.get(user_id, user_info)
            new_user_info['user_id'] = user_id
            new_user_info['avatar_url'] = new_user_info.get('avatar', "")
            new_user_info['avatar'] = new_user_info.pop('avatar_key') or ""
            new_user_info['user_name'] = new_user_info.pop('name', '')
            new_user_info['user_sign_off'] = user_info.get('user_sign_off', False)
            user_infos.append(new_user_info)

        data['data'] = user_infos
        self.response_content = json.dumps(data)


class CommentUserInfoHook(CommentRelayHook, CommentRelayHookMixin):
    PATH = "/user/info"

    def after_request(self):
        try:
            data = json.loads(self.response.content)
        except Exception:
            # 遇到解析异常，不做后续处理
            return

        user_info = data['data']
        self.full_user_vip_level(user_info, self.user)
        data['data'] = user_info
        self.response_content = json.dumps(data)
