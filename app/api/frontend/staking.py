# -*- coding: utf-8 -*-
from datetime import timedelta
from decimal import Decimal
from enum import Enum
import json
from math import ceil
from flask import g
from app.api.common.decorators import limit_user_frequency
from app.api.common.request import get_request_language, get_request_user, require_user_request_permission
from app.assets.asset import asset_to_default_chain, get_asset, get_asset_config, get_chain
from app.business.clients.server import ServerClient
from app.business.prices import PriceManager
from app.business.push import send_staking_remove_success_push
from app.business.staking import StakingOperation
from app.business.utils import AssetComparator
from app.common.constants import PrecisionEnum

from app.exceptions.basic import InvalidArgument
from app.models.daily import DailyStakingReport
from app.models.staking import StakingAccount, StakingHistory, StakingIntroduction, StakingUserSummary
from app.models.wallet import AssetPrice
from app.utils.amount import quantize_amount
from app.utils.date_ import date_to_datetime
from ..common import Namespace, Resource, respond_with_code, require_login
from ..common.fields import <PERSON>Field, LimitField, AssetField, EnumField, PositiveDecimalField
from ...business import CacheLock, Lock<PERSON>eys
from ...utils import today


url_prefix = '/staking'
ns = Namespace('Staking')

def _get_income_rate_map() -> dict[str, Decimal]:

    # 取近7天的报表
    today_ = today()
    all_records = DailyStakingReport.query.filter(
        DailyStakingReport.report_date >= today_ - timedelta(days=7),
    ).order_by(DailyStakingReport.report_date.desc()).with_entities(
        DailyStakingReport.asset,
        DailyStakingReport.report_date,
        DailyStakingReport.income_rate,
        DailyStakingReport.staking_amount,
        DailyStakingReport.system_staking_amount,
        DailyStakingReport.user_reward_amount
    ).all()
    records, added = [], set()
    for record in all_records:
        if record.asset in added:
            continue
        records.append(record)
        added.add(record.asset)
    
    date_price_map = dict()
    result = {}
    total_reward_usd = total_staking_usd = 0
    for record in records:
        record: DailyStakingReport
        if record.report_date not in date_price_map:
            date_price_map[record.report_date] = AssetPrice.get_close_price_map(record.report_date)
        price_map = date_price_map[record.report_date]
        total_reward_usd += record.user_reward_amount * price_map.get(record.asset, 0)
        total_staking_usd += record.staking_amount * price_map.get(record.asset, 0)
        result[record.asset] = record.income_rate
    average_rate = 0
    if total_staking_usd:
        average_rate = total_reward_usd * 365 / total_staking_usd
    result['ALL_ASSETS'] = quantize_amount(average_rate, PrecisionEnum.RATE_PLACES)
    return result


class OrderType(Enum):
    BY_ASSET = 'by_asset'
    DEFAULT = 'default'

def _sort_assets(items, order=None):
    """
    用此函数控制质押币种排序, 如币种不包含在asset_order, 前端不展示该币种
    """
    asset_order = StakingOperation.get_asset_order_list()
    if order == OrderType.BY_ASSET:
        result = sorted(items, key=lambda x: AssetComparator(x['asset']))
    else:
        score_map = {asset: i for i, asset in enumerate(asset_order)}
        result = sorted(items, key=lambda x: score_map.get(x['asset'], Decimal("inf")))
    return result


@ns.route('/accounts')
@respond_with_code
class StakingAccountsResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(max_limit=100),
        order=EnumField(OrderType)
    ))
    def get(cls, **kwargs):
        client = ServerClient()
        query = StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN,
                                            StakingAccount.is_display.is_(True))
        pagintaions = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        summary_records = StakingUserSummary.query.filter(
            StakingUserSummary.user_id == g.user.id,
        ).with_entities(
            StakingUserSummary.asset,
            StakingUserSummary.staking_amount,
            StakingUserSummary.unstaking_amount,
            StakingUserSummary.total_reward_usd,
            StakingUserSummary.daily_reward_usd,
            StakingUserSummary.total_reward,
            StakingUserSummary.daily_reward,
            StakingUserSummary.last_reward_at
        ).all()
        income_rate_map = _get_income_rate_map()
        summary_map = {record.asset: record for record in summary_records}
        user_id = g.user.id
        balances = client.get_user_balances(user_id, account_id=StakingAccount.ACCOUNT_ID)
        result = []
        yesterday_income = total_income = Decimal()
        today_dt = date_to_datetime(today())
        price_map = PriceManager.assets_to_usd()
        
        for record in pagintaions.items:
            balance = Decimal(balances.get(record.asset, {}).get("available", Decimal()))
            summary = summary_map.get(record.asset)
            last_income_usd = last_income = Decimal()
            if summary and summary.last_reward_at == today_dt:
                last_income_usd = summary.daily_reward_usd
                last_income = summary.daily_reward
            yesterday_income += last_income_usd
            total_income += summary.total_reward_usd if summary else Decimal()
            conf = get_asset_config(record.asset)
            unstaking_amount = summary.unstaking_amount if summary else Decimal()
            result.append(dict(
                asset=record.asset,
                amount_usd=balance * price_map.get(record.asset, Decimal()),
                amount=balance,
                staking_amount=summary.staking_amount if summary else Decimal(),
                unstaking_amount=unstaking_amount,
                total_income=summary.total_reward if summary else Decimal(),
                yesterday_income=last_income,
                income_rate=income_rate_map.get(record.asset, Decimal()),
                unstaking_period=ceil(conf.staking_withdraw_period / 24),
                can_unstake_amount=balance - unstaking_amount,
                **StakingOperation(record.asset).get_configs()
            ))
        return dict(
            accounts=_sort_assets(result, kwargs.get('order')),
            yesterday_income=yesterday_income,
            total_income=total_income,
            income_rate=income_rate_map.get('ALL_ASSETS', Decimal())
        )

@ns.route('')
@respond_with_code
class StakingOperationResource(Resource):


    @classmethod
    @require_login
    @limit_user_frequency(10, 600)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        amount=PositiveDecimalField(required=True),
    ))
    def post(cls, **kwargs):
        require_user_request_permission(g.user)
        asset, amount = kwargs['asset'], kwargs['amount']
        account = StakingAccount.query.filter(StakingAccount.asset == asset,
                                              StakingAccount.status == StakingAccount.Status.OPEN).first()
        if not account:
            raise InvalidArgument
        amount = quantize_amount(amount, PrecisionEnum.COIN_PLACES)
        with CacheLock(LockKeys.staking_operation(asset), wait=False):
            status = StakingOperation(asset).transfer_in(g.user, amount)
            if status == StakingHistory.Status.QUEUED:
                return dict(status='PROCESSING')
            else:
                return dict(status='FINISHED')
    
    @classmethod
    @require_login
    @limit_user_frequency(10, 600)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        amount=PositiveDecimalField(required=True),
    ))
    def delete(cls, **kwargs):
        asset, amount = kwargs['asset'], kwargs['amount']
        account = StakingAccount.query.filter(StakingAccount.asset == asset,
                                              StakingAccount.status == StakingAccount.Status.OPEN).first()
        if not account:
            raise InvalidArgument
        amount = quantize_amount(amount, PrecisionEnum.COIN_PLACES)
        with CacheLock(LockKeys.staking_operation(asset), wait=False):
            StakingOperation(asset).transfer_out(g.user, amount)
        send_staking_remove_success_push.delay(g.user.id, asset)

    
@ns.route('/history')
@respond_with_code
class StakingHistoryResource(Resource):

    @staticmethod
    def format_status(status):
        if status in (StakingHistory.Status.CREATED, 
                      StakingHistory.Status.QUEUED,
                      StakingHistory.Status.DEDUCTED):
            return StakingHistory.Status.CREATED.name
        else:
            return status.name

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        asset=AssetField,
        type=EnumField(StakingHistory.Type, required=False),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        asset = kwargs.get('asset')
        if asset:
            account = StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN,
                                                  StakingAccount.asset == asset).first()
            if not account:
                raise InvalidArgument
        query = StakingHistory.query.filter(
            StakingHistory.user_id == g.user.id,
            StakingHistory.status != StakingHistory.Status.FAILED
        ).with_entities(
            StakingHistory.created_at,
            StakingHistory.asset,
            StakingHistory.amount,
            StakingHistory.type,
            StakingHistory.status
        ).order_by(StakingHistory.id.desc())
        if asset:
            query = query.filter(StakingHistory.asset == asset)
        if type_:= kwargs.get('type'):
            query = query.filter(StakingHistory.type == type_)
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        result = []
        for item in pagination.items:
            result.append(dict(
                created_at=item.created_at,
                asset=item.asset,
                amount=item.amount,
                type=item.type.name,
                status=cls.format_status(item.status)
            ))
        return dict(
            data=result,
            limit=kwargs['limit'],
            page=kwargs['page'],
            total=pagination.total
        )


@ns.route('/introduction')
@respond_with_code
class StakingIntroductionResource(Resource):

    @staticmethod
    def _get_chain_name(asset):
        chain = asset_to_default_chain(asset)
        return get_chain(chain).display_name

    @staticmethod
    def _get_asset_full_name(asset):
        return get_asset(asset).name

    _chain_name_method_map = {
        "ETH": _get_asset_full_name,
        "CET": _get_chain_name,
        "TRX": _get_asset_full_name,
        "ADA": _get_chain_name,
        "SOL": _get_chain_name,
        "DOT": _get_chain_name,
        "SUI": _get_chain_name
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField,
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        query = StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN,
                                            StakingAccount.is_display.is_(True))
        if kwargs.get('asset'):
            query = query.filter(StakingAccount.asset == kwargs['asset'])
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        assets = [record.asset for record in pagination.items]
        language = get_request_language()
        introductions = StakingIntroduction.query.filter(
            StakingIntroduction.asset.in_(assets),
            StakingIntroduction.language == language
        ).with_entities(
            StakingIntroduction.asset,
            StakingIntroduction.content,
        ).all()
        introdction_map = dict(introductions)
        result = []
        user = get_request_user()
        summary_map = dict()
        if user:
            user_summary = StakingUserSummary.query.filter(
                StakingUserSummary.user_id == user.id,
            ).with_entities(
                StakingUserSummary.asset,
                StakingUserSummary.unstaking_amount,
            ).all()
            summary_map = dict(user_summary)
        income_rate_map = _get_income_rate_map()
        for item in pagination.items:
            name_meth = cls._chain_name_method_map.get(item.asset)

            conf = get_asset_config(item.asset)
            operation = StakingOperation(item.asset)
            result.append(dict(
                asset=item.asset,
                chain=name_meth(item.asset) if name_meth else item.asset,
                income_rate=income_rate_map.get(item.asset, Decimal()),
                introduction=json.loads(introdction_map.get(item.asset, '[]')),
                unstaking_amount=summary_map.get(item.asset, Decimal()),
                unstaking_period=ceil(conf.staking_withdraw_period / 24),
                **operation.get_configs()
            ))
        result = _sort_assets(result)
        return dict(assets=result)
