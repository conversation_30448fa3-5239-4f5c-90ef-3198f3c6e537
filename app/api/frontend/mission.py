from datetime import datetime

from flask import g
from flask_babel import gettext
from pytz import UTC
from webargs import fields as wa_fields

from app.api.common import Namespace, respond_with_code, Resource, get_request_user
from app.api.common.fields import <PERSON><PERSON>ield, LimitField, EnumField
from app.api.common.request import is_old_app_request
from app.business.mission_center.mission import MissionBiz
from app.business.mission_center.mission import UserMissionBiz, MissionUtils
from app.business.mission_center.risk import NewbieRiskSettings
from app.caches.mission import MissionCache
from app.exceptions import InvalidArgument, AppUpgradeRequired
from app.models.equity_center import EquityType
from app.models.mission_center import UserMission, SceneType, MissionCondition
from app.utils.amount import amount_to_str

ns = Namespace("Mission Center")
url_prefix = "/reward-center"


class MissionTasksMixin:

    @classmethod
    def _sort(cls, mission_data: dict) -> tuple:
        """
        排序规则:
        不同状态：进行中>结算中>已完成>已过期
        相同状态：
        进行中：
          新手任务>老用户任务
          同为新手任务：根据后台配置任务顺序
          同为老用户任务：根据后台任务生效时间倒序（后推送的排在前面）
        结算中：按照完成时间倒序
        已完成：按照完成时间倒序
        已过期：按照过期时间倒序
        :param mission_data:
        :return:
        """
        # 状态优先级：进行中(0) > 结算中(1) > 已完成(2) > 已过期(3)
        status_map = {
            UserMission.Status.PENDING.name: 0,
            UserMission.Status.SETTLING.name: 1,
            UserMission.Status.FINISHED.name: 2,
            UserMission.Status.EXPIRED.name: 3,
        }
        default_datetime = datetime.min.replace(tzinfo=UTC)
        scene_type = mission_data['scene_type']
        completed_at = mission_data.get('completed_at', default_datetime)
        mission_status = mission_data['status']
        status_score = status_map.get(mission_status, 5)
        
        scene_score = 0 if scene_type == SceneType.NEWBIE.name else 1

        if mission_status == UserMission.Status.PENDING.name:
            if scene_type == SceneType.NEWBIE.name:
                third_score = mission_data['sequence']
            else:
                # 使用负的时间戳来实现倒序
                start_at = mission_data.get('start_at', default_datetime)
                third_score = -start_at.timestamp() if start_at else 0
        elif mission_status == UserMission.Status.SETTLING.name:
            # 使用负的时间戳来实现倒序
            third_score = -completed_at.timestamp() if completed_at else 0
        elif mission_status == UserMission.Status.FINISHED.name:
            # 使用负的时间戳来实现倒序
            third_score = -completed_at.timestamp() if completed_at else 0
        elif mission_status == UserMission.Status.EXPIRED.name:
            # 使用负的时间戳来实现倒序
            expired_at = mission_data.get('expired_at', default_datetime)
            third_score = -expired_at.timestamp() if expired_at else 0
        else:
            third_score = -mission_data['id']
        return status_score, scene_score, third_score


@ns.route("/tasks")
@respond_with_code
class MissionTasksResource(Resource, MissionTasksMixin):
    
    NEW_MISSION_CONDITION_TYPES = [
        MissionCondition.COPY_TRADING_ONCE.name,
        MissionCondition.DEMO_TRADING_ONCE.name,
        MissionCondition.PERPETUAL_AMOUNT.name,
    ]
    
    @classmethod
    def filter_app_user_missions(cls, user_mission_data: list[dict]) -> list[dict]:
        if is_old_app_request(4010, 102):
            # 低版本用户接受到理财加息任务直接提示升级
            for mission in user_mission_data:
                if mission["reward"]["reward_type"] == EquityType.INVEST_INCREASE.name:
                    raise AppUpgradeRequired
        if is_old_app_request(3470, 90):  # app兼容老设备展示任务数据
            return [
                mission for mission in user_mission_data
                if mission['mission_type'] not in cls.NEW_MISSION_CONDITION_TYPES
            ]
        return user_mission_data

    @classmethod
    @ns.use_kwargs(dict(
        channel=wa_fields.String,
        refer_code=wa_fields.String,
        device_id=wa_fields.String,
        status=EnumField(UserMission.Status),
        page=PageField(missing=1),
        limit=LimitField(missing=10),
    ))
    def get(cls, **kwargs):
        page, limit = kwargs['page'], kwargs['limit']
        user = get_request_user(allow_none=True)
        if not user and MissionUtils.check_device_registered(kwargs.get("device_id")):
            return dict(
                total=0,
                curr_page=1,
                has_next=False,
                count=0,
                data=[]
            )
        user_params = MissionUtils.get_user_params(kwargs, g.get('user_tz_offset'))
        user_mission_data = UserMissionBiz.get_user_missions(user, user_params, kwargs.get("status"))
        filter_user_mission = cls.filter_app_user_missions(user_mission_data)
        items = sorted(filter_user_mission, key=cls._sort)[(page - 1) * limit: page * limit]
        total = len(filter_user_mission)
        return dict(
            total=total,
            curr_page=page,
            has_next=page * limit < total,
            count=len(items),
            data=items,
        )


@ns.route("/tasks/<int:id_>")
@respond_with_code
class MissionInfoResource(Resource):

    @classmethod
    def get_rule_info_params(cls, mission_id: int):
        cache_data = MissionCache.get_data_by_id(mission_id)
        return MissionBiz.get_build_title_params(cache_data)

    @classmethod
    def format_mission_rule_and_reward_desc(cls, mission_info: dict) -> tuple[str, str]:
        mission_type = mission_info['mission_type']
        mission_id = mission_info['mission_id']
        reward_type = mission_info['reward']['reward_type']
        value = mission_info["reward"]["value"]
        if reward_type != EquityType.INVEST_INCREASE.name:
            value = amount_to_str(value)
        params = dict(
            mission_type=gettext(MissionCondition[mission_type].value),
            total=amount_to_str(mission_info["progress"]["total"]),
            unit=mission_info["progress"]["unit"],
            value=value,
            value_type=mission_info["reward"]["value_type"],
            reward_type=gettext(EquityType[reward_type].value),
        )
        if mission_info['mission_type'] == MissionCondition.DEPOSIT_AMOUNT.name:
            mission_rule = gettext(
                "任务有效期内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的入金将无法计入有效金额；\n"
                "2. 链上充值、P2P买币、第三方买币、快捷买币均为有效入金，不包括站内转账和C-Box转账。",
            )
        elif mission_info['mission_type'] == MissionCondition.SPOT_AMOUNT.name:
            mission_rule = gettext(
                "任务有效期内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的币币交易将无法计入有效交易额；\n"
                "2. 兑换交易、现货交易、杠杆交易、策略交易均为有效币币交易；\n"
                "3. 子账户无法独立参与任务，交易金额将合并计入主账户。",
            )
        elif mission_info['mission_type'] == MissionCondition.PERPETUAL_AMOUNT.name:
            mission_rule = gettext(
                "任务有效期内完成%(mission_type)s，且累积金额 ≥ %(total)s %(unit)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "1. 请务必在规定时间内完成任务，超出活动时间的合约交易将无法计入有效交易额；\n"
                "2. 正向合约和反向合约均为有效合约交易；\n"
                "3. 子账户无法独立参与任务，交易金额将合并计入主账户。",
            )
        elif mission_info['mission_type'] == MissionCondition.COPY_TRADING_ONCE.name:
            mission_rule = gettext(
                "任务有效期内完成一次%(mission_type)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "请务必在规定时间内完成任务，超出活动时间的跟单交易将不再统计。",
            )
        elif mission_info['mission_type'] == MissionCondition.DEMO_TRADING_ONCE.name:
            mission_rule = gettext(
                "任务有效期内完成一次%(mission_type)s，"
                "获得 %(value)s %(value_type)s %(reward_type)s奖励。\n"
                "请注意：\n"
                "请务必在规定时间内完成任务，超出活动时间的模拟交易将不再统计。",
            )
        elif mission_info['mission_type'] == MissionCondition.INVITE_NEWBIE_DEPOSIT_COUNT.name:
            rule_params = cls.get_rule_info_params(mission_id)
            params['amount'] = rule_params.get("amount", 0)
            params['asset'] = rule_params.get('asset', "USDT")
            mission_rule = gettext(
                "在任务有效期内，成功邀请%(total)s位新用户注册，"
                "且每位新注册用户在任务时间结束前入金≥%(amount)s %(asset)s，可获得%(value)s %(value_type)s %(reward_type)s。\n"
                "请注意： \n"
                "1. 请务必在规定时间内完成任务，超出任务时间将无法统计新增新用户和入金额；\n"
                "2. 链上充值、P2P买币、第三方买币、快捷买币均为有效入金，不包括站内转账和C-Box转账。"
            )
        elif mission_info['mission_type'] == MissionCondition.INVITE_NEWBIE_TRADED_COUNT.name:
            rule_params = cls.get_rule_info_params(mission_id)
            params['amount'] = rule_params.get("amount", 0)
            params['asset'] = rule_params.get('asset', "USDT")
            mission_rule = gettext(
                "在任务有效期内，成功邀请%(total)s位新用户注册，"
                "且每位新注册用户在任务时间结束前交易量≥%(amount)s %(asset)s，可获得%(value)s %(value_type)s %(reward_type)s。\n"
                "请注意： \n"
                "1. 请务必在规定时间内完成任务，超出任务时间将无法统计新增新用户和交易量；\n"
                "2. 交易量包含母子账号累计的币币和合约交易。\n"
                "（1）币币交易包含全部币对现货（含AMM市场）、杠杆、兑换、策略交易；\n"
                "（2）合约交易包含全部市场正向合约和反向合约，但不包括跟单交易。\n"
            )
        elif mission_info['mission_type'] == MissionCondition.INVITE_NEWBIE_TRADED_AMOUNT.name:
            mission_rule = gettext(
                "在任务有效期内，成功邀请新用户注册，"
                "且这部分受邀新注册用户在任务时间结束前累计交易量≥%(total)s %(unit)s，可获得%(value)s %(value_type)s %(reward_type)s。\n"
                "请注意： \n"
                "1. 请务必在规定时间内完成任务，超出任务时间将无法统计新增新用户和交易量；\n"
                "2. 交易量包含母子账号累计的币币和合约交易。\n"
                "（1）币币交易包含全部币对现货（含AMM市场）、杠杆、兑换、策略交易；\n"
                "（2）合约交易包含全部市场正向合约和反向合约，但不包括跟单交易。\n"
            )
        else:
            mission_rule = ""
        if mission_info["mission_type"]:
            mission_rule = gettext(mission_rule, **params)

        reward_info = mission_info['reward']
        if reward_info['reward_type'] == EquityType.CASHBACK.name:
            reward_desc = gettext(
                "手续费返现奖励将在任务达标后发放并立即生效。\n"
                "请在有效期内根据规则使用权益，并获得手续费返现。\n"
                "请在「我的奖励」页面查看详情。\n\n"
                "CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
            )
        elif reward_info['reward_type'] == EquityType.AIRDROP.name:
            reward_desc = gettext(
                "空投奖励将在任务达标后发放至现货账户。\n"
                "请在「我的奖励」页面查看详情。\n\n"
                "CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
            )
        elif reward_info["reward_type"] == EquityType.INVEST_INCREASE.name:
            reward_desc = gettext(
                "活期理财加息奖励将在任务达标后发放至奖励中心，需激活后使用。\n"
                "请在「我的奖励」页面查看详情。\n\n"
                "CoinEx禁止作弊行为，如批量注册小号等，一经发现将取消其获奖资格。"
            )
        else:
            reward_desc = ""

        if mission_info['scene_type'] == SceneType.NEWBIE.name and NewbieRiskSettings.is_api_user:
            reward_desc += gettext("\n API用户暂不支持参与活动。")
        return mission_rule, reward_desc

    @classmethod
    def get(cls, id_):
        """
        任务详情
        :param id_: 用户登录的时候是 user_mission_id 未登录是 mission_id
        :return:
        """
        user = get_request_user(allow_none=True)
        data = UserMissionBiz.get_user_mission_info(user, id_)
        if not data:
            raise InvalidArgument
        mission_rule, reward_desc = cls.format_mission_rule_and_reward_desc(data)
        data['mission_rule'] = mission_rule
        data['reward_desc'] = reward_desc
        return data


@ns.route("/banner")
@respond_with_code
class MissionBannerResource(Resource, MissionTasksMixin):

    @classmethod
    @ns.use_kwargs(dict(
        channel=wa_fields.String,
        refer_code=wa_fields.String,
        device_id=wa_fields.String,
    ))
    def get(cls, **kwargs):
        user = get_request_user(allow_none=True)
        if not user and MissionUtils.check_device_registered(kwargs.get("device_id")):
            return []
        user_params = MissionUtils.get_user_params(kwargs, g.get('user_tz_offset'))
        user_mission_data = UserMissionBiz.get_user_missions(user, user_params, UserMission.Status.PENDING)
        # 仅仅返回新手任务
        newbie_mission_data = [row for row in user_mission_data if row['scene_type'] == SceneType.NEWBIE.name]
        data = sorted(newbie_mission_data, key=cls._sort)
        return data
