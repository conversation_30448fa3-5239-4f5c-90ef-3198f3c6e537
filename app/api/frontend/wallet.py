# -*- coding: utf-8 -*-

import json
import os
import re
import time
from datetime import timedelta
from decimal import Decimal
from typing import Dict, List, Set

from flask import g, request
from flask_babel import gettext
from markupsafe import escape
from sqlalchemy import func, or_
from webargs import fields, validate
from marshmallow import Schema, EXCLUDE

from ..common.request import is_old_app_request, require_user_request_permission
from ... import config
from ...assets import (AssetUtils, chain_to_assets, get_asset_chain_config,
                       has_asset, has_chain, list_all_assets,
                       normalise_asset_code, normalise_chain_name)
from ...assets.asset import get_chain_names, try_get_asset_chain_config
from ...business import (CacheLock, LockKeys, ServerClient,
                         UserPreferences, UserSettings,
                         WalletClient, cached, cancel_withdrawal,
                         get_user_withdrawal_limit_info,
                         get_user_withdrawable_amount, mem_cached,
                         send_api_withdrawal_address_confirmation_email,
                         send_withdrawal_approver_confirmation_email,
                         send_withdrawal_approver_join_confirmation_email,
                         send_withdrawal_approver_join_notice_email,
                         send_withdrawal_approver_leave_confirmation_email,
                         send_withdrawal_approver_leave_notice_email,
                         send_withdrawal_approver_reject_notice_email,
                         )
from ...business.deposit_audit import DepositAuditBusiness
from ...business.kyt import WithdrawalPreScreeningManager
from ...business.risk_control.withdrawal import WithdrawalRiskCheck
from ...business.security import SecuritySettingType, check_withdraw_password_by_api, update_security_statistics
from ...business.user import UserRepository
from ...business.wallet import (
    get_custom_withdrawal_fee_by_fee_asset, AddressExtraConfigs, deposit_privacy_asset_require_kyc,
    WithdrawalHelper, add_withdrawal_amount_to_cache,
    check_user_withdrawn_amount_exceeded,
)
from ...caches import (AssetConfigsViewCache, AssetsViewCache,
                       WalletBroadcastCache,
                       WithdrawalApproverCache)
from ...caches.assets import ChainDisplayMappingCache
from ...caches.user import UserBalanceSumCache, UserVisitPermissionCache
from ...common import (CUSTOM_WITHDRAWAL_FEE_ASSETS,
                       BalanceBusiness, Language,
                       MobileCodeType)
from ...exceptions import (AssetNotFound, DepositsSuspended, EmailNotBound, FileTooBig,
                           InsufficientBalance, InvalidAccount,
                           InvalidArgument, InvalidAssetCode, InvalidChainName,
                           InvalidLink, InvalidWithdrawalAddress,
                           OperationNotAllowed,
                           RecordNotFound, ServiceUnavailable,
                           WithdrawalAddressAlreadyExists,
                           WithdrawalAddressOverLimit,
                           WithdrawalApproverOverLimit,
                           )
from ...models import (ApiWithdrawalAddress, ApiWithdrawalAddressApprove,
                       ApiWithdrawalAddressRequest, Deposit, EmailToken,
                       LocalWithdrawalAddress, OperationLog,
                       SecurityToolHistory, User, Withdrawal,
                       WithdrawalAddress, WithdrawalApprove,
                       WithdrawalApprover, WithdrawalFeeHistory,
                       WithdrawalPrivilegedUser, db)
from ...models.wallet import KYTDepositAudit, KYTDepositAuditDetail, EDDAuditDetail, EDDAudit, WithdrawalCancel
from ...utils import (WhyNot, amount_to_str, datetime_to_str, export_xlsx,
                      first, new_hex_token, now,
                      validate_email, max_length_validator)
from ...utils.importer import get_table_rows
from ..common import (Namespace, Resource, get_request_host_url,
                      get_request_platform, get_request_user,
                      json_string_success, limit_user_frequency, lock_request,
                      require_2fa,
                      require_login, respond_with_code)
from ..common.fields import (AssetField, ChainField, EnumField, LimitField,
                             PageField, TimestampField)
from app.schedules.operation import notify_edd_status_changed_task

ns = Namespace('Wallet')


@ns.route('/assets')
@respond_with_code
class AssetsResource(Resource):

    @classmethod
    def get(cls):
        user = get_request_user(allow_none=True)
        is_wp = user is not None and user.id in cls.withdrawal_privileged_users()
        return cls._get(is_wp)

    @classmethod
    @mem_cached(120)
    def _get(cls, is_wp):
        cache = AssetsViewCache(is_wp)
        if data := cache.read():
            return json_string_success(data)
        return cache.reload_with_wp(is_wp)

    @classmethod
    @mem_cached(600)
    def withdrawal_privileged_users(cls) -> Set[int]:
        rows = WithdrawalPrivilegedUser.query.filter(
            WithdrawalPrivilegedUser.status == WithdrawalPrivilegedUser.Status.VALID
        ).with_entities(
            WithdrawalPrivilegedUser.user_id
        ).all()
        return {x for x, in rows}


@ns.route('/asset/configs')
@respond_with_code
class AssetConfigsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        asset=AssetField(required=True)
    ))
    def get(cls, **kwargs):
        asset = kwargs['asset']
        user = get_request_user(allow_none=True)
        is_wp = user is not None and user.id in AssetsResource.withdrawal_privileged_users()
        data = cls._get(asset, is_wp)
        for item in data:
            extra = AddressExtraConfigs.get_extra_conf(item['chain'])
            item['address_extra'] = extra
        return data

    @classmethod
    @mem_cached(120)
    def _get(cls, asset, is_wp):
        cache = AssetConfigsViewCache(is_wp)
        if data := cache.hget(asset):
            return json.loads(data)
        return cache.reload_with_wp(is_wp)[asset]


@ns.route('/address/config')
@respond_with_code
class AddressConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        chain=ChainField(required=True)
    ))
    def get(cls, **kwargs):
        """用于添加提现地址时获取地址配置"""
        chain = kwargs['chain']
        # below configs are same for all assets on the chain.
        if not (assets := chain_to_assets(chain)):
            raise InvalidArgument
        ac_conf = get_asset_chain_config(assets[0], chain)
        return dict(
            supports_memo=ac_conf.supports_manual_memo,
            memo_name=ac_conf.memo_name or '',
            address_extra=AddressExtraConfigs.get_extra_conf(chain)
        )


@ns.route('/fee')
@respond_with_code
class WalletFeeResource(Resource):

    @classmethod
    @mem_cached(120)
    def get(cls):
        return cls._get()

    @classmethod
    @cached(300)
    def _get(cls):
        data = AssetConfigsViewCache(False).read()
        result = {}
        for asset, conf in data.items():
            conf = json.loads(conf)
            result[asset] = [dict(
                chain=x['chain'],
                chain_name=x['chain_name'],
                min_deposit_amount=x['min_deposit_amount'],
                min_withdrawal_amount=x['min_withdrawal_amount'],
                withdrawal_fee=x['withdrawal_fee'],
                deflation_rate=x['deflation_rate'],
                withdrawals_enabled=x['withdrawals_enabled'],
                deposits_enabled=x['deposits_enabled'],
            ) for x in conf if x['deposits_visible'] or x['withdrawals_visible']]
        return [dict(asset=a, chains=v) for a in list_all_assets() if (v := result.get(a))]


@mem_cached(600)
def get_cached_ac_info(asset, chain):
    try:
        return get_asset_chain_config(asset, chain)
    except AssetNotFound:
        return None


def get_cached_chain_info(asset, chain):

    @mem_cached(600)
    def _get_ac_info(_chain):
        if not asset:
            _asset = chain_to_assets(_chain)[0]
        else:
            _asset = asset
        try:
            c = get_asset_chain_config(_asset, _chain)
        except AssetNotFound:
            return None
        # returns configs must be same for all assets on chain.
        return dict(
            safe_confirmations=c.safe_confirmations,
            irreversible_confirmations=c.irreversible_confirmations,
            memo_name=c.memo_name,
        )

    return _get_ac_info(chain)


@ns.route('/address/validation')
@respond_with_code
class AddressValidationResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(10, 180)
    @ns.use_kwargs(dict(
        chain=ChainField(required=True),
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        extra=fields.Dict(keys=fields.String(), values=fields.String()),
    ))
    def post(cls, **kwargs):
        """用于提现时判断是不是合约地址及CoinEx地址"""
        user = g.user
        chain = kwargs['chain']
        address = kwargs['address'].strip()
        memo = kwargs['memo']
        extra = kwargs.get('extra', {})

        extra = AddressExtraConfigs.reslove_extra(chain, extra)
        client = WalletClient()
        valid, is_contract, is_coinex_address = client.validate_address_info(chain, address, memo, extra)
        try:
            dep_address_result = client.get_deposit_addresses(user.id, chain, 1, 10)
            dep_address_set = {(x['address'], x['memo']) for x in dep_address_result}
        except:  # noqa
            # is_own_deposit_address用于边际情况，如果判断不了，可以不提示
            dep_address_set = set()
        is_own_deposit_address = (address, memo) in dep_address_set
        return {
            'valid': valid,
            'is_contract_address': is_contract,
            'is_coinex_address': is_coinex_address,
            'is_own_deposit_address': is_own_deposit_address,
        }


@ns.route('/address/withdrawal/validation')
@respond_with_code
class AddressWithdrawalValidationResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        chain=ChainField(),
        address=fields.String(required=True),
        asset=AssetField(required=True),
        amount=fields.Decimal(required=True),
    ))
    def post(cls, **kwargs):
        """用于提现时判断是否重复提交过"""
        # 只检查5分钟内的记录，limit=100一般够用
        withdrawal_list = Withdrawal.query.filter(
            Withdrawal.user_id == g.user.id,
        ).order_by(Withdrawal.id.desc()).limit(100).all()
        end_time = now()
        start_time = end_time - timedelta(minutes=5)
        filters = dict(
            amount=kwargs['amount'],
            asset=kwargs['asset'],
            chain=kwargs.get('chain', None),
            address=kwargs['address'].strip(),
        )
        results = []
        for row in withdrawal_list:
            if start_time < row.created_at < end_time:
                if all(getattr(row, k) == v for k, v in filters.items()):
                    results.append(row)
        is_duplicated = True if results else False
        return {
            'is_duplicated': is_duplicated,
        }


@ns.route('/deposits')
@respond_with_code
class DepositsResource(Resource):

    EXPORT_HEADERS = (
        {'field': 'time', Language.ZH_HANS_CN: '汇入时间',
         Language.EN_US: 'Deposit time'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种',
         Language.EN_US: 'Coin'},
        {'field': 'amount', Language.ZH_HANS_CN: '数量',
         Language.EN_US: 'Amount'},
        {'field': 'address', Language.ZH_HANS_CN: '地址',
         Language.EN_US: 'Deposit address'},
        {'field': 'status', Language.ZH_HANS_CN: '状态',
         Language.EN_US: 'Status'},
        {'field': 'tx_id', Language.ZH_HANS_CN: '交易ID',
         Language.EN_US: 'TXID'}
    )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField(),
        type=EnumField(Deposit.Type),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=PageField,
        limit=LimitField,
        export=fields.Integer(missing=0)
    ))
    def get(cls, **kwargs):
        user: User = g.user
        query = Deposit.query.filter(Deposit.user_id == user.id)
        if asset := kwargs.get('asset'):
            query = query.filter(Deposit.asset == asset)
        if d_type := kwargs.get('type'):
            if d_type == Deposit.Type.ON_CHAIN:
                query = query.filter(Deposit.type == Deposit.Type.ON_CHAIN)
            else:
                query = query.filter(Deposit.type == Deposit.Type.LOCAL)
        if start_time := kwargs.get('start_time'):
            query = query.filter(Deposit.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(Deposit.created_at < end_time)

        if kwargs['export']:
            pref = UserPreferences(user.id)
            deposits = query.order_by(Deposit.id.desc()).all()
            data = cls.deposits_to_dict(deposits)
            for d in data:
                d['time'] = datetime_to_str(d['time'], offset_minutes=pref.timezone_offset)
            return export_xlsx('coin-deposit', data, cls.EXPORT_HEADERS)

        records = query.order_by(Deposit.id.desc()) \
                       .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        data = cls.deposits_to_dict(records.items)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=data,
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    def deposits_to_dict(cls, deposits: List[Deposit]) -> List[dict]:
        addrs = [(x.chain, x.address) for x in deposits]
        txs = [(x.chain, x.tx_id) for x in deposits]
        client = WalletClient()
        addrs = client.get_explorer_addresses_url(addrs)
        txs = client.get_explorer_txs_url(txs)
        diff_amount_dict, min_amount_dict = cls.calc_too_small_deposit_diff_amount(deposits)
        chain_name_map = get_chain_names()
        sender_user_ids = {i.sender_user_id for i in deposits if i.type == Deposit.Type.LOCAL and i.sender_user_id}
        sender_user_id_email_dic = UserRepository.get_users_id_email_map(sender_user_ids)
        deposit_audits = DepositAuditBusiness.get_deposit_audits(deposits)
        return [cls.deposit_to_dict(sender_user_id_email_dic, x, diff_amount_dict.get((x.address, x.memo), Decimal()),
                                    min_amount_dict.get((x.asset, x.chain), Decimal()),
                                    addrs[i], txs[i], chain_name_map.get(x.chain, x.chain),
                                    deposit_audits.get(x.id))
                for i, x in enumerate(deposits)]

    @classmethod
    def deposit_to_dict(cls, sender_user_id_email_dic: dict, deposit: Deposit, diff_amount: Decimal = None,
                        min_amount: Decimal = None, explorer_address_url: str = None,
                        explorer_tx_url: str = None, chain_name_displayed: str = None,
                        deposit_audit: dict = None,
                        ) -> dict:
        status_mapping = {
            Deposit.Status.TO_HOT: Deposit.Status.FINISHED,
            Deposit.Status.EXCEPTION: Deposit.Status.FINISHED
        }
        status = status_mapping.get(deposit.status, deposit.status)
        if deposit.type == Deposit.Type.LOCAL:
            explorer_address_url = ''
            explorer_tx_url = ''
        if explorer_address_url is None:
            explorer_address_url = WalletClient().get_explorer_address_url(deposit.chain, deposit.address)
        if explorer_tx_url is None:
            explorer_tx_url = WalletClient().get_explorer_tx_url(deposit.chain, deposit.tx_id)
        if diff_amount is None or min_amount is None:
            diff_amount_dict, min_amount_dict = cls.calc_too_small_deposit_diff_amount([deposit])
            diff_amount = diff_amount_dict.get((deposit.address, deposit.memo), Decimal())
            min_amount = min_amount_dict.get((deposit.asset, deposit.chain), Decimal())

        c_info = dict(
            safe_confirmations=0,
            irreversible_confirmations=0,
            memo_name=''
        )
        delay_minutes = 0
        if deposit.type != Deposit.Type.LOCAL:
            address = deposit.address
            if c := get_cached_chain_info(deposit.asset, deposit.chain):
                c_info = dict(
                    safe_confirmations=c['safe_confirmations'],
                    irreversible_confirmations=c['irreversible_confirmations'],
                    memo_name=c['memo_name'] or ''
                )

            if status == Deposit.Status.PROCESSING:
                if ac_conf := get_cached_ac_info(deposit.asset, deposit.chain):
                    delay_minutes = AssetConfigsViewCache.calc_deposits_delay_minutes(ac_conf)
        else:
            pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
            sender_user_id = deposit.sender_user_id
            if sender_user_id == pool_user_id:
                address = 'viabtc'
            else:
                address = sender_user_id_email_dic.get(sender_user_id, '')
        return dict(
            id=deposit.id,
            time=deposit.created_at,
            type=Deposit.Type.ON_CHAIN.name if deposit.type is Deposit.Type.ABNORMAL else deposit.type.name,
            asset=deposit.asset or '',
            chain=deposit.chain or '',
            chain_name=chain_name_displayed or '',
            amount=deposit.amount,
            diff_amount=diff_amount,  # 最小充值金额的差额, 仅status=TOO_SMALL有用
            min_amount=min_amount,
            tx_id=deposit.tx_id,
            confirmations=min(deposit.confirmations, c_info['irreversible_confirmations']),
            address=address,
            memo=deposit.memo,
            explorer_address_url=explorer_address_url,
            explorer_tx_url=explorer_tx_url,
            status=status.name,
            audit=deposit_audit or {},
            delay_minutes=delay_minutes,
            **c_info
        )

    @classmethod
    def calc_too_small_deposit_diff_amount(cls, deposits: List[Deposit]) -> List[Dict]:
        """ 计算数额太小的充值 到最小充值金额 的差额 """
        too_small_deposits = [i for i in deposits if i.status == Deposit.Status.TOO_SMALL]
        diff_amount_dict = dict()
        min_amount_dict = dict()
        binary = func.binary
        for deposit in too_small_deposits:
            if (deposit.address, deposit.memo) in diff_amount_dict:
                continue
            rows = (
                Deposit.query.filter(
                    Deposit.type == Deposit.Type.ON_CHAIN,
                    Deposit.address == binary(deposit.address),
                    Deposit.memo == binary(deposit.memo),
                    Deposit.chain == binary(deposit.chain),
                    Deposit.asset == binary(deposit.asset),
                    Deposit.status == Deposit.Status.TOO_SMALL,
                )
                .with_entities(func.sum(Deposit.amount))
                .all()
            )
            total_too_small_amount = rows[0][0] if rows else Decimal()
            total_too_small_amount = total_too_small_amount or Decimal()
            _conf = try_get_asset_chain_config(deposit.asset, deposit.chain)
            if not _conf:
                continue
            min_amount = _conf.min_deposit_amount
            diff_amount = max(min_amount - total_too_small_amount, Decimal())
            diff_amount_dict[(deposit.address, deposit.memo)] = diff_amount
            min_amount_dict[(deposit.asset, deposit.chain)] = min_amount
        return [diff_amount_dict, min_amount_dict]


@ns.route('/deposit/<int:id_>')
@respond_with_code
class DepositResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        row: Deposit = Deposit.query \
            .filter(Deposit.id == id_,
                    Deposit.user_id == g.user.id) \
            .first()
        if row is None:
            raise InvalidArgument

        chain_name_map = get_chain_names()
        chain_name_displayed = chain_name_map.get(row.chain, row.chain)
        if row.type == Deposit.Type.LOCAL and row.sender_user_id:
            sender_user_id_email_dic = UserRepository.get_users_id_email_map([row.sender_user_id])
        else:
            sender_user_id_email_dic = {}
        deposit_audits = DepositAuditBusiness.get_deposit_audits([row])
        return DepositsResource.deposit_to_dict(
            sender_user_id_email_dic,
            row,
            chain_name_displayed=chain_name_displayed,
            deposit_audit=deposit_audits.get(row.id),
        )


@ns.route('/withdrawals/pre-screening')
@respond_with_code
class WithdrawalsPreScreeningResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(2, 5)
    @ns.use_kwargs(dict(
        type=EnumField(Withdrawal.Type, required=True),
        asset=AssetField(required=True),
        chain=ChainField(required=True),
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        amount=fields.Decimal(required=True),
    ))
    def post(cls, **kwargs):
        user: User = g.user
        w_type = kwargs['type']
        if w_type == Withdrawal.Type.LOCAL:
            raise InvalidArgument

        asset = kwargs['asset']
        chain = kwargs.get('chain')
        address = kwargs['address'].strip()
        memo = kwargs['memo']
        amount = kwargs['amount']
        risky, r_id = WithdrawalPreScreeningManager.check_address_risky(
            user.id, asset, chain, address, amount, memo
        )
        return {'risky': risky, 'id': r_id}

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        agreed=fields.Boolean(required=True),
    ))
    def patch(cls, **kwargs):
        WithdrawalPreScreeningManager.update_user_pre_screening_risk_agreement(
            kwargs['id'], g.user.id, kwargs['agreed']
        )
        return {}



@ns.route('/withdrawals')
@respond_with_code
class WithdrawalsResource(Resource):

    EXPORT_HEADERS = (
        {'field': 'time', Language.ZH_HANS_CN: '提现时间',
         Language.EN_US: 'Withdrawal time'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种',
         Language.EN_US: 'Coin'},
        {'field': 'actual_amount', Language.ZH_HANS_CN: '实际到账',
         Language.EN_US: 'Credited'},
        {'field': 'fee_amount', Language.ZH_HANS_CN: '手续费',
         Language.EN_US: 'Fee'},
        {'field': 'address', Language.ZH_HANS_CN: '提现地址',
         Language.EN_US: 'Withdrawal address'},
        {'field': 'status', Language.ZH_HANS_CN: '状态',
         Language.EN_US: 'Status'},
        {'field': 'tx_id', Language.ZH_HANS_CN: '交易ID',
         Language.EN_US: 'TXID'}
    )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField(),
        type=EnumField(Withdrawal.Type),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=PageField,
        limit=LimitField,
        export=fields.Integer(missing=0)
    ))
    def get(cls, **kwargs):
        user: User = g.user
        query = Withdrawal.query.filter(Withdrawal.user_id == user.id)
        if asset := kwargs.get('asset'):
            query = query.filter(Withdrawal.asset == asset)
        if w_type := kwargs.get('type'):
            query = query.filter(Withdrawal.type == w_type)
        if start_time := kwargs.get('start_time'):
            query = query.filter(Withdrawal.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(Withdrawal.created_at < end_time)

        if kwargs['export']:
            pref = UserPreferences(user.id)
            data = cls.withdrawals_to_dict(query.order_by(Withdrawal.id.desc()).all())
            for d in data:
                d['time'] = datetime_to_str(d['time'], offset_minutes=pref.timezone_offset)
                d['fee_amount'] = f"{amount_to_str(d['fee_amount'])} {d['fee_asset']}"
            return export_xlsx('coin-withdraw', data, cls.EXPORT_HEADERS)

        records = query.order_by(Withdrawal.id.desc()) \
                       .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=cls.withdrawals_to_dict(records.items),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    @require_2fa(MobileCodeType.WITHDRAWAL_APPLICATION, allow_sub_account=False)
    @ns.use_kwargs(dict(
        type=EnumField(Withdrawal.Type, required=True),
        asset=AssetField(required=True),
        chain=ChainField,
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        amount=fields.Decimal(required=True),
        extra=fields.Dict(keys=fields.String(), values=fields.String()),
        remark=fields.String(missing='', validate=validate.Length(max=256)),
        withdraw_password=fields.String(),  # 提现密码
        fee_asset=AssetField(required=False),  # 自定义的提现手续费币种
        screen_id=fields.Integer(required=False),
    ))
    def post(cls, **kwargs):
        user: User = g.user

        WithdrawalHelper.validate_user_permission(user)
        check_withdraw_password_by_api(user, kwargs.get('withdraw_password'))

        w_type = kwargs['type']
        # TODO: 确认 app 版本号进行兼容
        if not is_old_app_request(3183, 88):
            cls._try_validate_risk_agreement(w_type, user.id, kwargs.get('screen_id'))

        asset = kwargs['asset']
        chain = kwargs.get('chain')
        address = kwargs['address'].strip()
        memo = kwargs['memo']
        amount = kwargs['amount']
        extra = kwargs.get('extra', {})
        remark = kwargs['remark']
        fee_asset = kwargs.get('fee_asset') or asset  # 未传时默认是提现币种作为手续费

        WithdrawalHelper.validate_fee_asset(asset, fee_asset)

        if w_type == Withdrawal.Type.ON_CHAIN:
            WithdrawalHelper.validate_onchain_withdrawal_params(
                user, asset, chain, address, memo, amount, extra
            )
            fee = WithdrawalHelper.get_onchain_withdrawal_fee(asset, fee_asset, chain)
            row = WithdrawalHelper.do_withdrawal(
                w_type=w_type,
                user=user,
                asset=asset,
                chain=chain,
                address=address,
                memo=memo,
                extra=extra,
                amount=amount,
                fee=fee,
                fee_asset=fee_asset,
                recipient_id=None,
                remark=remark)
        else:
            WithdrawalHelper.validate_local_transfer_params(
                asset, amount
            )
            recipient_user = WithdrawalHelper.get_local_transfer_user(user.id, address)
            if UserVisitPermissionCache().check_user_permission(
                recipient_user.id,
                [UserVisitPermissionCache.FORBIDDEN_VALUE,
                UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE]
            ):
                raise InvalidArgument(message=gettext('该账号已失效，不支持内部转账'))
            row = WithdrawalHelper.do_withdrawal(
                w_type=w_type,
                user=user,
                asset=asset,
                chain=None,
                address=address,
                memo='',
                extra=None,
                amount=amount,
                fee=Decimal(),
                fee_asset=asset,
                recipient_id=recipient_user.id,
                remark=remark)
        return cls.withdrawal_to_dict(row, with_detail=True)

    @classmethod
    def _try_validate_risk_agreement(cls, w_type, user_id, screen_id=None):
        if screen_id and w_type == Withdrawal.Type.ON_CHAIN:
            r = WithdrawalPreScreeningManager.check_user_risk_agreement(
                screen_id, user_id
            )
            if not r:
                raise InvalidArgument(message='risk agreement not agreed!')

    @classmethod
    def withdrawals_to_dict(cls, withdrawals: List[Withdrawal], using_status_map: bool = True) -> List[dict]:
        addrs = [(x.chain, x.address) for x in withdrawals]
        txs = [(x.chain, x.tx_id) for x in withdrawals]
        client = WalletClient()
        addrs = client.get_explorer_addresses_url(addrs)
        txs = client.get_explorer_txs_url(txs)
        chain_name_map = get_chain_names()

        return [cls.withdrawal_to_dict(x,
                                       addrs[i],
                                       txs[i],
                                       False,
                                       using_status_map,
                                       chain_name_map.get(x.chain, x.chain),
                                       ) for i, x in enumerate(withdrawals)]

    @classmethod
    def withdrawal_to_dict(cls, withdrawal: Withdrawal,
                           explorer_address_url: str = None,
                           explorer_tx_url: str = None,
                           with_detail: bool = False,
                           using_status_map: bool = True,
                           chain_name_displayed: str = None,
                           ) -> dict:
        if using_status_map:
            status_mapping = {
                Withdrawal.Status.CONFIRMING: Withdrawal.Status.FINISHED,
                Withdrawal.Status.CANCELLATION_FAILED: Withdrawal.Status.CANCELLED,
                Withdrawal.Status.FAILED: Withdrawal.Status.PROCESSING
            }
        else:
            # 对于API应返回实际的状态 （CONFIRMING 可能被 CANCELLED）
            status_mapping = {}
        status = status_mapping.get(withdrawal.status, withdrawal.status)

        if withdrawal.type == Withdrawal.Type.LOCAL:
            explorer_address_url = ''
            explorer_tx_url = ''
        if explorer_address_url is None:
            try:
                explorer_address_url = WalletClient().get_explorer_address_url(withdrawal.chain, withdrawal.address)
            except:  # noqa
                explorer_address_url = ''
        if explorer_tx_url is None:
            try:
                explorer_tx_url = WalletClient().get_explorer_tx_url(withdrawal.chain, withdrawal.tx_id)
            except:  # noqa
                explorer_tx_url = ''

        ac_info = dict(
            irreversible_confirmations=0,
            deflation_rate='0',
            memo_name=''
        )
        if withdrawal.type != Withdrawal.Type.LOCAL:
            if c := get_cached_ac_info(withdrawal.asset, withdrawal.chain):
                ac_info = dict(
                    irreversible_confirmations=c.irreversible_confirmations,
                    deflation_rate=c.deflation_rate,
                    memo_name=c.memo_name or ''
                )

        fee_is_same_asset = withdrawal.fee_asset == withdrawal.asset
        result = dict(
            id=withdrawal.id,
            time=withdrawal.created_at,
            type=withdrawal.type.name,
            asset=withdrawal.asset,
            chain=withdrawal.chain or '',
            chain_name=chain_name_displayed or '',
            actual_amount=withdrawal.amount,
            fee_asset=withdrawal.fee_asset,
            fee_amount=withdrawal.fee,
            fee=withdrawal.fee if fee_is_same_asset else Decimal(),  # 老版本APP的兼容字段，手续费币种不同时 数目展示为0
            amount=withdrawal.amount + withdrawal.fee if fee_is_same_asset else withdrawal.amount,
            address=withdrawal.address,
            memo=withdrawal.memo or '',
            tx_id=withdrawal.tx_id,
            confirmations=min(withdrawal.confirmations, ac_info['irreversible_confirmations']),
            explorer_address_url=explorer_address_url,
            explorer_tx_url=explorer_tx_url,
            remark=withdrawal.remark or '',
            status=status.name,
            **ac_info
        )
        if with_detail:
            result.update(cls.match_address_book(withdrawal))
            result['approvers'] = cls.get_approve_status(withdrawal)
        return result

    @classmethod
    @mem_cached(600)
    def get_asset_chain_info(cls, asset, chain):
        try:
            ac_conf = get_asset_chain_config(asset, chain)
        except AssetNotFound:   # maybe offline
            return None

        return dict(
            memo_name=ac_conf.memo_name or '',
            irreversible_confirmations=ac_conf.irreversible_confirmations,
            deflation_rate=ac_conf.deflation_rate,
        )

    @classmethod
    def match_address_book(cls, w: Withdrawal) -> Dict:
        addr = None
        if w.type == Withdrawal.Type.ON_CHAIN:  # match by asset first
            row = WithdrawalAddress.get_address(w.user_id, w.asset, w.chain, w.address, w.memo)
            if not row or row.status != WithdrawalAddress.Status.VALID:
                row = WithdrawalAddress.get_address(w.user_id, '', w.chain, w.address, w.memo)
            if row and row.status != WithdrawalAddress.Status.VALID:
                row = None
            addr = row
        elif w.type == Withdrawal.Type.LOCAL:
            row = LocalWithdrawalAddress.get_address(w.user_id, w.address)
            if row and row.status != LocalWithdrawalAddress.Status.VALID:
                row = None
            addr = row

        return dict(
            is_address_in_book=addr is not None,
            address_remark=addr.remark if addr else ''
        )

    @classmethod
    def get_approve_status(cls, w: Withdrawal) -> List:
        rows = WithdrawalApprove.query.filter(WithdrawalApprove.withdrawal_id == w.id).all()
        if not rows:
            return []
        approvers = WithdrawalApprover.query.filter(WithdrawalApprover.id.in_([x.approver_id for x in rows])).all()
        approvers = {x.id: x for x in approvers}
        result = []
        for row in rows:
            approver = approvers[row.approver_id]
            result.append(dict(
                name=approver.name,
                account=approver.email,
                is_self=approver.is_self,
                status=row.status.name
            ))
        return result


@ns.route('/withdrawal/<int:id_>')
@respond_with_code
class WithdrawalResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        withdrawal = Withdrawal.query \
            .filter(Withdrawal.id == id_,
                    Withdrawal.user_id == g.user.id) \
            .first()
        if withdrawal is None:
            raise InvalidArgument
        chain_name_map = get_chain_names()
        chain_name_displayed = chain_name_map.get(withdrawal.chain, withdrawal.chain)
        result = WithdrawalsResource.withdrawal_to_dict(withdrawal, with_detail=True, chain_name_displayed=chain_name_displayed)
        return result

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(5, 600)
    def put(cls, id_):
        user: User = g.user
        withdrawal = Withdrawal.query \
            .filter(Withdrawal.id == id_,
                    Withdrawal.user_id == user.id) \
            .first()
        if withdrawal is None:
            raise InvalidArgument
        if withdrawal.status is not Withdrawal.Status.CREATED:
            raise OperationNotAllowed

        WithdrawalHelper.send_confirmation_emails(user, withdrawal.id, resend=True)
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    def delete(cls, id_):
        WithdrawalHelper.do_cancel_withdrawal(g.user.id, id_, cancel_type=WithdrawalCancel.CancelType.USER, cancel_user_id=g.user.id)
        return {}


@ns.route('/withdrawal/confirmation')
@respond_with_code
class WithdrawalConfirmationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL)
        if row is None:
            raise InvalidLink
        data = row.data_json

        withdrawal_id = data['withdrawal_id']
        approve_id = data.get('approve_id')
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if (withdrawal is None
                or withdrawal.status is not Withdrawal.Status.CREATED):
            raise InvalidLink
        if approve_id:
            approve = WithdrawalApprove.query.get(approve_id)
            if approve.status != WithdrawalApprove.Status.CREATED:
                raise InvalidLink

        user_id = withdrawal.user_id
        user = User.query.get(user_id)
        memo_name = ''
        chain_name = ''
        if withdrawal.type != Withdrawal.Type.LOCAL:
            chain_name = ChainDisplayMappingCache().hget(withdrawal.chain) or ''
            if c := get_cached_chain_info(withdrawal.asset, withdrawal.chain):
                memo_name = c['memo_name'] or ''

        return dict(
            account=user.email,
            time=withdrawal.created_at,
            type=withdrawal.type.name,
            asset=withdrawal.asset,
            chain=withdrawal.chain or '',
            chain_name=chain_name,
            actual_amount=withdrawal.amount,
            fee=withdrawal.fee if withdrawal.fee_asset == withdrawal.asset else Decimal(),
            fee_amount=withdrawal.fee,
            fee_asset=withdrawal.fee_asset,
            address=withdrawal.address,
            memo=withdrawal.memo,
            memo_name=memo_name,
            remark=withdrawal.remark
        )

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL)
        if row is None:
            raise InvalidLink
        data = row.data_json

        withdrawal_id = data['withdrawal_id']
        approve_id = data.get('approve_id')
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal is None or withdrawal.status != Withdrawal.Status.CREATED:
            raise InvalidLink
        user_id = withdrawal.user_id

        with CacheLock(LockKeys.user_withdrawal(user_id)), \
             CacheLock(LockKeys.withdrawal(withdrawal.id)):
            db.session.rollback()
            withdrawal = Withdrawal.query.get(withdrawal_id)
            if withdrawal.status != Withdrawal.Status.CREATED:
                raise InvalidLink

            approves = WithdrawalApprove.query.filter(WithdrawalApprove.withdrawal_id == withdrawal_id).all()
            if approves:
                approve = first(lambda x: x.id == approve_id, approves)
                if not approve or approve.status != WithdrawalApprove.Status.CREATED:
                    raise InvalidLink
                approve.status = WithdrawalApprove.Status.APPROVED
                # 待所有人审核通过后，才能确认提现
                if not all(x.status == WithdrawalApprove.Status.APPROVED for x in approves):
                    db.session.commit()
                    return {}

            asset = withdrawal.asset
            amount = withdrawal.amount
            fee_asset = withdrawal.fee_asset
            fee = withdrawal.fee

            server_client = ServerClient()
            try:
                withdrawable = get_user_withdrawable_amount(user_id, asset)
                if asset == fee_asset:
                    if amount + fee > withdrawable['withdrawable_asset']:
                        raise InsufficientBalance
                else:
                    if amount > withdrawable['withdrawable_asset']:
                        raise InsufficientBalance
                    balances = server_client.get_user_balances(user_id, asset=fee_asset)
                    fee_asset_avai = balances.get(fee_asset, {}).get('available', Decimal())
                    if fee > fee_asset_avai:
                        raise InsufficientBalance
                check_user_withdrawn_amount_exceeded(user_id)

            except Exception:
                withdrawal.status = Withdrawal.Status.CANCELLED
                WithdrawalHelper.add_cancel_record(withdrawal_id, user_id)
                db.session.commit()
                add_withdrawal_amount_to_cache(user_id, asset, -amount, withdrawal.created_at)
                raise

            def add_user_balance(*args):
                exc = None
                for i in range(3):
                    try:
                        return server_client.add_user_balance(*args)
                    except server_client.BadResponse as e:
                        if e.code == server_client.ResponseCode.INSUFFICIENT_BALANCE:
                            raise InsufficientBalance
                        exc = ServiceUnavailable
                    except ServiceUnavailable as e:
                        exc = e
                    if i < 2:
                        time.sleep(1)
                raise exc

            if fee > 0:
                add_user_balance(
                    user_id, fee_asset, -fee, BalanceBusiness.WITHDRAWAL_FEE,
                    withdrawal_id)
            add_user_balance(
                user_id, asset, -amount, BalanceBusiness.WITHDRAWAL,
                withdrawal_id)

            withdrawal.approved_by_user_at = now()
            withdrawal.status = Withdrawal.Status.AUDIT_REQUIRED
            db.session.add(WithdrawalFeeHistory(
                withdrawal_id=withdrawal.id,
                user_id=user_id,
                asset=fee_asset,
                amount=fee,
                fee_type=WithdrawalFeeHistory.FeeType.FEE
            ))
            db.session.commit()
            UserBalanceSumCache(withdrawal.user_id).delete()
            # 请求风控检查
            WithdrawalRiskCheck.add_check_request(withdrawal)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL)
        if row is None:
            raise InvalidLink
        data = row.data_json
        user_id = row.user_id

        withdrawal_id = data['withdrawal_id']
        approve_id = data.get('approve_id')
        row = Withdrawal.query.get(withdrawal_id)
        if row is None or row.status != Withdrawal.Status.CREATED:
            raise InvalidLink

        with CacheLock(LockKeys.withdrawal(withdrawal_id)):
            db.session.rollback()
            row = Withdrawal.query.get(withdrawal_id)
            if row.status != Withdrawal.Status.CREATED:
                raise InvalidLink
            # 多人审核时，任何人拒绝则取消提现
            approves = WithdrawalApprove.query.filter(WithdrawalApprove.withdrawal_id == withdrawal_id).all()
            if approves:
                approve = first(lambda x: x.id == approve_id, approves)
                if not approve or approve.status != WithdrawalApprove.Status.CREATED:
                    raise InvalidLink
                approve.status = WithdrawalApprove.Status.REJECTED
                db.session.commit()
            # holding lock, avoid deadlock.
            cancel_withdrawal(row.id, accquire_lock=False, cancel_type=WithdrawalCancel.CancelType.USER, cancel_user_id=user_id)
            return {}


@ns.route('/withdrawal/amount')
@respond_with_code
class WithdrawableAmountResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True)
    ))
    def get(cls, **kwargs):
        asset = kwargs['asset']
        details = get_user_withdrawable_amount(g.user.id, asset, realtime=False)
        return dict(
            withdrawable_asset=details['withdrawable_asset'],
            frozen_asset=details['frozen_asset'],
            confirming_usd=details['confirming_usd'],
            asset_price=details['asset_price']
        )


@ns.route('/withdrawal/limit')
@respond_with_code
class WithdrawalLimitResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user: User = g.user
        UserSettings(user.id).check_withdrawals_disabled_after_editing()
        return get_user_withdrawal_limit_info(user)


@ns.route('/withdrawal/fee/custom-assets')
@respond_with_code
class WithdrawalCustomFeeAssetsResource(Resource):

    @classmethod
    def get(cls):
        """ 自定义提现手续费-可选币种列表 """
        cs_assets = list(CUSTOM_WITHDRAWAL_FEE_ASSETS)
        AssetUtils.sort_asset_codes(cs_assets)
        return cs_assets


@ns.route('/withdrawal/fee/custom-amounts')
@respond_with_code
class WithdrawalCustomFeeAmountsResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=True),  # 提现币种
            fee_asset=AssetField(required=True),  # 手续费币种
        )
    )
    def get(cls, **kwargs):
        """ 自定义提现手续费币种-手续费数目 """
        asset = kwargs['asset']
        fee_asset = kwargs['fee_asset']
        if fee_asset not in CUSTOM_WITHDRAWAL_FEE_ASSETS:
            raise InvalidArgument
        chain_asset_fees = get_custom_withdrawal_fee_by_fee_asset(asset, fee_asset)
        return [{"chain": c, "amount": v} for c, v in chain_asset_fees.items()]


@ns.route('/deposit/addresses')
@respond_with_code
class DepositAddressesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        chain=ChainField(required=True)
    ))
    def get(cls, **kwargs):
        user: User = g.user
        require_user_request_permission(g.user)

        asset = kwargs['asset']
        chain = kwargs['chain']
        if not has_asset(asset, chain):
            raise AssetNotFound
        deposit_privacy_asset_require_kyc(user, asset)
        if not get_asset_chain_config(asset, chain).deposits_all_enabled:
            raise DepositsSuspended

        result = WalletClient().get_deposit_addresses(user.id, chain, 1, 10)
        return [dict(
                address=x['address'],
                memo=x['memo'],
                time=x['created_at']
                ) for x in result]


@ns.route('/deposit/address')
@respond_with_code
class DepositAddressResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request('deposit_address_renewal', with_user=True)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        chain=ChainField(required=True)
    ))
    def get(cls, **kwargs):
        user: User = g.user
        if not user.email:
            raise EmailNotBound
        require_user_request_permission(user)

        asset = kwargs['asset']
        chain = kwargs['chain']

        if not has_asset(asset, chain):
            raise AssetNotFound
        deposit_privacy_asset_require_kyc(user, asset)
        if not get_asset_chain_config(asset, chain).deposits_all_enabled:
            raise DepositsSuspended

        address, memo = WalletClient().get_or_create_deposit_address(chain, user.id)
        return dict(address=address, memo=memo)

    @classmethod
    @require_login
    @lock_request('deposit_address_renewal', with_user=True)
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        chain=ChainField(required=True)
    ))
    def put(cls, **kwargs):
        """ 充值-使用新地址 """
        require_user_request_permission(g.user)
        user_id = g.user.id

        asset = kwargs['asset']
        chain = kwargs['chain']

        if not has_asset(asset, chain):
            raise AssetNotFound
        deposit_privacy_asset_require_kyc(g.user, asset)
        if not get_asset_chain_config(asset, chain).deposits_all_enabled:
            raise DepositsSuspended

        client = WalletClient()
        address, memo = client.new_deposit_address(chain, user_id)
        return dict(address=address, memo=memo)


@ns.route('/withdrawal/addresses')
@respond_with_code
class WithdrawalAddressesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField,
        chain=ChainField,
        type=EnumField(['normal', 'common']),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        query = WithdrawalAddress.query \
            .filter(WithdrawalAddress.user_id == g.user.id,
                    WithdrawalAddress.status == WithdrawalAddress.Status.VALID)
        if asset := kwargs.get('asset'):
            query = query.filter(WithdrawalAddress.asset == asset)
        if chain := kwargs.get('chain'):
            query = query.filter(WithdrawalAddress.chain == chain)
        if t := kwargs.get('type'):  # 用于前端筛选币种(不)为空的地址
            if t == 'normal':
                query = query.filter(WithdrawalAddress.asset != '')
            else:
                query = query.filter(WithdrawalAddress.asset == '')

        records = query.order_by(WithdrawalAddress.id.desc()) \
                       .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(cls.withdrawal_address_to_dict, records.items)),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    @require_2fa(MobileCodeType.WITHDRAWAL_ADDRESS_ADDITION, allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField,
        chain=ChainField(required=True),
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        extra=fields.Dict(keys=fields.String(), values=fields.String()),
        remark=fields.String(missing='', validate=validate.Length(max=256))
    ))
    def post(cls, **kwargs):
        asset = kwargs.get('asset') or ''
        chain = kwargs['chain']
        address = kwargs['address'].strip()
        memo = kwargs['memo']
        extra = kwargs.get('extra', {})
        remark = kwargs['remark']
        user_id = g.user.id

        if asset and not has_asset(asset, chain):
            raise AssetNotFound(asset, chain)

        extra = AddressExtraConfigs.reslove_extra(chain, extra)
        valid = WalletClient().validate_withdrawal_address(chain, address, memo, extra, asset=asset)
        if not valid:
            raise InvalidWithdrawalAddress(message=valid.reason if isinstance(valid, WhyNot) else None)

        with CacheLock(LockKeys.withdrawal_address(user_id)):
            db.session.rollback()
            count = WithdrawalAddress.query.filter(
                WithdrawalAddress.user_id == user_id,
                WithdrawalAddress.chain == chain,
                WithdrawalAddress.status == WithdrawalAddress.Status.VALID
            ).with_entities(func.count('*')).scalar() or 0
            if count >= WithdrawalAddress.MAX_NUM_PER_CHAIN:
                raise WithdrawalAddressOverLimit(num=WithdrawalAddress.MAX_NUM_PER_CHAIN)

            row = WithdrawalAddress.get_address(user_id, asset, chain, address, memo)
            if row:
                if row.status == WithdrawalAddress.Status.VALID:
                    raise WithdrawalAddressAlreadyExists
                row.remark = remark
                row.attachment = AddressExtraConfigs.dump_extra(extra)
                row.status = WithdrawalAddress.Status.VALID
            else:
                row = WithdrawalAddress(
                    user_id=user_id,
                    asset=asset,
                    chain=chain,
                    address=address,
                    memo=memo,
                    attachment=AddressExtraConfigs.dump_extra(extra),
                    remark=remark,
                )
                db.session.add(row)
            db.session.commit()

            OperationLog.add(user_id,
                             OperationLog.Operation.ADD_WITHDRAW_ADDRESS,
                             f'{chain}: {address}', get_request_platform())

        return cls.withdrawal_address_to_dict(row)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        ids=fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        ids = cls.validate_address_id_list(kwargs['ids'])
        WithdrawalAddress.query \
            .filter(WithdrawalAddress.id.in_(ids),
                    WithdrawalAddress.user_id == g.user.id) \
            .delete()

        db.session.commit()
        OperationLog.add(g.user.id, OperationLog.Operation.DELETE_WITHDRAW_ADDRESS,
                         ','.join(map(str, ids)), get_request_platform())
        return {}

    @classmethod
    def validate_address_id_list(cls, value: str) -> List[int]:
        try:
            ids = {int(x) for x in value.split(',')}
        except Exception:
            raise InvalidArgument
        if not ids or len(ids) > 100 or any(x < 1 or x > 1 << 32 for x in ids):
            raise InvalidArgument
        return list(ids)

    @classmethod
    def withdrawal_address_to_dict(cls, address: WithdrawalAddress) -> dict:
        return dict(
            id=address.id,
            asset=address.asset,
            chain=address.chain,
            address=address.address,
            memo=address.memo,
            extra=AddressExtraConfigs.format_extra(address.attachment),
            remark=address.remark
        )


@ns.route('/withdrawal/address/book')
@respond_with_code
class WithdrawalAddressBookResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField,
        chain=ChainField,
    ))
    def get(cls, **kwargs):
        """该接口类似获取充值地址列表，但不分页"""
        asset = kwargs.get('asset')
        chain = kwargs.get('chain')
        query = WithdrawalAddress.query \
            .filter(WithdrawalAddress.user_id == g.user.id,
                    WithdrawalAddress.status == WithdrawalAddress.Status.VALID)
        if asset and chain:
            query = query.filter(WithdrawalAddress.chain == chain,
                                 or_(WithdrawalAddress.asset == asset, 
                                     WithdrawalAddress.asset == ''))
        if chain := kwargs.get('chain'):  # App兼容，币种和链应该必传
            query = query.filter(WithdrawalAddress.chain == chain)
        elif asset := kwargs.get('asset'):
            query = query.filter(WithdrawalAddress.asset == asset)
        else:
            raise InvalidArgument

        rows = query.order_by(WithdrawalAddress.id.desc()).all()
        if asset and chain:
            rows.sort(key=lambda x: (x.usage_count, x.id), reverse=True)

        return list(map(WithdrawalAddressesResource.withdrawal_address_to_dict, rows))


@ns.route('/withdrawal/address/<int:id_>')
@respond_with_code
class WithdrawalAddressResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        remark=fields.String(required=True, validate=validate.Length(max=256))
    ))
    def put(cls, id_, **kwargs):
        row = WithdrawalAddress.query \
            .filter(WithdrawalAddress.id == id_,
                    WithdrawalAddress.user_id == g.user.id,
                    WithdrawalAddress.status
                    == WithdrawalAddress.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound

        if remark := kwargs['remark']:
            row.remark = remark
            db.session.commit()
        return {}


@ns.route('/local/withdrawal/addresses')
@respond_with_code
class LocalWithdrawalAddressesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        rows = LocalWithdrawalAddress.query \
                .filter(LocalWithdrawalAddress.user_id == g.user.id,
                        LocalWithdrawalAddress.status == LocalWithdrawalAddress.Status.VALID).all()
        rows.sort(key=lambda x: (x.usage_count, x.id), reverse=True)
        return [cls.local_withdrawal_address_to_dict(row) for row in rows]

    @classmethod
    @require_2fa(MobileCodeType.WITHDRAWAL_ADDRESS_ADDITION, allow_sub_account=False)
    @ns.use_kwargs(dict(
        address=fields.String(required=True),
        remark=fields.String(missing='', validate=validate.Length(max=256))
    ))
    def post(cls, **kwargs):
        address = kwargs['address'].strip()
        remark = kwargs['remark']
        user_id = g.user.id

        if not isinstance(User.from_account(address), User):
            raise InvalidAccount

        with CacheLock(LockKeys.withdrawal_address(user_id)):
            db.session.rollback()
            
            count = LocalWithdrawalAddress.query.filter(
                LocalWithdrawalAddress.user_id == user_id,
                LocalWithdrawalAddress.status == LocalWithdrawalAddress.Status.VALID) \
                .with_entities(func.count('*')).scalar() or 0
            if count >= LocalWithdrawalAddress.MAX_NUM:
                raise WithdrawalAddressOverLimit(num=LocalWithdrawalAddress.MAX_NUM)

            row = LocalWithdrawalAddress.get_address(user_id, address)
            if row:
                if row.status == LocalWithdrawalAddress.Status.VALID:
                    raise WithdrawalAddressAlreadyExists
                row.remark = remark
                row.status = LocalWithdrawalAddress.Status.VALID
            else:
                row = LocalWithdrawalAddress(
                    user_id=user_id,
                    account=address,
                    remark=remark,
                )
                db.session.add(row)
            db.session.commit()

        OperationLog.add(user_id, OperationLog.Operation.ADD_WITHDRAW_ADDRESS,
                         f'local: {address}', get_request_platform())
        return cls.local_withdrawal_address_to_dict(row)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        ids=fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        ids = WithdrawalAddressesResource.validate_address_id_list(kwargs['ids'])
        LocalWithdrawalAddress.query \
            .filter(LocalWithdrawalAddress.id.in_(ids),
                    LocalWithdrawalAddress.user_id == g.user.id) \
            .delete()
        db.session.commit()
        OperationLog.add(g.user.id, OperationLog.Operation.DELETE_WITHDRAW_ADDRESS,
                         'local:' + ','.join(map(str, ids)), get_request_platform())
        return {}

    @classmethod
    def local_withdrawal_address_to_dict(cls, address: LocalWithdrawalAddress) -> dict:
        return dict(
            id=address.id,
            address=address.account,
            remark=address.remark
        )


@ns.route('/local/withdrawal/address/<int:id_>')
@respond_with_code
class LocalWithdrawalAddressResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        remark=fields.String(required=True, validate=validate.Length(max=256))
    ))
    def put(cls, id_, **kwargs):
        row: LocalWithdrawalAddress = LocalWithdrawalAddress.query \
            .filter(LocalWithdrawalAddress.id == id_,
                    LocalWithdrawalAddress.user_id == g.user.id,
                    LocalWithdrawalAddress.status
                    == LocalWithdrawalAddress.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound

        row.remark = kwargs['remark']
        row.updated_at = now()
        db.session.commit()
        return {}


@ns.route('/withdrawal/address/quickly')
@respond_with_code
class WithdrawalAddressQuickly(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        withdrawal_id=fields.Integer(required=True),
        type=EnumField(['normal', 'common']),
        remark=fields.String
    ))
    def post(cls, **kwargs):
        """由提现记录快捷添加提现地址簿"""
        wid = kwargs['withdrawal_id']
        t = kwargs.get('type')
        remark = kwargs.get('remark')
        user_id = g.user.id
        w = Withdrawal.query.get(wid)
        if not w or w.user_id != user_id:
            raise InvalidArgument
        with CacheLock(LockKeys.withdrawal_address(user_id)):
            if w.type == Withdrawal.Type.ON_CHAIN:
                db.session.rollback()
                count = WithdrawalAddress.query.filter(
                    WithdrawalAddress.user_id == user_id,
                    WithdrawalAddress.chain == w.chain,
                    WithdrawalAddress.status == WithdrawalAddress.Status.VALID) \
                    .with_entities(func.count('*')).scalar() or 0
                if count >= WithdrawalAddress.MAX_NUM_PER_CHAIN:
                    raise WithdrawalAddressOverLimit(num=WithdrawalAddress.MAX_NUM_PER_CHAIN)

                if t == 'normal':
                    asset = w.asset
                    row = WithdrawalAddress.get_address(user_id, w.asset, w.chain, w.address, w.memo)
                elif t == 'common':
                    asset = ''
                    row = WithdrawalAddress.get_address(user_id, '', w.chain, w.address, w.memo)
                else:
                    raise InvalidArgument
                if row:
                    if row.status == WithdrawalAddress.Status.VALID:
                        raise WithdrawalAddressAlreadyExists
                    row.remark = remark
                    row.attachment = w.attachment
                    row.status = WithdrawalAddress.Status.VALID
                else:
                    row = WithdrawalAddress(
                        user_id=user_id,
                        asset=asset,
                        chain=w.chain,
                        address=w.address,
                        memo=w.memo,
                        attachment=w.attachment,
                        remark=remark,
                    )
                    db.session.add(row)
                OperationLog.add(user_id, OperationLog.Operation.ADD_WITHDRAW_ADDRESS,
                                 f'{w.chain}: {w.address}', get_request_platform())
            else:
                count = LocalWithdrawalAddress.query.filter(
                    LocalWithdrawalAddress.user_id == user_id,
                    LocalWithdrawalAddress.status == LocalWithdrawalAddress.Status.VALID) \
                    .with_entities(func.count('*')).scalar() or 0
                if count >= LocalWithdrawalAddress.MAX_NUM:
                    raise WithdrawalAddressOverLimit(num=LocalWithdrawalAddress.MAX_NUM)

                row = LocalWithdrawalAddress.get_address(user_id, w.address)
                if row:
                    if row.status == LocalWithdrawalAddress.Status.VALID:
                        raise WithdrawalAddressAlreadyExists
                    row.remark = remark
                    row.status = LocalWithdrawalAddress.Status.VALID
                else:
                    row = LocalWithdrawalAddress(
                        user_id=user_id,
                        account=w.address,
                        remark=remark,
                    )
                    db.session.add(row)
                OperationLog.add(user_id, OperationLog.Operation.ADD_WITHDRAW_ADDRESS,
                                 f'local: {w.address}', get_request_platform())
            db.session.commit()
            return {}


@ns.route('/api/withdrawal/addresses')
@respond_with_code
class ApiWithdrawalAddressesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        asset=AssetField,
        chain=ChainField,
        kind=EnumField(['normal', 'common']),
        type=EnumField(ApiWithdrawalAddress.Type),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        user: User = g.user

        query = ApiWithdrawalAddress.query \
            .filter(ApiWithdrawalAddress.user_id == user.id,
                    ApiWithdrawalAddress.status == ApiWithdrawalAddress.Status.VALID)
        if asset := kwargs.get('asset'):
            query = query.filter(ApiWithdrawalAddress.asset == asset)
        if chain := kwargs.get('chain'):
            query = query.filter(ApiWithdrawalAddress.chain == chain)
        if t := kwargs.get('type'):
            query = query.filter(ApiWithdrawalAddress.type == t)
        if kind := kwargs.get('kind'):  # 用于前端筛选币种(不)为空的地址
            if kind == 'normal':
                query = query.filter(ApiWithdrawalAddress.asset != '')
            else:
                query = query.filter(ApiWithdrawalAddress.asset == '')

        records = query.order_by(ApiWithdrawalAddress.id.desc()) \
                       .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(cls.api_withdrawal_address_to_dict, records.items)),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    @require_2fa(MobileCodeType.API_WITHDRAWAL_ADDRESS_ADDITION, allow_sub_account=False)
    @ns.use_kwargs(dict(
        type=EnumField(ApiWithdrawalAddress.Type, required=True),
        asset=AssetField,
        chain=ChainField,
        address=fields.String(required=True),
        memo=fields.String(missing=''),
        extra=fields.Dict(keys=fields.String(), values=fields.String()),
        remark=fields.String(missing='', validate=validate.Length(max=256)),
        withdraw_password=fields.String()
    ))
    def post(cls, **kwargs):
        w_type = kwargs['type']
        asset = kwargs.get('asset') or ''
        chain = kwargs.get('chain') or ''
        address = kwargs['address'].strip()
        memo = kwargs['memo']
        extra = kwargs.get('extra', {})
        remark = kwargs['remark']
        user_id = g.user.id

        check_withdraw_password_by_api(g.user, kwargs.get('withdraw_password'))

        if w_type == ApiWithdrawalAddress.Type.ON_CHAIN:
            if not chain:
                raise InvalidArgument
            if asset and not has_asset(asset, chain):
                raise AssetNotFound(asset, chain)
            extra = AddressExtraConfigs.reslove_extra(chain, extra)
            valid = WalletClient().validate_withdrawal_address(chain, address, memo, extra, asset=asset)
            if not valid:
                raise InvalidWithdrawalAddress
            row = ApiWithdrawalAddress.get_on_chain_address(user_id, asset, chain, address, memo)
        else:
            if not isinstance(User.from_account(address), User):
                raise InvalidAccount
            asset = None
            chain = None
            memo = None
            extra = None
            row = ApiWithdrawalAddress.get_local_address(user_id, address)
        
        if row and row.status == ApiWithdrawalAddress.Status.VALID:
            raise WithdrawalAddressAlreadyExists

        data = dict(
            type=w_type.name,
            asset=asset,
            chain=chain,
            address=address,
            memo=memo,
            attachment=AddressExtraConfigs.dump_extra(extra),
            remark=remark
        )
        req = ApiWithdrawalAddressRequest(
            user_id=user_id,
            data=json.dumps([data]),
        )
        db.session.add(req)
        db.session.flush()

        approvers = WithdrawalApprover.get_approvers(g.user.id)
        if len(approvers) > 1:  # 多人审核，记录审核信息
            for approver in approvers:
                db.session.add(ApiWithdrawalAddressApprove(
                    request_id=req.id,
                    approver_id=approver.id
                ))
        db.session.commit()

        cls.send_confirmation_emails(g.user, req.id)
        return {'request_id': req.id}

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        ids=fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        ids = WithdrawalAddressesResource.validate_address_id_list(kwargs['ids'])
        ApiWithdrawalAddress.query \
            .filter(ApiWithdrawalAddress.id.in_(ids),
                    ApiWithdrawalAddress.user_id == g.user.id) \
            .delete()

        db.session.commit()
        OperationLog.add(g.user.id, OperationLog.Operation.DELETE_API_WITHDRAW_ADDRESS,
                         ','.join(map(str, ids)), get_request_platform())
        return {}

    @classmethod
    def api_withdrawal_address_to_dict(cls, address: ApiWithdrawalAddress) -> dict:
        return dict(
            id=address.id,
            time=address.created_at,
            type=address.type.name,
            asset=address.asset,
            chain=address.chain,
            address=address.address,
            memo=address.memo,
            extra=AddressExtraConfigs.format_extra(address.attachment),
            remark=address.remark
        )

    @classmethod
    def send_confirmation_emails(cls, user: User, request_id: str):
        approves = ApiWithdrawalAddressApprove.query.filter(ApiWithdrawalAddressApprove.request_id == request_id).all()
        if approves:    # 多人审核
            # 此处应查询所有状态的approver，approver可能被已删除，但之前的提现地址依然需要他审核
            approvers = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == user.id).all()
            approvers = {x.id: x for x in approvers}
            for approve in approves:
                if approve.status != ApiWithdrawalAddressApprove.Status.CREATED:  # 只向未确认的重发
                    continue
                email_token = EmailToken.new(
                    user.id,
                    approvers[approve.approver_id].email,
                    EmailToken.EmailType.API_WITHDRAWAL_ADDRESS,
                    dict(request_id=request_id, approve_id=approve.id),
                    ttl=3600
                )
                send_api_withdrawal_address_confirmation_email.delay(
                    approve.approver_id, request_id, email_token.token, get_request_host_url()
                )
            update_security_statistics([user.id], SecuritySettingType.WITHDRAWAL_APPROVER)
        else:
            email_token = EmailToken.new(
                user.id,
                user.email,
                EmailToken.EmailType.API_WITHDRAWAL_ADDRESS,
                dict(request_id=request_id),
                ttl=1800
            )
            send_api_withdrawal_address_confirmation_email.delay(None, request_id, email_token.token, get_request_host_url())

    @classmethod
    @require_2fa(MobileCodeType.API_WITHDRAWAL_ADDRESS_ADDITION, allow_sub_account=False)
    @limit_user_frequency(10, 600)
    def put(cls,):
        check_withdraw_password_by_api(g.user, request.form.get('withdraw_password'))

        succeed, failed = cls._get_validated_data()
        if failed:
            return {'status': 'fail', 'data': failed}

        rows = ApiWithdrawalAddress.query.filter(ApiWithdrawalAddress.user_id == g.user.id,
                                                 ApiWithdrawalAddress.type == ApiWithdrawalAddress.Type.ON_CHAIN,
                                                 ApiWithdrawalAddress.status == ApiWithdrawalAddress.Status.VALID).all()
        exists = {(x.asset, x.chain, x.address, x.memo) for x in rows}
        for row in succeed:
            key = (row['coin'], row['network'], row['address'], row['memo'])
            if key in exists:
                row['err_reason'] = 'address already exists'
                failed.append(row)
            exists.add(key)  # should check repeated address
        if failed:
            return {'status': 'fail', 'data': failed}

        data = []
        for row in succeed:
            data.append(dict(
                type=ApiWithdrawalAddress.Type.ON_CHAIN.name,
                asset=row['coin'],
                chain=row['network'],
                address=row['address'],
                memo=row['memo'],
                attachment=None,
                remark=row['remark']
            ))

        req = ApiWithdrawalAddressRequest(
            user_id=g.user.id,
            data=json.dumps(data),
        )
        db.session.add(req)
        db.session.flush()

        approvers = WithdrawalApprover.get_approvers(g.user.id)
        if len(approvers) > 1:  # 多人审核，记录审核信息
            for approver in approvers:
                db.session.add(ApiWithdrawalAddressApprove(
                    request_id=req.id,
                    approver_id=approver.id
                ))
        db.session.commit()

        cls.send_confirmation_emails(g.user, req.id)
        return {'status': 'success', 'data': [], 'request_id': req.id}

    @classmethod
    def _get_validated_data(cls):
        if not (file := request.files.get('file')):
            raise InvalidArgument

        _, ext = os.path.splitext(file.filename)
        ext = ext.lower() if ext else None
        if ext not in ['.csv']:
            raise InvalidArgument

        max_size = 1024 * 1024 * 5
        size = int(request.headers['CONTENT_LENGTH'])
        if size > max_size:
            raise FileTooBig

        return cls._validate_upload_data(file)

    @classmethod
    def _validate_upload_data(cls, file):
        headers = ['remark', 'coin', 'network', 'address', 'memo']
        rows = get_table_rows(file, headers, False)
        rows = [row for row in rows if any(value for value in row.values())]
        if not rows:
            raise InvalidArgument(message=gettext('请勿上传空表格'))

        count_limit = 100
        if len(rows) > count_limit:
            raise InvalidArgument(message=gettext('超过上传地址限制条数:%(count)s', count=count_limit))

        succeed, failed = [], []

        def fail(row, reason):
            row['err_reason'] = reason
            failed.append(row)

        for row in rows:
            asset = row['coin']
            chain = row['network']
            address = row['address']
            memo = row['memo'] or ''
            remark = row['remark']

            if asset:
                try:
                    asset = normalise_asset_code(asset)
                except InvalidAssetCode:
                    fail(row, f'coin {asset} invalid')
                    continue

            try:
                chain = normalise_chain_name(chain)
            except InvalidChainName:
                fail(row, f'network {chain} invalid')
                continue

            if asset:
                if not has_asset(asset, chain):
                    fail(row, f'asset {asset}-{chain} not exists')
                    continue
            else:
                if not has_chain(chain):
                    fail(row, f'network {chain} not exists')
                    continue

            # cannot support extra info when batch import.
            if AddressExtraConfigs.require_extra(chain):
                fail(row, f'network {chain} is not supported in batch import')
                continue

            if len(memo) > 256:
                fail(row, 'memo over length limit')
                continue

            if len(remark) > 256:
                fail(row, 'remark over length limit')
                continue

            # noinspection PyBroadException
            try:
                valid = WalletClient().validate_withdrawal_address(chain, address, memo, asset=asset)
            except Exception:
                fail(row, 'system error, please try again')
                continue

            if not valid:
                fail(row, gettext('提现地址错误。'))
                continue

            succeed.append(row)
        return succeed, failed


@ns.route('/api/withdrawal/address/requests')
@respond_with_code
class ApiWithdrawalAddressRequestsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        rows = ApiWithdrawalAddressRequest.query.filter(
            ApiWithdrawalAddressRequest.user_id == g.user.id,
            ApiWithdrawalAddressRequest.status != ApiWithdrawalAddressRequest.Status.FINISHED) \
            .order_by(ApiWithdrawalAddressRequest.id.desc()).limit(20).all()
        end_time = now() - timedelta(days=3)
        return [dict(
            id=x.id,
            time=x.created_at,
            status=x.status.name
        ) for x in rows if x.status == ApiWithdrawalAddressRequest.Status.CREATED or x.created_at > end_time]


@ns.route('/api/withdrawal/address/request/<int:id_>')
@respond_with_code
class ApiWithdrawalAddressRequestResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, id_):
        row = ApiWithdrawalAddressRequest.query.filter(ApiWithdrawalAddressRequest.id == id_,
                                                       ApiWithdrawalAddressRequest.user_id == g.user.id).first()
        if not row:
            raise InvalidArgument
        data = json.loads(row.data)
        return dict(
            addresses=[dict(
                type=x['type'],
                asset=x['asset'],
                chain=x['chain'],
                address=x['address'],
                memo=x['memo'],
                memo_name=c['memo_name'] if (x['chain'] and (c := get_cached_chain_info(x['asset'], x['chain']))) else '',
                extra=json.loads(x['attachment']) if x['attachment'] else None,
                remark=x['remark']
            ) for x in data],
            time=row.created_at,
            status=row.status.name,
            approvers=cls.get_approve_status(row)
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=5, interval=600)
    def put(cls, id_):
        row = ApiWithdrawalAddressRequest.query.filter(ApiWithdrawalAddressRequest.id == id_,
                                                       ApiWithdrawalAddressRequest.user_id == g.user.id).first()
        if not row:
            raise InvalidArgument
        if row.status != ApiWithdrawalAddressRequest.Status.CREATED:
            raise OperationNotAllowed
        ApiWithdrawalAddressesResource.send_confirmation_emails(g.user, id_)
        return {}

    @classmethod
    def get_approve_status(cls, req: ApiWithdrawalAddressRequest) -> List:
        rows = ApiWithdrawalAddressApprove.query.filter(ApiWithdrawalAddressApprove.request_id == req.id).all()
        if not rows:
            return []
        approvers = WithdrawalApprover.query.filter(WithdrawalApprover.id.in_([x.approver_id for x in rows])).all()
        approvers = {x.id: x for x in approvers}
        result = []
        for row in rows:
            approver = approvers[row.approver_id]
            result.append(dict(
                name=approver.name,
                account=approver.email,
                is_self=approver.is_self,
                status=row.status.name
            ))
        return result


@ns.route('/api/withdrawal/address/confirmation')
@respond_with_code
class ApiWithdrawalAddressConfirmationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.API_WITHDRAWAL_ADDRESS)
        if row is None:
            raise InvalidLink
        data = row.data_json
        request_id = data['request_id']
        approve_id = data.get('approve_id')
        req = ApiWithdrawalAddressRequest.query.get(request_id)
        if not req or req.status != ApiWithdrawalAddressRequest.Status.CREATED:
            raise InvalidLink
        if approve_id:
            approve = ApiWithdrawalAddressApprove.query.get(approve_id)
            if approve.status != ApiWithdrawalAddressApprove.Status.CREATED:
                raise InvalidLink
        approver = WithdrawalApprover.get_self(req.user_id)
        user = User.query.get(req.user_id)

        return {
            'time': req.created_at,
            'name': approver.name if approver else '',
            'account': user.email
        }

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.API_WITHDRAWAL_ADDRESS)
        if row is None:
            raise InvalidLink
        data = row.data_json
        request_id = data['request_id']
        approve_id = data.get('approve_id')
        req = ApiWithdrawalAddressRequest.query.get(request_id)
        if not req or req.status != ApiWithdrawalAddressRequest.Status.CREATED:
            raise InvalidLink

        with CacheLock(LockKeys.withdrawal_address(req.user_id)):
            db.session.rollback()
            req = ApiWithdrawalAddressRequest.query.get(request_id)
            if req.status != ApiWithdrawalAddressRequest.Status.CREATED:
                raise InvalidLink
            approves = ApiWithdrawalAddressApprove.query.filter(ApiWithdrawalAddressApprove.request_id == request_id).all()
            if approves:
                approve = first(lambda x: x.id == approve_id, approves)
                if not approve or approve.status != ApiWithdrawalAddressApprove.Status.CREATED:
                    raise InvalidLink
                approve.status = ApiWithdrawalAddressApprove.Status.APPROVED
                # 待所有人审核通过后，才能添加
                if not all(x.status == ApiWithdrawalAddressApprove.Status.APPROVED for x in approves):
                    db.session.commit()
                    return {}

            data = json.loads(req.data)
            # should check repeated address again
            rows = ApiWithdrawalAddress.query.filter(ApiWithdrawalAddress.user_id == req.user_id).all()
            exists = {(x.asset, x.chain, x.address, x.memo) for x in rows if x.status == ApiWithdrawalAddress.Status.VALID}
            deleted = {(x.asset, x.chain, x.address, x.memo): x for x in rows if x.status == ApiWithdrawalAddress.Status.DELETED}
            for row in data:
                key = (row['asset'], row['chain'], row['address'], row['memo'])
                if key in exists:
                    raise InvalidArgument(message='address already exists')

            for row in data:
                if record := deleted.get((row['asset'], row['chain'], row['address'], row['memo'])):
                    record.remark = row['remark']
                    record.attachment = row['attachment']
                    record.status = ApiWithdrawalAddress.Status.VALID
                else:
                    db.session.add(ApiWithdrawalAddress(
                        user_id=req.user_id,
                        type=row['type'],
                        asset=row['asset'],
                        chain=row['chain'],
                        address=row['address'],
                        memo=row['memo'],
                        attachment=row['attachment'],
                        remark=row['remark']
                    ))
            req.status = ApiWithdrawalAddressRequest.Status.FINISHED
            db.session.commit()

        OperationLog.add(req.user_id, OperationLog.Operation.ADD_API_WITHDRAW_ADDRESS,
                         f'count: {len(data)}', get_request_platform())
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.API_WITHDRAWAL_ADDRESS)
        if row is None:
            raise InvalidLink
        data = row.data_json
        request_id = data['request_id']
        approve_id = data.get('approve_id')
        req = ApiWithdrawalAddressRequest.query.get(request_id)
        if not req or req.status != ApiWithdrawalAddressRequest.Status.CREATED:
            raise InvalidLink
        with CacheLock(LockKeys.withdrawal_address(req.user_id)):
            db.session.rollback()
            req = ApiWithdrawalAddressRequest.query.get(request_id)
            if req.status != ApiWithdrawalAddressRequest.Status.CREATED:
                raise InvalidLink
            approves = ApiWithdrawalAddressApprove.query.filter(ApiWithdrawalAddressApprove.request_id == req.id).all()
            if approves:
                approve = first(lambda x: x.id == approve_id, approves)
                if not approve or approve.status != ApiWithdrawalAddressApprove.Status.CREATED:
                    raise InvalidLink
                approve.status = ApiWithdrawalAddressApprove.Status.REJECTED
            req.status = ApiWithdrawalAddressRequest.Status.CANCELLED
            db.session.commit()
            return {}


@ns.route('/withdrawal/approvers')
@respond_with_code
class WithdrawalApproversResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        rows = WithdrawalApprover.query.filter(
            WithdrawalApprover.user_id == g.user.id,
            WithdrawalApprover.status != WithdrawalApprover.Status.DELETED
        ).all()
        if not rows:    # always return self
            result = [dict(
                id=0,
                name='',
                account=g.user.email,
                is_self=True,
                status=WithdrawalApprover.Status.VALID.name
            )]
        else:
            result = [dict(
                id=x.id,
                name=x.name,
                account=x.email,
                is_self=x.is_self,
                status=x.status.name
            ) for x in rows]
        return {'approvers': result}

    @classmethod
    @require_2fa(MobileCodeType.ADD_WITHDRAWAL_APPROVER, allow_sub_account=False)
    @ns.use_kwargs(dict(
        approvers=fields.List(fields.Dict, required=True)
    ))
    def post(cls, **kwargs):
        pref = UserPreferences(g.user.id)
        if pref.opening_payment:
            raise InvalidArgument(message=gettext("为保障您资金安全，需关闭收付款功能才可设置多人提现审核"))

        payload = kwargs['approvers']
        approvers = []
        for item in payload:
            name = item.get('name', '')
            account = item.get('account', '')
            if not 0 < len(name) <= 64:
                raise InvalidArgument
            if not validate_email(account):
                raise InvalidArgument
            approvers.append(dict(name=name, account=account))
        if not approvers:
            raise InvalidArgument
        if len({x['account'] for x in approvers}) != len(approvers):
            raise InvalidArgument

        cls.validate_approvers(g.user.id, approvers)

        request_id = new_hex_token()
        cache = WithdrawalApproverCache(g.user.id, request_id)
        cache.set(json.dumps(approvers), ex=86400)

        cls.send_confirmation_email(g.user, request_id)
        return {'request_id': request_id}

    @classmethod
    def validate_approvers(cls, user_id, approvers: List):
        rows = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == user_id,
                                               WithdrawalApprover.status != WithdrawalApprover.Status.DELETED).all()
        has_self = first(lambda x: x.is_self, rows)
        if not has_self:    # must set self first.
            raise InvalidArgument
        for item in approvers:
            account = item['account']
            if first(lambda x: x.email == account, rows):   # exists email or self
                raise InvalidArgument
        if len(approvers) + len(rows) - 1 > WithdrawalApprover.MAX_APPROVER_NUM:
            raise WithdrawalApproverOverLimit(num=WithdrawalApprover.MAX_APPROVER_NUM)
    
    @classmethod
    def send_confirmation_email(cls, user: User, request_id: str):
        email_token = EmailToken.new(
            user.id,
            user.email,
            EmailToken.EmailType.WITHDRAWAL_APPROVER,
            dict(request_id=request_id),
            ttl=1800
        )
        send_withdrawal_approver_confirmation_email.delay(user.id, request_id, email_token.token, get_request_host_url())


@ns.route('/withdrawal/approver/<int:id_>')
@respond_with_code
class WithdrawalApproverResource(Resource):
    RE_USER_NAME = re.compile(r'^[0-9a-zA-Z\u4e00-\u9fa5]+$')

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        name=fields.String(required=True, validate=validate.Length(max=64))
    ))
    def put(cls, id_, **kwargs):
        name = kwargs['name']
        if not cls.RE_USER_NAME.fullmatch(name) and not validate_email(name):
            raise InvalidArgument(message=gettext("用户名不得包含特殊字符和空格"))

        if id_ != 0:    # edit self or others name
            row = WithdrawalApprover.query.filter(WithdrawalApprover.id == id_,
                                                  WithdrawalApprover.user_id == g.user.id,
                                                  WithdrawalApprover.status != WithdrawalApprover.Status.DELETED).first()
            if not row:
                raise InvalidArgument
            row.name = name
        else:   # first time to set self name
            row = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == g.user.id,
                                                  WithdrawalApprover.is_self.is_(True)).first()
            if row:
                raise InvalidArgument
            db.session.add(WithdrawalApprover(
                user_id=g.user.id,
                name=name,
                account=None,
                is_self=True,
                status=WithdrawalApprover.Status.VALID
            ))
        db.session.commit()
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    def delete(cls, id_):
        row = WithdrawalApprover.query.filter(WithdrawalApprover.id == id_,
                                              WithdrawalApprover.user_id == g.user.id,
                                              WithdrawalApprover.status != WithdrawalApprover.Status.DELETED).first()
        if not row or row.is_self:  # cannot delete self
            raise InvalidArgument
        
        if row.status in (WithdrawalApprover.Status.CREATED, WithdrawalApprover.Status.FAILED):
            row.status = WithdrawalApprover.Status.DELETED
            db.session.commit()
        elif row.status in (WithdrawalApprover.Status.VALID, WithdrawalApprover.Status.DELETING):
            # when deleting a valid approver, require 2fa validation and confirmation.
            require_2fa(MobileCodeType.DELETE_WITHDRAWAL_APPROVER)(lambda: True)()
            row.status = WithdrawalApprover.Status.DELETING
            row.applied_at = now()
            db.session.commit()
            email_token = EmailToken.new(
                row.user_id,
                row.email,
                EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER,
                dict(approver_id=row.id),
                ttl=3600
            )
            send_withdrawal_approver_leave_confirmation_email.delay(row.id, email_token.token, get_request_host_url())

        return {}


@ns.route('/withdrawal/approver/confirmation')
@respond_with_code
class WithdrawalApproverConfirmationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER)
        if row is None:
            raise InvalidLink
        user_id = row.user_id
        data = row.data_json
        request_id = data['request_id']
        cache = WithdrawalApproverCache(user_id, request_id)
        if not (data := cache.read()):
            raise InvalidLink
        approvers = json.loads(data)
        return {'approvers': approvers}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """确认添加审核人"""
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER)
        if row is None:
            raise InvalidLink
        user_id = row.user_id
        data = row.data_json
        request_id = data['request_id']
        with CacheLock(LockKeys.withdrawal_approver(user_id)):
            db.session.rollback()
            cache = WithdrawalApproverCache(user_id, request_id)
            if not (data := cache.read()):
                raise InvalidLink
            approvers = json.loads(data)
            WithdrawalApproversResource.validate_approvers(user_id, approvers)
            dels = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == user_id, 
                                                   WithdrawalApprover.status == WithdrawalApprover.Status.DELETED).all()
            dels = {x.account: x for x in dels}
            for item in approvers:
                if row := dels.get(item['account']):
                    name = str(escape(item['name']))
                    row.name = name
                    row.applied_at = now()
                    row.status = WithdrawalApprover.Status.CREATED
                else:
                    db.session.add(WithdrawalApprover(
                        user_id=user_id,
                        name=item['name'],
                        account=item['account'],
                        is_self=False,
                        applied_at=now()
                    ))
            db.session.commit()
            cache.delete()
            # send invitation email just one time, so we can delete cache here.
            approvers = WithdrawalApprover.query.filter(WithdrawalApprover.user_id == user_id,
                                                        WithdrawalApprover.status == WithdrawalApprover.Status.CREATED).all()
            for approver in approvers:
                email_token = EmailToken.new(
                    approver.user_id,
                    approver.email,
                    EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER,
                    dict(approver_id=approver.id),
                    ttl=3600
                )
                send_withdrawal_approver_join_confirmation_email.delay(approver.id, email_token.token, get_request_host_url())
        return {}

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(5, 600)
    @ns.use_kwargs(dict(
        request_id=fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """重发确认邮件"""
        request_id = kwargs['request_id']
        if not WithdrawalApproverCache(g.user.id, request_id).exists():
            raise InvalidLink

        WithdrawalApproversResource.send_confirmation_email(g.user, request_id)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """撤销添加审核人"""
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER)
        if row is None:
            raise InvalidLink
        user_id = row.user_id
        data = row.data_json
        request_id = data['request_id']
        cache = WithdrawalApproverCache(user_id, request_id)
        if not cache.exists():
            raise InvalidLink
        cache.delete()
        return {}


@ns.route('/withdrawal/approver/join')
@respond_with_code
class WithdrawalApproverJoinResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER)
        if row is None:
            raise InvalidLink
        data = row.data_json
        approver_id = data['approver_id']
        row = WithdrawalApprover.query.get(approver_id)
        if not row or row.status != WithdrawalApprover.Status.CREATED:
            raise InvalidLink
        user = User.query.get(row.user_id)
        return {'account': user.email}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """加入提现审核人"""
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER)
        if row is None:
            raise InvalidLink
        data = row.data_json
        approver_id = data['approver_id']
        row = WithdrawalApprover.query.get(approver_id)
        if not row or row.status != WithdrawalApprover.Status.CREATED:
            raise InvalidLink
        row.status = WithdrawalApprover.Status.VALID
        db.session.commit()
        detail = dict(email=row.email)
        OperationLog.add(row.user_id, OperationLog.Operation.ADD_WITHDRAWAL_APPROVER,
                         detail, get_request_platform())
        self_approver = User.query.get(row.user_id)
        SecurityToolHistory.add(
            row.user_id,
            SecurityToolHistory.OpType.ADD_WITHDRAWAL_APPROVER,
            SecurityToolHistory.OpRole.USER,
            self_approver.email,
            withdrawal_approver_email=row.account,
        )
        send_withdrawal_approver_join_notice_email.delay(row.id)
        return {}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """拒绝加入提现审核人"""
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER)
        if row is None:
            raise InvalidLink
        data = row.data_json
        approver_id = data['approver_id']
        row = WithdrawalApprover.query.get(approver_id)
        if not row or row.status != WithdrawalApprover.Status.CREATED:
            raise InvalidLink
        row.status = WithdrawalApprover.Status.FAILED
        db.session.commit()

        send_withdrawal_approver_reject_notice_email.delay(row.id)
        return {}


@ns.route('/withdrawal/approver/leave')
@respond_with_code
class WithdrawalApproverLeaveResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def get(cls, **kwargs):
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER)
        if row is None:
            raise InvalidLink
        data = row.data_json
        approver_id = data['approver_id']
        row = WithdrawalApprover.query.get(approver_id)
        if not row or row.status != WithdrawalApprover.Status.DELETING:
            raise InvalidLink
        user = User.query.get(row.user_id)
        return {'account': user.email}

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """确认退出提现审核人"""
        token = kwargs['token']
        row = EmailToken.validate(token, EmailToken.EmailType.WITHDRAWAL_APPROVER_MEMBER)
        if row is None:
            raise InvalidLink
        data = row.data_json
        approver_id = data['approver_id']
        row = WithdrawalApprover.query.get(approver_id)
        if not row or row.status != WithdrawalApprover.Status.DELETING:
            raise InvalidLink
        row.status = WithdrawalApprover.Status.DELETED
        db.session.commit()
        detail = dict(email=row.email)
        OperationLog.add(row.user_id, OperationLog.Operation.DELETE_WITHDRAW_APPROVER,
                         detail, get_request_platform())
        SecurityToolHistory.add(
            row.user_id,
            SecurityToolHistory.OpType.DELETE_WITHDRAWAL_APPROVER,
            SecurityToolHistory.OpRole.USER,
            row.account,
        )

        send_withdrawal_approver_leave_notice_email.delay(row.id)
        return {}


@ns.route('/broadcast')
@respond_with_code
class WalletBroadcastResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user_id = g.user.id
        cache = WalletBroadcastCache(user_id)
        if not cache.exists():
            cache.reload()
        return json_string_success(cache.read())


@ns.route('/kyt/proof')
@respond_with_code
class KYTProofResource(Resource):

    class KYTFileSchema(Schema):
        source_proof_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        address_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        edd_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        supplement_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        supplement_desc = fields.String(required=False, validate=max_length_validator(2048))

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        row = cls._get_row(kwargs['token'])
        data = row.data_json
        kyt_id = data['kyt_id']
        kyt = KYTDepositAudit.query.get(kyt_id)
        if not kyt or kyt.status == KYTDepositAudit.Status.CREATED:
            raise InvalidLink
        status = 'PROCESSING'
        if kyt.status in [
            KYTDepositAudit.Status.INFO_REQUIRED,
            KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
        ]:
            status = kyt.status.name
        user_id = kyt.user_id
        user = User.query.get(user_id)
        return dict(
            account=user.email,
            status=status,
        )

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True),
        files=fields.Nested(KYTFileSchema, required=True)
    ))
    def put(cls, **kwargs):
        row = cls._get_row(kwargs['token'])
        data = row.data_json
        kyt_id = data['kyt_id']
        kyt = KYTDepositAudit.query.get(kyt_id)
        if not kyt or kyt.status not in [
            KYTDepositAudit.Status.INFO_REQUIRED,
            KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
        ]:
            raise InvalidLink
        with CacheLock(LockKeys.kyt_deposit_audit(kyt.id)):
            db.session.rollback()
            kyt = KYTDepositAudit.query.get(kyt.id)
            if kyt.status not in [
                KYTDepositAudit.Status.INFO_REQUIRED,
                KYTDepositAudit.Status.EXTRA_INFO_REQUIRED,
            ]:
                raise InvalidLink
            cls._validate_file(kyt, kwargs['files'])
            kyt_detail = KYTDepositAuditDetail.get_or_create(kyt_id=kyt.id)
            if kyt.status is KYTDepositAudit.Status.INFO_REQUIRED:
                kyt_detail.source_proof_file_ids = json.dumps(kwargs['files']['source_proof_file_ids'])
                kyt_detail.address_file_ids = json.dumps(kwargs['files']['address_file_ids'])
                kyt_detail.edd_file_ids = json.dumps(kwargs['files']['edd_file_ids'])
                supplement_file_ids = kwargs['files'].get('supplement_file_ids')
                supplement_file_ids = json.dumps(supplement_file_ids) if supplement_file_ids else ''
                kyt_detail.supplement_file_ids = supplement_file_ids
                kyt_detail.supplement_desc = kwargs['files'].get('supplement_desc')
                db.session.add(kyt_detail)
            else:
                supplement_file_ids = kwargs['files']['supplement_file_ids']
                ori_supplement_file_ids = kyt_detail.get_supplement_file_ids()
                kyt_detail.supplement_file_ids = json.dumps(ori_supplement_file_ids + supplement_file_ids)
                if supplement_desc := kwargs['files'].get('supplement_desc'):
                    if kyt_detail.supplement_desc:
                        kyt_detail.supplement_desc += f'；{supplement_desc}'
                    else:
                        kyt_detail.supplement_desc = supplement_desc
                    if kyt_detail.supplement_desc:
                        kyt_detail.supplement_desc = kyt_detail.supplement_desc[:2048]
            kyt.status = KYTDepositAudit.Status.AUDIT_REQUIRED
            kyt.info_updated_at = now()
            db.session.commit()
        return {}

    @classmethod
    def _get_row(cls, token):
        row = EmailToken.validate(token, EmailToken.EmailType.KYT_DEPOSIT_AUDIT)
        if row is None:
            raise InvalidLink
        return row

    @classmethod
    def _validate_file(cls, kyt: KYTDepositAudit, files: dict):
        kyt_detail = KYTDepositAuditDetail.query.filter(
            KYTDepositAuditDetail.kyt_id == kyt.id
        ).first()
        if kyt.status is KYTDepositAudit.Status.INFO_REQUIRED:
            check_files = [
                files.get('source_proof_file_ids'),
                files.get('address_file_ids'),
                files.get('edd_file_ids'),
            ]
            for check_file in check_files:
                if not check_file:
                    raise InvalidArgument
            if kyt_detail:
                raise InvalidArgument
        elif kyt.status is KYTDepositAudit.Status.EXTRA_INFO_REQUIRED:
            supplement_file_ids = files.get('supplement_file_ids')
            supplement_desc = files.get('supplement_desc')
            if not supplement_file_ids and not supplement_desc:
                raise InvalidArgument
            if not kyt_detail:
                raise InvalidArgument


@ns.route('/edd/proof')
@respond_with_code
class EDDProofResource(Resource):

    class EDDSchema(Schema):
        name = fields.String(required=False)
        address = fields.String(required=False)
        mobile = fields.String(required=False)
        occupation = fields.String(required=False)
        employer = fields.String(required=False)
        employer_address = fields.String(required=False)
        annual_income = fields.String(required=False)
        income_sources = fields.List(EnumField(EDDAuditDetail.IncomeSource, required=True), required=False)
        income_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        income_source_desc = fields.String(required=False, validate=max_length_validator(1024))
        deposit_sources = fields.List(EnumField(EDDAuditDetail.DepositSource, required=True), required=False)
        deposit_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        deposit_source_desc = fields.String(required=False, validate=max_length_validator(1024))
        has_legal_proceedings = fields.Boolean(required=False, missing=False)
        legal_proceedings_desc = fields.String(required=False, validate=max_length_validator(1024))
        has_pep = fields.Boolean(required=False, missing=False)
        pep_desc = fields.String(required=False, validate=max_length_validator(1024))
        id_with_photo_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        address_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        supplement_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length_validator(5))
        supplement_desc = fields.String(required=False, validate=max_length_validator(1024))

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        row = cls._get_row(user_id, kwargs['id'])
        if row.status == EDDAudit.Status.CREATED:
            raise InvalidLink
        user_id = row.user_id
        user = User.query.get(user_id)
        deposit = dict(
            asset='',
            amount='',
        )
        if row.source == EDDAudit.Source.DEPOSIT_AUDIT:
            dep = Deposit.query.get(row.deposit_id)
            deposit = dict(
                asset=dep.asset,
                amount=dep.amount,
            )
        last_edd = cls._get_last_audit_edd(user_id, kwargs['id'])
        return dict(
            account=user.email,
            status=row.status.name,
            source=row.source.name,
            deposit=deposit,
            last_edd=last_edd,
            rejection_reason=row.get_reject_reason() or '',
        )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        files=fields.Nested(EDDSchema, required=True)
    ))
    def put(cls, **kwargs):
        user_id = g.user.id
        row = cls._get_row(user_id, kwargs['id'])
        if row.status not in [
            EDDAudit.Status.INFO_REQUIRED,
            EDDAudit.Status.EXTRA_INFO_REQUIRED,
        ]:
            raise InvalidLink
        with CacheLock(LockKeys.edd_audit(row.id)):
            db.session.rollback()
            row = EDDAudit.query.get(row.id)
            if row.status not in [
                EDDAudit.Status.INFO_REQUIRED,
                EDDAudit.Status.EXTRA_INFO_REQUIRED,
            ]:
                raise InvalidLink
            cls._validate_file(row, kwargs['files'])
            detail = EDDAuditDetail.get_or_create(edd_id=row.id)
            if row.status == EDDAudit.Status.INFO_REQUIRED:
                detail.name = kwargs['files']['name']
                detail.address = kwargs['files']['address']
                detail.mobile = kwargs['files']['mobile']
                detail.occupation = kwargs['files']['occupation']
                detail.employer = kwargs['files']['employer']
                detail.employer_address = kwargs['files']['employer_address']
                detail.annual_income = kwargs['files']['annual_income']

                detail.income_sources = json.dumps([x.name for x in kwargs['files']['income_sources']])
                detail.income_source_desc = kwargs['files']['income_source_desc']
                detail.income_file_ids = json.dumps(kwargs['files']['income_file_ids'])
                detail.has_legal_proceedings = kwargs['files']['has_legal_proceedings']
                detail.legal_proceedings_desc = kwargs['files'].get('legal_proceedings_desc')
                detail.has_pep = kwargs['files']['has_pep']
                detail.pep_desc = kwargs['files'].get('pep_desc')

                detail.id_with_photo_file_ids = json.dumps(kwargs['files']['id_with_photo_file_ids'])
                detail.address_file_ids = json.dumps(kwargs['files']['address_file_ids'])
                supplement_file_ids = kwargs['files'].get('supplement_file_ids')
                supplement_file_ids = json.dumps(supplement_file_ids) if supplement_file_ids else ''
                detail.supplement_file_ids = supplement_file_ids
                detail.supplement_desc = kwargs['files'].get('supplement_desc')
                if kwargs['files'].get('deposit_sources'):
                    detail.deposit_sources = json.dumps([x.name for x in kwargs['files']['deposit_sources']])
                    detail.deposit_source_desc = kwargs['files']['deposit_source_desc']
                    detail.deposit_file_ids = json.dumps(kwargs['files']['deposit_file_ids'])
                db.session.add(detail)
            else:
                supplement_file_ids = kwargs['files']['supplement_file_ids']
                ori_supplement_file_ids = detail.get_supplement_file_ids()
                detail.supplement_file_ids = json.dumps(ori_supplement_file_ids + supplement_file_ids)
                if supplement_desc := kwargs['files'].get('supplement_desc'):
                    if detail.supplement_desc:
                        detail.supplement_desc += f'；{supplement_desc}'
                    else:
                        detail.supplement_desc = supplement_desc
                if detail.supplement_desc:
                    detail.supplement_desc = detail.supplement_desc[:2048]
            row.status = EDDAudit.Status.AUDIT_REQUIRED
            row.info_updated_at = now()
            db.session.flush()
            db.session.commit()
            notify_edd_status_changed_task.delay(row.id)
        return {}

    @classmethod
    def _get_row(cls, user_id, id_):
        model = EDDAudit
        row = model.query.filter(
            model.user_id == user_id,
            model.id == id_,
        ).first()
        if row is None:
            raise InvalidLink
        return row

    @classmethod
    def _validate_file(cls, row: EDDAudit, files: dict):
        detail = EDDAuditDetail.query.filter(
            EDDAuditDetail.edd_id == row.id
        ).first()
        if row.status == EDDAudit.Status.INFO_REQUIRED:
            check_fields = [
                'name',
                'address',
                'mobile',
                'occupation',
                'employer',
                'employer_address',
                'annual_income',
                'income_sources',
                'income_file_ids',
                'id_with_photo_file_ids',
                'address_file_ids',
            ]
            for field in check_fields:
                if not files.get(field):
                    raise InvalidArgument
            check_files = [
                files.get('income_file_ids'),
                files.get('address_file_ids'),
                files.get('id_with_photo_file_ids'),
            ]
            if files.get('deposit_sources'):
                check_files.append(files.get('deposit_file_ids'))
            for check_file in check_files:
                if not check_file:
                    raise InvalidArgument
            if detail:
                raise InvalidArgument
        elif row.status == EDDAudit.Status.EXTRA_INFO_REQUIRED:
            supplement_file_ids = files.get('supplement_file_ids')
            supplement_desc = files.get('supplement_desc')
            if not supplement_file_ids and not supplement_desc:
                raise InvalidArgument
            if not detail:
                raise InvalidArgument

    @classmethod
    def _get_last_audit_edd(cls, user_id, id_) -> dict:
        model = EDDAudit
        row = model.query.filter(
            model.user_id == user_id,
            model.id != id_,
            model.status == model.Status.AUDITED,
        ).order_by(
            model.id.desc()
        ).first()
        if not row:
            return {}
        detail = EDDAuditDetail.query.filter(
            EDDAuditDetail.edd_id == row.id
        ).first()
        if not detail:
            return {}
        return dict(
            name=detail.name,
            address=detail.address,
            mobile=detail.mobile,
            occupation=detail.occupation,
            employer=detail.employer,
            employer_address=detail.employer_address,
            annual_income=detail.annual_income,
            income_sources=detail.get_income_sources(),
            income_source_desc=detail.income_source_desc,
            deposit_sources=detail.get_deposit_sources(),
            deposit_source_desc=detail.deposit_source_desc,
            has_legal_proceedings=detail.has_legal_proceedings,
            legal_proceedings_desc=detail.legal_proceedings_desc,
            has_pep=detail.has_pep,
            pep_desc=detail.pep_desc,
        )
