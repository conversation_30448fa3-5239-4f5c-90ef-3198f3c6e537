# -*- coding: utf-8 -*-
import math
import json
from datetime import timed<PERSON><PERSON>
from decimal import Decimal, ROUND_DOWN
from typing import List, Dict

from flask import g
from flask_babel import gettext
from marshmallow import Schema, EXCLUDE, fields as mm_fields

from app.api.common.request import require_user_request_permission
from app.business.clients.server import ServerClient
from app.models import db, VipUser
from app.models.pledge import (
    LoanAsset,
    PledgeAsset,
    PledgePosition,
    PledgePositionTrace,
    PledgeLoanOrder,
    PledgeLoanOrderRepayHistory,
    PledgeLoanOrderInterestHistory,
    PledgeLoanOrderRenewHistory,
    PledgeLoanHistory,
    PledgeRepayHistory,
    PledgeHistory,
    PledgeInterestHistory,
    PledgeAssetTempOfflineUser,
)
from app.common import PrecisionEnum
from app.exceptions import TwoFactorAuthenticationRequired, InvalidArgument, PledgeLoanAmountMinLimit, OperationDenied
from app.business import <PERSON>acheLock, LockKeys, SiteSettings
from app.business.pledge.helper import get_user_last_active_position, get_loan_asset_info, get_pledge_assets_infos
from app.business.pledge.position import PositionManager, pledge_position_trace_task, LoanOrderHelper
from app.business.pledge.loan import LoanOperator, PledgeOperator, LoanAmountHelper
from app.business.pledge.repay import SpotAccountRepayHelper, PledgeAccountRepayHelper
from app.caches.pledge import LoanAssetViewCache, PledgeAssetViewCache, PledgeAccountViewCache
from app.api.common import (
    Resource,
    Namespace,
    respond_with_code,
    require_login,
    lock_request,
    json_string_success,
    limit_user_frequency,
    get_request_user,
)
from app.api.common.fields import LimitField, PageField, PositiveDecimalField, AssetField, EnumField
from app.utils import amount_to_str


ns = Namespace("Pledge")


@ns.route("/loanable-assets")
@respond_with_code
class LoanableAssetsResource(Resource):
    @classmethod
    def get(cls):
        """ 可借币种的列表 """
        cache = LoanAssetViewCache()
        if data := cache.read():
            user = get_request_user()
            if user:
                res_items = json.loads(data)
                vip_row = VipUser.query.filter(
                    VipUser.user_id == user.id,
                    VipUser.status == VipUser.StatusType.PASS,
                ).with_entities(VipUser.level).first()
                vip_level = vip_row.level if vip_row else 0
                for item in res_items:
                    if vip_day_rate := item.get('day_rates', {}).get(str(vip_level)):
                        item['day_rate'] = vip_day_rate
                return res_items
            else:
                return json_string_success(data)
        return []


@ns.route("/collateral-assets")
@respond_with_code
class PledgeAssetsResource(Resource):
    
    @classmethod
    def get(cls):
        """ 可质押币种的列表 """
        cache = PledgeAssetViewCache()
        if data := cache.read():
            return json_string_success(data)
        return []


@ns.route("/accounts")
@respond_with_code
class PledgeAccountsResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 质押账户列表 """
        data = PledgeAccountViewCache().read()
        accounts = json.loads(data) if data else []
        if not accounts:
            return accounts

        user = g.user
        positions: List[PledgePosition] = PledgePosition.query.filter(
            PledgePosition.user_id == user.id,
            PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
        ).all()
        asset_position_dict: Dict[str, PledgePosition] = {i.loan_asset: i for i in positions}
        if not positions:
            position_ltvs_dict = {}
        elif len(positions) == 1:
            position_ltvs_dict = {positions[0].id: PositionManager.calc_ltv_and_liq_ltvs(positions[0])}
        else:
            position_ltvs_dict = PositionManager.batch_calc_ltv_and_liq_ltvs(positions)

        vip_row = VipUser.query.filter(
            VipUser.user_id == user.id,
            VipUser.status == VipUser.StatusType.PASS,
        ).with_entities(VipUser.level).first()
        vip_level = vip_row.level if vip_row else 0

        for account_item in accounts:
            if vip_day_rate := account_item.get('day_rates', {}).get(str(vip_level)):
                account_item['day_rate'] = vip_day_rate
            position = asset_position_dict.get(account_item["asset"])
            if position:
                cur_ltv, warning_ltv, liq_ltv = position_ltvs_dict[position.id]
                account_item["position"] = {
                    "position_id": position.id,
                    "status": position.status.name,
                    "debt_amount": position.debt_amount,
                    "interest_amount": position.interest_amount,
                    "cur_ltv": cur_ltv,
                    "warning_ltv": warning_ltv,
                    "liquidation_ltv": liq_ltv,
                }
            else:
                account_item["position"] = None
        accounts = [item for item in accounts if item["status"] == LoanAsset.Status.OPEN.name or item["position"]]
        return accounts


class _PledgeAssetAmountSchema(Schema):
    """ 质押的币种和数目 """

    class Meta:
        UNKNOWN = EXCLUDE

    asset = AssetField(required=True)
    amount = PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True)


@ns.route("/loan/amount")
@respond_with_code
class LoanAmountResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=True),
            pledge_assets=mm_fields.Nested(_PledgeAssetAmountSchema, many=True, required=False),
        )
    )
    def post(cls, **kwargs):
        """ 获取剩余最大借币数 """
        user = g.user
        require_user_request_permission(g.user)

        loan_asset = kwargs["asset"]
        if kwargs.get("pledge_assets"):
            pledge_asset_dict = {i["asset"]: i["amount"] for i in kwargs["pledge_assets"]}
        else:
            pledge_asset_dict = {}
        PledgeLoanResource.check(loan_asset, pledge_asset_dict, loan_asset_open=False)

        helper = LoanAmountHelper(user.id, loan_asset)
        rest_amount, max_amount = helper.calc_loanable_amounts(add_pledge_asset_dict=pledge_asset_dict)
        return {
            "min_loan_amount": helper.loan_asset_info.min_loan_amount,
            "rest_loanable_amount": rest_amount,
            "max_loanable_amount": max_amount,
        }


@ns.route("/loan")
@respond_with_code
class PledgeLoanResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(2, 5)
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
            pledge_assets=mm_fields.Nested(_PledgeAssetAmountSchema, many=True, required=False),
            is_renew=mm_fields.Bool(required=False, missing=True),
        )
    )
    def post(cls, **kwargs):
        """ 提交借币 """
        user = g.user
        if not user.has_2fa:
            raise TwoFactorAuthenticationRequired
        if not SiteSettings.pledge_enabled:
            raise OperationDenied
        require_user_request_permission(g.user)

        loan_asset = kwargs["asset"]
        loan_amount = kwargs["amount"]
        is_renew = kwargs["is_renew"]
        if kwargs.get("pledge_assets"):
            pledge_asset_dict = {i["asset"]: i["amount"] for i in kwargs["pledge_assets"]}
        else:
            pledge_asset_dict = {}
        cls.check(loan_asset, pledge_asset_dict, loan_amount)

        with CacheLock(key=LockKeys.user_pledge(user.id, loan_asset), wait=False):
            db.session.rollback()
            if pledge_asset_dict:
                # 无仓位借币 or 有仓位借币-增加质押币
                PledgeOperator.add_pledge_assets_and_loan(
                    user.id, loan_asset, loan_amount, pledge_asset_dict, is_renew
                )
            else:
                # 有仓位借币-未增加质押币
                loan_op = LoanOperator(user.id, loan_asset)
                loan_op.increase_loan(loan_amount, is_renew)

            # 更新下订单的续借状态
            last_position = get_user_last_active_position(user.id, loan_asset)
            bor_loan_orders = LoanOrderHelper.get_pos_borrowing_loan_orders(last_position.id)
            for loan_order in bor_loan_orders:
                if loan_order.is_renew != is_renew:
                    loan_order.is_renew = is_renew
            db.session.commit()

    @classmethod
    def check(cls, loan_asset: str, pledge_asset_dict: Dict[str, Decimal], loan_amount: Decimal = None,
              loan_asset_open: bool = True):
        if loan_asset in pledge_asset_dict:
            raise InvalidArgument
        try:
            loan_asset_info = get_loan_asset_info(loan_asset, only_open=loan_asset_open)
        except ValueError:
            raise InvalidArgument(message=gettext("你使用的借币币种%(asset)s已下架，无法操作", asset=loan_asset))
        if loan_amount is not None and loan_amount < loan_asset_info.min_loan_amount:
            raise PledgeLoanAmountMinLimit(asset=loan_asset, amount=loan_asset_info.min_loan_amount)

        try:
            get_pledge_assets_infos(list(pledge_asset_dict), only_open=True)  # inner check
        except ValueError:
            raise InvalidArgument


@ns.route("/position/collateral/add")
@respond_with_code
class PositionAddCollateralResource(Resource):

    NOT_ALLOW_OP_MSG = gettext("禁止操作")

    @classmethod
    def check_temp_offline(cls, user_id: int):
        row = PledgeAssetTempOfflineUser.get_by_user_id(user_id)
        if row:
            raise InvalidArgument(message=gettext("你使用的质押币%(asset)s临时下架，无法操作", asset=row.asset))

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(2, 5)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            pledge_assets=mm_fields.Nested(_PledgeAssetAmountSchema, many=True, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 增加质押币 """
        if not SiteSettings.pledge_enabled:
            raise OperationDenied

        user = g.user
        position_id = kwargs["position_id"]
        position = cls.check_and_get_active_position(user.id, position_id)
        if not position.is_borrowing():
            raise InvalidArgument(message=cls.NOT_ALLOW_OP_MSG)

        loan_asset = position.loan_asset
        pledge_asset_dict = {i["asset"]: i["amount"] for i in kwargs["pledge_assets"]}
        if not pledge_asset_dict:
            raise InvalidArgument
        off_record = PledgeAsset.query.filter(PledgeAsset.asset.in_(list(pledge_asset_dict)),
                                              PledgeAsset.status != PledgeAsset.Status.OPEN).first()
        if off_record:
            if off_record.status == PledgeAsset.Status.TEMP_OFFLINE:
                raise InvalidArgument(message=gettext("你使用的质押币%(asset)s临时下架，无法操作", 
                                      asset=off_record.asset))
            else:
                raise InvalidArgument(message=gettext("你使用的质押币%(asset)s已下架，无法操作", 
                                      asset=off_record.asset))

        PledgeLoanResource.check(loan_asset, pledge_asset_dict, loan_asset_open=False)
        with CacheLock(key=LockKeys.user_pledge(user.id, loan_asset), wait=False):
            db.session.rollback()
            PledgeOperator.add_pledge_assets(position, pledge_asset_dict)

    @classmethod
    def check_and_get_active_position(cls, user_id: int, position_id: int) -> PledgePosition:
        """ 检查并返回最新借币中的仓位 """
        param_pos: PledgePosition = PledgePosition.query.filter(
            PledgePosition.id == position_id,
            PledgePosition.user_id == user_id,
        ).first()
        if not param_pos:
            raise InvalidArgument
        last_position = get_user_last_active_position(user_id, param_pos.loan_asset)
        if not last_position or last_position.id != position_id:
            raise InvalidArgument
        return last_position


@ns.route("/position/collateral/remove")
@respond_with_code
class PositionRemoveCollateralResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            asset=AssetField(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 获取可减少质押币的数目 """
        user = g.user
        position_id = kwargs["position_id"]
        position = PositionAddCollateralResource.check_and_get_active_position(user.id, position_id)
        if not position.is_borrowing():
            raise InvalidArgument

        remove_asset = kwargs["asset"]
        max_remove_amount = PledgeOperator.get_max_remove_amount(position, remove_asset)
        return {
            "max_remove_amount": max_remove_amount,
        }

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(2, 5)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            asset=AssetField(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 减少质押币 """
        if not SiteSettings.pledge_enabled:
            raise OperationDenied

        user = g.user
        position_id = kwargs["position_id"]
        position = PositionAddCollateralResource.check_and_get_active_position(user.id, position_id)
        if not position.is_borrowing():
            raise InvalidArgument(message=PositionAddCollateralResource.NOT_ALLOW_OP_MSG)
        PositionAddCollateralResource.check_temp_offline(user.id)

        loan_asset = position.loan_asset
        remove_asset = kwargs["asset"]
        remove_amount = kwargs["amount"]
        with CacheLock(key=LockKeys.user_pledge(user.id, loan_asset), wait=False):
            db.session.rollback()
            PledgeOperator.remove_pledge_asset(position, remove_asset, remove_amount)


@ns.route("/position/repay")
@respond_with_code
class PositionRepayResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @limit_user_frequency(2, 5)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 还币(用借币币种) """
        if not SiteSettings.pledge_enabled:
            raise OperationDenied

        user = g.user
        position_id = kwargs["position_id"]
        repay_amount = kwargs["amount"]
        position = PositionAddCollateralResource.check_and_get_active_position(user.id, position_id)
        if not position.is_repayable():
            raise InvalidArgument(message=PositionAddCollateralResource.NOT_ALLOW_OP_MSG)
        if repay_amount > position.total_unflat_amount:
            raise InvalidArgument
        PositionAddCollateralResource.check_temp_offline(user.id)

        try:
            loan_asset_info = get_loan_asset_info(position.loan_asset)
        except ValueError:
            raise InvalidArgument
        min_loan_amount = loan_asset_info.min_loan_amount
        with CacheLock(key=LockKeys.user_pledge(user.id, position.loan_asset), wait=False):
            db.session.rollback()
            remain_unflat_amount = position.total_unflat_amount - repay_amount
            if 0 < remain_unflat_amount < min_loan_amount:
                raise InvalidArgument(
                    message=gettext(
                        f"还币后剩余待还数不能低于%(min_amount)s%(coin_type)s,请调整还币数量.",
                        min_amount=amount_to_str(min_loan_amount),
                        coin_type=position.loan_asset,
                    )
                )
            SpotAccountRepayHelper(position).repay(repay_amount)


@ns.route("/position/close")
@respond_with_code
class PositionCloseResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 还币(用质押币种)|平仓 """
        if not SiteSettings.pledge_enabled:
            raise OperationDenied

        user = g.user
        position_id = kwargs["position_id"]
        position = PositionAddCollateralResource.check_and_get_active_position(user.id, position_id)
        if not position.is_borrowing():
            raise InvalidArgument(message=PositionAddCollateralResource.NOT_ALLOW_OP_MSG)
        PositionAddCollateralResource.check_temp_offline(user.id)

        account_id = get_loan_asset_info(position.loan_asset).account_id
        balances = ServerClient().get_user_balances(user.id, account_id=account_id)
        balance_assets = {
            asset for asset, balance_info in balances.items()
            if balance_info["available"] > Decimal()
        }
        off_record = PledgeAsset.query.filter(
            PledgeAsset.asset.in_(balance_assets), 
            PledgeAsset.status != PledgeAsset.Status.OPEN
        ).first()
        if off_record:
            if off_record.status == PledgeAsset.Status.TEMP_OFFLINE:
                raise InvalidArgument(message=gettext("你使用的质押币%(asset)s临时下架，无法操作", 
                                      asset=off_record.asset))
            else:
                raise InvalidArgument(message=gettext("你使用的质押币%(asset)s已下架，无法操作", 
                                      asset=off_record.asset))
        with CacheLock(key=LockKeys.user_pledge(user.id, position.loan_asset), wait=False):
            db.session.rollback()
            PledgeAccountRepayHelper(position, PledgeRepayHistory.Type.REPAY_BY_PLEDGE).start_close_repay()


@ns.route("/position/<int:position_id>")
@respond_with_code
class PositionDetailResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, position_id):
        """ 仓位详情 """
        user = g.user
        position: PledgePosition = PledgePosition.query.filter(
            PledgePosition.id == position_id,
            PledgePosition.user_id == user.id,
        ).first()
        if not position:
            raise InvalidArgument

        trace: PledgePositionTrace = PledgePositionTrace.query.filter(
            PledgePositionTrace.position_id == position_id,
        ).first()
        try:
            loan_asset_info = get_loan_asset_info(position.loan_asset, only_open=False)
            account_id = loan_asset_info.account_id
        except ValueError:
            account_id = None

        allow_close_repay = False
        if account_id and position.is_borrowing() and not PledgeAssetTempOfflineUser.get_by_user_id(user.id):
            balances = ServerClient().get_user_balances(user.id, account_id=account_id)
            balance_assets = {
                asset for asset, balance_info in balances.items()
                if balance_info["available"] > Decimal()
            }
            off_record = PledgeAsset.query.filter(
                PledgeAsset.asset.in_(balance_assets),
                PledgeAsset.status != PledgeAsset.Status.OPEN,
            ).first()
            if not off_record:
                allow_close_repay = True

        cur_ltv, warning_ltv, liq_ltv = PositionManager.calc_ltv_and_liq_ltvs(position)
        res = dict(
            created_at=int(position.created_at.timestamp()),
            position_id=position.id,
            asset=position.loan_asset,
            account_id=account_id,
            status=position.status.name,
            allow_close_repay=allow_close_repay,
            debt_amount=position.debt_amount,
            interest_amount=position.interest_amount,
            cur_ltv=cur_ltv,
            warning_ltv=warning_ltv,
            liquidation_ltv=liq_ltv,
            finished_at=int(position.finished_at.timestamp()) if position.finished_at else None,
            finish_type=position.finish_type.name if position.finish_type else None,
            max_debt_amount=trace.max_debt_amount if trace else Decimal(),
            total_repay_interest_amount=trace.total_repay_interest_amount if trace else Decimal(),
            last_day_rate=trace.last_day_rate if trace else Decimal(),
        )
        return res


@ns.route("/position/history")
@respond_with_code
class PositionHistoryResource(Resource):
    @classmethod
    def re_trace_positions(cls, position_ids):
        for position_id in position_ids:
            pledge_position_trace_task.delay(position_id)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 仓位历史记录 """
        user = g.user
        q = PledgePosition.query.filter(
            PledgePosition.user_id == user.id,
            PledgePosition.status == PledgePosition.Status.FINISHED,
        ).order_by(PledgePosition.finished_at.desc())
        if asset := kwargs.get("asset"):
            q = q.filter(PledgePosition.loan_asset == asset)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[PledgePosition] = pagination.items
        pos_ids = [i.id for i in rows]
        if pos_ids:
            traces: List[PledgePositionTrace] = PledgePositionTrace.query.filter(
                PledgePositionTrace.position_id.in_(pos_ids),
            ).all()
            pos_trace_dict = {i.position_id: i for i in traces}
            cls.re_trace_positions(set(pos_trace_dict) - set(pos_ids))
        else:
            pos_trace_dict = {}

        items = []
        for r in rows:
            trace: PledgePositionTrace = pos_trace_dict.get(r.id)
            items.append(
                dict(
                    created_at=int(r.created_at.timestamp()),
                    position_id=r.id,
                    asset=r.loan_asset,
                    finished_at=int(r.finished_at.timestamp()),
                    max_debt_amount=trace.max_debt_amount if trace else Decimal(),
                    total_repay_interest_amount=trace.total_repay_interest_amount if trace else Decimal(),
                    last_day_rate=trace.last_day_rate if trace else Decimal(),
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/position/loan/history")
@respond_with_code
class PositionLoanHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 仓位的借币记录 """
        user = g.user
        position_id = kwargs["position_id"]
        cls.check_position(user.id, position_id)

        q = PledgeLoanHistory.query.filter(
            PledgeLoanHistory.position_id == position_id,
        ).order_by(PledgeLoanHistory.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[PledgeLoanHistory] = pagination.items
        items = []
        for r in rows:
            pledge_asset_dict: dict = json.loads(r.pledge_data) if r.pledge_data else {}
            pledge_assets = [{"asset": k, "amount": v} for k, v in pledge_asset_dict.items()]
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    loan_asset=r.loan_asset,
                    loan_amount=r.loan_amount,
                    pledge_assets=pledge_assets,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def check_position(cls, user_id: int, position_id: int):
        req_pos: PledgePosition = PledgePosition.query.filter(
            PledgePosition.id == position_id,
            PledgePosition.user_id == user_id,
        ).first()
        if not req_pos:
            raise InvalidArgument


@ns.route("/position/repay/history")
@respond_with_code
class PositionRepayHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 仓位的还币记录 """
        user = g.user
        position_id = kwargs["position_id"]
        PositionLoanHistoryResource.check_position(user.id, position_id)

        q = PledgeRepayHistory.query.filter(
            PledgeRepayHistory.position_id == position_id,
            PledgeRepayHistory.status == PledgeRepayHistory.Status.FINISHED,
        ).order_by(PledgeRepayHistory.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[PledgeRepayHistory] = pagination.items
        items = []
        for r in rows:
            if r.type in [PledgeRepayHistory.Type.REPAY_BY_LOAN, PledgeRepayHistory.Type.REPAY_BY_PLEDGE]:
                repay_type = "SELF"
            elif r.type in [PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN, PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE]:
                repay_type = "SYSTEM"
            else:
                repay_type = r.type.name
            if r.type in [
                PledgeRepayHistory.Type.REPAY_BY_LOAN,
                PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN,
                PledgeRepayHistory.Type.FUND,
            ]:
                repay_asset_type = "LOAN_ASSET"
            else:
                repay_asset_type = "PLEDGE_ASSET"
            pledge_asset_dict: dict = json.loads(r.used_pledge_data) if r.used_pledge_data else {}
            used_pledge_assets = [{"asset": k, "amount": v} for k, v in pledge_asset_dict.items()]
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    repay_type=repay_type,
                    repay_asset_type=repay_asset_type,
                    loan_asset=r.loan_asset,
                    repay_debt_amount=r.repay_debt_amount,
                    repay_interest_amount=r.repay_interest_amount,
                    used_pledge_assets=used_pledge_assets,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/position/collateral/history")
@respond_with_code
class PositionCollateralHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 仓位的质押记录 """
        user = g.user
        position_id = kwargs["position_id"]
        PositionLoanHistoryResource.check_position(user.id, position_id)

        q = PledgeHistory.query.filter(
            PledgeHistory.position_id == position_id,
        ).order_by(PledgeHistory.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[PledgeHistory] = pagination.items
        items = []
        for r in rows:
            pledge_asset_dict: dict = json.loads(r.pledge_data) if r.pledge_data else {}
            pledge_assets = [{"asset": k, "amount": v} for k, v in pledge_asset_dict.items()]
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    type=r.type.name,
                    loan_asset=r.loan_asset,
                    pledge_assets=pledge_assets,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/position/interest/history")
@respond_with_code
class PositionInterestHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            position_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 仓位的利息记录 """
        user = g.user
        position_id = kwargs["position_id"]
        PositionLoanHistoryResource.check_position(user.id, position_id)

        q = PledgeInterestHistory.query.filter(
            PledgeInterestHistory.position_id == position_id,
        ).order_by(PledgeInterestHistory.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[PledgeInterestHistory] = pagination.items
        items = []
        for r in rows:
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    loan_asset=r.loan_asset,
                    loan_amount=r.loan_amount,
                    day_rate=r.day_rate,
                    interest_amount=r.amount,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/order/history")
@respond_with_code
class LoanOrderHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            loan_asset=mm_fields.String,
            status=EnumField(PledgeLoanOrder.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借币订单记录 """
        user = g.user

        model = PledgeLoanOrder
        q = model.query.filter(
            model.user_id == user.id,
        ).order_by(model.id.desc())
        if loan_asset := kwargs.get("loan_asset"):
            q = q.filter(model.loan_asset == loan_asset)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[model] = pagination.items
        items = [cls.format_item(r) for r in rows]

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def format_item(cls, r: PledgeLoanOrder) -> dict:
        item = dict(
            id=r.id,
            created_at=int(r.created_at.timestamp()),
            position_id=r.position_id,
            loan_asset=r.loan_asset,
            loan_amount=r.loan_amount,
            debt_amount=r.debt_amount,
            interest_amount=r.interest_amount,
            day_rate=r.day_rate,
            is_renew=r.is_renew,
            status=r.status.name,
            expire_at=int(r.expire_at.timestamp()),
            finished_at=int(r.finished_at.timestamp()) if r.finished_at else None,
        )
        return item


@ns.route('/order/renew')
@respond_with_code
class LoanOrderRenewResource(Resource):
    @classmethod
    def check_and_get_loan_order(cls, user_id: int, loan_order_id: int) -> PledgeLoanOrder:
        req_loan_order: PledgeLoanOrder = PledgeLoanOrder.query.filter(
            PledgeLoanOrder.id == loan_order_id,
            PledgeLoanOrder.user_id == user_id,
        ).first()
        if not req_loan_order:
            raise InvalidArgument
        return req_loan_order

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            loan_order_id=mm_fields.Integer(required=True),
            is_renew=mm_fields.Boolean(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 借币订单-修改续借开关 """
        user = g.user
        loan_order_id = kwargs['loan_order_id']
        is_renew = kwargs['is_renew']
        loan_order = cls.check_and_get_loan_order(user.id, loan_order_id)
        if loan_order.status != PledgeLoanOrder.Status.BORROWING:
            raise InvalidArgument
        position = PositionAddCollateralResource.check_and_get_active_position(user.id, loan_order.position_id)
        with CacheLock(key=LockKeys.user_pledge(user.id, position.loan_asset), wait=False):
            db.session.rollback()
            # 目前自动续借状态对所有订单生效 用户选择开启则所有订单开启 选择关闭则所有订单关闭
            bor_loan_orders = LoanOrderHelper.get_pos_borrowing_loan_orders(position.id)
            for loan_order in bor_loan_orders:
                loan_order.is_renew = is_renew
            db.session.commit()


@ns.route("/order/repay/history")
@respond_with_code
class LoanOrderRepayHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            loan_order_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借币订单-还币历史 """
        user = g.user
        loan_order_id = kwargs['loan_order_id']
        LoanOrderRenewResource.check_and_get_loan_order(user.id, loan_order_id)

        model = PledgeLoanOrderRepayHistory
        q = model.query.filter(
            model.loan_order_id == loan_order_id,
        ).order_by(model.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[model] = pagination.items
        items = []
        for r in rows:
            if r.type in [PledgeRepayHistory.Type.REPAY_BY_LOAN, PledgeRepayHistory.Type.REPAY_BY_PLEDGE]:
                repay_type = "SELF"
            elif r.type in [PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN, PledgeRepayHistory.Type.SYS_REPAY_BY_PLEDGE]:
                repay_type = "SYSTEM"
            else:
                repay_type = r.type.name
            if r.type in [
                PledgeRepayHistory.Type.REPAY_BY_LOAN,
                PledgeRepayHistory.Type.SYS_REPAY_BY_LOAN,
                PledgeRepayHistory.Type.FUND,
            ]:
                repay_asset_type = "LOAN_ASSET"
            else:
                repay_asset_type = "PLEDGE_ASSET"
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    repay_type=repay_type,
                    repay_asset_type=repay_asset_type,
                    loan_asset=r.loan_asset,
                    repay_debt_amount=r.repay_debt_amount,
                    repay_interest_amount=r.repay_interest_amount,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/order/interest/history")
@respond_with_code
class LoanOrderInterestHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            loan_order_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借币订单-利息历史 """
        user = g.user
        loan_order_id = kwargs['loan_order_id']
        loan_order = LoanOrderRenewResource.check_and_get_loan_order(user.id, loan_order_id)
        page, limit = kwargs["page"], kwargs["limit"]

        model = PledgeLoanOrderInterestHistory
        q = model.query.filter(
            model.loan_order_id == loan_order_id,
        ).order_by(model.id.desc())

        pagination = q.paginate(page, limit, error_out=False)
        rows: List[model] = pagination.items
        items = []
        for r in rows:
            items.append(
                dict(
                    id=r.id,
                    created_at=int(r.created_at.timestamp()),
                    loan_asset=r.loan_asset,
                    loan_amount=r.loan_amount,
                    day_rate=r.day_rate,
                    interest_amount=r.amount,
                )
            )
        has_next = pagination.has_next
        curr_page = pagination.page
        total = pagination.total
        total_page = pagination.pages

        if loan_order.created_at.timestamp() < 1727395200:
            # 兼容历史数据，2024-9-27 之前创建的借币订单，2025-10-1后可删除
            first_row = model.query.filter(
                model.loan_order_id == loan_order_id,
            ).order_by(model.id.asc()).first()
            start_dt = first_row.created_at if first_row else None

            q = PledgeInterestHistory.query.filter(
                PledgeInterestHistory.position_id == loan_order.position_id,
                PledgeInterestHistory.created_at > loan_order.created_at,
            ).order_by(PledgeInterestHistory.id.desc())
            if start_dt:
                q = q.filter(PledgeInterestHistory.created_at < start_dt - timedelta(seconds=1))

            if len(items) < limit:
                total2_offset = 0
                ago_offset = total % limit
                if len(items) == 0 and ago_offset > 0:
                    # 无新数据 全部是老数据时，跳过最早的N条老数据
                    cur_offset = limit - ago_offset
                    ago_offset_row = q.limit(1).offset(cur_offset-1).with_entities(PledgeInterestHistory.id).first()
                    if ago_offset_row:
                        total2_offset = cur_offset
                        q = q.filter(PledgeInterestHistory.id < ago_offset_row.id)

                ago_total_page = math.ceil(total / limit) if limit else 1
                new_page = max(page - ago_total_page, 1)
                pagination = q.paginate(new_page, limit, error_out=False)
                rows: List[PledgeInterestHistory] = pagination.items
                items2 = []
                for r in rows:
                    items2.append(
                        dict(
                            id=r.id,
                            created_at=int(r.created_at.timestamp()),
                            loan_asset=r.loan_asset,
                            loan_amount=r.loan_amount,
                            day_rate=r.day_rate,
                            interest_amount=r.amount,
                        )
                    )
                items.extend(items2)
                items = items[:limit]
                total2 = pagination.total + total2_offset
                has_next = pagination.has_next
            else:
                pagination = q.paginate(1, 1, error_out=False)
                total2 = pagination.total
                has_next = has_next or pagination.has_next

            total += total2
            total_page = int(math.ceil(total / limit))

        return dict(
            has_next=has_next,
            curr_page=curr_page,
            count=len(items),
            data=items,
            total=total,
            total_page=total_page,
        )


@ns.route("/order/renew/history")
@respond_with_code
class LoanOrderRenewHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            loan_order_id=mm_fields.Integer(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借币订单-续借历史 """
        user = g.user
        loan_order_id = kwargs['loan_order_id']
        LoanOrderRenewResource.check_and_get_loan_order(user.id, loan_order_id)

        model = PledgeLoanOrderRenewHistory
        q = model.query.filter(
            model.loan_order_id == loan_order_id,
        ).order_by(model.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: List[model] = pagination.items
        items = []
        for r in rows:
            items.append(
                dict(
                    id=r.id,
                    loan_asset=r.loan_asset,
                    loan_order_id=r.loan_order_id,
                    status=r.status.name,
                    loan_amount=r.loan_amount,
                    day_rate=r.day_rate,
                    renew_at=int(r.renew_at.timestamp()),
                    expire_at=int(r.expire_at.timestamp()),
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )
