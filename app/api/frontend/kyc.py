# -*- coding: utf-8 -*-
import json
import os.path
from datetime import date, datetime
from typing import Union, List, Dict, Optional, Tuple
from flask import g, request, current_app
from flask_babel import gettext
from webargs import fields
from marshmallow import Schema, EXCLUDE

from app.business.lock import <PERSON>acheLock, LockKeys
from app.config import config
from app.models.system import CountrySetting
from app.models.user import KycVerificationPro, EmailToken, LivenessCheckProfile, \
    LivenessCheckHistory, BigUserCustomer, KycCountryConfig, KycVerificationProDraft, \
    KycVerificationExtendHistory, KycVerificationExtend

from app.utils.ip import GeoIP

from ..common.request import get_request_ip, get_request_user, is_old_app_request, get_request_platform
from ...business import CountrySettings
from ...business.kyc import KycBusiness, KYCInstitutionBusiness, SumsubClient, LivenessCheckBusiness, KycExtendBusiness
from ...business.risk_screen import RiskScreenBusiness
from ...models import KycVerification, User, File, db, KycVerificationHistory, InstitutionDirector, UserRiskScreen, \
    KYCInstitution, KYCFlow
from ...exceptions import (InvalidArgument,
                           DuplicateKYCVerification,
                           ImageFormatError, FileTooBig, ServiceUnavailable,
                           KycExceedsMaxAttempts, FileTooSmall, UserRiskScreenFailed, InvalidLink, InvalidKYC,
                           KycSupportIdDocsChanged, NoKycQualifications)
from ...utils import AWSBucketPrivate, now, strip_non_alpha_num, BaseHTTPClient
from ...common import get_country, list_country_codes_3
from ..common import (Namespace, Resource, respond_with_code,
                      require_login, lock_request, get_request_language, limit_user_frequency)
from ..common.fields import EnumField, DateField

from PIL import Image

ns = Namespace('KYC')


class KycMixin:

    @classmethod
    def check_and_parse_expire_date(cls, expire_date_str: str) -> Tuple[KycVerification.ExpireDateType, Optional[date]]:
        if expire_date_str == "":
            # 永不过期
            return KycVerification.ExpireDateType.NEVER, None
        if expire_date_str is None:
            # 老版本
            return KycVerification.ExpireDateType.EXPIRABLE, None
        try:
            expire_date = datetime.strptime(expire_date_str, "%Y-%m-%d").date()
            return KycVerification.ExpireDateType.EXPIRABLE, expire_date
        except Exception:
            raise InvalidArgument

    @classmethod
    def check_id_number_exist(cls, id_number: str, country: str, id_type: KycVerification.IDType, user_id: int):
        dup_record = KycVerification.query.filter(
            KycVerification.status.in_([
                KycVerification.Status.CREATED,
                KycVerification.Status.PASSED,
                KycVerification.Status.AUDIT_REQUIRED,
                KycVerification.Status.SCREENING
            ]),
            KycVerification.id_number == id_number,
            KycVerification.country == country,
            KycVerification.id_type == id_type,
        ).first()
        if dup_record and dup_record.user_id != user_id:
            raise DuplicateKYCVerification(message=gettext('证件号码已被使用'))

    @classmethod
    def check_user_status(cls, user: User):
        risk_screen_status = RiskScreenBusiness.get_user_risk_screen_status(user.id)
        if risk_screen_status == UserRiskScreen.Status.RISKED:
            raise UserRiskScreenFailed

        kyc_pro = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == user.id,
            KycVerificationPro.status.in_((
                KycVerificationPro.Status.CREATED,
                KycVerificationPro.Status.AUDIT_REQUIRED,
                KycVerificationPro.Status.PASSED,
            ))
        ).first()
        if kyc_pro:
            raise InvalidArgument(message=gettext('高级认证已通过或待审核时不允许发起初级认证重新认证。'))

        user_statuses = User.KYCStatus
        if user.kyc_status not in (user_statuses.NONE, user_statuses.FAILED, user_statuses.PASSED):
            raise DuplicateKYCVerification

        if KycVerification.has_user_reached_daily_limit(user.id):
            raise KycExceedsMaxAttempts

        institution = KYCInstitution.query.filter(
            KYCInstitution.user_id == user.id,
            KYCInstitution.status == KYCInstitution.Status.PASSED,
        ).first()
        if institution:
            raise InvalidArgument(message=gettext('你已完成机构认证，无法重新提交。'))

    @classmethod
    def get_file(cls, file_id, user_id):
        if not file_id:
            return None
        file = File.query.filter(
            File.id == file_id,
            File.user_id == user_id,
        ).first()
        if file is None:
            raise InvalidArgument
        return file.id

    @classmethod
    def get_submit_type(cls, user: User):
        submit_on_pass_record = None
        if user.kyc_status != User.KYCStatus.PASSED:
            submit_on_pass_record = KycVerification.query.filter(
                KycVerification.user_id == user.id,
                KycVerification.submit_type == KycVerification.SubmitType.SUBMIT_ON_PASS
            ).first()
        if user.kyc_status == User.KYCStatus.PASSED or submit_on_pass_record:
            submit_type = KycVerification.SubmitType.SUBMIT_ON_PASS
        else:
            submit_type = KycVerification.SubmitType.REGULAR
        return submit_type


@ns.route('/accepted_id_types')
@respond_with_code
class AcceptedIDTypesResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        kyc_type=EnumField([User.KYCType.INDIVIDUAL.name, User.KYCType.INSTITUTION.name],
                           missing=User.KYCType.INDIVIDUAL.name)
    ))
    def get(cls, **kwargs):
        """
        KYC: 查询各国家支持的证件类型
        目前只有 APP 端在使用。WEB 端 KYC 个人认证与 KYC 机构认证已经切换到新的接口：SupportedIDTypesResource
        （TODO: 后续 APP 切换新接口后，考虑删除）
        """
        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        lang = get_request_language()
        user = get_request_user()

        support_non_doc = get_support_non_doc()
        result = KycBusiness.get_all_country_id_types(kyc_platform,
                                                      kwargs['kyc_type'], lang,
                                                      user=user, support_non_doc=support_non_doc)
        if user and user.location_code != 'IRN':
            return [i for i in result if i['countryCode'] != 'IRN']
        return result


@ns.route('/supported')
@respond_with_code
class SupportedIDTypesResource(Resource):

    @classmethod
    def get(cls):
        """
        KYC: 查询各国家支持的证件类型（新版）
        """
        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        lang = get_request_language()
        user = get_request_user()
        result = KycBusiness.get_supported(
            kyc_platform, lang, user=user
        )
        if user and user.location_code != 'IRN':
            for item in result:
                if item['country_code'] == 'IRN':
                    item['kyc']['country_supported'] = False
                    item['kyc']['nationality_supported'] = False
                    break
        return result


def get_support_non_doc():
    # 安卓暂未支持，版本号待定
    return False if is_old_app_request(3390, 68) else True


@ns.route('/netverify')
@respond_with_code
class VerificationResource(KycMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        frontside_image_file=fields.Integer(required=True),
        backside_image_file=fields.Integer(missing=0),
        face_image_file=fields.Integer(required=True),
        country=fields.String(required=True),
        nationality=fields.String,
        id_number=fields.String(required=True),
        id_type=EnumField(KycVerification.IDType, required=True),
        first_name=fields.String,
        last_name=fields.String,
        name=fields.String,
        gender=EnumField(KycVerification.Gender, required=False),
        date_of_birth=DateField(to_date=True, required=False),
        expire_date=fields.String(required=False),
    ))
    def post(cls, **kwargs):
        """ KYC: 提交认证 (人工审核) """
        gender = gender.name if (gender := kwargs.get("gender")) else None
        date_of_birth = kwargs.get("date_of_birth")
        doc_expire_date_type, doc_expire_date = cls.check_and_parse_expire_date(kwargs.get("expire_date"))
        id_type = kwargs['id_type']
        user: User = g.user
        user_id = user.id
        cls.check_user_status(user)

        if not get_country(country := kwargs['country'].strip()):
            raise InvalidArgument
        if nationality := kwargs.get('nationality'):
            if not get_country(nationality):
                raise InvalidArgument
            if nationality != country:
                raise InvalidArgument(message=gettext('国籍和居住国家不一致，请前往WEB端认证。'))
        else:
            nationality = country
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument
        if not KycBusiness.validate_id_number(id_number, country, id_type):
            raise InvalidArgument(message=gettext('不正确的证件号码'))
        if not (name := kwargs.get('name', '').strip()):
            if not (first_name := kwargs['first_name'].strip()):
                raise InvalidArgument
            if not (last_name := kwargs['last_name'].strip()):
                raise InvalidArgument
            name = f'{first_name} {last_name}'
        big_user_customer = BigUserCustomer.is_big_customer(user_id)
        if not big_user_customer and not CountrySettings(nationality).allow_kyc_nationality:
            raise InvalidArgument
        if not big_user_customer and not CountrySettings(country).allow_kyc_country:
            raise InvalidArgument

        cls.check_id_number_exist(id_number, country, id_type, user_id)
        front_img_id = cls.get_file(kwargs['frontside_image_file'], user_id)
        back_img_id = cls.get_file(kwargs['backside_image_file'], user_id)
        face_img_id = cls.get_file(kwargs['face_image_file'], user_id)
        KycVerification.query.filter(
            KycVerification.user_id == user_id).update(
                dict(status=KycVerification.Status.CANCELLED))
        submit_type = cls.get_submit_type(user)
        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user.id,
            KycVerificationHistory.status == KycVerificationHistory.Status.CREATED
        ).update(dict(status=KycVerificationHistory.Status.CANCELLED))
        db.session.add(KycVerification(
            user_id=user_id,
            country=country,
            nationality=nationality or '',
            id_type=id_type,
            id_number=id_number,
            name=name,
            front_img_file_id=front_img_id,
            back_img_file_id=back_img_id,
            face_img_file_id=face_img_id,
            status=KycVerification.Status.AUDIT_REQUIRED,
            gender=gender,
            date_of_birth=date_of_birth,
            doc_expire_date_type=doc_expire_date_type,
            doc_expire_date=doc_expire_date,
            service_type=KycVerification.ServiceType.MANUAL_AUDIT,
            doc_type=KycVerification.DocType.DOC,
            submit_type=submit_type,
            platform=kyc_platform,
            flow=KYCFlow.STEP_BY_STEP,
        ))
        user.kyc_status = User.KYCStatus.PROCESSING
        db.session.commit()
        return {}


@ns.route('/image/upload')
@respond_with_code
class KYCImageUploadResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(20, 600)
    def post(cls):
        """
        KYC: 图片上传
        """
        user: User = g.user
        institution_type = request.form.get('institution_type')
        if institution_type:
            try:
                institution_type = User.KYCType(institution_type)
            except Exception:
                raise InvalidArgument

        if not (img := request.files.get('image')):
            raise InvalidArgument

        if institution_type and institution_type is User.KYCType.INSTITUTION:
            if KYCInstitutionBusiness(user).exceed_daily_limit():
                raise KycExceedsMaxAttempts

            min_size, max_size = 1024 * 5, 1024 * 1024 * 10
            allowed_ext = {'.png', '.jpeg', '.jpg', '.pdf', '.doc', '.docx'}

        else:
            if KycVerification.has_user_reached_daily_limit(user.id):
                raise KycExceedsMaxAttempts
            
            min_size, max_size = 1024 * 5, 1024 * 1024 * 5
            allowed_ext = {'.png', '.jpeg', '.jpg', '.pdf'}

        _, ext = os.path.splitext(img.filename)
        ext = ext.lower() if ext else None
        if ext not in allowed_ext:
            raise ImageFormatError
        if (not institution_type \
            or institution_type is User.KYCType.INDIVIDUAL) \
                and ext in ('.png', '.jpeg', '.jpg'):
            try:
                with Image.open(img) as img_obj:
                    _, height = img_obj.size
                    if height < 480:
                        raise FileTooSmall
                    img.seek(0)
            except Exception as e:
                if isinstance(e, FileTooSmall):
                    raise FileTooSmall
                raise InvalidArgument

        if (size := int(request.headers['CONTENT_LENGTH'])) > max_size:
            raise FileTooBig

        if size < min_size:
            raise FileTooSmall

        img_type = ext.split('.')[1]
        if img_type == 'pdf':
            mime_type = File.MimeType.FILE
        else:
            mime_type = File.MimeType.IMG_PNG if img_type == 'png' \
                else File.MimeType.IMG_JPG

        file_key = AWSBucketPrivate.new_file_key(suffix=img_type)
        if not AWSBucketPrivate.put_file(file_key, img):
            raise ServiceUnavailable
        url = AWSBucketPrivate.get_file_url(file_key)

        file = db.session_add_and_commit(File(
            user_id=user.id,
            bucket=AWSBucketPrivate.bucket_name,
            key=file_key,
            name=img.filename,
            size=size,
            provider=File.Provider.AWS,
            mime_type=mime_type
        ))
        return dict(
            file_url=url,
            file_id=file.id
        )


@ns.route('/basic-info')
@respond_with_code
class KycRequestIdResource(KycMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        country=fields.String(required=True),
        id_type=EnumField(KycVerification.IDType,
                          required=True),
        id_number=fields.String(required=True),
        first_name=fields.String,
        last_name=fields.String,
        name=fields.String,
        gender=EnumField(KycVerification.Gender, required=False),
        date_of_birth=DateField(to_date=True, required=False),
        expire_date=fields.String(required=False)
    ))
    def post(cls, **kwargs):
        """
        KYC: 校验基本信息
        """
        cls.check_and_parse_expire_date(kwargs.get("expire_date"))

        if not KycBusiness.validate_id_number(kwargs['id_number'],
                                              kwargs['country'], kwargs['id_type']):
            raise InvalidArgument(message=gettext('不正确的证件号码'))
        if not kwargs.get('name'):
            if not kwargs['first_name']:
                raise InvalidArgument
            if not kwargs['last_name']:
                raise InvalidArgument
        if not get_country(kwargs['country']):
            raise InvalidArgument

        country = kwargs['country']
        country_settings = CountrySettings(country)
        if not country_settings.allow_kyc_nationality:
            raise InvalidArgument
        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        support_non_doc = get_support_non_doc()
        big_user_customer = BigUserCustomer.is_big_customer(g.user.id)
        if not big_user_customer and not KycBusiness.is_country_accepted(country,
                                               kwargs['id_type'].name, 
                                               kyc_platform,
                                               user=g.user,
                                               support_non_doc=support_non_doc):
            return dict(
                is_accepted=False,
                request_id=''
            )

        user: User = g.user
        cls.check_user_status(user)
        cls.check_id_number_exist(kwargs['id_number'], kwargs['country'], kwargs['id_type'], user.id)


@ns.route('/status')
@respond_with_code
class KycStatusResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        user = g.user
        kyc_type = user.kyc_type
        risk_screen_status = RiskScreenBusiness.get_user_risk_screen_status(user.id)
        if kyc_type == user.KYCType.INSTITUTION:
            kyc_status = KYCInstitutionBusiness(user).get_kyc_status()
        else:
            with CacheLock(LockKeys.kyc_operation(user.id), wait=False):
                db.session.rollback()
                lang = get_request_language()
                kyc_status = KycBusiness.get_user_kyc_info(user, lang)

        forbidden_countries = CountrySetting.query.filter(
            CountrySetting.key == CountrySettings.allow_kyc_nationality.name,
            CountrySetting.value == '0'
        ).with_entities(CountrySetting.code).all()
        forbidden_str_list = []
        for item in forbidden_countries:
            code = item.code
            forbidden_str_list.append(gettext(get_country(code).cn_name))
        if not forbidden_str_list:
            kyc_message = ''
        else:
            forbbiden_str = ', '.join(forbidden_str_list)
            kyc_message = gettext('目前CoinEx 实名认证不支持%(countries)s的用户；', countries=forbbiden_str)
        kyc_status.update(kyc_type=kyc_type,
                          kyc_message=kyc_message,
                          risk_screen_status=risk_screen_status.name if risk_screen_status else None)
        return kyc_status


@ns.route('/opportunity')
@respond_with_code
class KycOpportunityResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        has_reached_limit = KycVerification.has_user_reached_daily_limit(g.user.id)
        kyc = KycVerification.query.filter(
            KycVerification.user_id == g.user.id,
            KycVerification.status.in_((
                KycVerification.Status.CREATED,
                KycVerification.Status.AUDIT_REQUIRED,
            ))).first()
        kyc_pro = KycVerificationPro.query.filter(
            KycVerificationPro.user_id == g.user.id,
            KycVerificationPro.status.in_((
                KycVerificationPro.Status.CREATED,
                KycVerificationPro.Status.PASSED,
                KycVerificationPro.Status.AUDIT_REQUIRED,
            ))
        ).first()
        return dict(
            has_kyc_opportunity=not kyc and not has_reached_limit and not kyc_pro and
                                g.user.kyc_status in (User.KYCStatus.NONE, User.KYCStatus.FAILED, User.KYCStatus.PASSED)
        )


@ns.route('/verification')
@respond_with_code
class VerificationSubmitResource(KycMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        frontside_image_file=fields.Integer(required=True),
        backside_image_file=fields.Integer(missing=0),
        face_image_file=fields.Integer(required=True),
        country=fields.String(required=True),
        nationality=fields.String,
        id_type=EnumField(KycVerification.IDType, required=True),
        id_number=fields.String(required=True),
        first_name=fields.String,
        last_name=fields.String,
        name=fields.String,
        gender=EnumField(KycVerification.Gender, required=False),
        date_of_birth=DateField(to_date=True, required=False),
        expire_date=fields.String(required=False)
    ))
    def post(cls, **kwargs):
        """
        KYC: 提交认证至第三方
        """
        gender = gender.name if (gender := kwargs.get("gender")) else None
        date_of_birth = kwargs.get("date_of_birth")
        doc_expire_date_type, doc_expire_date = cls.check_and_parse_expire_date(kwargs.get("expire_date"))
        id_type = kwargs['id_type']

        user: User = g.user
        user_id = user.id
        cls.check_user_status(user)

        if not get_country(country := kwargs['country'].strip()):
            raise InvalidArgument
        if nationality := kwargs.get('nationality'):
            if not get_country(nationality):
                raise InvalidArgument
            if nationality != country:
                raise InvalidArgument(message=gettext('国籍和居住国家不一致，请前往WEB端认证。'))
        else:
            nationality = country
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument
        if not (name := kwargs.get('name', '').strip()):
            if not (first_name := kwargs['first_name'].strip()):
                raise InvalidArgument
            if not (last_name := kwargs['last_name'].strip()):
                raise InvalidArgument
            name = f'{first_name} {last_name}'
        cls.check_id_number_exist(id_number, country, id_type, user_id)

        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        support_non_doc = get_support_non_doc()

        is_big_customer = BigUserCustomer.is_big_customer(user_id)
        if not is_big_customer and not KycBusiness.is_country_accepted(country,
                                               kwargs['id_type'].name, 
                                               kyc_platform,
                                               user=user,
                                               support_non_doc=support_non_doc):
            raise InvalidArgument
        front_img_id = cls.get_file(kwargs['frontside_image_file'], user_id)
        back_img_id = cls.get_file(kwargs['backside_image_file'], user_id)
        face_img_id = cls.get_file(kwargs['face_image_file'], user_id)

        request_id = KycBusiness.get_request_id(user_id, country)

        ip, country_code = get_request_ip(), ''
        if c:= get_country(GeoIP(ip).country_code):
            country_code = c.iso_3
        KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user.id,
            KycVerificationHistory.status == KycVerificationHistory.Status.CREATED
        ).update(dict(status=KycVerificationHistory.Status.CANCELLED))
        history = KycVerificationHistory(
            user_id=user_id,
            request_id=request_id,
            doc_type=KycVerification.DocType.DOC,
            detail=json.dumps(dict(
                country=country,
                nationality=nationality or '',
                id_type=kwargs['id_type'].name,
                id_number=id_number,
                name=name,
                front=front_img_id,
                back=back_img_id,
                face=face_img_id,
                platform=kyc_platform,
                gender=gender,
                date_of_birth=date_of_birth.strftime("%Y-%m-%d") if date_of_birth else None,
                doc_expire_date_type=doc_expire_date_type.name,
                doc_expire_date = doc_expire_date.strftime("%Y-%m-%d") if doc_expire_date else None,
                request_ip=ip,
                request_country_code=country_code,
            ))
        )
        user.kyc_status = User.KYCStatus.PROCESSING
        db.session.add(history)
        db.session.commit()

        KycBusiness.upload(history.id)


@ns.route('/verification/mixin')
@respond_with_code
class VerificationMixinSubmitResource(KycMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        frontside_image_file=fields.Integer(required=True),
        backside_image_file=fields.Integer(missing=0),
        face_image_file=fields.Integer(required=True),
        country=fields.String(required=True),
        nationality=fields.String(required=True),
        id_type=EnumField(KycVerification.IDType, required=True),
        id_number=fields.String(required=True),
        name=fields.String(required=True),
        gender=EnumField(KycVerification.Gender, required=True),
        date_of_birth=DateField(to_date=True, required=True),
        address=fields.String(required=True),
        address_file_ids=fields.List(fields.Integer(required=True), required=True),
        address_file_category=EnumField(KycVerificationPro.FileCategory, required=True),
        address_file_type=EnumField(KycVerificationPro.FileType, required=True),
    ))
    def post(cls, **kwargs):
        """
        KYC: 初高级混合提交认证
        """
        user: User = g.user
        user_id = user.id
        gender = gender.name if (gender := kwargs.get("gender")) else None
        date_of_birth = kwargs.get("date_of_birth")
        doc_expire_date_type, doc_expire_date = cls.check_and_parse_expire_date(kwargs.get("expire_date"))
        id_type = kwargs['id_type']
        is_big_customer = BigUserCustomer.is_big_customer(user_id)
        if not get_country(country := kwargs['country'].strip()):
            raise InvalidArgument
        if not is_big_customer and not CountrySettings(country).allow_kyc_country:
            raise InvalidArgument
        if not get_country(nationality := kwargs['nationality'].strip()):
            raise InvalidArgument
        if not is_big_customer and not CountrySettings(nationality).allow_kyc_nationality:
            raise InvalidArgument
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument
        if not (name := kwargs.get('name', '').strip()):
            raise InvalidArgument
        front_img_id = cls.get_file(kwargs['frontside_image_file'], user_id)
        back_img_id = cls.get_file(kwargs['backside_image_file'], user_id)
        face_img_id = cls.get_file(kwargs['face_image_file'], user_id)
        cls.check_user_status(user)
        cls.check_id_number_exist(id_number, country, id_type, user_id)

        user.kyc_status = User.KYCStatus.PROCESSING
        KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user.id,
            KycVerificationHistory.status == KycVerificationHistory.Status.CREATED
        ).update(dict(status=KycVerificationHistory.Status.CANCELLED))
        kyc_platform = KycVerification.PlatForm.WEB if get_request_platform().is_web() else KycVerification.PlatForm.APP
        history = None
        service_type = KycBusiness.get_service_type(nationality, id_type.name, kyc_platform, user)
        if service_type == KycVerification.ServiceType.MANUAL_AUDIT:
            KycVerification.query.filter(
                KycVerification.user_id == user_id
            ).update(
                dict(status=KycVerification.Status.CANCELLED)
            )
            submit_type = cls.get_submit_type(user)
            kyc = KycVerification(
                user_id=user_id,
                country=country,
                nationality=nationality,
                id_type=id_type,
                id_number=id_number,
                name=name,
                front_img_file_id=front_img_id,
                back_img_file_id=back_img_id,
                face_img_file_id=face_img_id,
                status=KycVerification.Status.AUDIT_REQUIRED,
                gender=gender,
                date_of_birth=date_of_birth,
                doc_expire_date_type=doc_expire_date_type,
                doc_expire_date=doc_expire_date,
                service_type=KycVerification.ServiceType.MANUAL_AUDIT,
                doc_type=KycVerification.DocType.DOC,
                submit_type=submit_type,
                platform=kyc_platform,
                flow=KYCFlow.IDENTITY_AND_ADDRESS
            )
            db.session.add(kyc)
        else:
            ip, country_code = get_request_ip(), ''
            if c := get_country(GeoIP(ip).country_code):
                country_code = c.iso_3
            history = KycVerificationHistory(
                user_id=user_id,
                request_id=KycBusiness.get_request_id(user_id, country),
                doc_type=KycVerification.DocType.DOC,
                detail=json.dumps(dict(
                    country=country,
                    nationality=nationality or '',
                    id_type=kwargs['id_type'].name,
                    id_number=id_number,
                    name=name,
                    front=front_img_id,
                    back=back_img_id,
                    face=face_img_id,
                    platform=kyc_platform,
                    gender=gender,
                    date_of_birth=date_of_birth.strftime("%Y-%m-%d") if date_of_birth else None,
                    doc_expire_date_type=doc_expire_date_type.name,
                    doc_expire_date = doc_expire_date.strftime("%Y-%m-%d") if doc_expire_date else None,
                    request_ip=ip,
                    request_country_code=country_code,
                    flow=KYCFlow.IDENTITY_AND_ADDRESS.name,
                ))
            )
            db.session.add(history)

        kyc_config = KycCountryConfig.query.filter(
            KycCountryConfig.country == country,
        ).first()
        if kyc_config.pro_supported:
            pro_platform = KycVerificationPro.Platform.WEB if get_request_platform().is_web() else KycVerificationPro.Platform.APP
            draft = KycVerificationProDraft.get_or_create(user_id=user_id)
            draft.detail = {
                'country': country,
                'pro_name': name,
                'platform': pro_platform.name,
                'file_type': kwargs['address_file_type'].name,
                'address': kwargs['address'],
                'address_file_ids': ','.join(map(str, kwargs['address_file_ids'])),
                'flow': KYCFlow.IDENTITY_AND_ADDRESS.name
            }
            db.session.add(draft)
        db.session.commit()
        if history:
            KycBusiness.upload(history.id)


@ns.route('/verification/non-doc')
@respond_with_code
class NonDocVerificationSubmitResource(KycMixin, Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        country=fields.String(required=True),
        nationality=fields.String,
        id_type=EnumField(KycVerification.IDType, required=True),
        id_number=fields.String(required=True),
        name=fields.String,
        gender=EnumField(KycVerification.Gender, required=False),
        date_of_birth=DateField(to_date=True, required=False),
    ))
    def post(cls, **kwargs):
        """
        KYC: 提交非文件认证至第三方
        """
        user: User = g.user
        cls.check_user_status(user)

        user_id = user.id
        if not get_country(country := kwargs['country'].strip()):
            raise InvalidArgument
        if nationality := kwargs.get('nationality'):
            if not get_country(nationality):
                raise InvalidArgument
            if nationality != country:
                raise InvalidArgument(message=gettext('国籍和居住国家不一致，请前往WEB端认证。'))
        else:
            nationality = country
        platform = get_request_platform()
        if platform.is_web():
            kyc_platform = KycVerification.PlatForm.WEB
        else:
            kyc_platform = KycVerification.PlatForm.APP
        id_type = kwargs['id_type']
        support_non_doc = get_support_non_doc()

        is_big_customer = BigUserCustomer.is_big_customer(user_id)

        if not is_big_customer and not KycBusiness.is_country_accepted(
                country, id_type.name, kyc_platform, user=user, support_non_doc=support_non_doc):
            raise KycSupportIdDocsChanged(id_type=gettext(id_type.value))
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument
        cls.check_id_number_exist(id_number, country, id_type, user_id)
        if not (name := kwargs.get('name', '').strip()):
            raise InvalidArgument
        gender = gender.name if (gender := kwargs.get("gender")) else None
        date_of_birth = kwargs.get("date_of_birth").strftime("%Y-%m-%d") if kwargs.get("date_of_birth") else None

        service_type = KycVerification.ServiceType.SUMSUB
        request_id = KycBusiness.get_non_doc_request_id(user_id, service_type)
        ip, country_code = get_request_ip(), ''
        if c := get_country(GeoIP(ip).country_code):
            country_code = c.iso_3
        new_detail = json.dumps(dict(
            country=country,
            nationality=nationality or '',
            id_type=id_type.name,
            id_number=id_number,
            name=name,
            platform=kyc_platform,
            gender=gender,
            date_of_birth=date_of_birth,
            request_ip=ip,
            request_country_code=country_code,
        ))
        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user_id,
            KycVerificationHistory.request_id == request_id,
            KycVerificationHistory.service_type == service_type,
            KycVerificationHistory.doc_type == KycVerification.DocType.NON_DOC,
            KycVerificationHistory.status == KycVerificationHistory.Status.CREATED
        ).first()
        if history:
            detail = json.loads(history.detail)
            if country != detail['country'] or id_type.name != detail['id_type'] or id_number != detail['id_number']:
                SumsubClient.upload_non_doc(user_id, request_id, country, id_type, id_number, date_of_birth)
            history.detail = new_detail
        else:
            applicant_id = SumsubClient.upload_non_doc(user_id, request_id, country, id_type, id_number, date_of_birth)
            history = KycVerificationHistory(
                user_id=user_id,
                request_id=request_id,
                transaction_id=applicant_id,
                service_type=service_type,
                doc_type=KycVerification.DocType.NON_DOC,
                detail=new_detail
            )
            db.session.add(history)

        access_token = SumsubClient.get_non_doc_access_token(request_id)
        db.session.commit()

        return dict(
            access_token=access_token
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    def patch(cls):
        """
        KYC: 非文件验证人脸识别后调用
        """
        user: User = g.user
        cls.check_user_status(user)

        user_id = user.id
        service_type = KycVerification.ServiceType.SUMSUB
        history = KycVerificationHistory.query.filter(
            KycVerificationHistory.user_id == user_id,
            KycVerificationHistory.service_type == service_type,
            KycVerificationHistory.doc_type == KycVerification.DocType.NON_DOC,
            KycVerificationHistory.status == KycVerificationHistory.Status.CREATED,
        ).first()
        if not history:
            raise InvalidArgument
        user.kyc_status = User.KYCStatus.PROCESSING
        db.session.commit()
        try:
            SumsubClient.request_check(history.transaction_id)
        except BaseHTTPClient.BadResponse as e:
            current_app.logger.warning(
                f"non-doc request check failed, code:{e.code}, data:{e.data}"
            )


@ns.route('/verification/extend/non-doc')
@respond_with_code
class NonDocVerificationExtendSubmitResource(Resource):

    @classmethod
    def check_id_number_exist(cls, id_number: str, nationality: str, id_type: KycVerification.IDType, user_id: int):
        dup_record = KycVerification.query.filter(
            KycVerification.status.in_([
                KycVerification.Status.CREATED,
                KycVerification.Status.PASSED,
                KycVerification.Status.AUDIT_REQUIRED,
                KycVerification.Status.SCREENING
            ]),
            KycVerification.id_number == id_number,
            KycVerification.nationality == nationality,
            KycVerification.id_type == id_type,
        ).first()
        if dup_record and dup_record.user_id != user_id:
            raise DuplicateKYCVerification(message=gettext('证件号码已被使用'))

        dup_record = KycVerificationExtend.query.filter(
            KycVerificationExtend.status.in_([
                KycVerificationExtend.Status.CREATED,
                KycVerificationExtend.Status.PASSED,
            ]),
            KycVerificationExtend.id_number == id_number,
            KycVerificationExtend.nationality == nationality,
            KycVerificationExtend.id_type == id_type,
        ).first()
        if dup_record and dup_record.user_id != user_id:
            raise DuplicateKYCVerification(message=gettext('证件号码已被使用'))

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        nationality=fields.String(required=True),
        id_type=EnumField(KycVerification.IDType, required=True),
        id_number=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """
        KYC: 扩展认证提交非文件认证至第三方
        """
        user: User = g.user
        user_id = user.id
        risk_screen_status = RiskScreenBusiness.get_user_risk_screen_status(user_id)
        if risk_screen_status == UserRiskScreen.Status.RISKED:
            raise UserRiskScreenFailed
        if user.kyc_status != User.KYCStatus.PASSED:
            raise NoKycQualifications
        kyc = user.kyc_verification
        if not kyc:
            raise NoKycQualifications

        if not get_country(nationality := kwargs['nationality']):
            raise InvalidArgument
        id_type = kwargs['id_type']
        supported_id_types = KycExtendBusiness.get_supported()
        if nationality not in supported_id_types or id_type.name not in supported_id_types[nationality]:
            raise InvalidArgument
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument
        cls.check_id_number_exist(id_number, nationality, id_type, user_id)

        data = dict(
            nationality=nationality,
            id_type=id_type.name,
            id_number=id_number,
            full_name=kyc.full_name,
        )
        access_token = KycExtendBusiness.create_extend_history(user, data)
        return dict(
            access_token=access_token
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(dict(
        nationality=fields.String(required=True),
        id_type=EnumField(KycVerification.IDType, required=True),
        id_number=fields.String(required=True),
    ))
    def patch(cls, **kwargs):
        """
        KYC: 扩展认证非文件验证人脸识别后调用
        """
        user: User = g.user
        user_id = user.id
        if not get_country(nationality := kwargs['nationality']):
            raise InvalidArgument
        id_type = kwargs['id_type']
        supported_id_types = KycExtendBusiness.get_supported()
        if nationality not in supported_id_types or id_type.name not in supported_id_types[nationality]:
            raise InvalidArgument
        if not (id_number := strip_non_alpha_num(kwargs['id_number'])):
            raise InvalidArgument

        history = KycVerificationExtendHistory.query.filter(
            KycVerificationExtendHistory.user_id == user_id,
            KycVerificationExtendHistory.status == KycVerificationExtendHistory.Status.CREATED,
            db.text(" AND ".join([
                "JSON_EXTRACT(detail, '$.id_number') = :id_number",
                "JSON_EXTRACT(detail, '$.nationality') = :nationality",
                "JSON_EXTRACT(detail, '$.id_type') = :id_type",
            ]))
        ).params({
            'id_number': id_number,
            'nationality': nationality,
            'id_type': id_type.name
        }).first()
        if not history:
            raise InvalidArgument
        KycVerificationExtend.add_or_update(
            history,
            KycVerificationExtend.Status.CREATED,
            commit=True
        )
        try:
            SumsubClient.request_check(history.transaction_id)
        except BaseHTTPClient.BadResponse as e:
            current_app.logger.warning(
                f"non-doc request check failed, code:{e.code}, data:{e.data}"
            )


@ns.route('/sumsub/token')
@respond_with_code
class SumsubAccessTokenResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        type=EnumField(["NON_DOC", "EXTEND_NON_DOC"], missing="NON_DOC"),
        nationality=fields.String,
        id_type=EnumField(KycVerification.IDType),
    ))
    def get(cls, **kwargs):
        """
        KYC: 获取sumsub access token
        """
        user_id = g.user.id
        type_ = kwargs['type']
        if type_ == "NON_DOC":
            request_id = KycBusiness.get_non_doc_request_id(
                user_id, KycVerification.ServiceType.SUMSUB)
        elif type_ == "EXTEND_NON_DOC":
            if not get_country(nationality := kwargs.get('nationality')):
                raise InvalidArgument
            if not (id_type := kwargs.get('id_type')):
                raise InvalidArgument
            request_id = KycExtendBusiness.get_non_doc_request_id(
                user_id, KycVerification.ServiceType.SUMSUB, nationality, id_type.name)
        else:
            raise InvalidArgument
        return dict(
            access_token=SumsubClient.get_non_doc_access_token(request_id)
        )


def max_length(size=64):
    def validate(s: Union[str, list]):
        return len(s) <= size

    return validate


@ns.route('/institution')
@respond_with_code
class KYCInstitutionResource(Resource):

    class CompanySchema(Schema):
        name = fields.String(required=True, validate=max_length(500))
        register_code = fields.String(required=True, validate=max_length(500))
        location_code = fields.String(required=True, validate=lambda s: s in list_country_codes_3())
        nature_of_business = fields.String(required=True, validate=max_length(500))
        funding_source = fields.String(required=True, validate=max_length(500))
        contact = fields.String(required=True, validate=max_length(500))
        register_address = fields.String(required=True, validate=max_length(500))
        government_url = fields.String(required=True, validate=max_length(500))
        apply_reason = fields.String(required=False, validate=max_length(500))

        certificate_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))
        structure_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))
        constitution_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))
        roster_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))
        authorization_letter_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))
        statement_file_ids = fields.List(fields.Integer(required=True), required=True, validate=max_length(5))

        coi_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length(5))
        other_file_ids = fields.List(fields.Integer(required=True), required=False, validate=max_length(5))

        class Meta:
            UNKNOWN = EXCLUDE

    class DirectorSchema(Schema):
        first_name = fields.String(required=True, validate=max_length(64))
        last_name = fields.String(required=True, validate=max_length(64))
        location_code = fields.String(required=True, validate=lambda s: s in list_country_codes_3())
        id_type = EnumField(InstitutionDirector.IDType, required=True)
        id_number = fields.String(required=True, validate=max_length(500))
        identity_front_file_id = fields.Integer(required=True)
        identity_back_file_id = fields.Integer(required=False)
        face_img_file_id = fields.Integer(required=True)
        is_beneficiary = fields.Boolean(required=True)
        shareholding_ratio = fields.Decimal(required=False)

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @require_login(allow_sub_account=False)
    @lock_request(with_user=True)
    @ns.use_kwargs(
        dict(
            company_info=fields.Nested(CompanySchema, required=True),
            manager_info=fields.Nested(DirectorSchema, required=True, many=True)
        )
    )
    def post(cls, **kwargs):
        """用户提交 KYC 机构认证"""
        company_info = kwargs.get('company_info')
        manager_info = kwargs.get('manager_info')

        risk_screen_status = RiskScreenBusiness.get_user_risk_screen_status(g.user.id)
        if risk_screen_status == UserRiskScreen.Status.RISKED:
            raise UserRiskScreenFailed

        cls.validate(company_info, manager_info)
        KYCInstitutionBusiness(g.user).create_institution(company_info, manager_info)

    @classmethod
    def validate(cls, company_info: Dict, manager_info: List[Dict]):
        if not cls.company_loc_allowed(company_info.get('location_code')):
            raise InvalidArgument

        if not cls.manager_info_satisfied(manager_info):
            raise InvalidArgument

        file_ids = cls.get_file_ids_or_raise(company_info, manager_info)
        if not cls.files_belongs_to_user(file_ids):
            raise InvalidArgument

        if not cls.user_kyc_status_satisfied():
            raise DuplicateKYCVerification

        if KYCInstitutionBusiness(g.user).exceed_daily_limit():
            raise KycExceedsMaxAttempts

    @classmethod
    def company_loc_allowed(cls, code: str):
        return CountrySettings(code).allow_kyc_nationality

    @classmethod
    def manager_info_satisfied(cls, manager_info: List[Dict]):
        for mi in manager_info:
            if not CountrySettings(mi.get('location_code')).allow_kyc_nationality:
                return False
        return any(manager.get('is_beneficiary') for manager in manager_info)

    @classmethod
    def get_file_ids_or_raise(cls, company_info: Dict, manager_info: List[Dict]):
        file_ids = [
            *company_info['certificate_file_ids'],
            *company_info['structure_file_ids'],
            *company_info['constitution_file_ids'],
            *company_info['roster_file_ids'],
            *company_info['authorization_letter_file_ids'],
            *company_info['statement_file_ids'],
        ]
        if company_info.get('other_file_ids'):
            file_ids.extend(company_info.get('other_file_ids'))
        if company_info.get('coi_file_ids'):
            file_ids.extend(company_info.get('coi_file_ids'))

        for info in manager_info:
            file_ids.append(info['identity_front_file_id'])
            file_ids.append(info['face_img_file_id'])
            if info['id_type'] is InstitutionDirector.IDType.ID_CARD:
                identity_back_file_id = info['identity_back_file_id']
                if not identity_back_file_id:
                    raise InvalidArgument

                file_ids.append(info['identity_back_file_id'])
        return file_ids

    @classmethod
    def files_belongs_to_user(cls, file_ids):
        file_ids = set(file_ids)

        files = File.query.with_entities(
            File.id
        ).filter(
            File.user_id == g.user.id,
            File.id.in_(file_ids)
        ).all()
        return len(files) == len(file_ids)

    @classmethod
    def user_kyc_status_satisfied(cls):
        return g.user.kyc_status in (User.KYCStatus.NONE, User.KYCStatus.FAILED)


@ns.route('/liveness')
@respond_with_code
class LivenessCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """
        获取生物识别认证结果
        """
        token = kwargs['token']
        email_token: EmailToken = EmailToken.query.filter(
            EmailToken.token == token,
            EmailToken.email_type == EmailToken.EmailType.LIVENESS_CHECK
        ).first()
        if email_token is None:
            raise InvalidLink
        data = email_token.data_json
        history_id = data['history_id']
        history: LivenessCheckHistory = LivenessCheckHistory.query.get(history_id)
        if not history:
            raise InvalidLink
        if history.transaction_id and history.status in [
            LivenessCheckHistory.Status.CREATED, LivenessCheckHistory.Status.AUDIT_REQUIRED
        ]:
            with CacheLock(LockKeys.kyc_liveness_operation(history.user_id), wait=False):
                db.session.rollback()
                LivenessCheckBusiness.update_check_result(history)
        return dict(
            status=history.status.name,
            reason=history.get_reject_reason() if history.status == LivenessCheckHistory.Status.REJECTED else '',
        )

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        """
        用户发起生物识别认证
        """
        token = kwargs['token']
        email_token: EmailToken = EmailToken.query.filter(
            EmailToken.token == token,
            EmailToken.email_type == EmailToken.EmailType.LIVENESS_CHECK
        ).first()
        if email_token is None:
            raise InvalidLink
        data = email_token.data_json
        history_id = data['history_id']
        history: LivenessCheckHistory = LivenessCheckHistory.query.get(history_id)
        if not history:
            raise InvalidLink
        user: User = User.query.get(history.user_id)
        if user.kyc_status != User.KYCStatus.PASSED:
            raise InvalidKYC
        profile: LivenessCheckProfile = LivenessCheckProfile.query.filter(
            LivenessCheckProfile.user_id == user.id,
            LivenessCheckProfile.kyc_id == history.kyc_id
        ).first()
        if not profile:
            raise InvalidArgument
        if history.status == LivenessCheckHistory.Status.CANCELLED:
            raise InvalidLink
        elif history.status != LivenessCheckHistory.Status.CREATED:
            return dict(
                access_token='',
                status=history.status.name,
                reason=history.get_reject_reason() if history.status == LivenessCheckHistory.Status.REJECTED else '',
            )
        else:
            if email_token.expired_at < now():
                raise InvalidLink
            action_id = history.action_id
            if not history.transaction_id:
                applicant_action_id = SumsubClient.create_liveness_action(profile.transaction_id, action_id)
                history.transaction_id = applicant_action_id
                db.session.commit()
            level_name = config["SUMSUB_CONFIG"]["liveness_action_level_name"]
            access_token = SumsubClient.get_access_token(profile.request_id, level_name, action_id)

            return dict(
                access_token=access_token,
                status=history.status.name,
                reason='',
            )

    @classmethod
    @ns.use_kwargs(dict(
        token=fields.String(required=True)
    ))
    def patch(cls, **kwargs):
        """
        用户完成生物识别认证
        """
        token = kwargs['token']
        email_token: EmailToken = EmailToken.query.filter(
            EmailToken.token == token,
            EmailToken.email_type == EmailToken.EmailType.LIVENESS_CHECK
        ).first()
        if email_token is None:
            raise InvalidLink
        data = email_token.data_json
        history_id = data['history_id']
        history: LivenessCheckHistory = LivenessCheckHistory.query.get(history_id)
        if not history:
            raise InvalidLink
        if not history.transaction_id:
            raise InvalidArgument
        user = User.query.get(history.user_id)
        if user.kyc_status != User.KYCStatus.PASSED:
            raise InvalidKYC
        with CacheLock(LockKeys.kyc_liveness_operation(history.user_id), wait=False):
            db.session.rollback()
            history.status = LivenessCheckHistory.Status.AUDIT_REQUIRED
            db.session.commit()
            SumsubClient.request_action_check(history.transaction_id)
