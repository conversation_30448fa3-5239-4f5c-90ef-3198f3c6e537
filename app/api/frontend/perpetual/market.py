# -*- coding: utf-8 -*-
import datetime
import json
from decimal import Decimal
from typing import List, Dict, Union

from flask import g
from flask_restx import fields, marshal
from sqlalchemy import func
from webargs import fields as wa_fields
from flask_babel import gettext as _
from app import config
from app.business import (
    PerpetualServerClient, mem_cached,
    adjust_leverage, UserPreferences, AssetComparator,
)
from app.business.fee_constant import (DEFAULT_CONTRACT_TAKER_FEE,
                                       DEFAULT_CONTRACT_MAKER_FEE, ALL_MARKETS)
from app.business.fee import Fee<PERSON>etcher, UserFeeParser, UserClassIdPerpetualEnum
from app.business.kline import MarketKlineHelper
from app.business.utils import PerpetualMarketComparator
from app.caches import PerpetualMarketCache, PerpetualCoinTypeCache, \
    PerpetualOfflineMarketCache, PerpetualMonthlyMarketChangeRateCache, \
    TodayPerpetualLiquidationCache
from app.common import (
    TradeBusinessType, TradeType, OrderSideType, TradeIntType, MessageTitle, MessageContent, MessageWebLink,
    position_deal_type_map, PerpetualMarketType, PERPETUAL_ALL_MARKET_TYPE)
from app.exceptions import InvalidArgument
from app.models import MarketSummaryHistory, Language, \
    DailyPerpetualInsuranceReport, DailyPerpetualMarginBurstReport, \
    DailyMarketSummaryHistory, PerpetualTradeSummary, \
    DailyPerpetualTradeSummary, BigCustomerMarketSummaryHistory, \
    DailyBigCustomerMarketSummaryHistory, db, Message, BasisRateHistory, FundingRateHistory, \
    HourlyPerpetualMarginBurstReport, NormalCustomerMarketSummaryHistory
from app.models.spot import MarketOfflineContent
from app.business.email import send_perpetual_trading_introduction_email
from app.business.copy_trading.trader import CopyTraderPerpetualOp
from app.utils import offset_to_page, query_to_page, amount_to_str, export_xlsx, \
    today_timestamp_utc, today, group_by
from app.utils.date_ import timestamp_to_datetime, date_to_datetime, current_timestamp
from app.utils.export import export_csv
from ...common import (
    Resource, Namespace, respond_with_code, ex_fields, require_login, success, DecimalType,
    copy_trading_sub_user_setter,
)
from ...common.fields import TimestampField, PerpetualMarketField, EnumField
from ...common.request import is_old_app_request

ns = Namespace('Perpetual')


HOUR_INTERVAL = ('6h', '12h', '24h')
DAY_INTERVAL = ('7d', '30d', '90d')
INTERVAL_LIMIT_DIC = {'6h': 6, '12h': 12, '24h': 24,
                      '7d': 7, '30d': 30, '90d': 90}


@ns.route('/list')
@respond_with_code
class MarketList(Resource):

    @staticmethod
    def get():
        """
        合约市场信息
        """
        result = PerpetualMarketCache().read_aside()
        result = list(result.values())
        offline = list(PerpetualOfflineMarketCache().read_aside().values())

        include_offline_visible = False if is_old_app_request(3420, 82) else True
        if include_offline_visible:
            default_funding = {'interval': 28800, 'max': '0', 'min': '-0', 'default_interval': 28800, 'dynamic_start': 0}
            for off_m in [i for i in offline if i.get('offline_visible')]:
                new_off_m = dict(off_m)
                # 填充缺少的字段，保持结构一致
                new_off_m['funding'] = default_funding
                new_off_m['open_interest_volume'] = '0'
                result.append(new_off_m)
            offline = [i for i in offline if not i.get('offline_visible')]

        for market_data in result:
            market_data['default_maker_fee_rate'] = amount_to_str(DEFAULT_CONTRACT_MAKER_FEE)
            market_data['default_taker_fee_rate'] = amount_to_str(DEFAULT_CONTRACT_TAKER_FEE)
        result.sort(key=lambda x: PerpetualMarketComparator(x['stock'], x['money']))
        direct_trading_area = {i['money'] for i in result if i['type'] == PerpetualMarketType.DIRECT.value}
        inverse_trading_area = {i['money'] for i in result if i['type'] == PerpetualMarketType.INVERSE.value}
        inverse_trading_area = sorted(inverse_trading_area)
        sort_trading_area = sorted(direct_trading_area, key=lambda x: 0 if x == 'USDT' else 1)
        return dict(
            direct_trading_area=sort_trading_area,
            inverse_trading_area=inverse_trading_area,
            online=result,
            offline=offline
        )


@ns.route('/insurance')
@respond_with_code
class MarketInsurances(Resource):

    @staticmethod
    def get():
        """
        实时保险基金，客户端和前端都没有调用
        """
        records = PerpetualServerClient().market_insurances()
        filter_response = []
        for record in records:
            # remove real amount
            record.pop("real_amount", None)
            filter_response.append(record)
        return filter_response


@ns.route('/premium')
@respond_with_code
class MarketPremiumHistory(Resource):

    @staticmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField(max_limit=2000),
    ))
    def get(**kwargs):
        """
        市场信息-溢价指数
        """
        market = kwargs['market']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        result = PerpetualServerClient().premium_history(
            market, start_time, end_time, page, limit)
        return offset_to_page(result)


@ns.route('/offline-info')
@respond_with_code
class PerMarketOfflineInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=PerpetualMarketField(required=True, allow_offline=True),
        )
    )
    def get(cls, **kwargs):
        market = kwargs['market']
        lang = Language(g.lang or Language.DEFAULT.value)
        row: MarketOfflineContent = MarketOfflineContent.query.filter(
            MarketOfflineContent.market == market,
            MarketOfflineContent.market_type == MarketOfflineContent.MarketType.PERPETUAL,
            MarketOfflineContent.lang == lang,
        ).with_entities(MarketOfflineContent.content).first()
        return {
            "content": row.content if row else ""
        }


@ns.route('/funding/history')
@respond_with_code
class MarketFundingHistory(Resource):

    @staticmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True, allow_offline=True),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        export=wa_fields.Boolean(missing=False),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
    ))
    def get(**kwargs):
        """
        市场信息-资金费率
        """
        market = kwargs['market']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        result = PerpetualServerClient().funding_history(
            market, start_time, end_time, page, limit)
        if kwargs['export']:
            data = result['records']
            for item in data:
                item['time'] = \
                    timestamp_to_datetime(float(item['time'])).strftime('%Y-%m-%d_%H:%M:%S.%f')
            return export_csv(data, ['time', 'market', 'funding_rate'], f'{market}-funding_rate')
        return offset_to_page(result)


@ns.route('/funding')
@respond_with_code
class FundingIntimeResource(Resource):
    return_fields = ('funding_rate_predict', 'funding_rate_next', 'funding_interval', 'funding_max', 'funding_min')

    @classmethod
    def get(cls):
        """获取实时资金费率"""
        res = []
        funding_time = 0
        client = PerpetualServerClient()
        ret = client.get_market_status()
        for market, item in ret.items():
            funding_time = item.get('funding_time', 0)
            row = {k: item.get(k) for k in cls.return_fields}
            row.update({'market': market})
            res.append(row)
        data = {
            'funding_time': funding_time,
            'items': res
        }
        return data


@ns.route('/insurance/history')
@respond_with_code
class InsuranceHistoryResource(Resource):
    return_fields = {
        'create_time': ex_fields.TimestampMarshalField(
            attribute=lambda x: x.report_date),
        'increase_amount': DecimalType,
        'decrease_amount': DecimalType,
        'total_balance': DecimalType
    }

    @classmethod
    @ns.use_kwargs(dict(
        asset=wa_fields.String(missing=''),
        market=PerpetualMarketField(missing=''),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        start_time=TimestampField,
        end_time=TimestampField
    ))
    def get(cls, **kwargs):
        """
        市场信息-保险基金
        """
        asset = kwargs['asset']
        market = kwargs['market']
        page = kwargs['page']
        limit = kwargs['limit']
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        if not asset and not market:
            raise InvalidArgument(message="error asset or market")
        elif asset:
            query = DailyPerpetualInsuranceReport.query.filter(
                DailyPerpetualInsuranceReport.asset == asset
            ).order_by(
                DailyPerpetualInsuranceReport.id.desc()
            )
        else:
            q_asset = PerpetualMarketCache.get_balance_asset(market)
            query = DailyPerpetualInsuranceReport.query.filter(
                DailyPerpetualInsuranceReport.asset == q_asset
            ).order_by(
                DailyPerpetualInsuranceReport.id.desc()
            )
        if start_time:
            query = query.filter(DailyPerpetualInsuranceReport.report_date > start_time)
        if end_time:
            query = query.filter(DailyPerpetualInsuranceReport.report_date <= end_time)
        return query_to_page(query, page, limit, cls.return_fields)


@ns.route('/position/history')
@respond_with_code
class PositionHistoryResource(Resource):
    return_fields = {
        'create_time': fields.Integer(
            attribute=lambda x: x.report_time),
        'position_short_users': fields.Integer,
        'position_long_users': fields.Integer,
        'position_short_amount': DecimalType,
        'position_long_amount': DecimalType
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True),
        limit=ex_fields.LimitField(missing=24),
    ))
    def get(cls, **kwargs):
        """
        市场信息-开仓数量、多空比例(APP端上线合约信息相关功能后可删除该接口)
        """
        from app.consumer.kline.booster import Calculator

        market = kwargs['market']
        limit = kwargs['limit']

        query = MarketSummaryHistory.query.filter(
            MarketSummaryHistory.market_type == market
        ).order_by(
            MarketSummaryHistory.id.desc()
        ).limit(limit)
        rows = query.all()
        # 因K线刷量， 放大合约持仓量
        multiple = Calculator.calc_position_multiple()
        for row in rows:
            row.position_short_amount = (row.position_short_amount * multiple).quantize(row.position_short_amount)
            row.position_long_amount = (row.position_long_amount * multiple).quantize(row.position_long_amount)
        return marshal(rows, cls.return_fields)


class DailyPointsMixin:

    @classmethod
    def get_daily_query_points(cls, limit):
        """查询最近7，30，90天数据时使用小时表数据（取0， 8，16点的数据）"""
        ts_interval = 8 * 60 * 60
        now_ = current_timestamp(to_int=True)
        end = now_ - now_ % ts_interval
        res = []
        for i in range(int(24 / 8 * limit)):
            ts = end - ts_interval * i
            res.append(ts)
        return res


@ns.route('/position-interval/history')
@respond_with_code
class PositionIntervalHistoryResource(Resource, DailyPointsMixin):
    return_fields = {
        'report_time': fields.Integer,
        'position_short_users': fields.Integer,
        'position_long_users': fields.Integer,
        'position_short_amount': DecimalType,
        'position_long_amount': DecimalType,
        'position_short_usd': DecimalType,
        'position_long_usd': DecimalType,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True, self_definition_val=MarketSummaryHistory.ALL_MARKETS),
        interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL), required=True)
    ))
    def get(cls, **kwargs):
        """
        市场信息-开仓数量、多空比例
        """
        from app.consumer.kline.booster import Calculator

        market, interval = kwargs['market'], kwargs['interval']
        limit = INTERVAL_LIMIT_DIC[interval]
        if market == PerpetualTradeSummary.ALL_MARKETS and interval == '7d':
            query_model = MarketSummaryHistory
            ts_points = cls.get_daily_query_points(limit)
            query = query_model.query.filter(
                query_model.market_type == market,
                query_model.report_time.in_(ts_points),
            ).order_by(
                query_model.report_time.desc()
            ).all()
        else:
            if interval in HOUR_INTERVAL:
                query_model = MarketSummaryHistory
            else:
                query_model = DailyMarketSummaryHistory

            query = query_model.query.filter(
                query_model.market_type == market,
            ).order_by(
                query_model.report_time.desc()
            ).limit(limit).all()
        # 因K线刷量， 放大合约持仓量
        multiple = Calculator.calc_position_multiple()
        for row in query:
            row.position_short_amount = (row.position_short_amount * multiple).quantize(row.position_short_amount)
            row.position_long_amount = (row.position_long_amount * multiple).quantize(row.position_long_amount)
            row.position_short_usd = (row.position_short_usd * multiple).quantize(row.position_short_usd)
            row.position_long_usd = (row.position_long_usd * multiple).quantize(row.position_long_usd)
        return marshal(query, cls.return_fields)


@ns.route('/big-customer-position/history')
@respond_with_code
class BigCustomerPositionHistoryResource(Resource, DailyPointsMixin):
    return_fields = {
        'report_time': fields.Integer,
        'position_short_users': fields.Integer,
        'position_long_users': fields.Integer,
        'position_long_usd': DecimalType,
        'position_short_usd': DecimalType,

    }

    @classmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True, self_definition_val=PerpetualTradeSummary.ALL_MARKETS),
        interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL),
                                     required=True)
    ))
    def get(cls, **kwargs):
        """
        市场信息-大户持仓多空比例
        """
        market, interval = kwargs['market'], kwargs['interval']
        if market == PerpetualTradeSummary.ALL_MARKETS:
            query = cls.get_all_markets_query(market, interval)
        else:
            query = cls.get_single_market_query(market, interval)
        return marshal(query, cls.return_fields)

    @classmethod
    def get_all_markets_query(cls, market, interval):
        query_model = NormalCustomerMarketSummaryHistory
        limit = INTERVAL_LIMIT_DIC[interval]

        if interval in HOUR_INTERVAL:
            query = query_model.query.filter(
                query_model.market_type == market
            ).order_by(
                query_model.report_time.desc()
            ).limit(limit).all()
        else:
            ts_points = cls.get_daily_query_points(limit)
            query = query_model.query.filter(
                query_model.market_type == market,
                query_model.report_time.in_(ts_points)
            ).order_by(
                query_model.report_time.desc()
            ).all()
        return query

    @classmethod
    def get_single_market_query(cls, market, interval):
        limit = INTERVAL_LIMIT_DIC[interval]
        if interval in HOUR_INTERVAL:
            query_model = BigCustomerMarketSummaryHistory
        else:
            query_model = DailyBigCustomerMarketSummaryHistory

        query = query_model.query.filter(
            query_model.market_type == market,
        ).order_by(
            query_model.report_time.desc()
        ).limit(limit).all()
        return query


@ns.route('/trade/history')
@respond_with_code
class PerpetualTradeHistoryResource(Resource, DailyPointsMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=PerpetualMarketField(required=True, self_definition_val=PerpetualTradeSummary.ALL_MARKETS),
            interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL),
                                         required=True)
        ))
    def get(cls, **kwargs):
        """交易量、主动买入卖出量"""
        market, interval = kwargs['market'], kwargs['interval']
        limit = INTERVAL_LIMIT_DIC[interval]
        if market == PerpetualTradeSummary.ALL_MARKETS and interval == '7d':
            res = cls.get_7_day_all_market_result()
        else:
            res = cls.get_trade_result(market, interval, limit)
        return res

    @classmethod
    def get_7_day_all_market_result(cls):
        query_model = PerpetualTradeSummary
        ts_points = cls.get_daily_query_points(7)
        ts_points.sort(reverse=True)
        query_start, query_end = ts_points[-1] - 8 * 3600, ts_points[0]
        query = query_model.query.filter(
            query_model.market == query_model.ALL_MARKETS,
            query_model.report_time >= query_start,
            query_model.report_time <= query_end,
        ).order_by(
            query_model.report_time.desc()
        ).all()
        res = []
        for idx, end_ts in enumerate(ts_points):
            if idx != len(ts_points) - 1:
                start_ts = ts_points[idx + 1]
            else:
                start_ts = end_ts - 8 * 3600
            tmp = {
                'report_time': end_ts,
                'taker_buy_amount': Decimal(),
                'taker_sell_amount': Decimal(),
                'trade_amount': Decimal(),
                'taker_buy_usd': Decimal(),
                'taker_sell_usd': Decimal(),
                'trade_usd': Decimal(),
            }
            for item in query:
                report_time = item.report_time
                if start_ts < report_time <= end_ts:
                    tmp['taker_buy_amount'] += item.taker_buy_amount
                    tmp['taker_sell_amount'] += item.taker_sell_amount
                    tmp['trade_amount'] += item.trade_amount
                    tmp['taker_buy_usd'] += item.taker_buy_usd
                    tmp['taker_sell_usd'] += item.taker_sell_usd
                    tmp['trade_usd'] += item.trade_usd
            res.append(tmp)
        return res

    @classmethod
    def get_trade_result(cls, market, interval, limit):
        if interval in HOUR_INTERVAL:
            query_model = PerpetualTradeSummary
            delta_ = 3600
        else:
            query_model = DailyPerpetualTradeSummary
            delta_ = 86400
        query = query_model.query.filter(
            query_model.market == market,
        ).order_by(
            query_model.id.desc()
        ).limit(limit).all()

        res = []
        for item in query:
            taker_buy_amount = item.taker_buy_amount
            taker_sell_amount = item.taker_sell_amount
            trade_amount = item.trade_amount
            report_time = item.report_time + delta_    # 前端展示需要，数据时间向后推一个时间段
            taker_buy_usd = item.taker_buy_usd
            taker_sell_usd = item.taker_sell_usd
            trade_usd = item.trade_usd

            res.append({
                'taker_buy_amount': amount_to_str(taker_buy_amount, 4),
                'taker_sell_amount': amount_to_str(taker_sell_amount, 4),
                'trade_amount': amount_to_str(trade_amount, 4),
                'report_time': report_time,
                'taker_buy_usd': amount_to_str(taker_buy_usd, 2),
                'taker_sell_usd': amount_to_str(taker_sell_usd, 2),
                'trade_usd': amount_to_str(trade_usd, 2),
            })
        return res


@ns.route('/weighted-funding-rate/history')
@respond_with_code
class WeightedFundingRateHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL),
                                     required=True))
    )
    def get(cls, **kwargs):
        """全市场加权资金费率"""
        interval = kwargs["interval"]
        end_time = today_timestamp_utc()
        delta = INTERVAL_LIMIT_DIC[interval]
        if interval in HOUR_INTERVAL:
            start_time = end_time - 3600 * delta
        else:
            start_time = end_time - 86400 * delta
        query_model = FundingRateHistory
        query = query_model.query.filter(
            query_model.market_type == query_model.ALL_MARKETS,
            query_model.report_time >= start_time,
            query_model.report_time <= end_time
        ).order_by(
            query_model.report_time.desc()
        ).all()
        res = []
        for row in query:
            item = dict(
                time=row.report_time,
                funding_rate=row.funding_rate
            )
            res.append(item)
        return res


@ns.route('/limit/config')
@respond_with_code
class MarketLimitConfig(Resource):

    @staticmethod
    def get():
        """
        合约杠杆配置
        """
        return PerpetualServerClient().limit_config()


@ns.route('/preference')
@respond_with_code
class MarketPreference(Resource):

    @staticmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True),
    ))
    def get(**kwargs):
        """
        用户全仓、逐仓配置
        """
        market = kwargs['market']
        return PerpetualServerClient().get_preference(g.user.id, market)


@ns.route('/fee/rate')
@respond_with_code
class MarketFeeRate(Resource):

    @staticmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(missing='', allow_offline=True),
    ))
    def get(**kwargs):
        """
        获取合约费率
        """
        market = kwargs['market']
        bus_type = TradeBusinessType.PERPETUAL
        if market:
            t = FeeFetcher(g.user.id)
            r = t.fetch(bus_type, market)
            special_fee = t.parser.get_marker_special_fee_info(bus_type, market)
        else:
            t = FeeFetcher(g.user.id)
            r = t.fetch_business_type_display(bus_type)
            special_fee = t.parser.get_marker_special_fee_info(bus_type, ALL_MARKETS)
        taker_fee_rate = r[TradeType.TAKER]
        maker_fee_rate = r[TradeType.MAKER]
        level_data = t.parser.get_level_cache_data(bus_type)
        level_fee_map = t.parser.get_level_fee_result(bus_type, True)
        show_special_fee = bool(special_fee)
        vip_taker_fee = level_fee_map[TradeType.TAKER] if show_special_fee else taker_fee_rate
        vip_maker_fee = level_fee_map[TradeType.MAKER] if show_special_fee else maker_fee_rate
        return dict(
            taker_fee_rate=amount_to_str(taker_fee_rate, 8),
            maker_fee_rate=amount_to_str(maker_fee_rate, 8),
            vip_level=int(level_data[UserClassIdPerpetualEnum.VIP.name]),
            mm_level=int(level_data[UserClassIdPerpetualEnum.MM.name]),
            show_special_fee=show_special_fee,
            special_valid_time=UserFeeParser.min_with_zero_as_max(
                [i['expired_time'] for i in special_fee.values()]) if special_fee else None,
            vip_taker_fee=amount_to_str(vip_taker_fee, 8),
            vip_maker_fee=amount_to_str(vip_maker_fee, 8),
        )


@ns.route('/sign')
@respond_with_code
class MarketSignAgreement(Resource):

    @staticmethod
    @require_login
    def post():
        """
        开通合约交易
        """
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if pref.opening_perpetual_trading:
            return

        pref.opening_perpetual_trading = True

        db.session.add(
            Message(
                user_id=user_id,
                title=MessageTitle.OPENING_PERPETUAL_TRADING.name,
                content=MessageContent.OPENING_PERPETUAL_TRADING.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.PERPETUAL_GUIDE_ARTICLE_URL.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.TRADE_NOTIFICATION,
            )
        )
        db.session.commit()
        send_perpetual_trading_introduction_email.delay(user_id)


@ns.route('/index')
@respond_with_code
class MarketIndex(Resource):

    @staticmethod
    def get():
        """
        市场信息-合约信息、指数价格
        """
        return PerpetualServerClient().index_list()


@ns.route('/leverage/adjust')
class MarketLeverageAdjust(Resource):

    @staticmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=True)
    @ns.use_kwargs(
        dict(
            market=PerpetualMarketField(required=True),
            position_type=wa_fields.Integer(
                required=True,
                validate=lambda x: x in (1, 2)
            ),
            leverage=wa_fields.Field(required=True),
        )
    )
    def post(**kwargs):
        """
        调整市场杠杆
        """
        user_id = g.user.id
        market = kwargs['market']
        position_type = kwargs['position_type']
        leverage = kwargs['leverage']
        if CopyTraderPerpetualOp.is_copy_trader_sub_mode():
            main_user = g.auth_user
            CopyTraderPerpetualOp.adjust_sub_leverage(main_user.id, user_id, market, position_type, leverage)
        else:
            adjust_leverage(user_id, market, position_type, str(leverage))
        return success(message=_("设置成功"))


@ns.route('/user/deals')
@respond_with_code
class MarketUserDeals(Resource):
    export_fields = {
        'time': ex_fields.LocalDateTimeStr,
        'market': fields.String,
        'deal_type': fields.String(attribute=lambda x: _(position_deal_type_map[x['deal_type']])),
        'side': ex_fields.EnumMarshalField(OrderSideType),
        'amount': ex_fields.AmountField,
        'price': ex_fields.AmountField,
        'deal_stock': ex_fields.AmountField,
        'fee_asset': fields.String(attribute=lambda x: x['fee_asset'] \
                                   or PerpetualMarketCache.get_fee_asset(x['market'])),
        'deal_fee': ex_fields.AmountField,
        'fee_real_rate': fields.String,
        'role': ex_fields.EnumMarshalField(TradeIntType),
        'deal_profit': ex_fields.DealProfitField(
            attribute=lambda x: (
                x['deal_profit'], x['deal_fee'], x['fee_asset']))
    }

    marshal_fields = {
        'amount': fields.String,
        'id': fields.Integer,
        'create_time': fields.Integer(attribute='time'),
        'market': fields.String,
        'deal_type': fields.Integer,
        'deal_type_detail': fields.String(attribute=lambda x: _(position_deal_type_map[x['deal_type']])),
        'deal_fee': ex_fields.AmountField,
        'deal_profit': ex_fields.DealProfitField(
            attribute=lambda x: (
                x['deal_profit'], x['deal_fee'], x['fee_asset'])
        ),
        'deal_stock': fields.String,
        'fee_asset': fields.String(attribute=lambda x: x['fee_asset'] \
                                   or PerpetualMarketCache.get_fee_asset(x['market'])),
        'fee_price': fields.String,
        'fee_rate': fields.String(attribute=lambda x: x['fee_real_rate']),
        'price': fields.String,
        'role': fields.Integer,
        'side': fields.Integer,
        'position_id': fields.Integer,
    }

    export_headers = (
        {"field": "time", Language.ZH_HANS_CN: "时间", Language.EN_US: "Time"},
        {"field": "market", Language.ZH_HANS_CN: "合约", Language.EN_US: "Contract"},
        {"field": "side", Language.ZH_HANS_CN: "方向", Language.EN_US: "Side"},
        {"field": "deal_type", Language.ZH_HANS_CN: "交易类型", Language.EN_US: "Trading Type"},
        {"field": "price", Language.ZH_HANS_CN: "成交价", Language.EN_US: "Price"},
        {"field": "amount", Language.ZH_HANS_CN: "成交量", Language.EN_US: "Executed"},
        {"field": "deal_stock", Language.ZH_HANS_CN: "成交金额", Language.EN_US: "Value"},
        {"field": "deal_fee", Language.ZH_HANS_CN: "手续费", Language.EN_US: "Fee"},
        {"field": "role", Language.ZH_HANS_CN: "角色类型", Language.EN_US: "Role"},
        {"field": "deal_profit", Language.ZH_HANS_CN: "已实现盈亏", Language.EN_US: "Realized PNL"},
    )

    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(missing='', allow_offline=True),
        side=wa_fields.String(missing='',
                              validate=lambda x: x in ('', 'buy', 'sell')),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField(unlimited=True),
        limit=ex_fields.LimitField,
        export=wa_fields.Integer(missing=0),
        market_type=EnumField(PerpetualMarketType),
    ))
    def get(cls, **kwargs):
        """
        订单信息-交易记录
        """
        client = PerpetualServerClient()
        market = kwargs['market']
        side = kwargs['side']
        side = OrderSideType[kwargs['side'].upper()] if side else 0
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        market_type = kwargs.get('market_type')
        market_type = market_type.value if market_type else PERPETUAL_ALL_MARKET_TYPE
        if kwargs['export']:
            records = client.user_deals(
                g.user.id,
                market,
                side,
                start_time,
                end_time,
                limit=config['EXPORT_ITEM_MAX_COUNT'],
                market_type=market_type,
            )
            return export_xlsx(
                filename='user_deals',
                data_list=marshal(records, cls.export_fields),
                export_headers=cls.export_headers
            )
        result = client.user_deals(
            g.user.id,
            market,
            side,
            start_time,
            end_time,
            page,
            limit,
            market_type=market_type,
        ).as_dict()
        result['data'] = marshal(result['data'], cls.marshal_fields)

        return result


@ns.route('/assets')
@respond_with_code
class AssetsResource(Resource):

    @classmethod
    def get(cls):
        client = PerpetualServerClient()
        cache_asset_set = set(PerpetualCoinTypeCache().read_aside())
        result = [i for i in client.asset_list() if i['name'] in cache_asset_set]
        result.sort(key=lambda x: AssetComparator(x['name']))
        return dict(assets=result)


@ns.route("/kline")
@respond_with_code
class MarketKlineResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            market=PerpetualMarketField(required=True, allow_offline=True),
            start_time=wa_fields.Integer(required=True),
            end_time=wa_fields.Integer(required=True),
            interval=wa_fields.Integer(required=True, validate=lambda x: x > 0),
        )
    )
    def get(cls, **kwargs):
        market = kwargs["market"]
        start_time = kwargs["start_time"]
        end_time = kwargs["end_time"]
        interval = kwargs["interval"]

        one_week_seconds = 86400 * 7
        one_month_seconds = 86400 * 30
        merge_nums = 0
        is_three_month = False
        if interval == one_week_seconds * 2:
            # 只处理2周
            merge_nums = 2
            interval = one_week_seconds
        elif interval == one_month_seconds * 3:
            merge_nums = 3
            interval = one_month_seconds
            is_three_month = True

        market_klines = MarketKlineHelper.full_market_kline(
            server_client=PerpetualServerClient(),
            market=market,
            start_time=start_time,
            end_time=end_time,
            interval=interval,
            is_three_month=is_three_month,
        )
        if merge_nums:
            return MarketKlineHelper.merge_kline(market_klines, merge_nums)
        return market_klines


@ns.route('/basis-history')
@respond_with_code
class BasisPriceResource(Resource, DailyPointsMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=PerpetualMarketField(required=True, self_definition_val=BasisRateHistory.ALL_MARKETS),
            interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL), required=True)
        )
    )
    def get(cls, **kwargs):
        """获取合约市场基差价格"""
        market = kwargs["market"]
        interval = kwargs["interval"]
        if market == BasisRateHistory.ALL_MARKETS:
            return cls.query_all_markets(market, interval)
        else:
            return cls.query_single_market(market, interval)

    @classmethod
    def query_all_markets(cls, market, interval):
        query_model = BasisRateHistory
        limit = INTERVAL_LIMIT_DIC[interval]
        if interval in HOUR_INTERVAL:
            query = query_model.query.filter(
                query_model.market_type == market,
            ).order_by(
                query_model.report_time.desc()
            ).limit(limit).all()
        else:
            ts_points = cls.get_daily_query_points(limit)
            query = query_model.query.filter(
                query_model.market_type == market,
                query_model.report_time.in_(ts_points)
            ).order_by(
                query_model.report_time.desc()
            ).all()
        res = dict(
            market=market,
            limit=limit
        )
        records = []
        for row in query:
            item = dict(
                time=row.report_time,
                basis_rate=row.basis_rate
            )
            records.append(item)
        res['records'] = records
        return res

    @classmethod
    def query_single_market(cls, market, interval):
        client = PerpetualServerClient()
        end_time = today_timestamp_utc() + 86400
        if interval == '7d':
            start_time = end_time - 86400 * 7
        else:
            start_time = end_time - 86400 * 30
        res = client.get_basis_history(market, start_time, end_time)
        records = res['records']

        if interval == '7d':    # 最近7天时，按小时级粒度的数据返回
            res['records'] = cls._fmt_records(records)
            return res
        else:   # 最近30天时，按天级粒度返回
            ret = cls._get_avg_daily_basis_data(records)
            res['records'] = ret
            res['limit'] = len(ret)
            return res

    @classmethod
    def _fmt_records(cls, records: List[Dict]):
        res = []
        for item in records:
            basis_rate = Decimal(item['price']) / Decimal(item['index']) if Decimal(item['index']) else 0   # 计算基差率
            item_dic = {
                'basis_rate': basis_rate,
                'time': item['time']
            }
            res.append(item_dic)
        return res

    @classmethod
    def _get_avg_daily_basis_data(cls, res: List[Dict]) -> List[Dict]:
        """
        将小时粒度的数据，求平均值后聚合为天粒度（小时级数据不保证完整，某些时间点可能会有缺失）
        """
        grouped_res = group_by(lambda x: x['time'] - x['time'] % 86400, res)
        ret = []
        for time, item_lis in grouped_res.items():
            price = index = 0
            for item in item_lis:
                price += Decimal(item['price'])
                index += Decimal(item['index'])
            ret.append({
                'time': time,
                'basis_rate': price / index if index else 0
            })
        ret.sort(key=lambda x: x['time'])
        return ret


@ns.route("/change-rate")
@respond_with_code
class MonthlyMarketChangeRateResource(Resource):
    @classmethod
    def get(cls):

        res = cls._get_data()
        return res

    @classmethod
    @mem_cached(60)
    def _get_data(cls):
        cache = PerpetualMonthlyMarketChangeRateCache()
        return cache.read_aside()


@ns.route('/liquidation/history')
@respond_with_code
class LiquidationHistoryResource(Resource):
    return_fields = {
        'report_date': ex_fields.TimestampMarshalField(
            attribute=lambda x: x.report_date),
        'burst_amount': DecimalType,
        'short_burst_amount': DecimalType,
        'long_burst_amount': DecimalType,
    }

    @classmethod
    @ns.use_kwargs(dict(
        market=PerpetualMarketField(required=True),
        limit=ex_fields.LimitField(missing=7),
    ))
    def get(cls, **kwargs):
        """合约市场强平信息统计"""
        market, limit = kwargs.get('market'), kwargs.get('limit')
        res = []
        current_data = cls._get_current_day_data(market)
        res.extend(current_data)
        history_data: List = cls._get_history_data(market, limit)
        res.extend(history_data)
        return res

    @classmethod
    def _get_current_day_data(cls, market: str) -> List[Dict[str, Union[int, Decimal]]]:
        """
        获取今天/昨天的爆仓数据
        """
        res = []
        today_ = today_timestamp_utc()
        yesterday_ = today_ - 86400
        today_data = cls._get_cache_data(today_, market)
        res.append(today_data)
        yesterday_data = cls._get_cache_data(yesterday_, market)
        res.append(yesterday_data)
        return res

    @classmethod
    def _get_cache_data(cls, date_timestamp: int, market: str):
        data = {'report_date': date_timestamp}
        cache = TodayPerpetualLiquidationCache(date_timestamp)
        cache_data = cache.get_data(market)
        data.update(cache_data)
        return data

    @classmethod
    def _get_history_data(cls, market: str, limit: int) -> List[Dict[str, Union[int, Decimal]]]:
        """获取爆仓历史数据"""
        today_ = today()
        end_time = today_ - datetime.timedelta(days=1)
        start_time = today_ - datetime.timedelta(days=limit-1)
        report_model = DailyPerpetualMarginBurstReport
        records = report_model.query.filter(
            report_model.market == market,
            report_model.report_date >= start_time,
            report_model.report_date < end_time
            ).order_by(report_model.id.desc()).all()
        return marshal(records, cls.return_fields)


@ns.route('/weighted-liquidation/history')
@respond_with_code
class WeightedLiquidationResource(Resource, DailyPointsMixin):

    @classmethod
    @ns.use_kwargs(dict(
        interval=ex_fields.EnumField(enum=(*HOUR_INTERVAL, *DAY_INTERVAL),
                                     required=True)),
    )
    def get(cls, **kwargs):
        """全市场加权爆仓数据"""
        interval = kwargs['interval']
        limit = INTERVAL_LIMIT_DIC[interval]
        if interval in HOUR_INTERVAL:
            return cls.get_hourly_res(limit)
        elif interval == '7d':
            return cls.get_7_day_res()
        else:
            return cls.get_daily_res(limit)

    @classmethod
    def get_hourly_res(cls, limit):
        query_model = HourlyPerpetualMarginBurstReport
        query = query_model.query.filter(
            query_model.market == query_model.ALL_MARKETS,
        ).order_by(query_model.id.desc()).limit(limit).all()
        res = []
        for row in query:
            item = {
                'time': row.report_time,
                'long_burst_usd': row.long_burst_usd,
                'short_burst_usd': row.short_burst_usd,
            }
            res.append(item)
        return res

    @classmethod
    def get_7_day_res(cls):
        query_model = HourlyPerpetualMarginBurstReport
        ts_points = cls.get_daily_query_points(7)
        ts_points.sort(reverse=True)
        query_start, query_end = ts_points[-1] - 8 * 3600, ts_points[0]
        query = query_model.query.filter(
            query_model.market == query_model.ALL_MARKETS,
            query_model.report_time >= query_start,
            query_model.report_time <= query_end,
        ).order_by(query_model.id.desc()).all()
        res = []
        for idx, end_ts in enumerate(ts_points):
            if idx != len(ts_points) - 1:
                start_ts = ts_points[idx + 1]
            else:
                start_ts = end_ts - 8 * 3600
            tmp = {
                'time': end_ts,
                'long_burst_usd': Decimal(),
                'short_burst_usd': Decimal(),
            }
            for item in query:
                report_time = item.report_time
                if start_ts < report_time <= end_ts:
                    tmp['long_burst_usd'] += item.long_burst_usd
                    tmp['short_burst_usd'] += item.short_burst_usd
            res.append(tmp)
        return res

    @classmethod
    def get_daily_res(cls, limit):
        query_model = DailyPerpetualMarginBurstReport
        first_rec = query_model.query.with_entities(
            query_model.report_date
        ).order_by(query_model.report_date.desc()).first()
        end_time = first_rec.report_date
        start_time = end_time - datetime.timedelta(days=limit)
        query = query_model.query.filter(
            query_model.report_date > start_time,
            query_model.report_date <= end_time
        ).with_entities(
            query_model.report_date,
            func.sum(query_model.long_burst_usd).label('long_burst_usd'),
            func.sum(query_model.short_burst_usd).label('short_burst_usd')
        ).group_by(
            query_model.report_date
        ).order_by(
            query_model.report_date.desc()
        ).all()
        res = []
        for row in query:
            item = {
                'time': int(date_to_datetime(row.report_date).timestamp()),
                'long_burst_usd': row.long_burst_usd,
                'short_burst_usd': row.short_burst_usd,
            }
            res.append(item)
        return res

