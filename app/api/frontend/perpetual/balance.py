# -*- coding: utf-8 -*-
import decimal
from decimal import Decimal

from flask import g, current_app
from flask_babel import gettext as _
from flask_restx import fields, marshal
from webargs import fields as wa_fields

from app.business import SiteSettings, perpetual_transfer_in, UserSettings, \
    perpetual_transfer_out, PerpetualServerClient, get_user_using_coupon_balance
from app.business.clients.biz_monitor import biz_monitor
from app.caches import PerpetualCoinTypeCache
from app.common import (
    BalanceBusiness, Language, PrecisionEnum,
    get_balance_business_display,
    SubAccountPermission,
)
from app.common.events import BalanceEvent
from app.exceptions import TransferNotAllowed, InvalidArgument, \
    TwoFactorAuthenticationRequired, PerpetualOrderExceptionMap, \
    PerpetualResponseCode, TransferOutNotAllowed
from app.utils import export_xlsx
from app.config import config
from ...common import Resource, Namespace, respond_with_code, ex_fields, \
    require_login, success, require_user_permission, copy_trading_sub_user_setter
from ...common.request import require_user_request_permission
from ...common.fields import PositiveDecimalField

ns = Namespace('Perpetual')

POST_MODEL_SCHEMA = dict(
    amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                rounding=decimal.ROUND_DOWN, required=True),
    coin_type=wa_fields.String(required=True)
)


def check_coin_type(coin_type):
    if not SiteSettings.perpetual_transfers_enabled:
        raise TransferNotAllowed
    if coin_type not in PerpetualCoinTypeCache().read_aside():
        raise InvalidArgument


@ns.route('/transfer/in')
class BalanceTransferIn(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.PERPETUAL])
    @ns.use_kwargs(POST_MODEL_SCHEMA)
    def post(cls, **kwargs):
        """
        合约划转（in）
        """
        require_user_request_permission(g.user)
        check_coin_type(kwargs['coin_type'])
        if kwargs["coin_type"] in SiteSettings.forbidden_perpetual_transfer_in_assets:
            raise TransferNotAllowed
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired

        if not perpetual_transfer_in(
                user_id=g.user.id,
                coin_type=kwargs['coin_type'],
                amount=kwargs['amount']
        ):
            raise PerpetualOrderExceptionMap[
                PerpetualResponseCode.CONTRACT_TRANSFER_ERROR]

        biz_monitor.increase_counter(
            BalanceEvent.SPOT_TO_PERPETUAL
        )

        return success(message=_("划转成功"))


@ns.route("/transfer/balance")
@respond_with_code
class TransferBalanceResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        coin_type=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """合约可划转数量"""
        coin_type = kwargs["coin_type"]
        user_id = g.user.id
        perpetual_client = PerpetualServerClient(current_app.logger)
        res = perpetual_client.get_user_balances(user_id, coin_type)
        transfer_amount = Decimal(res.get(coin_type, {}).get('transfer', '0'))
        using_coupon_balance = get_user_using_coupon_balance(user_id, coin_type)
        transfer_amount = transfer_amount - using_coupon_balance
        return dict(transfer_amount=transfer_amount if transfer_amount > 0 else 0)


@ns.route('/transfer/out')
class BalanceTransferOut(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(POST_MODEL_SCHEMA)
    def post(cls, **kwargs):
        """
        合约划转（out）
        """
        check_coin_type(kwargs['coin_type'])
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        if not UserSettings(g.user.id).perpetual_transfer_out_enabled:
            raise TransferOutNotAllowed

        if not perpetual_transfer_out(
                user_id=g.user.id,
                coin_type=kwargs['coin_type'],
                amount=kwargs['amount']
        ):
            raise PerpetualOrderExceptionMap[
                PerpetualResponseCode.CONTRACT_TRANSFER_ERROR]

        biz_monitor.increase_counter(
            BalanceEvent.PERPETUAL_TO_SPOT
        )

        return success(message=_("划转成功"))


@ns.route('/history')
@respond_with_code
class BalanceHistory(Resource):
    export_fields = {
        'time': ex_fields.LocalDateTimeStr,
        'display': fields.String,
        'asset': fields.String,
        'change': ex_fields.AmountField,
        'balance': ex_fields.AmountField
    }
    export_headers = (
        {"field": "time", Language.ZH_HANS_CN: "时间",
         Language.EN_US: "Time"},
        {"field": "display", Language.ZH_HANS_CN: "类型", Language.EN_US: "Type"},
        {"field": "asset", Language.ZH_HANS_CN: "币种", Language.EN_US: "Coins"},
        {"field": "change", Language.ZH_HANS_CN: "资产变化",
         Language.EN_US: "Asset change"},
        {"field": "balance", Language.ZH_HANS_CN: "账户资产",
         Language.EN_US: "Assets"},
    )

    @classmethod
    @require_login
    @copy_trading_sub_user_setter(require_running=False)
    @ns.use_kwargs(dict(
        asset=wa_fields.String(missing='', data_key='coin_type'),
        business=wa_fields.String(missing=''),
        start_time=wa_fields.Integer(missing=0),
        end_time=wa_fields.Integer(missing=0),
        page=ex_fields.PageField,
        limit=ex_fields.LimitField,
        export=wa_fields.Integer(missing=0),
    ))
    def get(cls, **kwargs):
        """
        合约资产流水
        """
        client = PerpetualServerClient(current_app.logger)
        asset = kwargs['asset']
        business = kwargs['business']
        start_time = kwargs['start_time']
        end_time = kwargs['end_time']
        page = kwargs['page']
        limit = kwargs['limit']
        if kwargs['export']:
            records = client.get_user_balance_history(
                g.user.id,
                asset,
                business,
                start_time,
                end_time,
                limit=config['EXPORT_ITEM_MAX_COUNT']
            )
            records = cls.parse_balance_records(records)
            return export_xlsx(
                filename='balance_history',
                data_list=marshal(records, cls.export_fields),
                export_headers=cls.export_headers
            )
        result = client.get_user_balance_history(
            g.user.id,
            asset,
            business,
            start_time,
            end_time,
            page,
            limit,
        )
        cls.parse_balance_records(result)
        return result.as_dict()

    @staticmethod
    def parse_balance_records(records):
        for record in records:
            business_display = _(get_balance_business_display(record["business"]))
            record['display'] = business_display
            if detail := record.get('detail'):
                record['market'] = detail.get('market', '')
            else:
                record['market'] = ''
        return records


@ns.route('/history/type')
@respond_with_code
class AssetHistoryType(Resource):

    @classmethod
    def get(cls):
        """
        合约资产流水类型
        """
        return [
            {
                'business': business.value,
                'display': _(get_balance_business_display(business.value))
            } for business in [
                BalanceBusiness.PERPETUAL_TRANSFER_IN,
                BalanceBusiness.PERPETUAL_TRANSFER_OUT,
                BalanceBusiness.PERPETUAL_CLEARING_FUNDING,
                BalanceBusiness.PERPETUAL_CLEARING_CLOSE,
                BalanceBusiness.GIFT,
            ]
        ]
