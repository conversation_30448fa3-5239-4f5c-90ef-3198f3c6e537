# -*- coding: utf-8 -*-

from flask import g
from ...utils import timezone_to_offset
from ..common import get_request_language, get_request_timezone, check_maintain_mode, \
    get_request_platform, auto_read_only

url_prefix = '/res'


def before_request():
    lang = get_request_language().value
    g.user_lang = g.lang = lang   # 接口层面返回用户语言相关的内容时，用g.lang或g.user_lang
    platform = get_request_platform()
    if platform.is_web():
        g.user_lang_web = lang   # 用于更新用户在web端选择的语言，web消息异步推送时，用web_language
    else:
        g.user_lang_app = lang   # 用于更新用户在app端选择的语言，app消息异步推送用户语言相关的内容时，用app_language
    g.user_tz = tz = get_request_timezone()
    if tz is not None:
        g.user_tz_offset = timezone_to_offset(tz) // 60
    check_maintain_mode()

    auto_read_only()
