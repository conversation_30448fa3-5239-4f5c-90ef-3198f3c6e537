# -*- coding: utf-8 -*-
import datetime
import decimal
import json
from collections import defaultdict
from decimal import Decimal

from flask import g, current_app
from flask_babel import gettext as _
from marshmallow import fields

from ..common import Namespace, Resource, respond_with_code, require_login, limit_user_frequency, \
    lock_request, fields as internal_fields, success, require_user_permission
from ..common.fields import PositiveDecimalField, PageField, LimitField, EnumField
from ..common.request import require_user_request_permission
from ...business import (
    <PERSON>acheLock, LockKeys, SPOT_ACCOUNT_ID, amount_to_str,
    UserPreferences, UserSettings, MAX_ORDER_ACCOUNT_ID, ServerClient,
    cached
)
from ...business.email import send_margin_function_introduction_email
from ...business.margin.helper import MarginUserAccountInfo, TRANSFER_RATE_DICT, \
    get_user_day_rate_fee, MarginAccountHelper, BUY_TYPE, SELL_TYPE, report_margin_transfer_biz_event
from ...business.margin.loan import MarginOrderLoanOperation, report_loan_event, report_flat_event
from ...business.margin.repayment import Margin<PERSON>rderFlatOperation
from ...business.margin.transfer import MarginTransferOperation
from ...business.order import OrderFeeOption
from ...business.fee import FeeFetcher
from ...caches import (
    MarginAccountIdCache, MarginAccountNameCache, MarginAssetRuleCache,
    MarketCache, MarginDayRateViewCache,
)
from ...common import MessageTitle, MessageContent, MessageWebLink, SubAccountPermission
from ...common import PrecisionEnum, OrderSideType, TradeBusinessType
from ...exceptions import InvalidArgument, MarginNotAgreement, MarginFlatAmountLimit, \
    MarginLoanOrderNotFound, TwoFactorAuthenticationRequired, MarginBurstForbiddenFlat
from ...models import ComposeIndex, MarginIndex, MarginIndexDetail, MarginTransferHistory, \
    MarginLoanOrder, db, MarginFlatHistory, MarginLiquidationRate, \
    MarginAccount, MarginInsurance, DailyMarginFundReport, Message
from ...utils.date_ import date_to_datetime
from ...utils.helper import Struct

ns = Namespace('Margin')


@ns.route('/account/list')
@respond_with_code
class AccountListResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        helper = MarginUserAccountInfo(g.user.id)
        status_result = helper.get_all_except_status()
        account_result = helper.get_all_account_data()
        result = {}
        for account_id in helper.all_account_info:
            result[account_id] = status_result[account_id]
            result[account_id]["loan"] = account_result[account_id]["loan"]
            result[account_id]["interest"] = account_result[account_id]["interest"]
        return result


@ns.route('/index')
@respond_with_code
class IndexResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        market_type = params.market_type
        data = ComposeIndex.query.filter(
            ComposeIndex.name == market_type,
            ComposeIndex.status == ComposeIndex.StatusType.OPEN
        ).first()
        if not data:
            data = MarginIndex.query.filter(
                MarginIndex.market_name == market_type,
                MarginIndex.status == MarginIndex.StatusType.OPEN).first()
            if not data:
                raise InvalidArgument(market_type)

            sources = [
                dict(exchange=x.exchange_name, weight=str(x.weight))
                for x in data.margin_index_details.filter(
                    MarginIndexDetail.status == MarginIndexDetail.StatusType.PASS
                )]
            result = dict(
                prec=int(data.price_precision),
                sources=sources
            )
        else:
            index_ids = [
                v.id for v in MarginIndex.query.filter(
                    MarginIndex.market_name.in_([data.first_market,
                                                 data.second_market])
                ).with_entities(MarginIndex.id)
            ]
            result = MarginIndexDetail.query.filter(
                MarginIndexDetail.margin_index_id.in_(index_ids),
                MarginIndexDetail.status == MarginIndexDetail.StatusType.PASS)
            sources = []
            for v in result:
                exchange_names = [s["exchange"] for s in sources]
                if v.exchange_name not in exchange_names:
                    sources.append(
                        dict(exchange=v.exchange_name,
                             weight=str(v.weight))
                    )
            result = dict(
                prec=int(data.price_precision),
                sources=sources
            )
        return result


@ns.route('/transfer')
class TransferResource(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.MARGIN])
    @ns.use_kwargs(
        dict(from_account=fields.Integer(required=True,
                                         validate=lambda x:
                                         SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID),
             to_account=fields.Integer(required=True,
                                       validate=lambda x:
                                       SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID),
             coin_type=fields.String(required=True, validate=lambda x: x.isupper()),
             amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                         rounding=decimal.ROUND_DOWN, required=True))
    )
    def post(cls, **kwargs):
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        params = Struct(**kwargs)
        if params.from_account == SPOT_ACCOUNT_ID:
            require_user_request_permission(g.user)

        margin_account_id = max(params.from_account, params.to_account)
        with CacheLock(LockKeys.margin_loan_or_flat(g.user.id), wait=False),\
             CacheLock(LockKeys.user_margin_account(g.user.id, margin_account_id), wait=False):
            db.session.rollback()
            operation = MarginTransferOperation(
                user_id=g.user.id,
                transfer_from=params.from_account,
                transfer_to=params.to_account,
                asset=params.coin_type,
                amount=params.amount)
            operation.transfer()

        report_margin_transfer_biz_event(params.from_account)

        return success(message=_("划转成功"))


@ns.route('/transfer/history')
@respond_with_code
class TransferListResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(page=PageField(),
             limit=LimitField(),
             op=EnumField(MarginTransferHistory.TransferType, missing=None, enum_by_value=True)
             )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        params = Struct(**kwargs)
        if params.op:
            query = MarginTransferHistory.query.filter(
                MarginTransferHistory.user_id == user_id,
                MarginTransferHistory.transfer_type == params.op,
                MarginTransferHistory.status == MarginTransferHistory.StatusType.SUCCESS,
            ).order_by(MarginTransferHistory.id.desc())
        else:
            query = MarginTransferHistory.query.filter(
                MarginTransferHistory.user_id == user_id,
                MarginTransferHistory.status == MarginTransferHistory.StatusType.SUCCESS,
            ).order_by(MarginTransferHistory.id.desc())
        records = query.paginate(params.page, params.limit, error_out=False)

        result = []
        for record in records.items:
            if record.from_account_id == SPOT_ACCOUNT_ID:
                amount_str = f"+{amount_to_str(record.amount, 8)}"
                account_id = record.to_account_id
            else:
                amount_str = f"-{amount_to_str(record.amount, 8)}"
                account_id = record.from_account_id

            result.append({
                "time": record.created_at,
                "market_type": MarginAccountIdCache(account_id=account_id).dict['name'],
                "coin_type": record.asset,
                "op": record.transfer_type.value,
                "amount": amount_str,
                "balance": amount_to_str(record.balance, 8),
            })
        return {"page": params.page,
                "limit": params.limit,
                "total": records.total,
                "data": result}


@ns.route('/enable/status')
@respond_with_code
class UserStatusResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        user_id = g.user.id
        if UserPreferences(user_id).opening_margin_function and \
                UserSettings(user_id).can_margin_loan():
            return {}
        raise MarginNotAgreement

    @classmethod
    @require_login
    def post(cls):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if pref.opening_margin_function:
            return

        pref.opening_margin_function = True

        db.session.add(
            Message(
                user_id=user_id,
                title=MessageTitle.OPENING_MARGIN_FUNCTION.name,
                content=MessageContent.OPENING_MARGIN_FUNCTION.name,
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.MARGIN_GUIDE_ARTICLE_URL.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.TEXT,
                channel=Message.Channel.TRADE_NOTIFICATION,
            )
        )
        db.session.commit()
        send_margin_function_introduction_email.delay(user_id)


@ns.route('/market/detail')
@respond_with_code
class MarketDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        market_type=fields.String(required=True, validate=lambda x: x.isupper())
    ))
    def get(cls, **kwargs):
        market_type = kwargs["market_type"]
        result = MarginUserAccountInfo(None).all_account_info
        market_cache = MarginAccountNameCache(market_type).dict
        if not market_type or market_cache['status'] != MarginAccount.StatusType.OPEN:
            raise InvalidArgument(market_type)
        account_id = market_cache["id"]
        rate_query = MarginLiquidationRate.query.filter(
            MarginLiquidationRate.account_id == account_id,
            MarginLiquidationRate.status == MarginLiquidationRate.StatusType.PASS
        ).with_entities(
            MarginLiquidationRate.account_id,
            MarginLiquidationRate.operator,
            MarginLiquidationRate.liquidation_amount,
            MarginLiquidationRate.liquidation_rate,
        )
        rate_data = []
        for rate_record in rate_query:
            rate_data.append({
                "operator": rate_record.operator,
                "burst_amount": amount_to_str(rate_record.liquidation_amount, 8),
                "burst_rate": amount_to_str(rate_record.liquidation_rate, 8)}
            )
        cache_data = result[account_id]
        return {
            "transfer_rate": str(TRANSFER_RATE_DICT[(cache_data["leverage"])]),
            "account_id": account_id,
            "market_type": cache_data["market_type"],
            "sell_asset_type": cache_data["sell_asset_type"],
            "buy_asset_type": cache_data["buy_asset_type"],
            "warn_rate": cache_data['warning_rate'],
            "max_burst_rate": cache_data['max_liquidation_rate'],
            "leverage": cache_data["leverage"],
            "rules": rate_data
        }


@ns.route('/asset/day-rate')
@respond_with_code
class MarginAssetDayRateResource(Resource):

    @classmethod
    def get(cls):
        if s := MarginDayRateViewCache().read():
            return json.loads(s)
        return []


@ns.route('/market/leverage')
@respond_with_code
class MarketLeverageResource(Resource):

    @classmethod
    @cached(300)
    def get(cls):
        result = MarginUserAccountInfo(None).all_account_info
        account_ids = result.keys()
        rate_query = MarginLiquidationRate.query.filter(
            MarginLiquidationRate.account_id.in_(account_ids),
            MarginLiquidationRate.status == MarginLiquidationRate.StatusType.PASS
        ).with_entities(
            MarginLiquidationRate.account_id,
            MarginLiquidationRate.operator,
            MarginLiquidationRate.liquidation_amount,
            MarginLiquidationRate.liquidation_rate,
        )
        rate_data = defaultdict(list)
        for rate_record in rate_query:
            rate_data[rate_record.account_id].append({
                "operator": rate_record.operator,
                "burst_amount": amount_to_str(rate_record.liquidation_amount, 8),
                "burst_rate": amount_to_str(rate_record.liquidation_rate, 8)}
            )
        data = []
        for account_id, cache_data in result.items():
            data.append({
                "transfer_rate": str(TRANSFER_RATE_DICT[(cache_data["leverage"])]),
                "account_id": account_id,
                "market_type": cache_data["market_type"],
                "sell_asset_type": cache_data["sell_asset_type"],
                "buy_asset_type": cache_data["buy_asset_type"],
                "warn_rate": cache_data['warning_rate'],
                "max_burst_rate": cache_data['max_liquidation_rate'],
                "leverage": cache_data["leverage"],
                "rules": sorted(rate_data[account_id], key=lambda x: Decimal(x["burst_rate"]))
            })
        return sorted(data, key=lambda x: MarketCache.market_sort_func(
            x["sell_asset_type"], x["buy_asset_type"]))


@ns.route('/fund/history')
@respond_with_code
class InsuranceResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(page=PageField(),
             limit=LimitField(),
             coin_type=fields.String(required=True, validate=lambda x: x.isupper())
             )
    )
    def get(cls, **kwargs):
        args = Struct(**kwargs)
        total_record = MarginInsurance.query.filter(
            MarginInsurance.asset == args.coin_type
        ).first()
        data = {
            "amount": 0,
            "data": []
        }
        if not total_record:
            return {"page": args.page, "limit": args.limit, "total": 0, "data": data}
        records = DailyMarginFundReport.query.filter(
            DailyMarginFundReport.asset == args.coin_type
        ).order_by(
            DailyMarginFundReport.report_date.desc()
        )
        pagination = records.paginate(args.page, args.limit, error_out=False)
        page_data = [{
            "create_time": v.report_date.strftime("%Y-%m-%d"),
            "create_timestamp": int(date_to_datetime(v.report_date).timestamp()),
            "amount": amount_to_str(v.amount, 8),
            "fund": amount_to_str(v.interest_fund+v.liquidation_fund, 8),
            "burst": amount_to_str(v.liquidation, 8),
            "balance": amount_to_str(v.balance, 8),
        } for v in pagination.items]
        data["data"] = page_data
        return {"page": args.page,
                "limit": args.limit,
                "total": pagination.total,
                "has_next": pagination.has_next,
                "has_prev": pagination.has_prev,
                "total_pages": pagination.pages,
                "data": data}


@ns.route('/fund/chart')
@respond_with_code
class InsuranceChartResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            coin_type=fields.String(required=True, validate=lambda x: x.isupper())
        )
    )
    def get(cls, **kwargs):
        args = Struct(**kwargs)
        asset = args.coin_type
        total_record = MarginInsurance.query.filter(
            MarginInsurance.asset == asset
        ).first()
        data = {
            "amount": 0,
            "data": [],
        }
        if not total_record:
            return data
        data["amount"] = amount_to_str(total_record.amount, 8)
        # 30天
        t = datetime.datetime.utcnow().date() - datetime.timedelta(days=60)
        records = DailyMarginFundReport.query.filter(
            DailyMarginFundReport.asset == asset,
            DailyMarginFundReport.report_date > t,
        ).order_by(
            DailyMarginFundReport.report_date.asc()
        )
        amount_data = [{
            "date": v.report_date.strftime("%Y-%m-%d"),
            "amount": amount_to_str(v.balance, 8)
        } for v in records]
        data["data"] = amount_data
        return data


@ns.route('/loan')
@respond_with_code
class LoanResource(Resource):
    POST_SCHEMA = dict(
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID < x < MAX_ORDER_ACCOUNT_ID,
            required=True
        ),
        coin_type=fields.String(required=True, validate=lambda x: x.isupper()),
        amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                    rounding=decimal.ROUND_DOWN, required=True),
        renew=fields.Boolean(missing=False)
    )

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.MARGIN])
    @lock_request()
    @limit_user_frequency(count=1, interval=2)
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        require_user_request_permission(g.user)
        params = Struct(**kwargs)
        with CacheLock(LockKeys.margin_loan_or_flat(g.user.id), wait=False),\
             CacheLock(LockKeys.user_margin_account(g.user.id, params.account_id), wait=False):
            if not g.auth_user.has_2fa:
                raise TwoFactorAuthenticationRequired
            db.session.rollback()
            operation = MarginOrderLoanOperation(g.user.id, margin_identity=params.account_id)
            loan_id = operation.add_new_loan_order(params.coin_type, params.amount, params.renew)
            report_loan_event(g.user.id)
            return dict(loan_id=loan_id)

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID < x < MAX_ORDER_ACCOUNT_ID
        ),
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        args = Struct(**kwargs)
        margin_account_utils = MarginAccountHelper(user_id, args.account_id)
        cache_data = margin_account_utils.account_detail
        loan_max_amount = margin_account_utils.calculate_loan_max_amount()
        buy_asset_rate = get_user_day_rate_fee(user_id, cache_data["buy_asset_type"])
        sell_asset_rate = get_user_day_rate_fee(user_id, cache_data["sell_asset_type"])
        buy_asset_rule = MarginAssetRuleCache(cache_data["buy_asset_type"]).dict
        sell_asset_rule = MarginAssetRuleCache(cache_data["sell_asset_type"]).dict
        result = {
            "account_id": cache_data["account_id"],
            "market_type": cache_data["market_type"],
            "base_asset_max_loan": cache_data["base_asset_max_loan"],
            "quote_asset_max_loan": cache_data["quote_asset_max_loan"],
            "buy_asset_type": {
                "coin_type": cache_data["buy_asset_type"],
                "day_rate": amount_to_str(buy_asset_rate, PrecisionEnum.COIN_PLACES),
                "period": buy_asset_rule["period"],
                "loan_amount": amount_to_str(loan_max_amount[BUY_TYPE], PrecisionEnum.COIN_PLACES),
                "min_loan_amount": amount_to_str(buy_asset_rule['min_loan'], PrecisionEnum.COIN_PLACES),
                    "max_loan_amount": amount_to_str(buy_asset_rule['max_loan'], PrecisionEnum.COIN_PLACES),
            },
            "sell_asset_type": {
                "coin_type": cache_data["sell_asset_type"],
                "day_rate": amount_to_str(sell_asset_rate, PrecisionEnum.COIN_PLACES),
                "period": sell_asset_rule["period"],
                "loan_amount": amount_to_str(loan_max_amount[SELL_TYPE], PrecisionEnum.COIN_PLACES),
                "min_loan_amount": amount_to_str(sell_asset_rule["min_loan"], PrecisionEnum.COIN_PLACES),
                "max_loan_amount": amount_to_str(sell_asset_rule["max_loan"], PrecisionEnum.COIN_PLACES),
            },
        }
        return result


@ns.route('/loan/history')
@respond_with_code
class LoanHistoryResource(Resource):

    GET_SCHEMA = dict(
        page=PageField(),
        limit=LimitField(),
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID <= x < MAX_ORDER_ACCOUNT_ID),
        order_id=fields.Integer(),
        status=EnumField(MarginLoanOrder.StatusType, enum_by_value=True)
    )

    @classmethod
    @ns.use_kwargs(GET_SCHEMA)
    @require_login
    def get(cls, **kwargs):
        params = Struct(**kwargs)
        result = MarginOrderLoanOperation.get_loan_history(
            g.user.id,
            kwargs.get("account_id", None),
            kwargs.get("order_id", None),
            kwargs.get("status", None),
            params.page,
            params.limit
        )
        return result


@ns.route('/loan/renew')
@respond_with_code
class LoanRenewResource(Resource):

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.MARGIN])
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(dict(
        loan_id=fields.Integer(required=True),
        renew=fields.Boolean(required=True)
    ))
    def post(cls, **kwargs):
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        db_record = MarginLoanOrder.query.filter(MarginLoanOrder.id == kwargs['loan_id']).first()
        if not db_record:
            raise MarginLoanOrderNotFound
        db_record.is_renew = kwargs['renew']
        db.session.commit()


@ns.route('/flat')
@respond_with_code
class FlatResource(Resource):
    POST_SCHEMA = dict(
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID < x < MAX_ORDER_ACCOUNT_ID),
        coin_type=fields.String(validate=lambda x: x.isupper(), required=True),
        loan_id=fields.Integer(),
        amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                    rounding=decimal.ROUND_DOWN, required=True),
    )

    @classmethod
    @require_login
    @ns.use_kwargs(POST_SCHEMA)
    def post(cls, **kwargs):
        user_id = g.user.id
        data = Struct(**kwargs)
        account_id = data.account_id
        with CacheLock(LockKeys.margin_loan_or_flat(user_id), wait=False),\
             CacheLock(LockKeys.user_margin_account(user_id, account_id), wait=False):
            if not g.auth_user.has_2fa:
                raise TwoFactorAuthenticationRequired
            db.session.rollback()
            setting = UserSettings(g.user.id)
            if account_id in setting.forbidden_margin_flat:
                raise MarginBurstForbiddenFlat
            if data.amount < Decimal('0.********'):
                raise InvalidArgument(data.amount)
            tool = MarginOrderFlatOperation(user_id=user_id,
                                            account_id=account_id,
                                            asset=data.coin_type)
            pay_off_amount = tool.get_unflat_with_interest_amount
            margin_rule_config = MarginAssetRuleCache(data.coin_type).dict
            min_loan_amount = margin_rule_config['min_loan']
            # 剩余待还
            remain_asset_balance = pay_off_amount - data.amount
            # 剩余待还 >= 最小借币数 或者 剩余待还=0 则正常还币
            # 0 < 剩余待还 < 最小借币数 则不允许还币
            if 0 < remain_asset_balance < min_loan_amount:
                raise InvalidArgument(message=_(
                    f"还币后剩余待还数不能低于%(min_amount)s%(coin_type)s,请调整还币数量.",
                    min_amount=min_loan_amount,
                    coin_type=data.coin_type
                ))
            if remain_asset_balance < 0:
                raise MarginFlatAmountLimit
            max_amount = tool.get_can_flat_amount()
            if max_amount < data.amount:
                raise MarginFlatAmountLimit
            if data.loan_id:
                tool.flat_one_normal_order(data.loan_id, data.amount)
            else:
                tool.flat_normal_orders(data.amount)

            # 添加还币完成之后欠款状态检查以及状态更新
            tool.check_user_arrers()
            report_flat_event(user_id)


@ns.route('/flat/history/<int:loan_id>')
@respond_with_code
class LoanOrderFlatHistoryResource(Resource):

    @classmethod
    @require_login
    def get(cls, loan_id):
        records = MarginFlatHistory.query.filter(
            MarginFlatHistory.user_id == g.user.id,
            MarginFlatHistory.margin_loan_order_id == loan_id,
            MarginFlatHistory.status == MarginFlatHistory.StatusType.SUCCESS)
        data = [
            {"update_time": record.updated_at,
             "amount": amount_to_str(record.amount, 8),
             "flat_type": record.flat_type}
            for record in records
        ]
        return data


@ns.route('/flat/history')
@respond_with_code
class FlatHistoryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            page=PageField(),
            limit=LimitField(),
            market=fields.String(),
            flat_type=EnumField(MarginFlatHistory.FlatType, enum_by_value=True)
        )
    )
    def get(cls, **kwargs):
        q = MarginFlatHistory.query.filter(
            MarginFlatHistory.user_id == g.user.id,
            MarginFlatHistory.status == MarginFlatHistory.StatusType.SUCCESS
        ).order_by(
            MarginFlatHistory.id.desc()
        )
        page, limit = kwargs['page'], kwargs["limit"]
        if "market" in kwargs:
            market = kwargs["market"]
            if market not in MarginAccountIdCache.list_all_markets().values():
                raise InvalidArgument
            q = q.filter(MarginFlatHistory.market_name == market)
        if "flat_type" in kwargs:
            q = q.filter(MarginFlatHistory.flat_type == kwargs["flat_type"])
        records = q.paginate(page, limit, error_out=False)
        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(cls._flat_history_to_dict, records.items)),
            total=records.total,
            total_page=records.pages
        )

    @classmethod
    def _flat_history_to_dict(cls, record: MarginFlatHistory) -> dict:
        return {
            "update_time": record.updated_at,
            "amount": amount_to_str(record.amount, PrecisionEnum.COIN_PLACES),
            "flat_type": record.flat_type,
            "asset": record.asset,
            "market": record.market_name
        }


@ns.route('/unflat')
@respond_with_code
class MarginLoanUnflatAmountResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID < x < MAX_ORDER_ACCOUNT_ID
        ),
        market_type=fields.String(required=True),
        side=internal_fields.IntEnumField(enum=OrderSideType,
                                          example='sell',
                                          description='order type, "sell" or "buy"',
                                          required=True),
        price=internal_fields.PositiveDecimalField(description='order price',
                                                   example='10.34')
    ))
    def get(cls, **kwargs):
        market = kwargs['market_type']
        side = kwargs['side']
        price = kwargs.get('price')
        if market not in MarketCache.list_online_markets():
            raise InvalidArgument
        client = ServerClient(current_app.logger)
        if not price:
            price = client.market_last(market)
        market_cache = MarketCache(market).dict
        if side == OrderSideType.BUY:
            coin_type = market_cache['base_asset']
        else:
            coin_type = market_cache['quote_asset']
        operation = MarginOrderFlatOperation(g.user.id,
                                             kwargs['account_id'],
                                             coin_type)
        unflat_amount = operation.get_unflat_with_interest_amount
        balances = client.get_user_balances(g.user.id, coin_type, account_id=kwargs['account_id'])
        total_amount = Decimal()
        if coin_type in balances:
            total_amount = balances[coin_type]['available'] + balances[coin_type]['frozen']
        rates = FeeFetcher(g.user.id).fetch(TradeBusinessType.SPOT, market)
        fee_rate = max(rates.values())
        fee_option = OrderFeeOption(market_cache, g.user.id)
        fee_discount = fee_option.fee_discount
        amount = unflat_amount - total_amount
        # 如果开启费率抵扣并且所得币种不是CET
        if fee_discount < Decimal(1) and coin_type != 'CET':
            fee_rate = Decimal()

        if side == OrderSideType.BUY:
            pending_amount = amount * (Decimal('1') + fee_rate)
        else:
            pending_amount = amount / (price * (Decimal('1') - fee_rate))
        val = max(Decimal(), pending_amount)
        return dict(amount=amount_to_str(val, 8))

