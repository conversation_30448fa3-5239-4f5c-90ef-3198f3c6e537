# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from decimal import Decimal, ROUND_DOWN
from datetime import timedelta

from flask import g
from flask_babel import gettext

from app.api.common.request import require_user_request_permission
from app.common import PrecisionEnum
from app.exceptions.basic import InvalidArgument, TwoFactorAuthenticationRequired
from app.models.fixed_investment import (
    FixedInvestmentProduct,
    FixedInvestmentRecord,
    UserFixedInvestmentSummary,
    FixedInvestmentOperateHistory,
)
from app.business import PriceManager
from app.business.fixed_investment import FixedInvestOperator
from app.caches.fixed_investment import FixedInvestAssetRankCache, FixedInvestAssetProductsCache
from app.api.common import Namespace, Resource, respond_with_code, require_login
from app.api.common.fields import mm_fields, PageField, LimitField, EnumField, PositiveDecimalField
from app.utils import amount_to_str


url_prefix = '/fixed-invest'
ns = Namespace('Fixed-Investment')


class FixedInvestApiMixin:

    @classmethod
    def format_product_name(cls, asset: str, lock_days: str) -> str:
        return gettext("%(asset)s-%(lock_days)s天", asset=asset, lock_days=lock_days)

    @classmethod
    def get_product_map(cls, product_ids: set[int]) -> dict[int, FixedInvestmentProduct]:
        rows = FixedInvestmentProduct.query.filter(
            FixedInvestmentProduct.id.in_(product_ids),
        ).with_entities(
            FixedInvestmentProduct.id,
            FixedInvestmentProduct.total_amount,
            FixedInvestmentProduct.subscribed_amount,
        ).all()
        return {i.id: i for i in rows}


@ns.route('/assets')
@respond_with_code
class FixedInvestAssetListResource(Resource):

    @classmethod
    def get(cls):
        """定期理财-币种列表"""
        return FixedInvestAssetRankCache('').read()


@ns.route('/products')
@respond_with_code
class FixedInvestAssetProductsResource(Resource):

    @classmethod
    def fill_extra_fields(cls, items: list[dict]):
        product_ids = set()
        for item in items:
            for p in item['products']:
                product_ids.add(p['product_id'])
        if product_ids:
            p_row_map = FixedInvestApiMixin.get_product_map(product_ids)
        else:
            p_row_map = {}

        default_extra_field_map = {
            'product_name': '',
            'is_sold_out': False,  # 是否已售罄
        }

        for item in items:
            for p in item['products']:
                p: dict
                p.update(default_extra_field_map)
                p['product_name'] = FixedInvestApiMixin.format_product_name(item['asset'], p['lock_days'])
                p_row = p_row_map.get(p['product_id'])
                if not p_row:
                    continue
                p['total_amount'] = p_row.total_amount
                p['subscribed_amount'] = min(p_row.total_amount, p_row.subscribed_amount)
                if p_row.subscribed_amount >= p_row.total_amount:
                    p['is_sold_out'] = True

    @classmethod
    def get(cls):
        """定期理财-产品列表"""
        items = []
        cache_data_map = FixedInvestAssetProductsCache().read()
        for asset_, product_data in cache_data_map.items():
            item = {
                "asset": asset_,
                "products": json.loads(product_data),
            }
            items.append(item)
        cls.fill_extra_fields(items)

        # sort apr_desc
        items.sort(key=lambda x: max([Decimal(i["base_apr"]) for i in x["products"]] or [Decimal(0)]), reverse=True)

        pag_res = dict(
            has_next=False,
            curr_page=1,
            count=len(items),
            data=items,
            total=1,
            total_page=1,
        )
        return pag_res


@ns.route("/subscribe")
@respond_with_code
class FixedInvestProductSubscribeResource(Resource):
    @classmethod
    def check_and_get_product(cls, product_id: int, allow_offline: bool = False) -> FixedInvestmentProduct:
        product: FixedInvestmentProduct = FixedInvestmentProduct.query.get(product_id)
        if not product:
            raise InvalidArgument
        valid_status_list = [FixedInvestmentProduct.Status.ONLINE]
        if allow_offline:
            valid_status_list.append(FixedInvestmentProduct.Status.OFFLINE)
        if product.status not in valid_status_list:
            raise InvalidArgument
        return product

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            product_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
        )
    )
    def get(cls, **kwargs):
        """定期产品-获取剩余申购数目"""
        user = g.user
        user_id = user.id

        product_id = kwargs["product_id"]
        product = cls.check_and_get_product(product_id)

        remaining_subscription_amount = FixedInvestOperator.get_user_remaining_subscription_amount(user_id=user_id, product=product)

        return {
            "remaining_subscription_amount": remaining_subscription_amount,
        }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            product_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
            amount=PositiveDecimalField(places=PrecisionEnum.PRICE_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """定期产品-申购操作"""
        user = g.user
        if not user.has_2fa:
            raise TwoFactorAuthenticationRequired

        require_user_request_permission(user)

        amount = kwargs["amount"]
        product_id = kwargs["product_id"]
        product = cls.check_and_get_product(product_id)
        record, op_his = FixedInvestOperator.subscribe(user, amount, product)

        return FixedInvestRecordsResource.format_item(record)


@ns.route("/redeem")
@respond_with_code
class FixedInvestProductRedeemResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            record_id=mm_fields.Integer(required=True, validate=lambda x: x > 0),
        )
    )
    def post(cls, **kwargs):
        """定期产品-赎回操作"""
        user = g.user
        user_id = user.id

        record_id = kwargs["record_id"]
        record: FixedInvestmentRecord = FixedInvestmentRecord.query.filter(
            FixedInvestmentRecord.id == record_id,
            FixedInvestmentRecord.user_id == user_id,
        ).first()
        if not record:
            raise InvalidArgument
        if record.status != FixedInvestmentRecord.Status.HOLDING:
            raise InvalidArgument

        # 已下架产品的未到期的存单仍可提前赎回
        product_id = record.product_id
        product = FixedInvestProductSubscribeResource.check_and_get_product(product_id, allow_offline=True)

        record, op_his = FixedInvestOperator.early_redeem(record, product)

        return {
            "redeem_id": op_his.id,
        }


@ns.route('/records')
@respond_with_code
class FixedInvestRecordsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            asset=mm_fields.String(required=False),
            status=EnumField(FixedInvestmentRecord.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """定期理财-用户申购记录"""
        user = g.user
        user_id = user.id

        model = FixedInvestmentRecord
        q = model.query.filter(
            model.user_id == user_id,
        ).order_by(model.id.desc())
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if asset_ := kwargs.get("asset"):
            q = q.filter(model.asset == asset_)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: list[model] = pagination.items
        items = [cls.format_item(r) for r in rows]

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def format_item(cls, r: FixedInvestmentRecord) -> dict:
        expected_redeem_time = r.created_at + timedelta(days=r.lock_days)
        item = dict(
            record_id=r.id,
            created_at=int(r.created_at.timestamp()),
            product_name=FixedInvestApiMixin.format_product_name(r.asset, r.lock_days),
            asset=r.asset,
            amount=r.amount,
            lock_days=r.lock_days,
            expected_interest_amount=r.expected_interest_amount if r.status == FixedInvestmentRecord.Status.HOLDING else r.interest_amount,
            base_apr=r.base_apr,
            subsidy_apr=r.subsidy_apr,
            status=r.status.name,
            redeem_time=int(r.redeem_time.timestamp()) if r.redeem_time else int(expected_redeem_time.timestamp()),
        )
        return item


@ns.route("/history")
@respond_with_code
class FixedInvestOpHistoryResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            asset=mm_fields.String(required=False),
            type=EnumField(FixedInvestmentOperateHistory.Type),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """定期产品-用户操作记录"""
        user = g.user
        user_id = user.id

        model = FixedInvestmentOperateHistory
        q = model.query.filter(
            model.user_id == user_id,
        ).order_by(model.id.desc())
        if asset_ := kwargs.get("asset"):
            q = q.filter(model.asset == asset_)
        if type_ := kwargs.get("type"):
            q = q.filter(model.type == type_)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        rows: list[model] = pagination.items

        items = []
        for r in rows:
            if r.status in [model.Status.CREATED, model.Status.DEDUCTED]:
                status_name = model.Status.CREATED.name
            else:
                status_name = r.status.name
            items.append(
                dict(
                    created_at=int(r.created_at.timestamp()),
                    asset=r.asset,
                    amount=r.amount,
                    type=r.type.name,
                    status=status_name,
                )
            )

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route("/profit-summary")
@respond_with_code
class FixedInvestProfitSummaryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """定期产品-收益汇总信息"""
        user = g.user
        user_id = user.id

        sum_rows: list[UserFixedInvestmentSummary] = UserFixedInvestmentSummary.query.filter(
            UserFixedInvestmentSummary.user_id == user_id,
        ).with_entities(
            UserFixedInvestmentSummary.asset,
            UserFixedInvestmentSummary.total_interest_amount,
        ).all()
        holding_record_rows: list[FixedInvestmentRecord] = FixedInvestmentRecord.query.filter(
            FixedInvestmentRecord.user_id == user_id,
            FixedInvestmentRecord.status == FixedInvestmentRecord.Status.HOLDING,
        ).with_entities(
            FixedInvestmentRecord.asset,
            FixedInvestmentRecord.amount,
            FixedInvestmentRecord.base_apr,
            FixedInvestmentRecord.subsidy_apr,
            FixedInvestmentRecord.lock_days,
        ).all()

        expected_profit_amount_map = defaultdict(Decimal)
        for r in holding_record_rows:
            expected_interest_amount = FixedInvestmentRecord.calc_interest_amount(r.amount, r.base_apr + r.subsidy_apr, r.lock_days)
            expected_profit_amount_map[r.asset] += expected_interest_amount

        assets = {i.asset for i in sum_rows} | set(expected_profit_amount_map.keys())
        asset_prices = PriceManager.assets_to_usd(assets)
        total_profit_usd = sum([asset_prices.get(i.asset, 0) * i.total_interest_amount for i in sum_rows])
        expected_profit_usd = sum([asset_prices.get(k, 0) * v for k, v in expected_profit_amount_map.items()])

        return {
            "total_profit_usd": amount_to_str(total_profit_usd, 8),
            "expected_profit_usd": amount_to_str(expected_profit_usd, 8),
        }
