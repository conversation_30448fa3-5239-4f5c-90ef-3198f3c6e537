# -*- coding: utf-8 -*-
from decimal import Decimal

from flask import g
from webargs import fields

from ..common import (Namespace, Resource, respond_with_code, require_login,
                      get_request_platform, json_string_success
                      )
from ..common.decorators import require_geetest, require_2fa, \
    require_email_code, limit_user_frequency
from ..common.fields import (EnumField, PageField,
                             LimitField, TimestampField)
from ..common.fields import PositiveDecimalField
from ..common.request import require_user_request_permission, RequestPermissionCheck
from ... import Language
from ...business import CacheLock, LockKeys
from ...business.red_packet.constants import validate_c_box_code
from ...business.red_packet.grab import get_grab_pub_sign_data, verify_grab_data, \
    get_user_receive_info, ReceiveEmailCBoxManager, ReceiveCodeCBoxManager, \
    get_red_packet_info
from ...business.red_packet.send import SendEmailCBoxManager, \
    SendCodeCBoxManager, CBoxCodeManager, get_c_box_themes, user_has_new_user_conf, \
    get_send_hot_coins, user_has_withdrawal_approver, user_send_limit_usd, get_today_red_packet_amount
from ...business.red_packet.send import get_all_coin_types_rate, \
    check_user_in_whitelist, get_user_send_info
from ...caches.operation import CBoxPromotionCache
from ...common import MobileCodeType
from ...models import (User, RedPacket, CBoxPromotion)

url_prefix = '/c-box'

ns = Namespace('CBOX')


@ns.route('/by_email')
@respond_with_code
class EmailCBoxResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.SEND_C_BOX, allow_sub_account=False)
    @require_email_code
    @ns.use_kwargs(dict(
        coin_type=fields.String(required=True),
        c_box_type=EnumField(
            RedPacket.PacketType, enum_by_value=True, required=True),
        total_amount=PositiveDecimalField(required=True),
        count=fields.Integer(validate=lambda x: x > 0, required=True),
        greeting=fields.String(required=True, validate=lambda x: len(x) <= 32),
        theme_id=fields.Integer(required=True),
        only_new_user=fields.Boolean(missing=False),
        valid_days=fields.Integer(validate=lambda x: x in RedPacket.VALID_DAYS_LIST, missing=2)
    ))
    def post(cls, **kwargs):
        """
        发邮箱c-box
        """
        user: User = g.user
        require_user_request_permission(user)
        manager = SendEmailCBoxManager(user, kwargs['c_box_type'], kwargs['coin_type'],
                                       kwargs['total_amount'], kwargs['count'],
                                       kwargs['greeting'],
                                       kwargs['theme_id'], kwargs.get('only_new_user'), kwargs['valid_days'])
        return manager.send()

    @classmethod
    @require_geetest
    @ns.use_kwargs(dict(
        c_box_id=fields.String(required=True),
        body=fields.String(required=True),
        sign=fields.String(required=True),
    ))
    def patch(cls, **kwargs):
        """领取邮箱c-box"""
        require_user_request_permission(None, [RequestPermissionCheck.IP])
        return ReceiveEmailCBoxManager.receive(
            kwargs['c_box_id'], kwargs['sign'], kwargs['body'])


@ns.route('/by_code')
@respond_with_code
class CodeCBoxResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.SEND_C_BOX, allow_sub_account=False)
    @require_email_code
    @ns.use_kwargs(dict(
        coin_type=fields.String(required=True),
        c_box_type=EnumField(
            RedPacket.PacketType, enum_by_value=True, required=True),
        total_amount=PositiveDecimalField(required=True),
        count=fields.Integer(validate=lambda x: x > 0, required=True),
        greeting=fields.String(required=True, validate=lambda x: len(x) <= 32),
        theme_id=fields.Integer(required=True),
        code=fields.String(validate=validate_c_box_code, required=True,
                           error_messages={"validator_failed": "invalid code！"}),
        valid_days=fields.Integer(validate=lambda x: x in RedPacket.VALID_DAYS_LIST, missing=2)
    ))
    def post(cls, **kwargs):
        """
        发口令c-box
        """
        user: User = g.user
        require_user_request_permission(user)
        manager = SendCodeCBoxManager(user, kwargs['c_box_type'], kwargs['coin_type'],
                                      kwargs['total_amount'], kwargs['count'],
                                      kwargs['greeting'],
                                      kwargs['theme_id'], kwargs['code'].upper(), kwargs['valid_days'])
        return manager.send()

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        code=fields.String(validate=validate_c_box_code, required=True,
                           error_messages={"validator_failed": "invalid code！"})
    ))
    def patch(cls, **kwargs):
        """领取口令c-box"""
        require_user_request_permission(g.user)
        user = g.user
        return ReceiveCodeCBoxManager.receive(user, kwargs['code'].upper())

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        code=fields.String(validate=validate_c_box_code, required=True,
                           error_messages={"validator_failed": "invalid code！"})
    ))
    def get(cls, **kwargs):
        """获取口令对应的c-box信息"""
        require_user_request_permission(g.user)
        user = g.user
        return ReceiveCodeCBoxManager.get_red_packet_status(user, kwargs['code'].upper())


@ns.route('/code/available')
@respond_with_code
class CBoxCodeAvailableResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=20, interval=60)
    @ns.use_kwargs(
        dict(
            code=fields.String(validate=validate_c_box_code, required=True,
                               error_messages={"validator_failed": "invalid code！"})
        )
    )
    def get(cls, **kwargs):
        """校验口令是否可用"""
        user = g.user
        status = CBoxCodeManager.check_code_available(kwargs['code'].upper(), user.id)
        return {'status': status}


@ns.route('/code')
@respond_with_code
class CBoxCodeResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=10, interval=60)
    def post(cls):
        """生成口令"""
        user = g.user
        code = CBoxCodeManager.generate_c_box_code(user.id)
        return {'code': code}


@ns.route('/themes')
@respond_with_code
class CBoxThemeResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        version=fields.Integer(missing=1)
    ))
    def get(cls, **kwargs):
        """获取主题列表"""
        ret = get_c_box_themes(g.lang, kwargs['version'])
        return ret


# noinspection PyUnresolvedReferences
@ns.route('/info/<string:encrypt_c_box_id>')
@respond_with_code
class CBoxInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, encrypt_c_box_id, **kwargs):
        """
        c-box信息
        """
        return get_red_packet_info(encrypt_c_box_id, kwargs['page'], kwargs['limit'])


@ns.route('/conf')
@respond_with_code
class CBoxConfResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """获取用户是否在admin配置了仅限新用户领取及是否设置了提现多人审核"""
        user_id = g.user.id
        new_user_conf = user_has_new_user_conf(user_id)
        withdrawal_approver = user_has_withdrawal_approver(user_id)
        return {'has_new_user_conf': new_user_conf, 'has_withdrawal_approver': withdrawal_approver}


@ns.route('/verify')
@respond_with_code
class ReceiveVerifyResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        c_box_id=fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """
        获取c-box验证信息
        """
        return get_grab_pub_sign_data(kwargs['c_box_id'])

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        c_box_id=fields.String(required=True),
        body=fields.String(required=True),
        sign=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """
        验证是否已领c-box
        """
        with CacheLock(LockKeys.grab_red_packet(g.user.id), wait=False):
            verify_grab_data(
                kwargs['c_box_id'], kwargs['sign'], kwargs['body'])


@ns.route('/user/send_history')
@respond_with_code
class UserSendCBoxListResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        start_time=TimestampField(),
        end_time=TimestampField(),
        coin_type=fields.String(missing=None),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        获取c-box发送记录
        """
        return get_user_send_info(
            g.user.id, kwargs['coin_type'], kwargs.get('start_time'),
            kwargs.get('end_time'), kwargs['page'], kwargs['limit'])


@ns.route('/user/receive_history')
@respond_with_code
class UserReceiveCBoxListResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        start_time=TimestampField(),
        end_time=TimestampField(),
        coin_type=fields.String(missing=None),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """
        获取领取c-box记录
        """
        return get_user_receive_info(
            g.user.id, kwargs['coin_type'], kwargs.get('start_time'),
            kwargs.get('end_time'), kwargs['page'], kwargs['limit'])


@ns.route('/rate')
@respond_with_code
class CBoxRateResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """
        c-box汇率，剩余额度
        """
        user_id = g.user.id
        rates = get_all_coin_types_rate()
        hot_coins = get_send_hot_coins()
        limit_usd = user_send_limit_usd(user_id)
        send_usd = get_today_red_packet_amount(user_id)
        if send_usd >= limit_usd:
            rest = Decimal()
        else:
            rest = limit_usd - send_usd
        return dict(
            rates=rates,
            hot_coins=hot_coins,
            whitelist=check_user_in_whitelist(user_id),
            rest=rest,
            limit=limit_usd,
            send=send_usd
        )


@ns.route('/send-limit-info')
@respond_with_code
class CBoxSendLimitResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """
        c-box已发、剩余额度
        """
        user_id = g.user.id
        limit_usd = user_send_limit_usd(user_id)
        send_usd = get_today_red_packet_amount(user_id)
        if send_usd >= limit_usd:
            rest = Decimal()
        else:
            rest = limit_usd - send_usd
        return dict(
            rest=rest,
            limit=limit_usd,
            send=send_usd
        )


@ns.route('/promotion')
@respond_with_code
class CBoxPromotionResource(Resource):

    @classmethod
    def get(cls):
        """c-box活动区推广展示"""
        platform = get_request_platform()
        lang = g.get('lang')
        if platform.is_ios():
            cache_platform = CBoxPromotion.Platform.IOS
        elif platform.is_android():
            cache_platform = CBoxPromotion.Platform.ANDROID
        elif platform.is_web():
            cache_platform = CBoxPromotion.Platform.WEB
        else:
            cache_platform = None
        if not lang or not cache_platform:
            return []

        if data := CBoxPromotionCache(cache_platform, Language(lang)).read():
            return json_string_success(data)
