# -*- coding: utf-8 -*-

import json
from datetime import timedelta, datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from flask import g
from flask_babel import gettext
from flask_restx import fields, marshal
from sqlalchemy import and_, or_
from webargs import fields as wa_fields

from app.config import config
from app.models.amm import UserLiquidityMarketIncomeDetail, UserLiquidityProfit
from ..common import (
    Namespace,
    Resource,
    require_login,
    respond_with_code,
    get_request_user,
    require_user_permission,
)
from ..common.fields import (AmountField, EnumMarshalField, LimitField,
                             PageField, PositiveDecimalField,
                             TimestampMarshalField, EnumField, TimestampField)
from ..common.request import require_user_request_permission
from ...business import ServerClient, cached
from ...business import mem_cached
from ...business.amm import (LiquidityService, add_liquidity, remove_liquidity,
                             update_liquidity_rank_task, update_liquidity_pool_cache_task)
from ...business.clients.biz_monitor import biz_monitor
from ...business.credit import credit_user_has_unflat_asset
from ...business.user import UserSettings
from ...caches import LiquidityRankCache, MarketCache, LiquidityPoolCache, AmmMarketCache
from ...caches.amm import LiquidityPoolAmountCache, LiquiditySeriesCache, AmmOverViewCache
from ...caches.system import MarketMaintainCache
from ...common import Language, BalanceEvent, SubAccountPermission
from ...exceptions import (InsufficientBalance,
                           InsufficientLiquidity, InvalidArgument,
                           LessThanMinLiquidity, LiquidityFluctuate,
                           LiquidityLocked, TransferNotAllowed,
                           AMMWithdrawalForbidden)
from ...models import LiquidityHistory, LiquidityLock, UserLiquidity, \
    LiquiditySlice, UserLiquiditySlice, UserLiquidityIncomeSummary, AmmMarket
from ...utils import quantize_amount, export_xlsx, amount_to_str, \
    quantize_amount_non_zero
from ...utils.date_ import today

ns = Namespace('AMM')


@ns.route('/markets')
@respond_with_code
class AmmMarketsResource(Resource):

    @classmethod
    def get(cls):
        markets = AmmMarketCache.list_amm_markets()
        bidding_markets = AmmMarketCache.list_allow_bidding_markets()

        return dict(markets=markets, bidding_markets=bidding_markets)


@ns.route('/liquidity/pool')
@respond_with_code
class LiquidityPoolResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        if not AmmMarketCache.has(kwargs['market']):
            raise InvalidArgument
        srv = LiquidityService(kwargs['market'])
        base_amount, quote_amount = srv.get_assets_amount()
        deal_info = srv.get_recent_deal_sum(days=7)
        if (days := deal_info['days']) == 0:
            profit_rate = Decimal()
        else:
            profit_rate = srv.calc_profit_rate(deal_info['fee_base_amount'] / days,
                                               deal_info['fee_quote_amount'] / days,
                                               base_amount, quote_amount)

        deal_info_last = srv.get_recent_deal_sum(days=1)
        if (deal_info_last['days']) == 0:
            profit_rate_last = Decimal()
        else:
            profit_rate_last = srv.calc_profit_rate(deal_info_last['fee_base_amount'],
                                                    deal_info_last['fee_quote_amount'],
                                                    base_amount, quote_amount)

        ret = dict(
            liquidity=srv.pool.liquidity,
            base_amount=base_amount,
            quote_amount=quote_amount,
            base_asset=srv.market['base_asset'],
            quote_asset=srv.market['quote_asset'],
            profit_rate=profit_rate,
            profit_rate_last=profit_rate_last,
            deal_usd=deal_info['deal_usd'],
            fee_usd=deal_info['fee_usd'],
            refund_fee_usd=deal_info['refund_fee_usd'],
            fee_base_amount=deal_info['refund_fee_base_amount'],
            fee_quote_amount=deal_info['refund_fee_quote_amount'],
            fee_refund_rate=srv.fee_refund_rate,
            amm_type=srv.amm_market.amm_type.name,
        )
        if srv.amm_market.amm_type == AmmMarket.AmmType.FINITE:
            ret['min_price'] = srv.amm_market.min_price
            ret['max_price'] = srv.amm_market.max_price
        return ret


@ns.route('/liquidity/pool/price')
@respond_with_code
class LiquidityPoolPriceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        """accquire reatime pool price when adding/removing liquidity."""
        if not AmmMarketCache.has(kwargs['market']):
            raise InvalidArgument
        srv = LiquidityService(kwargs['market'])
        base_amount, quote_amount = srv.get_assets_amount(use_cache=False)
        pool_price = srv.get_pool_price(base_amount, quote_amount)
        return dict(
            liquidity=srv.pool.liquidity,
            base_amount=base_amount,
            quote_amount=quote_amount,
            base_asset=srv.market['base_asset'],
            quote_asset=srv.market['quote_asset'],
            pool_price=pool_price
        )


@ns.route('/liquidity')
@respond_with_code
class LiquidityResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        market_name = kwargs['market']
        if not AmmMarketCache.has(market_name):
            raise InvalidArgument
        srv = LiquidityService(market_name)
        row = UserLiquidity.query.filter(
            UserLiquidity.user_id == g.user.id,
            UserLiquidity.market == market_name
        ).first()
        if not row:
            liq = base_amount = quote_amount = Decimal()
        else:
            liq = row.liquidity
            base_amount, quote_amount = srv.liquidity_to_asset_amounts(liq)

        if srv.pool.liquidity > 0:
            share = quantize_amount(liq / srv.pool.liquidity, 6)
        else:
            share = Decimal()

        return dict(
            liquidity=liq,
            share=share,
            base_amount=base_amount,
            quote_amount=quote_amount,
            base_asset=srv.market['base_asset'],
            quote_asset=srv.market['quote_asset'],
        )

    @classmethod
    @require_login
    @require_user_permission(sub_account_permissions=[SubAccountPermission.AMM])
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        base_amount=PositiveDecimalField(required=True, allow_zero=True),
        quote_amount=PositiveDecimalField(required=True, allow_zero=True)
    ))
    def post(cls, **kwargs):
        require_user_request_permission(g.user)
        if credit_user_has_unflat_asset(g.user):
            raise TransferNotAllowed

        market_name = kwargs['market']
        base_amount = kwargs['base_amount']
        quote_amount = kwargs['quote_amount']
        if not AmmMarketCache.has(market_name):
            raise InvalidArgument
        market_maintains = MarketMaintainCache.get_market_maintains()
        if market_name in market_maintains:
            raise InvalidArgument(
                message=gettext(
                    "%(market)s市场暂停交易中，无法增加流动性",
                    market=market_name,
                )
            )

        srv = LiquidityService(market_name)
        srv.validate_market()

        # pre-check
        result = ServerClient().get_user_balances(g.user.id)
        balance_base = balance_quote = Decimal()
        if (balance := result.get(srv.market['base_asset'])):
            balance_base = balance.get('available', Decimal())
        if (balance := result.get(srv.market['quote_asset'])):
            balance_quote = balance.get('available', Decimal())

        pool_base, pool_quote = srv.get_assets_amount(use_cache=False)
        _base_amount, _quote_amount = srv.calc_real_liquidity_amounts(
                                        base_amount, quote_amount,
                                        pool_base, pool_quote,
                                        balance_base, balance_quote)
        if _base_amount > balance_base or _quote_amount > balance_quote:
            raise InsufficientBalance
        if not srv.test_min_liquidity(_base_amount, _quote_amount):
            # 这里提示的为双边市值
            raise LessThanMinLiquidity(amount=srv.MIN_LIQUIDITY_USD*2)
        if not srv.test_price_fluctuation(base_amount, quote_amount,
                                          _base_amount, _quote_amount):
            raise LiquidityFluctuate

        history = add_liquidity(market_name, g.user.id, base_amount, quote_amount)
        srv.add_limit_cache(g.user.id)
        LiquidityPoolAmountCache(market_name).delete()
        update_liquidity_rank_task.delay(market_name)
        update_liquidity_pool_cache_task.delay(market_name)

        biz_monitor.increase_counter(BalanceEvent.ADD_LIQUIDITY)

        return dict(
            base_amount=history.base_amount,
            quote_amount=history.quote_amount,
            liquidity=history.liquidity
        )


    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        market_name = kwargs['market']
        if not AmmMarketCache.has(market_name):
            raise InvalidArgument
        if not UserSettings(g.user.id).amm_withdrawals_enabled:
            raise AMMWithdrawalForbidden
        srv = LiquidityService(market_name)
        srv.validate_market()

        if LiquidityLock.locked(market_name, g.user.id):
            raise LiquidityLocked
        row = UserLiquidity.query.filter(
            UserLiquidity.market == market_name,
            UserLiquidity.user_id == g.user.id
        ).first()
        if not row or row.liquidity <= 0:
            raise InsufficientLiquidity

        srv.check_for_remove_liquidity(g.user.id)

        history = remove_liquidity(market_name, g.user.id)

        LiquidityPoolAmountCache(market_name).delete()
        update_liquidity_rank_task.delay(market_name)
        update_liquidity_pool_cache_task.delay(market_name)

        biz_monitor.increase_counter(BalanceEvent.REMOVE_LIQUIDITY)

        return dict(
            base_amount=history.base_amount,
            quote_amount=history.quote_amount,
            liquidity=history.liquidity
        )


@ns.route('/accounts')
@respond_with_code
class AccountsResource(Resource):

    @classmethod
    def get(cls):
        data = cls._get_pool_data()
        if not data:
            return []

        # 允许未登录态访问
        if user := get_request_user():
            rows = UserLiquidity.query.filter(
                UserLiquidity.user_id == user.id,
                UserLiquidity.liquidity > 0
            ).all()
            liqs = {x.market: x.liquidity for x in rows}
            user_fee_data, user_total_fee_data = cls._get_user_fee_usd(user.id)
        else:
            liqs = dict()
            user_fee_data = dict()
            user_total_fee_data = dict()

        result = []
        for pool in data:
            if not (liq := liqs.get(pool['market'])):
                liq = base_amount = quote_amount = share = Decimal()
            else:
                total_liq = Decimal(pool['liquidity'])
                pool_base, pool_quote = Decimal(pool['base_amount']), Decimal(pool['quote_amount'])
                if total_liq == 0:
                    liq = base_amount = quote_amount = share = Decimal()
                else:
                    share = liq / total_liq
                    base_amount = quantize_amount(pool_base * share, 8)
                    quote_amount = quantize_amount(pool_quote * share, 8)
                    share = quantize_amount(share, 6)

            result.append(dict(
                market=pool['market'],
                base_asset=pool['base_asset'],
                quote_asset=pool['quote_asset'],
                liquidity=liq,
                base_amount=base_amount,
                quote_amount=quote_amount,
                share=share,
                profit_rate=pool['profit_rate'],
                profit_rate_last=pool['profit_rate_last'],
                total_liquidity=pool['liquidity'],
                total_base_amount=pool['base_amount'],
                total_quote_amount=pool['quote_amount'],
                deal_usd=pool['deal_usd'],
                fee_usd=pool['fee_usd'],
                refund_fee_usd=pool['refund_fee_usd'],
                fee_base_amount=pool['refund_fee_base_amount'],
                fee_quote_amount=pool['refund_fee_quote_amount'],
                user_fee_usd=user_fee_data.get(pool["market"], Decimal('0')),
                user_total_fee_usd=user_total_fee_data.get(pool["market"], Decimal('0'))
            ))
        return sorted(
            result,
            key=lambda x: MarketCache.market_sort_func(x["base_asset"], x["quote_asset"])
        )

    @classmethod
    @mem_cached(60)
    def _get_pool_data(cls):
        if data := LiquidityPoolCache().read():
            return json.loads(data)
        return []

    @classmethod
    @cached(600)
    def _get_user_fee_usd(cls, user_id: int):
        records = UserLiquidityMarketIncomeDetail.query.filter(
            UserLiquidityMarketIncomeDetail.user_id == user_id,
        ).with_entities(
            UserLiquidityMarketIncomeDetail.market,
            UserLiquidityMarketIncomeDetail.daily_fee_usd,
            UserLiquidityMarketIncomeDetail.total_fee_usd
        ).all()
        market_today_fees = UserLiquidityProfit.query.filter(
            UserLiquidityProfit.user_id == user_id,
            UserLiquidityProfit.date == today(),
        ).with_entities(
            UserLiquidityProfit.market,
            UserLiquidityProfit.fee_usd,
        ).all()
        market_today_fee_map = {i.market: i.fee_usd for i in market_today_fees}
        yesterday_map, total_map = dict(), dict()
        for item in records:
            yesterday_map[item.market] = item.daily_fee_usd
            market_today_fee = market_today_fee_map.get(item.market, Decimal())
            total_map[item.market] = item.total_fee_usd + market_today_fee
        return yesterday_map, total_map


@ns.route('/liquidity/history')
@respond_with_code
class LiquidityHistoryResource(Resource):

    marshal_fields = {
        'time': TimestampMarshalField(attribute='created_at'),
        'market': fields.String,
        'business': EnumMarshalField(LiquidityHistory.Business),
        'business_name': EnumMarshalField(LiquidityHistory.Business,
                                          output_field_name=False,
                                          output_field_lower=False,
                                          use_translate=True,
                                          attribute='business'),
        'liquidity': AmountField,
        'liquidity_usd': AmountField,
        'base_amount': AmountField,
        'quote_amount': AmountField
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        scope=EnumField(['user', 'all']),
        market=wa_fields.String,
        business=EnumField(x.name.lower() for x in LiquidityHistory.Business),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        scope = kwargs.get('scope', 'user')
        query = LiquidityHistory.query.filter(
            LiquidityHistory.status == LiquidityHistory.Status.FINISHED
        ).order_by(LiquidityHistory.id.desc())
        if scope == 'user':
            query = query.filter(LiquidityHistory.user_id == g.user.id)
        if market_name := kwargs.get('market'):
            if not AmmMarketCache.has(market_name):
                raise InvalidArgument
            query = query.filter(LiquidityHistory.market == market_name)
        if business := kwargs.get('business'):
            query = query.filter(LiquidityHistory.business == business)

        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        items = marshal(pagination.items, cls.marshal_fields)

        for item in items:
            market = MarketCache(item['market']).dict
            item['base_asset'] = market['base_asset']
            item['quote_asset'] = market['quote_asset']

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages
        )


@ns.route('/liquidity/rank')
@respond_with_code
class LiquidityRankResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True)
    ))
    def get(cls, **kwargs):
        market_name = kwargs['market']
        if not AmmMarketCache.has(market_name):
            raise InvalidArgument
        data = LiquidityRankCache().hget(market_name)
        if not data:
            return []
        srv = LiquidityService(market_name)
        data = json.loads(data)
        pool_base, pool_quote = srv.get_assets_amount()
        for item in data:
            base_amount, quote_amount = srv.liquidity_to_asset_amounts(
                Decimal(item['liquidity']), pool_base, pool_quote)
            item['base_amount'] = base_amount
            item['quote_amount'] = quote_amount
            item['base_asset'] = srv.market['base_asset']
            item['quote_asset'] = srv.market['quote_asset']
        return data


@ns.route('/liquidity/slice')
@respond_with_code
class LiquiditySliceResource(Resource):

    export_marshal_fields = dict(
        date=fields.String(attribute=lambda x: x['date'].strftime("%Y-%m-%d")),
        market=fields.String,
        liquidity_usd=fields.String(attribute=lambda x: amount_to_str(x['liquidity_usd'], 2)),
        fee_usd=fields.String(attribute=lambda x: amount_to_str(x['fee_usd'], 2)),
        user_liquidity_usd=fields.String(attribute=lambda x: amount_to_str(x['user_liquidity_usd'], 2)),
        user_fee_usd=fields.String(attribute=lambda x: amount_to_str(x['user_fee_usd'], 2)),
        profit_rate=fields.String(attribute=lambda x: amount_to_str(x['profit_rate'] * 100, 4) + '%'),
    )

    export_headers = (
        {"field": "date", Language.ZH_HANS_CN: "日期", Language.EN_US: "Date"},
        {"field": "market", Language.ZH_HANS_CN: "交易对", Language.EN_US: "Trading Pair"},
        {"field": "liquidity_usd", Language.ZH_HANS_CN: "总流动性", Language.EN_US: "Total Liquidity"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "总手续费分红",
         Language.EN_US: "Tx Fee Dividend"},
        {"field": "user_liquidity_usd", Language.ZH_HANS_CN: "我的流动性",
         Language.EN_US: "My Liquidity"},
        {"field": "user_fee_usd", Language.ZH_HANS_CN: "我的分红",
         Language.EN_US: "My Dividend"},
        {"field": "profit_rate", Language.ZH_HANS_CN: "当日年化收益率",
         Language.EN_US: "Day APY"},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        market=wa_fields.String,
        scope=EnumField(['user', 'all']),
        start_date=TimestampField,
        end_date=TimestampField,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Bool(default=False, missing=False)
    ))
    def get(cls, **kwargs):
        market = kwargs.get('market')
        scope = kwargs.get('scope', 'user')
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        limit = kwargs['limit'] if not kwargs['export'] else config['EXPORT_ITEM_MAX_COUNT']
        if market and not AmmMarketCache.has(market):
            raise InvalidArgument
        if scope == 'user':
            res = cls.query_user_liquidity_slice(
                g.user.id, market, kwargs['page'], limit, start_date, end_date)
        else:
            res = cls.query_liquidity_slice(
                g.user.id, market, kwargs['page'], limit, start_date, end_date)
        if not kwargs['export']:
            return res

        export_data = marshal(res['data'], cls.export_marshal_fields)
        file_name = datetime.utcnow().strftime(
            '%Y%m%d-amm-market-income-summary')
        return export_xlsx(
            filename=file_name,
            data_list=export_data,
            export_headers=cls.export_headers
        )

    @classmethod
    def query_liquidity_slice(cls, user_id: int, market: Optional[str], page, limit, start_date, end_date):
        query = LiquiditySlice.query.order_by(LiquiditySlice.date.desc(), LiquiditySlice.market)
        if market:
            query = query.filter(LiquiditySlice.market == market)
        if start_date:
            query = query.filter(LiquiditySlice.date >= start_date)
        if end_date:
            query = query.filter(LiquiditySlice.date <= end_date)

        pagination = query.paginate(page, limit, error_out=False)
        items = pagination.items

        if items:
            user_slice_query = UserLiquiditySlice.query.filter(
                UserLiquiditySlice.user_id == user_id,
                or_(
                    *[and_(
                        UserLiquiditySlice.market == x.market,
                        UserLiquiditySlice.date == x.date
                    ) for x in items]
                )
            )
            profit_query = UserLiquidityProfit.query.filter(
                UserLiquidityProfit.user_id == user_id,
                or_(
                    *[and_(
                        UserLiquidityProfit.market == x.market,
                        UserLiquidityProfit.date == x.date,
                    ) for x in items]
                )
            )
            if start_date:
                user_slice_query = user_slice_query.filter(
                    UserLiquiditySlice.date >= start_date
                )
                profit_query = profit_query.filter(
                    UserLiquidityProfit.date >= start_date
                )
            if end_date:
                user_slice_query = user_slice_query.filter(
                    UserLiquiditySlice.date <= end_date
                )
                profit_query = profit_query.filter(
                    UserLiquidityProfit.date <= end_date
                )
            user_slices = user_slice_query.all()
            user_slices = {(x.date, x.market): x for x in user_slices}
            profits = profit_query.all()
            profit_map = {(x.date, x.market): x for x in profits}
        else:
            user_slices = {}
            profit_map = {}

        data = []
        for slice in items:
            user_slice = user_slices.get((slice.date, slice.market))
            profit = profit_map.get((user_slice.date, user_slice.market)) if user_slice else None
            data.append(cls.make_result(slice, user_slice, profit))

        return dict(
            data=data,
            count=len(pagination.items),
            curr_page=pagination.page,
            total=pagination.total,
            has_next=pagination.has_next,
            total_page=pagination.pages
        )

    @classmethod
    def query_user_liquidity_slice(cls, user_id: int, market: Optional[str], page, limit, start_date, end_date):
        query = UserLiquiditySlice.query.filter(
            UserLiquiditySlice.user_id == user_id
        ).order_by(UserLiquiditySlice.date.desc(), UserLiquiditySlice.market)
        if market:
            query = query.filter(UserLiquiditySlice.market == market)
        if start_date:
            query = query.filter(UserLiquiditySlice.date >= start_date)
        if end_date:
            query = query.filter(UserLiquiditySlice.date <= end_date)
        pagination = query.paginate(page, limit, error_out=False)
        items = pagination.items

        if items:
            slice_query = LiquiditySlice.query.filter(
                or_(
                    *[and_(
                        LiquiditySlice.market == x.market,
                        LiquiditySlice.date == x.date
                    ) for x in items]
                )
            )
            profit_query = UserLiquidityProfit.query.filter(
                UserLiquidityProfit.user_id == user_id,
                or_(
                    *[and_(
                        UserLiquidityProfit.market == x.market,
                        UserLiquidityProfit.date == x.date,
                    ) for x in items]
                )
            )
            if start_date:
                slice_query = slice_query.filter(
                    LiquiditySlice.date >= start_date
                )
                profit_query = profit_query.filter(
                    UserLiquidityProfit.date >= start_date
                )
            if end_date:
                slice_query = slice_query.filter(
                    LiquiditySlice.date <= end_date
                )
                profit_query = profit_query.filter(
                    UserLiquidityProfit.date <= end_date
                )
            slices = slice_query.all()
            slices = {(x.date, x.market): x for x in slices}
            profits = profit_query.all()
            profit_map = {(x.date, x.market): x for x in profits}
        else:
            slices = {}
            profit_map = {}

        data = []
        for user_slice in items:
            slice = slices.get((user_slice.date, user_slice.market))
            if not slice:
                continue
            profit = profit_map.get((user_slice.date, user_slice.market))
            data.append(cls.make_result(slice, user_slice, profit))

        return dict(
            data=data,
            count=len(pagination.items),
            curr_page=pagination.page,
            total=pagination.total,
            has_next=pagination.has_next,
            total_page=pagination.pages
        )

    @staticmethod
    def _quantize_amount(val, place):
        if val >= Decimal('1'):
            val = quantize_amount(val, place)
        else:
            val = quantize_amount_non_zero(val, place)
        return val

    @classmethod
    def make_result(
            cls,
            slice: LiquiditySlice,
            user_slice: Optional[UserLiquiditySlice],
            user_profit: Optional[UserLiquidityProfit],
    ) -> dict:
        market = MarketCache(slice.market).dict
        info = dict(
            date=slice.date,
            market=slice.market,
            liquidity_usd=cls._quantize_amount(slice.liquidity_usd, 2),
            base_amount=quantize_amount(slice.base_amount, 8),
            quote_amount=quantize_amount(slice.quote_amount, 8),
            deal_usd=cls._quantize_amount(slice.deal_usd, 2),
            fee_usd=cls._quantize_amount(slice.fee_usd, 2),
            fee_base_amount=quantize_amount(slice.fee_base_amount, 8),
            fee_quote_amount=quantize_amount(slice.fee_quote_amount, 8),
            profit_rate=quantize_amount(slice.profit_rate, 4),
            base_asset=market['base_asset'],
            quote_asset=market['quote_asset']
        )
        if user_slice and slice.liquidity:
            rate = user_slice.liquidity / slice.liquidity
            if user_profit:
                user_fee_usd = cls._quantize_amount(user_profit.fee_usd, 2)
                user_fee_base_amount = quantize_amount(user_profit.fee_base_amount, 8)
                user_fee_quote_amount = quantize_amount(user_profit.fee_quote_amount, 8)
            else:
                user_fee_usd = cls._quantize_amount(slice.fee_usd * rate, 2)
                user_fee_base_amount = quantize_amount(slice.fee_base_amount * rate, 8)
                user_fee_quote_amount = quantize_amount(slice.fee_quote_amount * rate, 8)
            info.update(dict(
                user_liquidity=quantize_amount(user_slice.liquidity, 8),
                user_liquidity_usd=cls._quantize_amount(user_slice.liquidity_usd, 2),
                user_fee_usd=user_fee_usd,
                user_fee_base_amount=user_fee_base_amount,
                user_fee_quote_amount=user_fee_quote_amount,
                user_base_amount=quantize_amount(slice.base_amount * rate, 8),
                user_quote_amount=quantize_amount(slice.quote_amount * rate, 8),
                liquidity_rate=quantize_amount(user_slice.liquidity / slice.liquidity, 6)
            ))
        else:
            info.update(dict(
                user_liquidity_usd=Decimal(),
                user_fee_usd=Decimal(),
                user_fee_base_amount=Decimal(),
                user_fee_quote_amount=Decimal(),
                user_base_amount=Decimal(),
                user_quote_amount=Decimal(),
                liquidity_rate=Decimal()
            ))
        return info


@ns.route('/liquidity/slice/total')
@respond_with_code
class LiquidityTotalSliceResource(Resource):

    export_marshal_fields = dict(
        date=fields.String(attribute=lambda x: x['date'].strftime("%Y-%m-%d")),
        market_count=fields.Integer,
        liquidity_usd=fields.String(attribute=lambda x: amount_to_str(x['liquidity_usd'], 2)),
        fee_usd=fields.String(attribute=lambda x: amount_to_str(x['fee_usd'], 2)),
        profit_rate=fields.String(attribute=lambda x: amount_to_str(x['profit_rate'] * 100, 4) + '%')
    )

    export_headers = (
        {"field": "date", Language.ZH_HANS_CN: "日期", Language.EN_US: "Date"},
        {"field": "market_count", Language.ZH_HANS_CN: "参与市场数", Language.EN_US: "Available market amount"},
        {"field": "liquidity_usd", Language.ZH_HANS_CN: "我的流动性", Language.EN_US: "My Liquidity"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "我的分红",
         Language.EN_US: "My Dividend"},
        {"field": "profit_rate", Language.ZH_HANS_CN: "我的当日年化收益率",
         Language.EN_US: "My APY of the day"},
    )

    @staticmethod
    def _quantize_amount(val, place):
        if val >= Decimal('1'):
            val = quantize_amount(val, place)
        else:
            val = quantize_amount_non_zero(val, place)
        return val

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        start_date=TimestampField,
        end_date=TimestampField,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Bool(default=False, missing=False)
    ))
    def get(cls, **kwargs):
        page, limit = kwargs['page'], kwargs['limit'] if not kwargs['export'] else config['EXPORT_ITEM_MAX_COUNT']
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        query = UserLiquidityIncomeSummary.query.filter(
            UserLiquidityIncomeSummary.user_id == g.user.id
        ).order_by(UserLiquidityIncomeSummary.date.desc())
        if start_date:
            query = query.filter(UserLiquidityIncomeSummary.date >= start_date)
        if end_date:
            query = query.filter(UserLiquidityIncomeSummary.date <= end_date)

        records = query.paginate(page, limit, error_out=False)
        items = records.items
        if len(records.items) == 0:
            return dict(
                has_next=records.has_next,
                curr_page=records.page,
                count=len(items),
                data=items,
                total=records.total,
                total_page=records.pages
            )
        res = []
        for item in items:
            item: UserLiquidityIncomeSummary
            if item.liquidity_usd > 0:
                profit_rate = item.fee_usd * 365 / item.liquidity_usd
            else:
                profit_rate = Decimal()
            res.append(dict(
                date=item.date,
                market_count=item.market_count,
                liquidity_usd=cls._quantize_amount(item.liquidity_usd, 2),
                fee_usd=cls._quantize_amount(item.fee_usd, 2),
                profit_rate=quantize_amount(profit_rate, 4)
            ))
        if kwargs['export']:
            export_data = marshal(res, cls.export_marshal_fields)

            file_name = datetime.utcnow().strftime('%Y%m%d-amm-total-income-summary')
            return export_xlsx(
                filename=file_name,
                data_list=export_data,
                export_headers=cls.export_headers
            )
        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(res),
            data=res,
            total=records.total,
            total_page=records.pages
        )


@ns.route('/income/summary')
@respond_with_code
class LastLiquidityIncomeTotalResource(Resource):

    @staticmethod
    def _quantize_amount(val, place):
        if val >= Decimal('1'):
            val = quantize_amount(val, place)
        else:
            val = quantize_amount_non_zero(val, place)
        return val

    @classmethod
    @require_login
    def get(cls):
        last_date = UserLiquidityIncomeSummary.query.order_by(
            UserLiquidityIncomeSummary.date.desc()).first().date
        user_last_summary = UserLiquidityIncomeSummary.query.filter(
            UserLiquidityIncomeSummary.user_id == g.user.id,
            UserLiquidityIncomeSummary.date == last_date,
        ).first()
        if not user_last_summary:
            zero = Decimal()
            return dict(liquidity_usd=zero, fee_usd=zero, profit_rate=zero)

        if user_last_summary.liquidity_usd > 0:
            profit_rate = user_last_summary.fee_usd * 365 / user_last_summary.liquidity_usd
        else:
            profit_rate = Decimal()
        return dict(
            fee_usd=cls._quantize_amount(user_last_summary.fee_usd, 2),
            profit_rate=quantize_amount(profit_rate, 4)
        )


@ns.route('/info')
@respond_with_code
class AmmInfoResource(Resource):

    @classmethod
    def get(cls):
        cache = AmmOverViewCache()
        return cache.read_aside()


@ns.route('/series')
@respond_with_code
class LiquiditySeriesResource(Resource):

    class SeriesType(Enum):
        liquidity = 'liquidity'
        deal = 'deal'

    @classmethod
    @ns.use_kwargs(dict(
        series_type=EnumField(SeriesType, required=True)
    ))
    def get(cls, **kwargs):
        cache = LiquiditySeriesCache()
        data = cache.read()
        if not data:
            return []
        data = json.loads(data)
        return data[kwargs['series_type'].name]


@ns.route('/profit_rate/history')
@respond_with_code
class DayRateHistoryResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(required=True),
            time_type=EnumField(
                enum=['30d', '90d', '180d', '365d'], required=True)
        ))
    def get(cls, **kwargs):
        """查看用户做市账户历史年化收益率"""
        market = kwargs['market']
        time_type = kwargs['time_type']
        _today = today()
        if time_type == '7d':
            start_time, end_time = _today - timedelta(days=7), _today
        elif time_type == '30d':
            start_time, end_time = _today - timedelta(days=30), _today
        elif time_type == '90d':
            start_time, end_time = _today - timedelta(days=90), _today
        elif time_type == '180d':
            start_time, end_time = _today - timedelta(days=180), _today
        elif time_type == '365d':
            start_time, end_time = _today - timedelta(days=365), _today

        result = LiquiditySlice.query.filter(
            LiquiditySlice.market == market,
            LiquiditySlice.date >= start_time,
            LiquiditySlice.date < end_time,
        ).order_by(
            LiquiditySlice.date.asc()
        ).with_entities(
            LiquiditySlice.date,
            LiquiditySlice.profit_rate,
            LiquiditySlice.seven_profit_rate,
        ).all()

        return [[i.date, i.profit_rate, i.seven_profit_rate] for i in result]
