from flask import g

from flask_babel import gettext as _

from marshmallow import fields as mm_fields

from app.common.onchain import Chain

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import require_login
from app.api.common import respond_with_code
from app.api.common import limit_user_frequency
from app.api.common import get_request_language
from app.api.common.fields import EnumField

from app.business.clients.wallet import WalletClient
from app.business.onchain.base import OnchainSettings
from app.business.onchain.base import OnchainAddressHelper
from app.business.onchain.client import CoingeckoHelper
from app.business.onchain.client import OnchainSwapClient
from app.business.onchain.kline import OnchainKlineHelper
from app.business.onchain.balance import OnchainTokenBalanceBiz
from app.business.onchain.token import get_risk_msg_list
from app.business.onchain.token import get_token
from app.business.onchain.token import get_token_by_chain_and_contract
from app.business.onchain.token import get_token_info
from app.business.onchain.token import batch_get_token
from app.business.onchain.token import get_or_create_token_by_token_address
from app.business.onchain.token import token_result_from_token_ids
from app.business.onchain.token import is_support_swap
from app.business.onchain.token import check_and_get_token_quote
from app.business.onchain.favorite import OnchainTokenFavoriteBiz

from app.exceptions import InvalidArgument

from app.models.onchain import OnchainTokenBlocklist
from app.models.onchain import OnchainTokenAboutTranslation

from app.caches.onchain import OnchainHotTokenCache
from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainUpdateTokenInfoCache
from app.caches.onchain import OnchainUpdateTokenQuoteCache
from app.caches.onchain import OnchainUpdateTokenBaseInfoCache
from app.caches.onchain import OnchainCurrencyKlineCache
from app.caches.onchain import OnchainTokenVisitAtCache

ns = Namespace('Token')


@ns.route('/hot')
@respond_with_code
class OnchainTokenHotResource(Resource):

    @classmethod
    def get(cls):
        return token_result_from_token_ids(OnchainHotTokenCache().all(), with_is_top=True)


@ns.route('/<int:token_id>')
@respond_with_code
class OnchainTokenResource(Resource):

    @classmethod
    def get(cls, token_id):
        token_model = get_token(token_id)
        if not token_model:
            raise InvalidArgument
        info_model = get_token_info(token_id)
        if not info_model:
            raise InvalidArgument

        block: OnchainTokenBlocklist = OnchainTokenBlocklist.query.filter(
            OnchainTokenBlocklist.chain == token_model.chain,
            OnchainTokenBlocklist.contract == token_model.contract,
        ).first()
        prompt_msg = None
        spot_asset = None
        if block and block.block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
            prompt_msg = _(block.notice_type.value)
            if block.notice_type != OnchainTokenBlocklist.NoticeType.ABNORMAL:
                spot_asset = block.spot_asset

        token_quote = check_and_get_token_quote(token_model)

        risk = info_model.risk

        hot_top10_percentage_limit = OnchainSettings.get_hot_limit_value(
            token_model.chain,
            OnchainSettings.HotLimitKey.TOP10_PERCENTAGE,
        )
        if (hot_top10_percentage_limit and info_model.top10_percentage
                and info_model.top10_percentage > hot_top10_percentage_limit):
            risk['is_reasonable_holder'] = False
        else:
            risk['is_reasonable_holder'] = True

        try:
            explorer_address_url = WalletClient().get_explorer_address_url(token_model.chain.name, token_model.contract)
        except:  # noqa
            explorer_address_url = ''

        about_translation: OnchainTokenAboutTranslation = OnchainTokenAboutTranslation.query.filter(
            OnchainTokenAboutTranslation.token_id == token_id,
            OnchainTokenAboutTranslation.lang == get_request_language(),
        ).first()
        about_is_translation = bool(about_translation)
        about = about_translation.about if about_is_translation else info_model.about

        OnchainUpdateTokenBaseInfoCache().add(token_id)
        OnchainUpdateTokenInfoCache().add(token_id)
        OnchainUpdateTokenQuoteCache().add(token_id)
        OnchainTokenVisitAtCache().update_one(token_id)

        return dict(
            token_id=token_id,
            base=dict(
                chain=token_model.chain.get_display_name(),
                contract=token_model.contract,
                symbol=token_model.symbol,
                name=token_model.name,
                decimals=token_model.decimals,
                logo=token_model.logo,
                explorer_address_url=explorer_address_url,
                is_support_swap=is_support_swap(token_model.chain, token_model.contract),
            ),
            quote=dict(
                amount_24h=token_quote.get('amount_24h'),
                price=token_quote.get('price'),
                change_24h=token_quote.get('change_24h'),
                highest_24h=token_quote.get('highest_24h'),
                lowest_24h=token_quote.get('lowest_24h'),
            ),
            info=dict(
                community=info_model.community_url,
                about=about,
                about_is_translation=about_is_translation,
                about_source=info_model.about,
                top_pool=info_model.top_pool,
                top_pool_name=info_model.top_pool_name,
                top_pool_launch_time=info_model.top_pool_launch_time,
                circulating_supply=info_model.circulating_supply,
                total_supply=info_model.total_supply,
                holders_count=info_model.holders_count,
                top10_percentage=info_model.top10_percentage,
            ),
            security=risk,
            security_msg=get_risk_msg_list(risk, token_model.chain),
            prompt=dict(
                message=prompt_msg,
                spot_asset=spot_asset,
            ),
        )


@ns.route('/search')
@respond_with_code
class OnchainTokenSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        contract=mm_fields.String(required=True),
    ))
    def get(cls, **kwargs):
        contract = kwargs['contract']
        if (not OnchainAddressHelper(Chain.SOL).validate_address(contract) and
                not OnchainAddressHelper(Chain.ERC20).validate_address(contract)):
            # 地址不规范的直接返回空
            return []
        data = OnchainSwapClient().coin_search(contract)
        result = []
        for item in data:
            if item['type'] == 'SOL':
                chain = Chain.SOL
            elif item['type'] == 'ETH':
                chain = Chain.ERC20
            elif item['type'] == 'BNB':
                chain = Chain.BSC
            else:
                continue
            token = get_token_by_chain_and_contract(chain, contract)
            logo = item['logo']
            if not logo and token:
                # 钱包查询未返回Logo的尝试从DB中获取已收录Token的Logo
                logo = token.logo
            result.append(dict(
                token_id=token.id if token else None,
                chain=chain.get_display_name(),
                contract=OnchainAddressHelper(chain).normalise_address(item['address']),
                symbol=item['symbol'],
                name=item['name'],
                decimal=item['decimal'],
                logo=logo,
            ))
        return result


@ns.route('/id')
@respond_with_code
class OnchainTokenIDResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain.get_display_name_list(), required=True),
        contract=mm_fields.String(required=True),
    ))
    def get(cls, **kwargs):
        chain = Chain.display_name_to_enum(kwargs['chain'])
        token_id = get_or_create_token_by_token_address(chain, kwargs['contract'])
        if token_id is None:
            raise InvalidArgument
        return dict(
            token_id=token_id,
        )


@ns.route("/<int:token_id>/kline")
@respond_with_code
class OnchainTokenKlineResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=mm_fields.Integer(required=True),
            end_time=mm_fields.Integer(required=True),
            interval=mm_fields.Integer(required=True, validate=lambda x: x > 0 and x % 60 == 0),
            currency=mm_fields.String(missing=None),
        )
    )
    def get(cls, token_id, **kwargs):
        token_info = get_token(token_id)
        if not token_info:
            raise InvalidArgument

        currency = kwargs['currency']
        if currency and currency != 'USD' and currency not in OnchainCurrencyKlineCache.SUPPORT_CURRENCY:
            raise InvalidArgument

        kline_helper = OnchainKlineHelper(token_id, kwargs['interval'], CoingeckoHelper(token_info.chain), currency)
        return [
            [
                item.t,
                item.o,
                item.c,
                item.h,
                item.l,
                item.v,
            ] for item in kline_helper.full_kline(kwargs['start_time'], kwargs['end_time'])
        ]


@ns.route("/balance")
@respond_with_code
class OnchainTokenBalanceResource(Resource):
    @classmethod
    @require_login
    def get(cls):
        balance_map = OnchainTokenBalanceBiz.all_user_balances(g.user.id)
        token_data_map = {item['token_id']: item for item in token_result_from_token_ids(list(balance_map.keys()))}

        explorer_address_url_map = {}
        query_list = []
        query_token_ids = []
        for token_id, token in token_data_map.items():
            chain, contract = Chain.display_name_to_enum(token['chain']), token['contract']
            query_list.append((chain.name, contract))
            query_token_ids.append(token_id)
        for index, explorer_address_url in enumerate(WalletClient().get_explorer_addresses_url(query_list)):
            explorer_address_url_map[query_token_ids[index]] = explorer_address_url

        data = []
        for token_id, balance_data in balance_map.items():
            if token_id not in token_data_map:
                raise InvalidArgument(message=f'missing token_id: {token_id}')
            balance_data.update(token_data_map[token_id])
            balance_data['explorer_address_url'] = explorer_address_url_map.get(token_id, '')
            balance_data['chain_full_name'] = Chain.display_name_to_enum(balance_data['chain']).get_display_full_name()
            data.append(balance_data)
        return data


@ns.route("/favorite")
@respond_with_code
class OnchainTokenFavoriteResource(Resource):
    @classmethod
    @require_login
    def get(cls):
        return token_result_from_token_ids(OnchainTokenFavoriteBiz(g.user.id).favorites())

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            token_id=mm_fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        OnchainTokenFavoriteBiz(g.user.id).add(kwargs['token_id'])
        return dict()

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            token_id=mm_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        OnchainTokenFavoriteBiz(g.user.id).delete(kwargs['token_id'])
        return dict()


@ns.route("/quote/batch")
@respond_with_code
class OnchainTokenQuoteBatchResource(Resource):
    MAX_TOKEN_IDS = 10

    @classmethod
    @ns.use_kwargs(
        dict(
            token_ids=mm_fields.String(required=True),
        )
    )
    def get(cls, **kwargs):
        token_ids_str: str = kwargs['token_ids']
        token_ids = []
        for item in token_ids_str.split(','):
            if not item.isdigit():
                InvalidArgument(message='invalid token_ids')
            token_ids.append(int(item))
        if len(token_ids) > cls.MAX_TOKEN_IDS:
            token_ids = token_ids[:cls.MAX_TOKEN_IDS]

        quote_map = OnchainTokenQuoteCache().get_many(token_ids)
        token_base_map = batch_get_token(token_ids)

        data = []
        for token_id, token_base in token_base_map.items():
            quote = quote_map.get(token_id, {})
            data.append(dict(
                token_id=token_id,
                symbol=token_base.symbol,
                name=token_base.name,
                price=quote.get('price'),
                change_24h=quote.get('change_24h'),
                highest_24h=quote.get('highest_24h'),
                lowest_24h=quote.get('lowest_24h'),
                amount_24h=quote.get('amount_24h'),
            ))

        OnchainTokenVisitAtCache().update_many(token_ids)
        return data
