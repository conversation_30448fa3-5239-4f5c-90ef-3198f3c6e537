# -*- coding: utf-8 -*-
import decimal
import json
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict

from flask import g
from marshmallow import fields, validate
from sqlalchemy import or_

from app.api.v2.common import (
    BalanceAccountType, withdrawal_privileged_user_ids, WithdrawalMethod,
    DepositMethod,
)
from ...common import (Namespace, Resource, respond_with_code_new_message)
from ...common.decorators import require_api_v2_auth, lock_request
from ...common.fields import (
    EnumField, PageField, LimitField, AssetField, PositiveDecimalField,
    MarketField, TimestampField, ChainField, DateField,
)
from ...common.request import require_user_request_permission
from ...common.responses import api_v2_success
from ...frontend.wallet import DepositsResource as _FrontendDepositsResource
from app.api.frontend.wallet import WithdrawalsResource as _FrontendWithdrawalsResource
from ....assets import get_asset_chain_config, has_asset, list_all_assets
from ....business import (
    CacheLock, LockKeys, SiteSettings, WalletClient, mem_cached, cached,
)
from ....business import (
    UserSettings, SPOT_ACCOUNT_ID, perpetual_transfer_in,
    perpetual_transfer_out,
)
from ....business.clients.biz_monitor import biz_monitor
from ....business.margin.helper import MarginHelper, report_margin_transfer_biz_event
from ....business.margin.transfer import MarginTransferOperation
from ....business.wallet import (
    WithdrawalHelper,
)
from ....caches import (
    MarginAccountNameCache, PerpetualCoinTypeCache, APIV2AssetsViewCache,
)
from ....caches.wallets import APIV2AssetsChainViewCache
from ....common import PrecisionEnum, BalanceEvent
from ....exceptions import (
    InvalidArgument, TransferNotAllowed, PerpetualOrderExceptionMap, PerpetualResponseCode,
    TransferOutNotAllowed, EmailNotBound, DepositsSuspended, AssetNotFound
)
from ....models import (
    db, PerpetualBalanceTransfer, MarginTransferHistory, User, Deposit, Withdrawal, AssetPrice,
)
from ....models.wallet import WithdrawalCancel
from ....utils.date_ import datetime_to_ms


ns = Namespace('Assets - Assets')

url_prefix = ""


@ns.route('/info')
@respond_with_code_new_message
class AssetInformationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ccy=AssetField(required=False),
    ))
    def get(cls, **kwargs):
        ccy = kwargs.get("ccy", "")
        res = cls.cache_read(ccy)
        return res

    @classmethod
    @mem_cached(60)
    def cache_read(cls, ccy: str):
        res = []
        if ccy:
            data = APIV2AssetsChainViewCache().hget(key=ccy)
            if data:
                res.append(json.loads(data))
            return res

        data = APIV2AssetsChainViewCache().hgetall()
        res = [json.loads(value) for _, value in data.items() if value]
        return res


@ns.route('/transfer')
@respond_with_code_new_message
class AssetTransferResource(Resource):

    @classmethod
    def do_margin_transfer(cls,
                           from_: BalanceAccountType,
                           to_: BalanceAccountType,
                           market: str,
                           asset: str,
                           amount: Decimal):
        margin_markets = list(
            MarginAccountNameCache.list_online_markets().values())
        if market not in margin_markets:
            raise InvalidArgument
        margin_account_id = MarginHelper.get_account_id_by_market_type(market)
        if from_ == BalanceAccountType.SPOT:
            from_account_id = SPOT_ACCOUNT_ID
            to_account_id = margin_account_id
        elif to_ == BalanceAccountType.SPOT:
            from_account_id = margin_account_id
            to_account_id = SPOT_ACCOUNT_ID
        else:
            raise InvalidArgument

        with CacheLock(LockKeys.margin_loan_or_flat(g.user.id), wait=False), \
             CacheLock(LockKeys.user_margin_account(g.user.id, margin_account_id),
                       wait=False):
            db.session.rollback()
            operation = MarginTransferOperation(
                user_id=g.user.id,
                transfer_from=from_account_id,
                transfer_to=to_account_id,
                asset=asset,
                amount=amount)
            operation.transfer()

        report_margin_transfer_biz_event(from_account_id)
        return {}

    @classmethod
    def report_perpetual_transfer_event(cls, from_: BalanceAccountType):
        if from_ == BalanceAccountType.SPOT:
            event = BalanceEvent.SPOT_TO_PERPETUAL
        else:
            event = BalanceEvent.PERPETUAL_TO_SPOT
        biz_monitor.increase_counter(event)

    @classmethod
    def do_perpetual_transfer(cls,
                              from_: BalanceAccountType,
                              to_: BalanceAccountType,
                              asset: str,
                              amount: Decimal):
        if not SiteSettings.perpetual_transfers_enabled:
            raise TransferNotAllowed
        if asset not in PerpetualCoinTypeCache().read_aside():
            raise InvalidArgument

        if from_ == BalanceAccountType.SPOT:
            if asset in SiteSettings.forbidden_perpetual_transfer_in_assets:
                raise TransferNotAllowed
            func = perpetual_transfer_in
            if not func(
                    user_id=g.user.id,
                    coin_type=asset,
                    amount=amount
            ):
                raise PerpetualOrderExceptionMap[
                    PerpetualResponseCode.CONTRACT_TRANSFER_ERROR]
        elif to_ == BalanceAccountType.SPOT:
            func_ = perpetual_transfer_out
            if not UserSettings(g.user.id).perpetual_transfer_out_enabled:
                raise TransferOutNotAllowed
            if not func_(
                    user_id=g.user.id,
                    coin_type=asset,
                    amount=amount
            ):
                raise PerpetualOrderExceptionMap[
                    PerpetualResponseCode.CONTRACT_TRANSFER_ERROR]
        else:
            raise InvalidArgument
        cls.report_perpetual_transfer_event(from_)
        return {}

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(dict(
        from_account_type=EnumField(BalanceAccountType, case_sensitive=False, required=True),
        to_account_type=EnumField(BalanceAccountType, case_sensitive=False, required=True),
        ccy=AssetField(required=True),
        market=MarketField(),
        amount=PositiveDecimalField(required=True)
    ))
    def post(cls, **kwargs):
        # 只允许 现货账户 与杠杆账户或合约账户互转，即 转入/转出 账户里必须有一个为 现货账户。
        enabled_transfer_data = [
            (BalanceAccountType.SPOT, BalanceAccountType.MARGIN),
            (BalanceAccountType.MARGIN, BalanceAccountType.SPOT),
            (BalanceAccountType.SPOT, BalanceAccountType.FUTURES),
            (BalanceAccountType.FUTURES, BalanceAccountType.SPOT),
        ]

        from_ = kwargs['from_account_type']
        to_ = kwargs['to_account_type']
        asset = kwargs["ccy"]
        amount = kwargs["amount"]
        if (from_, to_) not in enabled_transfer_data:
            raise InvalidArgument
        margin_transfers = [
            (BalanceAccountType.SPOT, BalanceAccountType.MARGIN),
            (BalanceAccountType.MARGIN, BalanceAccountType.SPOT),
        ]
        if (from_, to_) in margin_transfers:
            if "market" not in kwargs:
                raise InvalidArgument
            market = kwargs["market"]
            return cls.do_margin_transfer(from_, to_, market, asset, amount)
        perpetual_transfers = [
            (BalanceAccountType.SPOT, BalanceAccountType.FUTURES),
            (BalanceAccountType.FUTURES, BalanceAccountType.SPOT),
        ]
        if (from_, to_) in perpetual_transfers:
            return cls.do_perpetual_transfer(from_, to_, asset, amount)

        raise InvalidArgument


@ns.route('/transfer-history')
class AssetTransferHistoryResource(Resource):

    @classmethod
    def get_perpetual_transfer_history(cls, asset: Optional[str],
                                       start_time: Optional[datetime],
                                       end_time: Optional[datetime],
                                       page: int,
                                       limit: int
                                       ):
        transfers = PerpetualBalanceTransfer.query.filter(
            PerpetualBalanceTransfer.status == PerpetualBalanceTransfer.Status.FINISHED,
            PerpetualBalanceTransfer.user_id == g.user.id,
        )
        if asset:
            transfers = transfers.filter(PerpetualBalanceTransfer.coin_type == asset)
        if start_time:
            transfers = transfers.filter(PerpetualBalanceTransfer.created_at >= start_time)
        if end_time:
            transfers = transfers.filter(PerpetualBalanceTransfer.created_at <= end_time)

        transfers = transfers.order_by(PerpetualBalanceTransfer.id.desc())
        page = transfers.paginate(page, limit)
        records = []

        def parse_account_type(_type: PerpetualBalanceTransfer.TransferType):
            if _type == PerpetualBalanceTransfer.TransferType.TRANSFER_IN:
                return BalanceAccountType.SPOT, BalanceAccountType.FUTURES
            return BalanceAccountType.FUTURES, BalanceAccountType.SPOT
        for item in page.items:
            from_, to_ = parse_account_type(item.transfer_type)
            records.append({
                'id': item.id,
                'created_at': datetime_to_ms(item.created_at),
                "from_account_type": from_,
                "to_account_type": to_,
                "ccy": item.coin_type,
                "amount": item.amount,
                "status": item.status.name.lower(),
            })

        pagination = dict(
            total=page.total,
            has_next=page.has_next
        )

        return api_v2_success(dict(data=records, pagination=pagination))

    @classmethod
    def get_margin_transfer_history(cls,
                                    asset: Optional[str],
                                    market: Optional[str],
                                    start_time: Optional[datetime],
                                    end_time: Optional[datetime],
                                    page: int,
                                    limit: int
                                    ):
        transfers = MarginTransferHistory.query.filter(
            MarginTransferHistory.status == MarginTransferHistory.StatusType.SUCCESS,
            MarginTransferHistory.user_id == g.user.id,
        )
        id_to_market_mapping = {
            int(_id): _m
            for _id, _m in MarginAccountNameCache.list_all_markets().items()
        }

        market_id_mapping = {
            _m: int(_id)
            for _id, _m in id_to_market_mapping.items()
        }

        if market:
            account_id = market_id_mapping.get(market, None)
            if account_id:
                transfers = transfers.filter(
                    or_(
                        MarginTransferHistory.from_account_id == account_id,
                        MarginTransferHistory.to_account_id == account_id
                    )
                )
        if asset:
            transfers = transfers.filter(MarginTransferHistory.asset == asset)
        if start_time:
            transfers = transfers.filter(MarginTransferHistory.created_at >= start_time)
        if end_time:
            transfers = transfers.filter(MarginTransferHistory.created_at <= end_time)

        transfers = transfers.order_by(MarginTransferHistory.id.desc())
        page_records = transfers.paginate(page, limit)
        records = []

        def parse_account_type(_type: MarginTransferHistory.TransferType):
            if _type == MarginTransferHistory.TransferType.IN:
                return BalanceAccountType.SPOT, BalanceAccountType.MARGIN
            return BalanceAccountType.MARGIN, BalanceAccountType.SPOT

        def get_market_name(_type: MarginTransferHistory.TransferType,
                            from_account_id: int,
                            to_account_id: int):
            _from_, _to = parse_account_type(_type)
            if _to == BalanceAccountType.MARGIN:
                return id_to_market_mapping.get(to_account_id, "")
            else:
                return id_to_market_mapping.get(from_account_id, "")

        for item in page_records.items:
            item: MarginTransferHistory
            from_, to_ = parse_account_type(item.transfer_type)
            records.append({
                'id': item.id,
                'created_at': datetime_to_ms(item.created_at),
                "from_account_type": from_,
                "to_account_type": to_,
                "margin_market": get_market_name(item.transfer_type, item.from_account_id,
                                                 item.to_account_id),
                "ccy": item.asset,
                "amount": item.amount,
                "status": "FINISHED".lower()
            })

        pagination = dict(
            total=page_records.total,
            has_next=page_records.has_next
        )
        return api_v2_success(dict(data=records, pagination=pagination))

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(dict(
        transfer_type=EnumField(BalanceAccountType, case_sensitive=False, required=True),
        ccy=AssetField(required=True),
        market=MarketField(),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(missing=1),
        limit=LimitField(missing=10)
    ))
    def get(cls, **kwargs):
        transfer_type = kwargs["transfer_type"]
        asset = kwargs["ccy"]
        market = kwargs.get("market", None)
        start_time = kwargs.get("start_time", None)
        end_time = kwargs.get("end_time", None)
        page = kwargs["page"]
        limit = kwargs["limit"]
        if transfer_type not in (BalanceAccountType.MARGIN, BalanceAccountType.FUTURES):
            raise InvalidArgument
        if transfer_type is BalanceAccountType.FUTURES:
            return cls.get_perpetual_transfer_history(asset, start_time, end_time, page, limit)
        else:
            return cls.get_margin_transfer_history(asset, market, start_time, end_time, page, limit)


@ns.route("/deposit-withdraw-config")
@respond_with_code_new_message
class AssetConfigResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ccy=AssetField(required=True)
    ))
    def get(cls, **kwargs):
        asset = kwargs['ccy']
        user = None
        try:
            user = require_api_v2_auth(lambda: g.user)()
        except Exception:
            pass

        is_wp = user is not None and user.id in withdrawal_privileged_user_ids()
        db.session.close()
        return cls.cache_read(asset, is_wp)

    @classmethod
    @mem_cached(60)
    def cache_read(cls, asset: str, is_wp: bool):
        cache_str = APIV2AssetsViewCache(is_wp).hget(asset)
        if not cache_str:
            raise AssetNotFound
        return json.loads(cache_str)


@ns.route("/all-deposit-withdraw-config")
@respond_with_code_new_message
class AllAssetConfigResource(Resource):
    @classmethod
    def get(cls):
        user = None
        try:
            user = require_api_v2_auth(lambda: g.user)()
        except Exception:
            pass

        is_wp = user is not None and user.id in withdrawal_privileged_user_ids()
        return cls._get(is_wp)

    @classmethod
    @mem_cached(60)
    def _get(cls, is_wp: bool):
        result = cls.cache_read(is_wp)
        return result

    @classmethod
    @cached(60)
    def cache_read(cls, is_wp: bool):
        cache_data = {asset: json.loads(_str) for asset, _str in APIV2AssetsViewCache(is_wp).hgetall().items()}
        result = []
        assets = list_all_assets()
        for asset in assets:
            if asset in cache_data:
                result.append(cache_data[asset])
        return result


@ns.route('/deposit-address')
@respond_with_code_new_message
class DepositAddressResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @lock_request('deposit_address_renewal', with_user=True)
    @ns.use_kwargs(dict(
        ccy=AssetField(required=True),
        chain=ChainField(required=True),
    ))
    def get(cls, **kwargs):
        user: User = g.user
        require_user_request_permission(user)
        if not user.email:
            raise EmailNotBound
        require_user_request_permission(user)

        asset = kwargs['ccy']
        chain = kwargs['chain']

        if not has_asset(asset, chain):
            raise AssetNotFound
        if not get_asset_chain_config(asset, chain).deposits_all_enabled:
            raise DepositsSuspended

        address, memo = WalletClient().get_or_create_deposit_address(chain, user.id)
        return dict(address=address, memo=memo)


@ns.route('/renewal-deposit-address')
@respond_with_code_new_message
class RenewDepositAddressResource(Resource):

    @classmethod
    @require_api_v2_auth
    @lock_request('deposit_address_renewal', with_user=True)
    @ns.use_kwargs(dict(
        ccy=AssetField(required=True),
        chain=fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """ 充值-使用新地址 """
        user_id = g.user.id
        require_user_request_permission(user_id)

        asset = kwargs['ccy']
        chain = kwargs['chain']

        if not has_asset(asset, chain):
            raise AssetNotFound
        if not get_asset_chain_config(asset, chain).deposits_all_enabled:
            raise DepositsSuspended

        client = WalletClient()
        address, memo = client.new_deposit_address(chain, user_id)
        return dict(address=address, memo=memo)


@ns.route('/deposit-history')
class DepositHistoryResource(Resource):

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(dict(
        ccy=AssetField(),
        tx_id=fields.String(),
        type=EnumField(DepositMethod, case_sensitive=False),
        status=EnumField(Deposit.Status, case_sensitive=False),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        user: User = g.user
        query = Deposit.query.filter(Deposit.user_id == user.id).order_by(
            Deposit.id.desc()
        )
        if _asset := kwargs.get("ccy"):
            query = query.filter(Deposit.asset == _asset)
        if _type := kwargs.get('type'):
            _d_type = _type.get_deposit_type()
            query = query.filter(Deposit.type == _d_type)
        if tx_id := kwargs.get("tx_id"):
            query = query.filter(Deposit.tx_id == tx_id)
        if status := kwargs.get("status"):
            query = query.filter(Deposit.status == status)
        # sql:35s count(*)
        records = query.order_by(Deposit.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])
        data = [cls.format_deposit(_record) for _record in
                _FrontendDepositsResource.deposits_to_dict(records.items)]

        return api_v2_success(dict(
            data=data,
            paginatation=dict(total=records.total, has_next=records.has_next)
        ))

    @classmethod
    def format_deposit(cls, _record: Dict) -> Dict:
        _type = Deposit.Type[_record["type"]]
        return {
            "deposit_id": _record['id'],
            "created_at": datetime_to_ms(_record["time"]),
            "tx_id": _record["tx_id"],
            "tx_id_display": _record.get("tx_id_display", ""),
            "ccy": _record["asset"],
            "chain": _record["chain"],
            "deposit_method": DepositMethod.get_method(_type).name,
            "amount": _record["amount"],
            "actual_amount": _record.get("actual_amount", ""),
            "to_address": _record["address"],
            "confirmations": _record["confirmations"],
            "status": _record["status"].lower(),
            "tx_explorer_url": _record["explorer_tx_url"],
            "to_addr_explorer_url": _record["explorer_address_url"],
            "remark": _record.get("remark", ""),
        }


@ns.route('/withdraw')
class WithdrawalResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(dict(
        ccy=AssetField(),
        withdraw_id=fields.Integer(),
        type=EnumField(WithdrawalMethod, case_sensitive=False),
        status=EnumField(Withdrawal.Status, case_sensitive=False),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        user: User = g.user
        query = Withdrawal.query.filter(Withdrawal.user_id == user.id).order_by(
            Withdrawal.id.desc()
        )
        if withdraw_id := kwargs.get('withdraw_id'):
            query = query.filter(Withdrawal.id == withdraw_id)
        if asset := kwargs.get('ccy'):
            query = query.filter(Withdrawal.asset == asset)
        if _type := kwargs.get('type'):
            w_type = _type.get_withdrawal_type()
            query = query.filter(Withdrawal.type == w_type)
        if _status := kwargs.get('status'):
            query = query.filter(Withdrawal.status == _status)
        if start_time := kwargs.get('start_time'):
            query = query.filter(Withdrawal.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(Withdrawal.created_at <= end_time)

        records = query.order_by(Withdrawal.id.desc()) \
                       .paginate(kwargs['page'], kwargs['limit'])
        return api_v2_success(dict(
            data=[
                cls.format_withdrawal(_record)
                for _record in _FrontendWithdrawalsResource.withdrawals_to_dict(records.items, using_status_map=False)],
            pagination=dict(total=records.total, has_next=records.has_next))
        )

    @classmethod
    def format_withdrawal(cls, _record: Dict) -> Dict:
        _type = Withdrawal.Type[_record["type"]]
        return dict(
            withdraw_id=_record["id"],
            created_at=datetime_to_ms(_record["time"]),
            withdraw_method=WithdrawalMethod.get_method(_type).name,
            ccy=_record["asset"],
            amount=_record["amount"],
            actual_amount=_record["actual_amount"],
            chain=_record["chain"],
            tx_fee=_record["fee"],
            fee_ccy=_record["fee_asset"],
            fee_amount=_record["fee_amount"],
            to_address=_record["address"],
            memo=_record["memo"],
            tx_id=_record["tx_id"],
            confirmations=_record["confirmations"],
            explorer_address_url=_record["explorer_address_url"],
            explorer_tx_url=_record["explorer_tx_url"],
            remark=_record["remark"],
            status=_record["status"].lower(),
        )

    @classmethod
    @require_api_v2_auth
    @respond_with_code_new_message
    @ns.use_kwargs(dict(
        ccy=AssetField(required=True),
        chain=ChainField(required=False),
        to_address=fields.String(required=True),
        withdraw_method=EnumField(WithdrawalMethod,
                                  enum_by_value=True,
                                  case_sensitive=False,
                                  missing=WithdrawalMethod.ON_CHAIN),
        memo=fields.String(missing=''),
        amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                    rounding=decimal.ROUND_DOWN, required=True),
        fee_ccy=fields.String(required=False),  # 自定义的提现手续费币种
        extra=fields.Dict(keys=fields.String(), values=fields.String()),
        remark=fields.String(missing='', validate=validate.Length(max=256)),
    ))
    def post(cls, **kwargs):

        user: User = g.user
        user_id = user.id

        WithdrawalHelper.validate_user_permission(user)

        method = kwargs['withdraw_method']
        w_type = method.get_withdrawal_type()

        asset = kwargs['ccy']
        fee_asset = kwargs.get("fee_ccy")
        chain = kwargs.get('chain', '')
        address = kwargs['to_address'].strip()
        memo = kwargs['memo']
        amount = kwargs['amount']
        extra = kwargs.get('extra', {})
        remark = kwargs["remark"]

        if w_type == Withdrawal.Type.ON_CHAIN:
            WithdrawalHelper.validate_api_on_chain_withdrawal_params(
                user, asset, chain, address, memo, amount, extra)

            fee_asset = fee_asset or asset
            fee = WithdrawalHelper.get_onchain_withdrawal_fee(asset, fee_asset, chain)
            recipient_id = None
        else:
            WithdrawalHelper.validate_api_local_transfer_params(
                user_id, asset, address, amount)
            recipient_id = WithdrawalHelper.get_local_transfer_user(user_id, address).id
            fee = Decimal()
            fee_asset = fee_asset or asset
            chain = None
            memo = ''
            extra = None

        row = WithdrawalHelper.do_api_withdrawal(
            w_type, user, asset, chain, address, memo, extra,
            amount, fee, fee_asset, recipient_id, remark)
        return cls.format_withdrawal(
            _FrontendWithdrawalsResource.withdrawal_to_dict(row, with_detail=True))


@ns.route("/cancel-withdraw")
@respond_with_code_new_message
class CancelWithdrawResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(withdraw_id=fields.Integer(required=True))
    )
    def post(cls, **kwargs):
        WithdrawalHelper.do_cancel_withdrawal(g.user.id, kwargs["withdraw_id"], WithdrawalCancel.CancelType.USER, cancel_user_id=g.user.id)
        return {}


@ns.route("/rate")
@respond_with_code_new_message
class AssetRateResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(date=DateField(to_date=True))
    )
    def get(cls, **kwargs):
        _today = datetime.today().date()
        _date = kwargs.get('date') or _today
        return AssetPrice.get_close_price_map(_date)
