# -*- coding: utf-8 -*-
import decimal
import json
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from itertools import product
from typing import List, Dict, Tuple

from flask import g
from flask_babel import gettext as _
from marshmallow import validate
from sqlalchemy import func, or_
from sqlalchemy.orm import aliased
from webargs import fields

from app.api.common import Namespace, Resource, respond_with_code_new_message
from app.api.common.decorators import require_api_v2_auth
from app.api.common.fields import (
    EnumField, AllMarketField, PageField, LimitField, AssetField,
    PositiveDecimalField, TimestampField, MarketField,
)
from app.api.common.request import RequestPlatform, get_request_ip, require_user_request_permission
from app.api.common.responses import api_v2_success
from app.api.v2.common import BalanceAccountType, EMPTY_PAGINATION_DATA, SubAccountParamPermission
from app.business import (
    UserPre<PERSON>s, SubAccountManager, CacheLock, LockK<PERSON>s, ServerClient, SPOT_ACCOUNT_ID,
    PerpetualServerClient, get_user_using_coupon_balance,
)
from app.business.fee import FeeFetcher
from app.business.sub_account import (
    get_sub_account_balance_map,
    update_sub_account_balance_cache_task,
)
from app.business.user import UserSettings
from app.business.credit import update_credit_user_risk_record_task
from app.caches import ApiAuthCache, PerpetualCoinTypeCache, MarketCache, PerpetualMarketCache
from app.caches.user import SubMainUserCache
from app.business.margin.helper import get_user_margin_balances
from app.caches.margin import MarginAccountNameCache

from app.common import TradeBusinessType, TradeType, SubAccountPermission, PrecisionEnum
from app.exceptions import (
    InvalidArgument, SubAccountBeyondAmountLimit, SubAccountUsernameExists,
    SubAccountRelationError, RecordNotFound, SubAccountPermissionRequire, TransferNotAllowed,
    TransferOutNotAllowed, InsufficientBalance,
)
from app.models import OperationLog, SubAccount, User, db, ApiAuth, SubAccountAssetTransfer, MaskUser
from app.utils import now, GeoIP, validate_ip_address, amount_to_str
from app.utils.date_ import datetime_to_ms

ns = Namespace('Account')


def _auth_to_dict(auth: ApiAuth, show_secret_key=True) -> dict:
    r = dict(
        api_id=auth.id,
        created_at=datetime_to_ms(auth.created_at),
        access_id=auth.access_id,
        trade_enabled=auth.trading_enabled,
        withdraw_enabled=auth.withdrawals_enabled,
        is_expired=auth.is_expired,
        expires_at=(datetime_to_ms(expired_at)
                    if (expired_at := auth.expired_at)
                    else 0),
        remark=auth.remark,
    )
    if show_secret_key:
        r['secret_key'] = auth.secret_key
    r['ip_whitelist'] = auth.allowed_ips.split('\n') if auth.allowed_ips else []
    return r


@ns.route("/settings")
@respond_with_code_new_message
class AccountSettingsAPIResource(Resource):

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(
        dict(
            cet_discount_enabled=fields.Boolean(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 修改账户设置 """

        user_id = g.user.id
        pref = UserPreferences(user_id)
        cet_discount = kwargs["cet_discount_enabled"]
        pref.cet_discount_enabled = cet_discount
        OperationLog.add(
            user_id,
            OperationLog.Operation.TURN_FEE_SWITCH,
            "on" if cet_discount else "off",
            RequestPlatform.WEB,
        )
        return {}


@ns.route("/trade-fee-rate")
@respond_with_code_new_message
class UserTradeFeeResource(Resource):

    @classmethod
    @require_api_v2_auth
    @ns.use_kwargs(
        dict(
            market_type=EnumField(BalanceAccountType, required=True),
            market=AllMarketField(required=True)
        )
    )
    def get(cls, **kwargs):
        market_type = kwargs["market_type"]
        market = kwargs["market"]
        if market_type == BalanceAccountType.SPOT:
            if market not in MarketCache.list_online_markets():
                raise InvalidArgument
            trade_type = TradeBusinessType.SPOT
        elif market_type == BalanceAccountType.FUTURES:
            if not PerpetualMarketCache.is_market_exists(market):
                raise InvalidArgument
            trade_type = TradeBusinessType.PERPETUAL
        else:
            raise InvalidArgument
        rates = FeeFetcher(g.user.id).fetch(trade_type, market)
        return {
            "market": market,
            "maker_rate": rates[TradeType.MAKER],
            "taker_rate": rates[TradeType.TAKER]
        }


@ns.route("/subs")
class SubAccountResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(),
            is_frozen=fields.Boolean(default=False, missing=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 子账号列表 """
        main_user = g.user
        page, limit = kwargs["page"], kwargs["limit"]
        search_sub_user_id = None
        if sub_user_name := kwargs.get("sub_user_name"):
            sub_acc = SubAccountManager.get_sub_account_by_name(
                main_user.id, 
                sub_user_name, 
                SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)
            if sub_acc:
                search_sub_user_id = sub_acc.user_id
            else:
                return api_v2_success(EMPTY_PAGINATION_DATA)

        q = SubAccount.query.filter(
            SubAccount.main_user_id == main_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        )
        if search_sub_user_id:
            q = q.filter(SubAccount.user_id == search_sub_user_id)
        if (is_freeze := kwargs.get("is_frozen")) is not None:
            if is_freeze:
                q = q.filter(SubAccount.status == SubAccount.Status.FROZEN)
            else:
                q = q.filter(SubAccount.status == SubAccount.Status.VALID)
        q = q.order_by(SubAccount.id.desc())
        pagination = q.paginate(page, limit, error_out=False)

        items = cls.format_sub_accounts(pagination.items)
        return api_v2_success(dict(
            data=items,
            pagination=dict(
                total=pagination.total,
                has_next=pagination.has_next,
            )
        ))

    @classmethod
    def format_sub_accounts(cls, rows: List[SubAccount]) -> List[Dict]:
        main_user = g.user
        sub_user_ids = [i.user_id for i in rows]
        user_name_map = dict(
            User.query.filter(User.id.in_(sub_user_ids)).with_entities(User.id, User.name).all())
        sub_balance_map = get_sub_account_balance_map(main_user.id, sub_user_ids)
        items = []
        for row in rows:
            item = dict(
                sub_user_name=user_name_map[row.user_id],
                is_frozen=row.status == SubAccount.Status.FROZEN,
                is_authorized=row.manage_status == SubAccount.ManageStatus.MANAGED,
                permissions=[SubAccountParamPermission.parse_origin(i).name
                             for i in row.enum_permissions],
                balance_usd=sub_balance_map.get(row.user_id, Decimal()),
            )
            items.append(item)
        return items

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
            permissions=fields.List(EnumField(SubAccountParamPermission)),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 新增子账户 """
        from app.api.frontend.sub_accounts import RE_NAME, new_sub_user_preference
        require_user_request_permission(g.user)
        main_user: User = g.user
        main_user_id = main_user.id
        with CacheLock(LockKeys.sub_account_created(main_user_id)):
            db.session.rollback()
            sub_acc_count = SubAccount.query.filter(
                SubAccount.main_user_id == main_user_id,
                SubAccount.type == SubAccount.Type.NORMAL,
            ).with_entities(func.count()).scalar() or 0
            num_limit = SubAccountManager.get_user_sub_account_num_limit(main_user_id)
            if sub_acc_count >= num_limit:
                raise SubAccountBeyondAmountLimit(num=num_limit)

            sub_user_name = kwargs["sub_user_name"]
            if not 3 <= len(sub_user_name) <= 26 or not RE_NAME.fullmatch(sub_user_name):
                raise InvalidArgument

            if len(remark := kwargs["remark"]) > 50:
                raise InvalidArgument

            mu_model = aliased(User)
            if (
                    SubAccount.query.join(
                        User,
                        User.id == SubAccount.user_id
                    ).join(
                        mu_model,
                        mu_model.id == SubAccount.main_user_id
                    ).filter(mu_model.id == main_user_id,
                             User.name == sub_user_name,
                             SubAccount.type == SubAccount.Type.NORMAL).first()
                    is not None
            ):
                raise SubAccountUsernameExists

            user = User(
                login_password_updated_at=now(),
                registration_ip=(ip := get_request_ip()),
                registration_location=GeoIP(ip).location,
                location_code=main_user.location_code,
                name=sub_user_name,
                user_type=User.UserType.SUB_ACCOUNT,
            )
            db.session.add(user)
            db.session.flush()

            if "permissions" in kwargs:
                permissions = [i.get_origin_permission().name for i in kwargs["permissions"]]
                permissions = sorted(set(permissions), key=permissions.index)
                permissions_str = json.dumps(permissions)
            else:
                permissions_str = ""  # 默认全部权限
            sub_acc = SubAccount(
                user_id=user.id,
                main_user_id=main_user.id,
                remark=remark,
                permissions=permissions_str,
            )
            db.session.add(sub_acc)
            db.session.commit()

            new_sub_user_preference(user.id, main_user.id)
            update_credit_user_risk_record_task.delay(main_user.id)
            SubMainUserCache.add_sub_user(user.id, main_user.id)
            return api_v2_success(dict(data={}))


@ns.route("/subs/frozen")
@respond_with_code_new_message
class SubAccountFrozenResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 禁用子账户 """
        main_user: User = g.user
        sub_user_name = kwargs["sub_user_name"]
        if (
                sub_acc := SubAccountManager.get_sub_account_by_name(
                    main_user.id, 
                    sub_user_name,
                    SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)
        ) is None:
            raise InvalidArgument

        sub_user_id = sub_acc.user_id
        if not SubAccountManager.has_relationship(
            main_user.id,
            sub_user_id, 
            SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError
        SubAccountManager.freeze_account(sub_user_id)
        return {}


@ns.route("/subs/unfrozen")
@respond_with_code_new_message
class SubAccountUnFrozenResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 取消禁用子账户 """

        main_user: User = g.user
        sub_user_name = kwargs["sub_user_name"]
        if (
                sub_acc := SubAccountManager.get_sub_account_by_name(
                    main_user.id,
                    sub_user_name,
                    SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)
        ) is None:
            raise InvalidArgument

        sub_user_id = sub_acc.user_id
        if not SubAccountManager.has_relationship(
            main_user.id, 
            sub_user_id, 
            SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError
        SubAccountManager.unfreeze_account(sub_user_id)
        return {}


@ns.route("/subs/api")
class SubAccountAPIResource(Resource):

    @classmethod
    @respond_with_code_new_message
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
            remark=fields.String(validate=validate.Length(max=50), missing=''),
            ip_whitelist=fields.List(fields.String, required=True),
            trade_enabled=fields.Boolean(required=True)
        )
    )
    def post(cls, **kwargs):
        """为子账户新增APIKEY"""
        main_user: User = g.user
        require_user_request_permission(g.user)
        sub_user_name = kwargs.get('sub_user_name')
        sub_user = User.query.join(SubAccount, User.id == SubAccount.user_id).filter(
            User.name == sub_user_name,
            SubAccount.main_user_id == main_user.id,
            SubAccount.status == SubAccount.Status.VALID).with_entities(User.id).first()
        if not sub_user:
            raise RecordNotFound
        sub_acc: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == sub_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).first()
        if not sub_acc:
            raise RecordNotFound
        if not sub_acc.has_permission(SubAccountPermission.API):
            raise SubAccountPermissionRequire.from_name(name=SubAccountPermission.API.name)

        SubAccountManager.check_hosting(sub_acc.user_id)
        api_counts = ApiAuth.query.filter(
            ApiAuth.user_id == sub_user.id,
            ApiAuth.status == ApiAuth.Status.VALID).with_entities(func.count()).scalar() or 0
        if api_counts >= ApiAuth.API_NUM_LIMIT:
            raise InvalidArgument(
                message='Number of APIKEY cannot be more than {}'.format(ApiAuth.API_NUM_LIMIT))
        if allowed_ips := kwargs['ip_whitelist']:
            if len(allowed_ips) > ApiAuth.ALLOW_IPS_NUM_LIMIT:
                raise InvalidArgument(
                    message='Ip Address cannot be more than {}'.format(ApiAuth.ALLOW_IPS_NUM_LIMIT))
            if not all(map(validate_ip_address, allowed_ips)):
                raise InvalidArgument(message='Invalid Ip Address')
        row = ApiAuth.new(
            sub_user.id,
            main_user.id,
            '\n'.join(allowed_ips),
            kwargs['remark'],
            False,
            kwargs['trade_enabled'],
            None if allowed_ips else ApiAuth.DEFAULT_TTL
        )
        return _auth_to_dict(row)

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField(missing=10),
            sub_user_name=fields.String(required=True)
        )
    )
    def get(cls, **kwargs):
        """查询子账户的APIKEY"""
        main_user: User = g.user
        sub_user_name = kwargs.get('sub_user_name')
        sub_user = User.query.join(SubAccount, User.id == SubAccount.user_id).filter(
            User.name == sub_user_name,
            SubAccount.main_user_id == main_user.id,
            SubAccount.status == SubAccount.Status.VALID).with_entities(User.id).first()
        if not sub_user:
            raise RecordNotFound
        sub_acc: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == sub_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).first()
        if not sub_acc:
            raise RecordNotFound
        SubAccountManager.check_hosting(sub_acc.user_id)
        records = ApiAuth.query \
            .filter(ApiAuth.user_id == sub_user.id,
                    ApiAuth.status == ApiAuth.Status.VALID) \
            .order_by(ApiAuth.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'])
        return api_v2_success(dict(
            data=list(map(lambda x: _auth_to_dict(x, False), records.items)),
            pagination=dict(total=records.total, has_next=records.has_next),
            )
        )


@ns.route("/subs/api-detail")
@respond_with_code_new_message
class SubAccountAPIDetailResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            api_id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """查询APIKEY的详细信息"""
        id_ = kwargs["api_id"]
        main_user: User = g.user
        sub_users = SubAccount.query.filter(
            SubAccount.main_user_id == main_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
            SubAccount.status == SubAccount.Status.VALID
        ).with_entities(SubAccount.user_id).all()

        sub_user_ids = [i.user_id for i in sub_users]
        row = ApiAuth.query \
            .filter(ApiAuth.id == id_,
                    ApiAuth.user_id.in_(sub_user_ids),
                    ApiAuth.status == ApiAuth.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound
        SubAccountManager.check_hosting(row.user_id)
        return _auth_to_dict(row)


@ns.route("/subs/info")
@respond_with_code_new_message
class SubAccountInfoResource(Resource):
    @classmethod
    @require_api_v2_auth(allow_sub_account=True)
    def get(cls, **kwargs):
        """子账号信息查询"""
        cur_user = g.user
        if cur_user.is_sub_account:
            sub_account = SubAccount.query.filter(
                SubAccount.user_id == cur_user.id,
                SubAccount.type == SubAccount.Type.NORMAL,
                SubAccount.is_visible.is_(True),
            ).first()
            if sub_account is None:
                raise InvalidArgument
            return dict(
                user_type=User.UserType.SUB_ACCOUNT.name,
                sub_user_name=cur_user.name,
            )
        return dict(user_type=User.UserType.NORMAL.name, sub_user_name="")


@ns.route("/subs/edit-api")
@respond_with_code_new_message
class SubAccountAPIEditResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
            api_id=fields.Integer(required=True),
            trade_enabled=fields.Boolean(),
            ip_whitelist=fields.List(fields.String, required=False),
            remark=fields.String(validate=validate.Length(max=50), missing=''),
    )
    )
    def post(cls, **kwargs):
        """编辑APIKEY"""
        id_ = kwargs["api_id"]
        require_user_request_permission(g.user)
        main_user: User = g.user
        sub_user_name = kwargs.get('sub_user_name')
        sub_user = User.query.join(SubAccount, User.id == SubAccount.user_id).filter(
            User.name == sub_user_name,
            SubAccount.main_user_id == main_user.id,
            SubAccount.status == SubAccount.Status.VALID).with_entities(User.id).first()
        if not sub_user:
            raise RecordNotFound
        sub_acc: SubAccount = SubAccount.query.filter(
            SubAccount.user_id == sub_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).first()
        if not sub_acc:
            raise RecordNotFound
        if not sub_acc.has_permission(SubAccountPermission.API):
            raise SubAccountPermissionRequire.from_name(name=SubAccountPermission.API.name)
        SubAccountManager.check_hosting(sub_acc.user_id)

        row: ApiAuth = ApiAuth.query \
            .filter(ApiAuth.id == id_,
                    ApiAuth.user_id == sub_user.id,
                    ApiAuth.status == ApiAuth.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound
        row.remark = kwargs['remark']

        if "ip_whitelist" in kwargs and (allowed_ips := kwargs['ip_whitelist']):
            if len(allowed_ips) > ApiAuth.ALLOW_IPS_NUM_LIMIT:
                raise InvalidArgument(message='Ip Address cannot be more than 50')
            if not all(map(validate_ip_address, allowed_ips)):
                raise InvalidArgument(message='Invalid Ip Address')
            row.allowed_ips = '\n'.join(allowed_ips)
        if "trade_enabled" in kwargs:
            row.trading_enabled = kwargs['trade_enabled']
        row.expired_at = (
            None if row.allowed_ips
            else now() + timedelta(seconds=ApiAuth.DEFAULT_TTL))
        db.session.commit()
        ApiAuthCache(row.access_id).delete()
        return _auth_to_dict(row)


@ns.route("/subs/delete-api")
@respond_with_code_new_message
class SubAccountAPIDeleteResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            api_id=fields.Integer(required=True),
        )
    )
    def post(cls, **kwargs):
        """删除APIKEY"""
        id_ = kwargs["api_id"]
        main_user: User = g.user
        sub_users = SubAccount.query.filter(
            SubAccount.main_user_id == main_user.id,
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
            SubAccount.status == SubAccount.Status.VALID,
        ).with_entities(SubAccount.user_id).all()

        sub_user_ids = [i.user_id for i in sub_users]
        row = ApiAuth.query \
            .filter(ApiAuth.id == id_,
                    ApiAuth.user_id.in_(sub_user_ids),
                    ApiAuth.status == ApiAuth.Status.VALID) \
            .first()
        if row is None:
            raise RecordNotFound
        SubAccountManager.check_hosting(row.user_id)
        row.status = ApiAuth.Status.DELETED
        db.session.commit()
        ApiAuthCache(row.access_id).delete()
        return {}


@ns.route("/subs/transfer")
@respond_with_code_new_message
class SubAccountTransferResource(Resource):

    @classmethod
    def parse_transfer_user_ids(cls, from_user_name: str, to_user_name: str) -> Tuple[int, int]:
        if not from_user_name and not to_user_name:
            raise InvalidArgument
        if from_user_name == to_user_name:
            raise InvalidArgument

        main_user: User = g.user
        if from_user_name:
            if (from_sub_acc := SubAccountManager.get_sub_account_by_name(
                main_user.id,
                from_user_name,
                SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)) is None:
                raise InvalidArgument
            source_user_id = from_sub_acc.user_id
        else:
            source_user_id = main_user.id

        if to_user_name:
            if (to_sub_acc := SubAccountManager.get_sub_account_by_name(
                main_user.id,
                to_user_name,
                SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)) is None:
                raise InvalidArgument
            target_user_id = to_sub_acc.user_id
        else:
            target_user_id = main_user.id

        if source_user_id == target_user_id:
            raise InvalidArgument
        return source_user_id, target_user_id

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            from_user_name=fields.String,  # user_name为空表示主账号
            to_user_name=fields.String,
            from_account_type=EnumField(BalanceAccountType, required=True),
            to_account_type=EnumField(BalanceAccountType, required=True),
            ccy=AssetField(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES,
                                        rounding=decimal.ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 子账户的通用资产划转, 支持 主子账号、子子账号 间的划转 """
        require_user_request_permission(g.user)
        from_user_name = kwargs.get("from_user_name")
        to_user_name = kwargs.get("to_user_name")
        source_user_id, target_user_id = cls.parse_transfer_user_ids(from_user_name, to_user_name)

        source_account_type = kwargs["from_account_type"]
        target_account_type = kwargs["to_account_type"]
        asset = kwargs["ccy"]
        amount = kwargs["amount"]
        enable_params = list(product(
            (BalanceAccountType.SPOT, BalanceAccountType.FUTURES),
            (BalanceAccountType.SPOT, BalanceAccountType.FUTURES)))
        if (source_account_type, target_account_type) not in enable_params:
            raise InvalidArgument
        mapping = {
            BalanceAccountType.SPOT: SubAccountAssetTransfer.AccountType.SPOT,
            BalanceAccountType.FUTURES: SubAccountAssetTransfer.AccountType.PERPETUAL
        }
        # convert
        source_account_type = mapping[source_account_type]
        target_account_type = mapping[target_account_type]

        perpetual_account = SubAccountAssetTransfer.AccountType.PERPETUAL

        main_user: User = g.user
        if source_user_id == target_user_id:
            raise InvalidArgument
        if main_user.id != source_user_id and main_user.id != target_user_id:
            # 子账号A 和 子账号B 的划转，只允许 现货账户 的划转
            if source_account_type == perpetual_account or target_account_type == perpetual_account:
                raise InvalidArgument

        if main_user.id != source_user_id:
            source_sub_acc = SubAccountManager.get_valid_sub_account(
                main_user.id, 
                source_user_id, 
                SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not source_sub_acc:
                raise InvalidArgument
        if main_user.id != target_user_id:
            target_sub_acc = SubAccountManager.get_valid_sub_account(
                main_user.id, 
                target_user_id, 
                SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not target_sub_acc:
                raise InvalidArgument
            if target_account_type == perpetual_account:
                if not target_sub_acc.has_permission(SubAccountPermission.PERPETUAL):
                    raise InvalidArgument(message=_("%(name)s无合约交易权限，不可向合约账户转入资金",
                                                    name=target_sub_acc.user.name))
        if source_account_type == perpetual_account or target_account_type == perpetual_account:
            if asset not in set(PerpetualCoinTypeCache().read_aside()):
                raise InvalidArgument

        source_user_settings = UserSettings(source_user_id)
        if not source_user_settings.sub_account_transfer_enabled or not UserSettings(
                target_user_id).sub_account_transfer_enabled:
            raise TransferNotAllowed
        if source_user_settings.sub_account_transfer_out_disabled:
            raise TransferOutNotAllowed
        if (source_account_type == perpetual_account and
                not source_user_settings.perpetual_transfer_out_enabled):
            raise TransferOutNotAllowed

        if source_account_type == perpetual_account:
            balances = PerpetualServerClient().get_user_balances(source_user_id, asset)
            transfer_amount = Decimal(balances.get(asset, {}).get("transfer", "0"))
            using_coupon_amount = get_user_using_coupon_balance(source_user_id, asset)
            if transfer_amount - using_coupon_amount < amount:
                raise InsufficientBalance
        else:
            balance = ServerClient().get_user_balances(source_user_id, asset)[asset]
            if balance["available"] < amount:
                raise InsufficientBalance

        with CacheLock(LockKeys.sub_account_transfer(source_user_id, target_user_id)):
            db.session.rollback()
            SubAccountManager.transfer_asset(
                main_user_id=main_user.id,
                source_user_id=source_user_id,
                source_account_type=source_account_type,
                target_user_id=target_user_id,
                target_account_type=target_account_type,
                asset=asset,
                amount=amount,
            )

        sub_user_ids = {source_user_id, target_user_id} - {main_user.id}
        update_sub_account_balance_cache_task.delay(main_user.id, list(sub_user_ids))

        return {}


@ns.route("/subs/transfer-history")
class SubAccountTransferHistoryResource(Resource):
    @classmethod
    @require_api_v2_auth(allow_sub_account=True)
    @ns.use_kwargs(dict(
        sub_user_name=fields.String(),
        ccy=AssetField(),
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """ 子账号划转记录（包括子子划转记录） """
        if g.user.user_type == User.UserType.SUB_ACCOUNT:
            main_user = g.user.main_user
            sub_user_name = g.user.name
            sub_user_id = g.user.id
        else:
            main_user, sub_user_id = g.user, None
            if sub_user_name := kwargs.get('sub_user_name'):
                if (sub_account := SubAccountManager.get_sub_account_by_name(
                    main_user.id,
                    sub_user_name,
                    SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)) is None:
                    raise InvalidArgument
                else:
                    sub_user_id = sub_account.user_id
 
        main_user_id = main_user.id
        # sql:1s count(*)
        query = SubAccountAssetTransfer.query.filter(
            SubAccountAssetTransfer.main_user_id == main_user_id,
            SubAccountAssetTransfer.status == SubAccountAssetTransfer.Status.FINISHED,
        )
        if sub_user_id:
            query = query.filter(
                or_(
                    SubAccountAssetTransfer.target == sub_user_id,
                    SubAccountAssetTransfer.source == sub_user_id,
                )
            )
        if asset := kwargs.get('ccy'):
            query = query.filter(SubAccountAssetTransfer.asset == asset)
        if start_time := kwargs.get("start_time"):
            query = query.filter(SubAccountAssetTransfer.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            query = query.filter(SubAccountAssetTransfer.created_at <= end_time)

        records = query.order_by(SubAccountAssetTransfer.id.desc()).paginate(kwargs['page'],
                                                                             kwargs['limit'],
                                                                             error_out=False)

        rows = records.items
        user_ids = {i.source for i in rows}
        user_ids.update({i.target for i in rows})
        user_rows = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.name).all()
        user_names = {s.id: s.name for s in user_rows}
        user_names[main_user_id] = main_user.name_displayed

        def convert_account_type(_type: SubAccountAssetTransfer.AccountType):
            mapping = {
                SubAccountAssetTransfer.AccountType.SPOT: BalanceAccountType.SPOT,
                SubAccountAssetTransfer.AccountType.PERPETUAL: BalanceAccountType.FUTURES
            }
            return mapping.get(_type, None)

        def history_to_dict(_h: SubAccountAssetTransfer) -> dict:
            return dict(
                id=_h.id,
                created_at=datetime_to_ms(_h.created_at),
                from_account_type=convert_account_type(_h.source_account_type),
                to_account_type=convert_account_type(_h.target_account_type),
                from_user_name=user_names[_h.source],
                to_user_name=user_names[_h.target],
                ccy=_h.asset,
                amount=_h.amount,
                status=_h.status.name.lower()
            )

        return api_v2_success(dict(
                                  data=list(map(history_to_dict, records.items)),
                                  pagination=dict(total=records.total, has_next=records.has_next)
                              ))


@ns.route("/subs/spot-balance")
class SubAccountSpotBalanceResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=True)
    @ns.use_kwargs(
        dict(
            sub_user_name=fields.String(required=True),
            ccy=AssetField(),
        )
    )
    def get(cls, **kwargs):
        if g.user.user_type == User.UserType.SUB_ACCOUNT:
            main_user = g.user.main_user
            sub_user_name = g.user.name
        else:
            main_user = g.user
            sub_user_name = kwargs["sub_user_name"]
        if (
                sub_acc := SubAccountManager.get_sub_account_by_name(
                    main_user.id,
                    sub_user_name,
                    SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)) is None:
            raise InvalidArgument

        sub_user_id = sub_acc.user_id
        if not SubAccountManager.has_relationship(
            main_user.id, 
            sub_user_id, 
            SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError
        asset = kwargs.get("ccy", None)
        if asset:
            result = ServerClient().get_user_balances(sub_user_id, asset,
                                                      account_id=SPOT_ACCOUNT_ID)
        else:
            result = ServerClient().get_user_balances(sub_user_id, account_id=SPOT_ACCOUNT_ID)
        sub_balances = [dict(ccy=asset,
                             available=amount_to_str(d["available"], PrecisionEnum.COIN_PLACES),
                             frozen=amount_to_str(d["frozen"], PrecisionEnum.COIN_PLACES),
                             )
                        for asset, d in result.items() if
                        Decimal(d['available']) + Decimal(d['frozen']) > 0]
        return api_v2_success(dict(data=sub_balances))


@ns.route("/subs/balance")
class SubAccountBalanceResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            market_type=EnumField(BalanceAccountType, required=True),
            sub_user_name=fields.String(required=True),
            market=MarketField(),
        )
    )
    def get(cls, **kwargs):
        """获取子账号余额(现货/杠杆/合约)"""
        if g.user.user_type == User.UserType.SUB_ACCOUNT:
            main_user = g.user.main_user
            sub_user_name = g.user.name
        else:
            main_user = g.user
            sub_user_name = kwargs["sub_user_name"]
        if (
            sub_acc := SubAccountManager.get_sub_account_by_name(
                main_user.id,
                sub_user_name,
                SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES,
            )
        ) is None:
            raise InvalidArgument

        sub_user_id = sub_acc.user_id
        if not SubAccountManager.has_relationship(
            main_user.id,
            sub_user_id,
            SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES,
        ):
            raise SubAccountRelationError

        market_type = kwargs["market_type"]
        if market_type not in (BalanceAccountType.SPOT, BalanceAccountType.MARGIN,
                               BalanceAccountType.FUTURES):
            raise InvalidArgument

        if market_type == BalanceAccountType.SPOT:
            result = ServerClient().get_user_balances(
                sub_user_id, account_id=SPOT_ACCOUNT_ID
            )
            sub_balances = [
                dict(
                    ccy=asset,
                    available=amount_to_str(d["available"], PrecisionEnum.COIN_PLACES),
                    frozen=amount_to_str(d["frozen"], PrecisionEnum.COIN_PLACES),
                )
                for asset, d in result.items()
                if Decimal(d["available"]) + Decimal(d["frozen"]) > 0
            ]
            return api_v2_success(dict(data=sub_balances))

        # MARGIN -> 与 /assets/margin/balance 对齐
        if market_type == BalanceAccountType.MARGIN:
            market = kwargs.get("market", "")
            if market:
                if market not in MarginAccountNameCache.list_online_markets().values():
                    raise InvalidArgument(market)
                balance_list = get_user_margin_balances(sub_user_id, market)
            else:
                balance_list = get_user_margin_balances(sub_user_id)
            result = []
            for data in balance_list:
                balance_base_ccy = data["balance_data"]["sell_type"]
                balance_quote_ccy = data["balance_data"]["buy_type"]
                result.append(
                    dict(
                        margin_account=data["market_type"],
                        base_ccy=data["sell_asset_type"],
                        quote_ccy=data["buy_asset_type"],
                        available=dict(
                            base_ccy=balance_base_ccy["available"],
                            quote_ccy=balance_quote_ccy["available"],
                        ),
                        frozen=dict(
                            base_ccy=balance_base_ccy["frozen"],
                            quote_ccy=balance_quote_ccy["frozen"],
                        ),
                        repaid=dict(
                            base_ccy=data["loan"]["sell_type"],
                            quote_ccy=data["loan"]["buy_type"],
                        ),
                        interest=dict(
                            base_ccy=data["interest"]["sell_type"],
                            quote_ccy=data["interest"]["buy_type"],
                        ),
                        rik_rate=data["rate"],
                        liq_price=data["burst_price"],
                    )
                )
            return api_v2_success(dict(data=result))

        # FUTURES -> 与合约账户余额返回字段对齐
        if market_type == BalanceAccountType.FUTURES:
            perpetual_client = PerpetualServerClient()
            res = perpetual_client.get_user_balances(sub_user_id)
            items = []
            for asset, d in res.items():
                available = d.get("available", Decimal("0"))
                frozen = d.get("frozen", d.get("freeze", Decimal("0")))
                margin = d.get("margin", Decimal("0"))
                transferrable = d.get("transfer", Decimal("0"))
                unrealized = d.get("profit_unreal", Decimal("0"))
                total = Decimal(available) + Decimal(frozen) + Decimal(margin) + Decimal(unrealized)
                if total == 0:
                    continue
                items.append(
                    dict(
                        ccy=asset,
                        available=amount_to_str(available, PrecisionEnum.COIN_PLACES),
                        frozen=amount_to_str(frozen, PrecisionEnum.COIN_PLACES),
                        margin=amount_to_str(margin, PrecisionEnum.COIN_PLACES),
                        transferrable=amount_to_str(transferrable, PrecisionEnum.COIN_PLACES),
                        unrealized_pnl=amount_to_str(unrealized, PrecisionEnum.COIN_PLACES),
                    )
                )
            return api_v2_success(dict(data=items))
        raise InvalidArgument



@ns.route("/info")
@respond_with_code_new_message
class AccountInfoResource(Resource):

    @classmethod
    @require_api_v2_auth(allow_sub_account=True)
    def get(cls, **kwargs):
        """账号信息查询"""
        cur_user = g.user
        row = MaskUser.get_or_create(cur_user.id)
        return dict(user_id=row.mask_id)