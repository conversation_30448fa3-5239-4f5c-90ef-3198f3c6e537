# -*- coding: utf-8 -*-
from flask_restx import fields as fx_fields
from webargs import fields as wa_fields

from flask import g

from app.business.auth import get_admin_user_name_map

from ...common import (Resource, Namespace, respond_with_code)
from ...common.fields import (EnumField, TimestampMarshalField, PageField,
                              LimitField, AmountField, EnumMarshalField, TimestampField)
from ....business import LockAssetHelper
from ....caches import AmmMarketCache
from ....exceptions import InvalidArgument
from ....models import LockedAssetBalance, db, LiquidityLock, User
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectWallet
from ....utils import query_to_page, now

ns = Namespace('Asset - Lock')


@ns.route('/')
@respond_with_code
class LockConfigListResource(Resource):

    marshal_fields = {
        'id': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'create_time': TimestampMarshalField(attribute='created_at'),
        'asset': fx_fields.String,
        'amount': AmountField,
        'lock_time': TimestampMarshalField(attribute='locked_at'),
        'unlock_time': TimestampMarshalField(attribute='unlocked_at'),
        'created_by': fx_fields.Integer,
        'business': EnumMarshalField(
            LockedAssetBalance.Business, output_field_lower=False),
        'business_id': fx_fields.Integer,
        'status': EnumMarshalField(
            LockedAssetBalance.Status, output_field_lower=False),
        'remark': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        sort=EnumField(['ID', 'LOCKED_AT', 'UNLOCKED_AT'], required=True),
        asset=wa_fields.String,
        status=EnumField(LockedAssetBalance.Status),
        user_id=wa_fields.Integer,
        business=EnumField(LockedAssetBalance.Business),
        page=PageField(unlimited=True),
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """资产锁定列表"""
        query = LockedAssetBalance.query
        sort = kwargs['sort']
        if sort == 'ID':
            query = query.order_by(LockedAssetBalance.id.desc())
        elif sort == 'LOCKED_AT':
            query = query.order_by(LockedAssetBalance.locked_at.desc())
        elif sort == 'UNLOCKED_AT':
            query = query.order_by(LockedAssetBalance.unlocked_at.desc())
        if asset := kwargs.get('asset'):
            query = query.filter(LockedAssetBalance.asset == asset)
        if status := kwargs.get('status'):
            query = query.filter(LockedAssetBalance.status == status)
        if user_id := kwargs.get('user_id'):
            query = query.filter(LockedAssetBalance.user_id == user_id)
        if business := kwargs.get('business'):
            query = query.filter(LockedAssetBalance.business == business)
        query = query.order_by(LockedAssetBalance.id.desc())
        result = query_to_page(query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        user_ids = list({item['created_by'] for item in result['data']})
        name_map = get_admin_user_name_map(user_ids)
        for item in result['data']:
            item['created_user_name'] = name_map.get(item['created_by'])
        result["extra"] = dict(
            statuses=LockedAssetBalance.Status,
            businesses=LockedAssetBalance.Business,
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        business_id=wa_fields.Integer(required=True),
        remark=wa_fields.String(required=True),
    ))
    def put(cls, **kwargs):
        """
        钱包-资产-资产解锁
        """
        user_id = kwargs['user_id']
        business_id = kwargs['business_id']
        remark = kwargs['remark']
        if not remark:
            raise InvalidArgument(message='请输入备注')
        row = LockAssetHelper.get_uni_row(LockedAssetBalance.Business.ADMIN, business_id, user_id)
        old_data = row.to_dict(enum_to_name=True)
        row = LockAssetHelper.unlock(
            LockedAssetBalance.Business.ADMIN,
            business_id,
            user_id,
            remark=remark
        )
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.AssetLock,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )


@ns.route('/liquidity')
@respond_with_code
class LiquidityLockResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String,
        status=EnumField(LiquidityLock.Status),
        user_id=wa_fields.Integer,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """钱包-资产-流动性锁定"""
        page, limit = kwargs['page'], kwargs['limit']
        query = LiquidityLock.query
        markets = AmmMarketCache.list_amm_markets()
        if user_id := kwargs.get('user_id'):
            query = query.filter(LiquidityLock.user_id == user_id)
        else:
            if start_time := kwargs.get('start_time'):
                query = query.filter(LiquidityLock.locked_at >= start_time)
            if end_time := kwargs.get('end_time'):
                query = query.filter(LiquidityLock.locked_at <= end_time)
            if market := kwargs.get('market'):
                query = query.filter(LiquidityLock.market == market)
            if status := kwargs.get('status'):
                query = query.filter(LiquidityLock.status == status)
        query = query.order_by(LiquidityLock.id.desc())
        records = query.paginate(page, limit, error_out=False)
        user_ids = [record.user_id for record in records.items]
        users = User.query.filter(User.id.in_(user_ids)).with_entities(
            User.id,
            User.email
        ).all()
        user_email_map = {u.id: u.email for u in users}
        res = []
        for record in records.items:
            res.append(dict(
                user_id=record.user_id,
                email=user_email_map[record.user_id],
                market=record.market,
                locked_at=record.locked_at,
                unlocked_at=record.unlocked_at,
                remark=record.remark,
                status=record.status.name,
                is_locked=record.status == LiquidityLock.Status.LOCKED
            ))
        return dict(
            total=records.total,
            items=res,
            markets=['ALL', ] + markets,
            statuses=LiquidityLock.Status
        )

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        user_id=wa_fields.Integer(required=True),
        remark=wa_fields.String
    ))
    def post(cls, **kwargs):
        """
        钱包-资产-新增流动性锁定
        """
        market, user_id = kwargs['market'], kwargs['user_id']
        lock = LiquidityLock.query.filter(
            LiquidityLock.market == market,
            LiquidityLock.user_id == user_id
        ).first()
        if lock and lock.status == LiquidityLock.Status.LOCKED:
            raise InvalidArgument(message=f"用户<id:{user_id}>的{market}交易对已被锁定过")
        
        if lock:
            lock.locked_at = now()
            lock.unlocked_at = None
            lock.status = LiquidityLock.Status.LOCKED
            lock.remark = kwargs.get('remark')
        else:
            db.session.add(
                LiquidityLock(
                    market=market,
                    user_id=user_id,
                    status=LiquidityLock.Status.LOCKED,
                    locked_at=now(),
                    remark=kwargs.get('remark')
                )
            )
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.LiquidityLock,
            detail=kwargs,
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        market=wa_fields.String(required=True),
        user_id=wa_fields.Integer(required=True),
    ))
    def put(cls, **kwargs):
        """
        钱包-资产-流动性解锁
        """
        now_ = now()
        market, user_id = kwargs['market'], kwargs['user_id']
        lock = LiquidityLock.query.filter(
            LiquidityLock.market == market,
            LiquidityLock.user_id == user_id,
            LiquidityLock.status == LiquidityLock.Status.LOCKED
        ).first()
        if not lock:
            raise InvalidArgument
        old_data = lock.to_dict(enum_to_name=True)

        lock.status = LiquidityLock.Status.UNLOCKED
        lock.unlocked_at = now_
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectWallet.LiquidityLock,
            old_data=old_data,
            new_data=lock.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )

        return dict(
            unlocked_at=now_
        )
