# -*- coding: utf-8 -*-
from decimal import Decimal
from enum import Enum

from flask import g
from webargs import fields

from ....business import get_special_conf_create_operators, ExchangeLogDB
from ....business.admin_tag import AdminTagHelper
from ....business.balance.helper import UserTotalBalanceHelper
from ....exceptions import InvalidArgument
from ....models import User, db, ClearedUser, UserSpecialConfigChangeLog
from ....models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...common import Namespace, Resource, respond_with_code, fields as common_fields
from ...common.fields import PageField, LimitField
from ....caches.user import UserVisitPermissionCache
from ....utils import today_datetime, amount_to_str, batch_iter


ns = Namespace("Only Withdrawal")


@ns.route("")
@respond_with_code
class OnlyWithdrawalListUsersResource(Resource):

    class BalanceLevel(Enum):
        LT_1 = '小于1U的用户'
        GTE_1 = '大于1U的用户'
        GTE_100 = '大于100U的用户'
        GTE_1K = '大于1000U的用户'
        GTE_10K = '大于1万U的用户'
        GTE_100K = '大于10万U的用户'
        GTE_1M = '大于100万U的用户'

    @classmethod
    @ns.use_kwargs(
        dict(
            balance_level=common_fields.EnumField(enum=BalanceLevel),
            user_id=fields.Integer,
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-仅支持提现用户列表 """
        page, limit = kwargs["page"], kwargs["limit"]
        model = ClearedUser
        query = model.query.filter(model.status == model.Status.WITHDRAWAL_ONLY,
                                   model.valid.is_(True))
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        records = query.order_by(model.id.desc()).all()
        user_ids = [record.user_id for record in records]

        ts = int(today_datetime().timestamp())
        if not ExchangeLogDB.user_account_balance_sum_table(ts).exists():
            ts = ts - 86400
        balance_level = kwargs.get("balance_level")
        min_value = {
            cls.BalanceLevel.GTE_1: 1,
            cls.BalanceLevel.GTE_100: 100,
            cls.BalanceLevel.GTE_1K: 1000,
            cls.BalanceLevel.GTE_10K: 10000,
            cls.BalanceLevel.GTE_100K: 100000,
            cls.BalanceLevel.GTE_1M: 1000000,
        }.get(balance_level, None)

        user_balance_map = {}
        for ch_user_ids in batch_iter(user_ids, 1000):
            rs = UserTotalBalanceHelper.get_users_total_balance(ts, ch_user_ids)
            if min_value:
                user_balance_map.update({k: v for k, v in rs.items() if v >= min_value})
            else:
                user_balance_map.update({k: v for k, v in rs.items()})

        if balance_level:
            if balance_level == cls.BalanceLevel.LT_1:
                records = [record for record in records if user_balance_map.get(record.user_id, Decimal()) < 1]
            else:
                records = [record for record in records if user_balance_map.get(record.user_id, None) is not None]

        total = len(records)
        records = records[(page - 1) * limit: page * limit]
        record_ids = [i.id for i in records]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids,
            UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL)
        res = []
        for row in records:
            item = row.to_dict()
            item.update(
                balance=amount_to_str(user_balance_map.get(row.user_id, Decimal()), 2),
                operator=operator_name_dict.get(row.id),
                operator_id=operator_id_dict.get(row.id)
            )
            res.append(item)
        return dict(
            items=res,
            total=total,
            extra=dict(
                balance_levels=cls.BalanceLevel,
                timestamp=ts,
            )
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-仅支持提现用户-新增 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message=f"用户{user_id}不存在")
        whitelist_user_ids = AdminTagHelper.query_clear_whitelist_user_ids()
        if user_id in whitelist_user_ids:
            raise InvalidArgument(message=f"用户{user_id}在白名单中, 请先在Admin Tag标签中移除白名单才能添加")

        model = ClearedUser
        row: model = model.query.filter(
            model.valid.is_(True),
            model.user_id == user_id,
        ).first()
        if row:
            raise InvalidArgument(message=f"用户{user_id}已在仅支持提现/清退用户中")
        else:
            row = model.get_or_create(user_id=user_id)
            row.valid = True
            row.status = model.Status.WITHDRAWAL_ONLY
        row.remark = kwargs["remark"]
        db.session_add_and_commit(row)
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail='加入仅支持提现用户',
            change_remark=kwargs['remark'],
            op_id=row.id
        )
        UserVisitPermissionCache().add_user(user_id, UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Cleared,
            detail=kwargs,
            target_user_id=user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def patch(cls, **kwargs):
        """ 用户-特殊配置-仅支持提现用户-编辑 """
        model = ClearedUser
        user_id = kwargs["user_id"]
        row: model = model.query.filter(
            model.user_id == user_id,
            model.valid.is_(True),
            model.status == model.Status.WITHDRAWAL_ONLY,
        ).first()
        if row is None:
            raise InvalidArgument(message=f"用户{user_id}不在 仅支持提现用户中")
        old_data = row.to_dict(enum_to_name=True)

        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail='修改备注',
            change_remark=kwargs.get("remark", ''),
            op_id=row.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Cleared,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-仅支持提现用户-删除 """
        model = ClearedUser
        user_id = kwargs["user_id"]
        row: model = model.query.filter(
            model.status == model.Status.WITHDRAWAL_ONLY,
            model.user_id == user_id,
            model.valid.is_(True),
        ).first()
        if row is not None:
            row.valid = False
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.ONLY_WITHDRAWAL,
                op_type=UserSpecialConfigChangeLog.OpType.DELETE,
                admin_user_id=g.user.id,
                change_detail='删除仅支持提现用户',
                op_id=row.id
            )
            UserVisitPermissionCache().del_user(user_id,
                                                UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE)

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.Cleared,
                detail=dict(id=row.id, remark=row.remark),
                target_user_id=row.user_id,
            )
        return {}
