# -*- coding: utf-8 -*-
import copy
import datetime
from datetime import date
from decimal import Decimal
from collections import defaultdict
from enum import Enum
from typing import Type, List, Dict
from dateutil.relativedelta import relativedelta

from flask import g
from sqlalchemy import func
from webargs import fields
from app.common import Language, LANGUAGE_NAMES, ReportType, language_name_cn_names, ADMIN_EXPORT_LIMIT, \
    COUNTRY_CODE_CN_NAME_DIC, ReportPlatform
from app.common.countries import list_country_cn_name, AreaInfo, AREAS_MAPPING
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common import fields as common_fields
from app.api.common.fields import PageField, LimitField, DateField, EnumField
from app.exceptions import InvalidArgument
from app.models import (
    DailyUserReport,
    MonthlyUserReport,
    QuarterlyUserReport,
    DailyCountryUserReport,
    MonthlyCountryUserReport,
    DailyLanguageUserReport,
    DailyChannelIncreaseUserReport, DailyCountryIncreaseUserReport,
    MonthlyLanguageUserReport, DailyUserHierarchicalReport,
    DailyPublicityChannelUserReport, DailyUserActiveRetainedReport, DailyTradeUserRetainedReport,
    MonthlyUserActiveRetainedReport,
    MonthlyUserActiveRetainedDetail, MonthlyTradeUserRetainedReport,
    MonthlyTradeUserRetainedDetail, DailyCountryTradeReport, db,
    MonthlyCountryTradeReport, DailyLanguageTradeReport, MonthlyLanguageTradeReport,
    PublicityChannel, DailyPublicityChannelReport,
    DailyPublicityLanguageAreaReport, DailyPublicityCountryReport, DailyPublicityShortLinkReport,
    WeeklyPublicityShortLinkReport, WeeklyPublicityChannelReport,
    WeeklyPublicityCountryReport, WeeklyPublicityLanguageAreaReport,
    MonthlyPublicityShortLinkReport, MonthlyPublicityChannelReport,
    MonthlyPublicityCountryReport, MonthlyPublicityLanguageAreaReport,
    QuarterlyPublicityShortLinkReport, QuarterlyPublicityChannelReport,
    QuarterlyPublicityCountryReport, QuarterlyPublicityLanguageAreaReport,
    LanguageArea, LanguageAreaUserActiveRetainReport,
    CountryUserActiveRetainReport,
    ShortLinkInfo, BusinessSegment, WeeklyPublicityCategoryReport, QuarterlyPublicityCategoryReport,
    WeeklyPublicityBusinessSegmentReport, QuarterlyPublicityBusinessSegmentReport, PublicityMixin,
    WeeklyAppPublicityChannelReport, QuarterlyAppPublicityChannelReport, QuarterlyAreaUserReport,
    QuarterlyCountryUserReport, QuarterlyLanguageUserReport, QuarterlyCountryTradeReport,
    QuarterlyLanguageTradeReport, QuarterlyAreaTradeReport,
)
from app.models.daily import (DailyLossUserReport, DailyAreaUserReport, DailyVipReport, DailyAreaTradeReport,
                              DailyPublicityCategoryReport, DailyPublicityBusinessSegmentReport,
                              DailyAppPublicityChannelReport)
from app.models.mongo.user_business import AfChannelInfoMySQL
from app.models.monthly import MonthlyLossUserReport, MonthlyAreaUserReport, MonthlyVipReport, MonthlyAreaTradeReport, \
    MonthlyPublicityCategoryReport, MonthlyPublicityBusinessSegmentReport, MonthlyAppPublicityChannelReport
from app.utils import timestamp_to_date, current_timestamp, quantize_amount, \
    today, export_xlsx, format_percent, amount_to_str, safe_div
from app.utils.date_ import date_to_datetime, datetime_to_time, cur_quarter, cur_month
from app.schedules.reports.admin_async_download import async_download_increase_channel_report, \
    async_download_active_retained_report, async_download_user_hierarchical_report

ns = Namespace('Report - User')


@ns.route('')
@respond_with_code
class DailySpotDealsResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "total_user", Language.ZH_HANS_CN: "总用户"},
        {"field": "increase_user", Language.ZH_HANS_CN: "新增用户"},
        {"field": "increase_trade_user", Language.ZH_HANS_CN: "新增交易用户"},
        {"field": "increase_sub_user", Language.ZH_HANS_CN: "新增子账号"},
        {"field": "increase_cet_user", Language.ZH_HANS_CN: "CET新增用户"},
        {"field": "increase_refer_user", Language.ZH_HANS_CN: "新增refer"},
        {"field": "increase_kyc", Language.ZH_HANS_CN: "新增KYC"},
        {"field": "deposit_user", Language.ZH_HANS_CN: "充值用户"},
        {"field": "withdraw_user", Language.ZH_HANS_CN: "提现用户"},
        {"field": "local_transfer_user", Language.ZH_HANS_CN: "站内转账用户"},
        {"field": "active_user", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "active_trade_user", Language.ZH_HANS_CN: "交易用户"},
        {"field": "active_spot_user", Language.ZH_HANS_CN: "现货用户"},
        {"field": "exchange_user", Language.ZH_HANS_CN: "兑换用户"},
        {"field": "active_margin_user", Language.ZH_HANS_CN: "杠杆用户"},
        {"field": "active_perpetual_user", Language.ZH_HANS_CN: "合约用户"},
        {"field": "asset_user", Language.ZH_HANS_CN: "资产用户"},
        {"field": "amm_user", Language.ZH_HANS_CN: "AMM用户"},
        {"field": "api_user", Language.ZH_HANS_CN: "API用户"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-用户报表"""

        page = kwargs['page']
        limit = kwargs['limit']

        model_map = {
            'DAILY': DailyUserReport,
            'MONTHLY': MonthlyUserReport,
            'QUARTERLY': QuarterlyUserReport,
        }

        model = model_map.get(kwargs['report_type'], DailyUserReport)

        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)

        total = 0
        if kwargs['export']:
            items = list(query.limit(ADMIN_EXPORT_LIMIT).all())
        else:
            paginate = query.paginate(page, limit, error_out=False)
            items = list(paginate.items)
            total = paginate.total
            page = paginate.page

        if kwargs['export']:
            return export_xlsx(
                filename='user_report',
                data_list=[i.to_dict() for i in items],
                export_headers=cls.export_headers
            )

        return dict(
            records=items,
            total=total,
            page=page,
        )


@ns.route('/hierarchical')
@respond_with_code
class UserHierarchicalReportResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        vip_level=EnumField(DailyUserHierarchicalReport.VipLevel, missing=DailyUserHierarchicalReport.VipLevel.ALL),
        page=PageField(unlimited=True),
        limit=LimitField(max_limit=3000)
    ))
    def get(cls, **kwargs):
        """报表-用户报表-用户分层报表"""
        page, limit = kwargs['page'], kwargs['limit']
        vip_level = kwargs['vip_level']
        query = DailyUserHierarchicalReport.query.filter(
            DailyUserHierarchicalReport.vip_level == vip_level
        )
        if start_date := kwargs.get('start_date'):
            query = query.filter(DailyUserHierarchicalReport.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(DailyUserHierarchicalReport.report_date < end_date)
        pagination = query.order_by(
            DailyUserHierarchicalReport.report_date.desc()
        ).paginate(page, limit, error_out=False)
        records = []
        for item in pagination.items:  # type: DailyCountryUserReport
            records.append(dict(
                id=item.id,
                report_date=item.report_date,
                vip_level="全部" if item.vip_level == DailyUserHierarchicalReport.VipLevel.ALL else item.vip_level.name,
                user_count=item.user_count,
                user_rate=item.user_rate,
                active_user_count=item.active_user_count,
                active_user_rate=quantize_amount(
                    item.active_user_count / item.user_count if item.user_count else 0, 4),
                avg_cet_position_amount=item.avg_cet_position_amount,
                cet_trading_user_count=item.cet_trading_user_count,
                cet_trading_user_rate=quantize_amount(
                    item.cet_trading_user_count / item.user_count if item.user_count else 0, 4),
                spot_trading_user_count=item.spot_trading_user_count,
                spot_trading_user_rate=quantize_amount(
                    item.spot_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_spot_balance_usd=item.median_spot_balance_usd,
                perpetual_trading_user_count=item.perpetual_trading_user_count,
                perpetual_trading_user_rate=quantize_amount(
                    item.perpetual_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_perpetual_balance_usd=item.median_perpetual_balance_usd,
                margin_trading_user_count=item.margin_trading_user_count,
                margin_trading_user_rate=quantize_amount(
                    item.margin_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_margin_balance_usd=item.median_margin_balance_usd,
                investment_trading_user_count=item.investment_trading_user_count,
                investment_trading_user_rate=quantize_amount(
                    item.investment_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_investment_balance_usd=item.median_investment_balance_usd,
                amm_trading_user_count=item.amm_trading_user_count,
                amm_trading_user_rate=quantize_amount(
                    item.amm_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_amm_balance_usd=item.median_amm_balance_usd,
            ))

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
            vip_levels=[i.name for i in DailyUserHierarchicalReport.VipLevel]
        )


@ns.route('/hierarchical-async-download')
@respond_with_code
class UserHierarchicalReportAsyncDownloadResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-用户分层报表-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_user_hierarchical_report.delay(
            email=g.user.email,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/hierarchical/detail')
@respond_with_code
class UserHierarchicalReportDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(required=True, to_date=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-用户分层报表详情"""
        report_date = kwargs['report_date']
        report_query = DailyUserHierarchicalReport.query.filter(
            DailyUserHierarchicalReport.report_date == report_date
        ).all()
        sort_mapper = {level: index for index, level in enumerate(DailyUserHierarchicalReport.VipLevel)}
        report_query.sort(key=lambda x: sort_mapper.get(x, 0))
        records = []
        for item in report_query:
            records.append(dict(
                report_date=item.report_date,
                vip_level="全部" if item.vip_level == DailyUserHierarchicalReport.VipLevel.ALL else item.vip_level.name,
                user_count=item.user_count,
                user_rate=item.user_rate,
                active_user_count=item.active_user_count,
                active_user_rate=quantize_amount(
                    item.active_user_count / item.user_count if item.user_count else 0, 4),
                avg_cet_position_amount=item.avg_cet_position_amount,
                cet_trading_user_count=item.cet_trading_user_count,
                cet_trading_user_rate=quantize_amount(
                    item.cet_trading_user_count / item.user_count if item.user_count else 0, 4),
                spot_trading_user_count=item.spot_trading_user_count,
                spot_trading_user_rate=quantize_amount(
                    item.spot_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_spot_balance_usd=item.median_spot_balance_usd,
                perpetual_trading_user_count=item.perpetual_trading_user_count,
                perpetual_trading_user_rate=quantize_amount(
                    item.perpetual_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_perpetual_balance_usd=item.median_perpetual_balance_usd,
                margin_trading_user_count=item.margin_trading_user_count,
                margin_trading_user_rate=quantize_amount(
                    item.margin_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_margin_balance_usd=item.median_margin_balance_usd,
                investment_trading_user_count=item.investment_trading_user_count,
                investment_trading_user_rate=quantize_amount(
                    item.investment_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_investment_balance_usd=item.median_investment_balance_usd,
                amm_trading_user_count=item.amm_trading_user_count,
                amm_trading_user_rate=quantize_amount(
                    item.amm_trading_user_count / item.user_count if item.user_count else 0, 4),
                median_amm_balance_usd=item.median_amm_balance_usd,
            ))
        return dict(records=records)


@ns.route('/country-statistics')
@respond_with_code
class CountryStatisticsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "balance", Language.ZH_HANS_CN: "总资产"},
        {"field": "total_user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "normal_refer_new_user_count", Language.ZH_HANS_CN: "普通refer用户"},
        {"field": "self_reg_new_user_count", Language.ZH_HANS_CN: "自然注册用户"},
        {"field": "ambassador_refer_new_user_count", Language.ZH_HANS_CN: "大使refer用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "increase_trade_user_count", Language.ZH_HANS_CN: "新增交易用户"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换用户"},
        {"field": "margin_user_count", Language.ZH_HANS_CN: "杠杆用户"},
        {"field": "amm_user_count", Language.ZH_HANS_CN: "AMM用户"},
        {"field": "cet_new_user_count", Language.ZH_HANS_CN: "CET新增用户"},
        {"field": "ambassador_user_count", Language.ZH_HANS_CN: "大使用户"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            sort_type=EnumField(["asc", "desc"]),
            sort_name=EnumField(
                [
                    "balance",
                    "total_user_count",
                    "normal_refer_new_user_count",
                    "margin_user_count",
                    "amm_user_count",
                    "cet_new_user_count",
                    "self_reg_new_user_count",
                    "active_user_count",
                    "trade_user_count",
                    "new_user_count",
                    "asset_user_count",
                    "total_user_count",
                    "increase_trade_user_count",
                    "ambassador_user_count",
                    "ambassador_refer_new_user_count",
                    'spot_trade_user_count',
                    'perpetual_trade_user_count',
                    'exchange_trade_user_count',
                ]
            ),
            report_type=fields.String(missing="DAILY"),
            area=EnumField(AreaInfo),
            query_date=DateField(to_date=True),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-国家分布报表 """
        report_type = kwargs['report_type']
        sort_type = kwargs['sort_type']
        sort_name = kwargs['sort_name']
        _query = DailyCountryUserReport.query
        area = kwargs.get("area")
        area_code_mapping = {
            k: [i.info.cn_name for i in v]
            for k, v in AREAS_MAPPING.items()
        }
        areas_mapping = {k.name: [m.info.cn_name for m in v]
                         for k, v in AREAS_MAPPING.items()}
        areas = {k.name: k.value for k in AREAS_MAPPING.keys()}

        if report_type == 'DAILY':
            yesterday = timestamp_to_date(current_timestamp() - 86400)
            query_date = kwargs.get('query_date') or yesterday
            _query = DailyCountryUserReport.query.filter(
                DailyCountryUserReport.report_date == query_date
            )
            if area:
                _query = _query.filter(
                    DailyCountryUserReport.country.in_(area_code_mapping[area])
                )
        elif report_type == 'MONTHLY':
            today_ = datetime.datetime.today()
            if _query_date := kwargs.get('query_date'):
                query_date = cur_month(_query_date.year, _query_date.month)
            else:
                query_date = cur_month(today_.year, today_.month)
            _query = MonthlyCountryUserReport.query.filter(
                MonthlyCountryUserReport.report_date == query_date
            )
            if area:
                _query = _query.filter(
                    MonthlyCountryUserReport.country.in_(area_code_mapping[area])
                )
        elif report_type == 'QUARTERLY':
            today_ = datetime.datetime.today()
            if _query_date := kwargs.get('query_date'):
                query_date = cur_quarter(_query_date.year, _query_date.month)
            else:
                query_date = cur_quarter(today_.year, today_.month)
            _query = QuarterlyCountryUserReport.query.filter(
                QuarterlyCountryUserReport.report_date == query_date
            )
            if area:
                _query = _query.filter(
                    QuarterlyCountryUserReport.country.in_(area_code_mapping[area])
                )

        record_data = [item.to_dict() for item in _query.all()]
        record_data.sort(
            key=lambda x: x[sort_name],
            reverse=True if sort_type == 'desc' else False)

        sum_active_user_count = 0
        sum_trade_user_count = 0
        sum_new_user_count = 0
        sum_asset_user_count = 0
        sum_total_user_count = 0
        sum_increase_trade_user_count = 0
        sum_ambassador_user_count = 0
        sum_ambassador_refer_new_user_count = 0
        sum_normal_refer_new_user_count = 0
        sum_self_reg_new_user_count = 0
        sum_spot_trade_user_count = 0
        sum_perpetual_trade_user_count = 0
        sum_exchange_trade_user_count = 0
        sum_margin_user_count = 0
        sum_amm_user_count = 0
        sum_cet_new_user_count = 0
        sum_balance = 0
        for country_data in record_data:
            sum_active_user_count += country_data['active_user_count']
            sum_trade_user_count += country_data['trade_user_count']
            sum_new_user_count += country_data['new_user_count']
            sum_asset_user_count += country_data['asset_user_count']
            sum_total_user_count += country_data["total_user_count"]
            sum_increase_trade_user_count += country_data["increase_trade_user_count"]
            sum_ambassador_user_count += country_data["ambassador_user_count"]
            sum_ambassador_refer_new_user_count += country_data["ambassador_refer_new_user_count"]
            sum_normal_refer_new_user_count += country_data['normal_refer_new_user_count']
            sum_self_reg_new_user_count += country_data['self_reg_new_user_count']
            sum_spot_trade_user_count += country_data['spot_trade_user_count']
            sum_perpetual_trade_user_count += country_data['perpetual_trade_user_count']
            sum_exchange_trade_user_count += country_data['exchange_trade_user_count']
            sum_margin_user_count += country_data['margin_user_count']
            sum_amm_user_count += country_data['amm_user_count']
            sum_cet_new_user_count += country_data['cet_new_user_count']
            sum_balance += country_data['balance']

        total_result = {
            "country": "ALL",
            "report_date": record_data[0]["report_date"] if record_data else None,
            "active_user_count": sum_active_user_count,
            "trade_user_count": sum_trade_user_count,
            "new_user_count": sum_new_user_count,
            "asset_user_count": sum_asset_user_count,
            "total_user_count": sum_total_user_count,
            "increase_trade_user_count": sum_increase_trade_user_count,
            "ambassador_user_count": sum_ambassador_user_count,
            "ambassador_refer_new_user_count": sum_ambassador_refer_new_user_count,
            "normal_refer_new_user_count": sum_normal_refer_new_user_count,
            "self_reg_new_user_count": sum_self_reg_new_user_count,
            "spot_trade_user_count": sum_spot_trade_user_count,
            "perpetual_trade_user_count": sum_perpetual_trade_user_count,
            "exchange_trade_user_count": sum_exchange_trade_user_count,
            "margin_user_count": sum_margin_user_count,
            "amm_user_count": sum_amm_user_count,
            "cet_new_user_count": sum_cet_new_user_count,
            "balance": sum_balance
        }
        record_data.insert(0, total_result)

        # 计算百分比
        percent_fields = [
            "active_user_count",
            "trade_user_count",
            "new_user_count",
            "asset_user_count",
            "total_user_count",
            "increase_trade_user_count",
            "ambassador_user_count",
            "ambassador_refer_new_user_count",
            "normal_refer_new_user_count",
            "self_reg_new_user_count",
            "spot_trade_user_count",
            "perpetual_trade_user_count",
            "exchange_trade_user_count",
            "margin_user_count",
            "amm_user_count",
            "cet_new_user_count",
            "balance",
        ]
        for record in record_data:
            for field in percent_fields:
                p_field = f"{field}_percent"
                total_val = total_result[field]
                if field in (
                        "ambassador_refer_new_user_count", 'normal_refer_new_user_count', 'self_reg_new_user_count'):
                    # 某国家大使邀请注册用户占比：该国家大使邀请注册用户总数 / 该国家注册用户总数
                    total_val = record["new_user_count"]
                if total_val == 0:
                    record[p_field] = 0
                else:
                    record[p_field] = quantize_amount(record[field] / total_val, 4)

        if kwargs['export']:
            return export_xlsx(
                filename='user_country_distribution_report',
                data_list=cls.get_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(records=record_data,
                    areas=areas,
                    areas_mapping=areas_mapping
                    )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            total_user_count = f"{record['total_user_count']} " \
                               f"（{format_percent(record['total_user_count_percent'], 2)}）"
            new_user_count = f"{record['new_user_count']} " \
                             f"（{format_percent(record['new_user_count_percent'], 2)}）"
            ambassador_refer_new_user_count = f"{record['ambassador_refer_new_user_count']} " \
                                              f"（{format_percent(record['ambassador_refer_new_user_count_percent'], 2)}）"
            active_user_count = f"{record['active_user_count']} " \
                                f"（{format_percent(record['active_user_count_percent'], 2)}）"
            asset_user_count = f"{record['asset_user_count']} " \
                               f"（{format_percent(record['asset_user_count_percent'], 2)}）"
            trade_user_count = f"{record['trade_user_count']} " \
                               f"（{format_percent(record['trade_user_count_percent'], 2)}）"
            increase_trade_user_count = f"{record['increase_trade_user_count']} " \
                                        f"（{format_percent(record['increase_trade_user_count_percent'], 2)}）"
            ambassador_user_count = f"{record['ambassador_user_count']} " \
                                    f"（{format_percent(record['ambassador_user_count_percent'], 2)}）"

            self_reg_new_user_count = f"{record['self_reg_new_user_count']} " \
                                    f"（{format_percent(record['self_reg_new_user_count_percent'], 2)}）"
            normal_refer_new_user_count = f"{record['normal_refer_new_user_count']} " \
                                    f"（{format_percent(record['normal_refer_new_user_count_percent'], 2)}）"
            spot_trade_user_count = f"{record['spot_trade_user_count']} " \
                                    f"（{format_percent(record['spot_trade_user_count_percent'], 2)}）"
            perpetual_trade_user_count = f"{record['perpetual_trade_user_count']} " \
                                    f"（{format_percent(record['perpetual_trade_user_count_percent'], 2)}）"     
            exchange_trade_user_count = f"{record['exchange_trade_user_count']} " \
                                    f"（{format_percent(record['exchange_trade_user_count_percent'], 2)}）"
            margin_user_count = f"{record['margin_user_count']} " \
                                    f"（{format_percent(record['margin_user_count_percent'], 2)}）"
            amm_user_count = f"{record['amm_user_count']} " \
                                    f"（{format_percent(record['amm_user_count_percent'], 2)}）"
            cet_new_user_count = f"{record['cet_new_user_count']} " \
                                    f"（{format_percent(record['cet_new_user_count_percent'], 2)}）"
            balance = f"{record['balance']} " \
                                 f"（{format_percent(record['balance_percent'], 2)}）"
            record['report_date'] = '--'
            record['total_user_count'] = total_user_count
            record['new_user_count'] = new_user_count
            record['ambassador_refer_new_user_count'] = ambassador_refer_new_user_count
            record['active_user_count'] = active_user_count
            record['asset_user_count'] = asset_user_count
            record['trade_user_count'] = trade_user_count
            record['increase_trade_user_count'] = increase_trade_user_count
            record['ambassador_user_count'] = ambassador_user_count

            record['self_reg_new_user_count'] = self_reg_new_user_count
            record['normal_refer_new_user_count'] = normal_refer_new_user_count
            record['spot_trade_user_count'] = spot_trade_user_count
            record['perpetual_trade_user_count'] = perpetual_trade_user_count
            record['exchange_trade_user_count'] = exchange_trade_user_count
            record['margin_user_count'] = margin_user_count
            record['amm_user_count'] = amm_user_count
            record['cet_new_user_count'] = cet_new_user_count
            record['balance'] = balance
        return records


@ns.route("/country-statistics-detail")
@respond_with_code
class CountryStatisticsDetailResource(Resource):
    export_headers = CountryStatisticsResource.export_headers

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=fields.String(missing="DAILY"),
            country=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            export=fields.Boolean(missing=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-国家分布报表-详情 """
        report_type = kwargs["report_type"]
        start_date: datetime.date = kwargs.get("start_date")
        end_date: datetime.date = kwargs.get("end_date")
        country = kwargs["country"]
        if report_type == "DAILY":
            model = DailyCountryUserReport
        elif report_type == "MONTHLY":
            model = MonthlyCountryUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == "QUARTERLY":
            model = QuarterlyCountryUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        q = model.query.filter(model.country == country)
        if start_date:
            q = q.filter(model.report_date >= start_date)
        if end_date:
            q = q.filter(model.report_date <= end_date)
        q = q.order_by(model.report_date.desc())
        if kwargs['export']:
            items = []
            for row in q.limit(ADMIN_EXPORT_LIMIT).all():
                data = row.to_dict()
                if report_type != "DAILY":
                    data['report_date'] = date_to_datetime(data['report_date']).strftime('%Y-%m')
                items.append(data)
            return export_xlsx(
                filename='country_statistic_detail_report',
                data_list=items,
                export_headers=cls.export_headers
            )

        page, limit = kwargs["page"], kwargs["limit"]
        page_rows = q.paginate(page, limit)
        items = [item.to_dict() for item in page_rows.items]
        return dict(
            total=page_rows.total,
            items=items,
            extra={
                "country_list": list_country_cn_name(),
            },
        )


@ns.route("/country-series")
@respond_with_code
class CountrySeriesResource(Resource):
    class SeriesType(Enum):
        balance = "总资产"
        total_user_count = "总用户"
        new_user_count = "注册用户"
        active_user_count = "活跃用户"
        asset_user_count = "资产用户"
        trade_user_count = "交易用户"
        increase_trade_user_count = "新增交易用户"
        ambassador_user_count = "大使用户"
        ambassador_refer_new_user_count = "大使邀请注册用户"
        normal_refer_new_user_count = "普通refer用户"
        self_reg_new_user_count = "自然注册用户"
        spot_trade_user_count = "现货用户"
        margin_user_count = "杠杆用户"
        perpetual_trade_user_count = "合约用户"
        amm_user_count = "AMM用户"
        cet_new_user_count = "CET新增用户"

    @classmethod
    @ns.use_kwargs(
        dict(
            country=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            series_type=EnumField(SeriesType, required=True),
            report_type=EnumField(ReportType, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-国家分布报表-报表曲线 """
        country = kwargs.get("country")
        series_type = kwargs["series_type"]
        report_type = kwargs["report_type"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyCountryUserReport
        elif report_type == ReportType.MONTHLY:
            report_model = MonthlyCountryUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == ReportType.QUARTERLY:
            report_model = QuarterlyCountryUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        series_type_cols_map = {e: [e.name] for e in cls.SeriesType}
        query_cols = [getattr(report_model, col) for col in series_type_cols_map[series_type]]
        query = report_model.query.filter(
                report_model.country == country
            )
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        query = (
            query
            .with_entities(
                report_model.report_date,
                *query_cols,
            )
            .order_by(report_model.report_date.desc())
            .all()
        )

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = i[1]
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


@ns.route('/area-statistics')
@respond_with_code
class AreaStatisticsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "area", Language.ZH_HANS_CN: "地区"},
        {"field": "balance", Language.ZH_HANS_CN: "总资产"},
        {"field": "total_user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "normal_refer_new_user_count", Language.ZH_HANS_CN: "普通refer用户"},
        {"field": "self_reg_new_user_count", Language.ZH_HANS_CN: "自然注册用户"},
        {"field": "ambassador_refer_new_user_count", Language.ZH_HANS_CN: "大使refer用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "increase_trade_user_count", Language.ZH_HANS_CN: "新增交易用户"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换用户"},
        {"field": "margin_user_count", Language.ZH_HANS_CN: "杠杆用户"},
        {"field": "amm_user_count", Language.ZH_HANS_CN: "AMM用户"},
        {"field": "cet_new_user_count", Language.ZH_HANS_CN: "CET新增用户"},
        {"field": "ambassador_user_count", Language.ZH_HANS_CN: "大使用户"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            sort_type=EnumField(["asc", "desc"]),
            sort_name=EnumField(
                [
                    "balance",
                    "total_user_count",
                    "normal_refer_new_user_count",
                    "margin_user_count",
                    "amm_user_count",
                    "cet_new_user_count",
                    "self_reg_new_user_count",
                    "active_user_count",
                    "trade_user_count",
                    "new_user_count",
                    "asset_user_count",
                    "total_user_count",
                    "increase_trade_user_count",
                    "ambassador_user_count",
                    "ambassador_refer_new_user_count",
                    'spot_trade_user_count',
                    'perpetual_trade_user_count',
                    'exchange_trade_user_count',
                ]
            ),
            report_type=fields.String(missing="DAILY"),
            area=EnumField(AreaInfo),
            query_date=DateField(to_date=True),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-地区分布报表 """
        report_type = kwargs['report_type']
        sort_type = kwargs['sort_type']
        sort_name = kwargs['sort_name']
        _query = DailyAreaUserReport.query

        if report_type == 'DAILY':
            yesterday = timestamp_to_date(current_timestamp() - 86400)
            query_date = kwargs.get('query_date') or yesterday
            model = DailyAreaUserReport
        elif report_type == 'MONTHLY':
            today_ = datetime.datetime.today()
            if _query_date := kwargs.get('query_date'):
                query_date = cur_month(_query_date.year, _query_date.month)
            else:
                query_date = cur_month(today_.year, today_.month)
            model = MonthlyAreaUserReport
        elif report_type == 'QUARTERLY':
            today_ = datetime.datetime.today()
            if _query_date := kwargs.get('query_date'):
                query_date = cur_quarter(_query_date.year, _query_date.month)
            else:
                query_date = cur_quarter(today_.year, today_.month)
            model = QuarterlyAreaUserReport
        _query = model.query.filter(
            model.report_date == query_date
        )
        if area := kwargs.get('area'):
            _query = _query.filter(model.area == area.name)

        record_data = [item.to_dict() for item in _query.all()]
        record_data.sort(
            key=lambda x: x[sort_name],
            reverse=True if sort_type == 'desc' else False)

        sum_active_user_count = 0
        sum_trade_user_count = 0
        sum_new_user_count = 0
        sum_asset_user_count = 0
        sum_total_user_count = 0
        sum_increase_trade_user_count = 0
        sum_ambassador_user_count = 0
        sum_ambassador_refer_new_user_count = 0
        sum_normal_refer_new_user_count = 0
        sum_self_reg_new_user_count = 0
        sum_spot_trade_user_count = 0
        sum_perpetual_trade_user_count = 0
        sum_exchange_trade_user_count = 0
        sum_margin_user_count = 0
        sum_amm_user_count = 0
        sum_cet_new_user_count = 0
        sum_balance = 0
        for country_data in record_data:
            sum_active_user_count += country_data['active_user_count']
            sum_trade_user_count += country_data['trade_user_count']
            sum_new_user_count += country_data['new_user_count']
            sum_asset_user_count += country_data['asset_user_count']
            sum_total_user_count += country_data["total_user_count"]
            sum_increase_trade_user_count += country_data["increase_trade_user_count"]
            sum_ambassador_user_count += country_data["ambassador_user_count"]
            sum_ambassador_refer_new_user_count += country_data["ambassador_refer_new_user_count"]
            sum_normal_refer_new_user_count += country_data['normal_refer_new_user_count']
            sum_self_reg_new_user_count += country_data['self_reg_new_user_count']
            sum_spot_trade_user_count += country_data['spot_trade_user_count']
            sum_perpetual_trade_user_count += country_data['perpetual_trade_user_count']
            sum_exchange_trade_user_count += country_data['exchange_trade_user_count']
            sum_margin_user_count += country_data['margin_user_count']
            sum_amm_user_count += country_data['amm_user_count']
            sum_cet_new_user_count += country_data['cet_new_user_count']
            sum_balance += country_data['balance']

        total_result = {
            "area": "ALL",
            "report_date": record_data[0]["report_date"] if record_data else None,
            "active_user_count": sum_active_user_count,
            "trade_user_count": sum_trade_user_count,
            "new_user_count": sum_new_user_count,
            "asset_user_count": sum_asset_user_count,
            "total_user_count": sum_total_user_count,
            "increase_trade_user_count": sum_increase_trade_user_count,
            "ambassador_user_count": sum_ambassador_user_count,
            "ambassador_refer_new_user_count": sum_ambassador_refer_new_user_count,
            "normal_refer_new_user_count": sum_normal_refer_new_user_count,
            "self_reg_new_user_count": sum_self_reg_new_user_count,
            "spot_trade_user_count": sum_spot_trade_user_count,
            "perpetual_trade_user_count": sum_perpetual_trade_user_count,
            "exchange_trade_user_count": sum_exchange_trade_user_count,
            "margin_user_count": sum_margin_user_count,
            "amm_user_count": sum_amm_user_count,
            "cet_new_user_count": sum_cet_new_user_count,
            "balance": sum_balance
        }
        record_data.insert(0, total_result)

        # 计算百分比
        percent_fields = [
            "active_user_count",
            "trade_user_count",
            "new_user_count",
            "asset_user_count",
            "total_user_count",
            "increase_trade_user_count",
            "ambassador_user_count",
            "ambassador_refer_new_user_count",
            "normal_refer_new_user_count",
            "self_reg_new_user_count",
            "spot_trade_user_count",
            "perpetual_trade_user_count",
            "exchange_trade_user_count",
            "margin_user_count",
            "amm_user_count",
            "cet_new_user_count",
            "balance",
        ]
        for record in record_data:
            for field in percent_fields:
                p_field = f"{field}_percent"
                total_val = total_result[field]
                if field in (
                        "ambassador_refer_new_user_count", 'normal_refer_new_user_count', 'self_reg_new_user_count'):
                    # 某国家大使邀请注册用户占比：该国家大使邀请注册用户总数 / 该国家注册用户总数
                    total_val = record["new_user_count"]
                if total_val == 0:
                    record[p_field] = 0
                else:
                    record[p_field] = quantize_amount(record[field] / total_val, 4)

        if kwargs['export']:
            return export_xlsx(
                filename='user_area_distribution_report',
                data_list=cls.get_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(records=record_data,
                    areas={v.name: v.value for v in AreaInfo}
                    )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            total_user_count = f"{record['total_user_count']} " \
                               f"（{format_percent(record['total_user_count_percent'], 2)}）"
            new_user_count = f"{record['new_user_count']} " \
                             f"（{format_percent(record['new_user_count_percent'], 2)}）"
            ambassador_refer_new_user_count = f"{record['ambassador_refer_new_user_count']} " \
                                              f"（{format_percent(record['ambassador_refer_new_user_count_percent'], 2)}）"
            active_user_count = f"{record['active_user_count']} " \
                                f"（{format_percent(record['active_user_count_percent'], 2)}）"
            asset_user_count = f"{record['asset_user_count']} " \
                               f"（{format_percent(record['asset_user_count_percent'], 2)}）"
            trade_user_count = f"{record['trade_user_count']} " \
                               f"（{format_percent(record['trade_user_count_percent'], 2)}）"
            increase_trade_user_count = f"{record['increase_trade_user_count']} " \
                                        f"（{format_percent(record['increase_trade_user_count_percent'], 2)}）"
            ambassador_user_count = f"{record['ambassador_user_count']} " \
                                    f"（{format_percent(record['ambassador_user_count_percent'], 2)}）"

            self_reg_new_user_count = f"{record['self_reg_new_user_count']} " \
                                    f"（{format_percent(record['self_reg_new_user_count_percent'], 2)}）"
            normal_refer_new_user_count = f"{record['normal_refer_new_user_count']} " \
                                    f"（{format_percent(record['normal_refer_new_user_count_percent'], 2)}）"
            spot_trade_user_count = f"{record['spot_trade_user_count']} " \
                                    f"（{format_percent(record['spot_trade_user_count_percent'], 2)}）"
            perpetual_trade_user_count = f"{record['perpetual_trade_user_count']} " \
                                    f"（{format_percent(record['perpetual_trade_user_count_percent'], 2)}）"
            exchange_trade_user_count = f"{record['exchange_trade_user_count']} " \
                                    f"（{format_percent(record['exchange_trade_user_count_percent'], 2)}）"
            margin_user_count = f"{record['margin_user_count']} " \
                                    f"（{format_percent(record['margin_user_count_percent'], 2)}）"
            amm_user_count = f"{record['amm_user_count']} " \
                                    f"（{format_percent(record['amm_user_count_percent'], 2)}）"
            cet_new_user_count = f"{record['cet_new_user_count']} " \
                                    f"（{format_percent(record['cet_new_user_count_percent'], 2)}）"
            balance = f"{record['balance']} " \
                      f"（{format_percent(record['balance_percent'], 2)}）"
            record['report_date'] = '--'
            record['total_user_count'] = total_user_count
            record['new_user_count'] = new_user_count
            record['ambassador_refer_new_user_count'] = ambassador_refer_new_user_count
            record['active_user_count'] = active_user_count
            record['asset_user_count'] = asset_user_count
            record['trade_user_count'] = trade_user_count
            record['increase_trade_user_count'] = increase_trade_user_count
            record['ambassador_user_count'] = ambassador_user_count

            record['self_reg_new_user_count'] = self_reg_new_user_count
            record['normal_refer_new_user_count'] = normal_refer_new_user_count
            record['spot_trade_user_count'] = spot_trade_user_count
            record['perpetual_trade_user_count'] = perpetual_trade_user_count
            record['exchange_trade_user_count'] = exchange_trade_user_count
            record['margin_user_count'] = margin_user_count
            record['amm_user_count'] = amm_user_count
            record['cet_new_user_count'] = cet_new_user_count
            record['balance'] = balance
        return records


@ns.route("/area-statistics-detail")
@respond_with_code
class AreaStatisticsDetailResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "area", Language.ZH_HANS_CN: "地区"},
        {"field": "balance", Language.ZH_HANS_CN: "总资产"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "normal_refer_new_user_count", Language.ZH_HANS_CN: "普通refer用户"},
        {"field": "self_reg_new_user_count", Language.ZH_HANS_CN: "自然注册用户"},
        {"field": "ambassador_refer_new_user_count", Language.ZH_HANS_CN: "大使refer用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "increase_trade_user_count", Language.ZH_HANS_CN: "新增交易用户"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "margin_user_count", Language.ZH_HANS_CN: "杠杆用户"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "amm_user_count", Language.ZH_HANS_CN: "AMM用户"},
        {"field": "ambassador_user_count", Language.ZH_HANS_CN: "大使用户"},
        {"field": "cet_new_user_count", Language.ZH_HANS_CN: "CET新增用户"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=fields.String(missing="DAILY"),
            area=EnumField(AreaInfo, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            export=fields.Boolean(missing=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-地区分布报表-详情 """
        report_type = kwargs["report_type"]
        start_date: datetime.date = kwargs.get("start_date")
        end_date: datetime.date = kwargs.get("end_date")
        area = kwargs["area"]
        if report_type == "DAILY":
            model = DailyAreaUserReport
        elif report_type == "MONTHLY":
            model = MonthlyAreaUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == "QUARTERLY":
            model = QuarterlyAreaUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        q = model.query.filter(model.area == area.name)
        if start_date:
            q = q.filter(model.report_date >= start_date)
        if end_date:
            q = q.filter(model.report_date <= end_date)
        q = q.order_by(model.report_date.desc())
        if kwargs['export']:
            items = []
            for row in q.limit(ADMIN_EXPORT_LIMIT).all():
                data = row.to_dict()
                if report_type != "DAILY":
                    data['report_date'] = date_to_datetime(data['report_date']).strftime('%Y-%m')
                items.append(data)
            return export_xlsx(
                filename='area_statistic_detail_report',
                data_list=items,
                export_headers=cls.export_headers
            )

        page, limit = kwargs["page"], kwargs["limit"]
        page_rows = q.paginate(page, limit)
        items = [item.to_dict() for item in page_rows.items]
        return dict(
            total=page_rows.total,
            items=items,
            extra={
                "areas": {v.name: v.value for v in AreaInfo},
                "country_list": list_country_cn_name(),
            },
        )


@ns.route("/area-series")
@respond_with_code
class AreaSeriesResource(Resource):
    SeriesType = CountrySeriesResource.SeriesType

    @classmethod
    @ns.use_kwargs(
        dict(
            area=EnumField(AreaInfo, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            series_type=EnumField(SeriesType, required=True),
            report_type=EnumField(ReportType, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-地区分布报表-报表曲线 """
        area = kwargs.get("area")
        series_type = kwargs["series_type"]
        report_type = kwargs["report_type"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyAreaUserReport
        elif report_type == ReportType.MONTHLY:
            report_model = MonthlyAreaUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == ReportType.QUARTERLY:
            report_model = QuarterlyAreaUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        series_type_cols_map = {e: [e.name] for e in cls.SeriesType}
        query_cols = [getattr(report_model, col) for col in series_type_cols_map[series_type]]
        query = report_model.query.filter(
            report_model.area == area.name,
        )
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        query = (
            query
            .with_entities(
                report_model.report_date,
                *query_cols,
            )
            .order_by(report_model.report_date.desc())
            .all()
        )

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = i[1]
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


@ns.route("/language-statistics")
@respond_with_code
class LanguageStatisticsResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            sort_type=EnumField(["asc", "desc"]),
            sort_name=EnumField(
                [
                    "balance",
                    "total_user_count",
                    "normal_refer_new_user_count",
                    "margin_user_count",
                    "amm_user_count",
                    "cet_new_user_count",
                    "self_reg_new_user_count",
                    "active_user_count",
                    "trade_user_count",
                    "new_user_count",
                    "asset_user_count",
                    "total_user_count",
                    "increase_trade_user_count",
                    "ambassador_user_count",
                    "ambassador_refer_new_user_count",
                    'spot_trade_user_count',
                    'perpetual_trade_user_count',
                    'exchange_trade_user_count',
                ]
            ),
            query_date=DateField(to_date=True),
            report_type=EnumField(ReportType, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-语言分布报表 """
        report_type = kwargs["report_type"]
        sort_type = kwargs["sort_type"]
        sort_name = kwargs["sort_name"]

        if report_type == ReportType.DAILY:
            yesterday = timestamp_to_date(current_timestamp() - 86400)
            query_date = kwargs.get("query_date") or yesterday
            _query = DailyLanguageUserReport.query.filter(
                DailyLanguageUserReport.report_date == query_date,
                DailyLanguageUserReport.language.notin_(('',))
            )
        elif report_type == ReportType.MONTHLY:
            _query_date = kwargs.get('query_date') or datetime.datetime.today()
            query_date = cur_month(_query_date.year, _query_date.month)
            _query = MonthlyLanguageUserReport.query.filter(
                MonthlyLanguageUserReport.report_date == query_date,
                MonthlyLanguageUserReport.language.notin_(('',))
            )
        elif report_type == ReportType.QUARTERLY:
            _query_date = kwargs.get('query_date') or datetime.datetime.today()
            query_date = cur_quarter(_query_date.year, _query_date.month)
            _query = QuarterlyLanguageUserReport.query.filter(
                QuarterlyLanguageUserReport.report_date == query_date,
                QuarterlyLanguageUserReport.language.notin_(('',))
            )

        records = [item.to_dict() for item in _query.all()]
        records.sort(
            key=lambda x: x[sort_name],
            reverse=True if sort_type == "desc" else False,
        )

        sum_active_user_count = 0
        sum_trade_user_count = 0
        sum_new_user_count = 0
        sum_asset_user_count = 0
        sum_total_user_count = 0
        sum_increase_trade_user_count = 0
        sum_ambassador_user_count = 0
        sum_ambassador_refer_new_user_count = 0
        sum_normal_refer_new_user_count = 0
        sum_self_reg_new_user_count = 0
        sum_spot_trade_user_count = 0
        sum_perpetual_trade_user_count = 0
        sum_exchange_trade_user_count = 0
        sum_margin_user_count = 0
        sum_amm_user_count = 0
        sum_cet_new_user_count = 0
        sum_balance = 0
        for language_record in records:
            sum_active_user_count += language_record["active_user_count"]
            sum_trade_user_count += language_record["trade_user_count"]
            sum_new_user_count += language_record["new_user_count"]
            sum_asset_user_count += language_record["asset_user_count"]
            sum_total_user_count += language_record["total_user_count"]
            sum_increase_trade_user_count += language_record["increase_trade_user_count"]
            sum_ambassador_user_count += language_record["ambassador_user_count"]
            sum_ambassador_refer_new_user_count += language_record["ambassador_refer_new_user_count"]
            sum_normal_refer_new_user_count += language_record['normal_refer_new_user_count']
            sum_self_reg_new_user_count += language_record['self_reg_new_user_count']
            sum_spot_trade_user_count += language_record['spot_trade_user_count']
            sum_perpetual_trade_user_count += language_record['perpetual_trade_user_count']
            sum_exchange_trade_user_count += language_record['exchange_trade_user_count']
            sum_margin_user_count += language_record['margin_user_count']
            sum_amm_user_count += language_record['amm_user_count']
            sum_cet_new_user_count += language_record['cet_new_user_count']
            sum_balance += language_record['balance']
            lang_str = language_record["language"]
            if hasattr(Language, lang_str):
                # see module `utils.config_`: _CONVERTERS[Enum] -> _to_enum
                lang_enum = getattr(Language, lang_str)
                language_chinese_name = LANGUAGE_NAMES[lang_enum].chinese
            else:
                language_chinese_name = lang_str
            language_record["language_chinese_name"] = language_chinese_name

        total_record = {
            "language": "ALL",
            "language_chinese_name": "ALL",
            "report_date": records[0]["report_date"] if records else None,
            "active_user_count": sum_active_user_count,
            "trade_user_count": sum_trade_user_count,
            "new_user_count": sum_new_user_count,
            "asset_user_count": sum_asset_user_count,
            "total_user_count": sum_total_user_count,
            "increase_trade_user_count": sum_increase_trade_user_count,
            "ambassador_user_count": sum_ambassador_user_count,
            "ambassador_refer_new_user_count": sum_ambassador_refer_new_user_count,
            "normal_refer_new_user_count": sum_normal_refer_new_user_count,
            "self_reg_new_user_count": sum_self_reg_new_user_count,
            "spot_trade_user_count": sum_spot_trade_user_count,
            "perpetual_trade_user_count": sum_perpetual_trade_user_count,
            "exchange_trade_user_count": sum_exchange_trade_user_count,
            "margin_user_count": sum_margin_user_count,
            "amm_user_count": sum_amm_user_count,
            "cet_new_user_count": sum_cet_new_user_count,
            "balance": sum_balance
        }
        records.insert(0, total_record)

        # 计算百分比
        percent_fields = [
            "active_user_count",
            "trade_user_count",
            "new_user_count",
            "asset_user_count",
            "total_user_count",
            "increase_trade_user_count",
            "ambassador_user_count",
            "ambassador_refer_new_user_count",
            "normal_refer_new_user_count",
            "self_reg_new_user_count",
            "spot_trade_user_count",
            "perpetual_trade_user_count",
            "exchange_trade_user_count",
            "margin_user_count",
            "amm_user_count",
            "cet_new_user_count",
            "balance",
        ]
        for record in records:
            for field in percent_fields:
                p_field = f"{field}_percent"
                total_val = total_record[field]
                if field in (
                        "ambassador_refer_new_user_count", 'normal_refer_new_user_count', 'self_reg_new_user_count'):
                    # 某国家大使邀请注册用户占比：该国家大使邀请注册用户总数 / 该国家注册用户总数
                    total_val = record["new_user_count"]
                if total_val == 0:
                    record[p_field] = 0
                else:
                    record[p_field] = quantize_amount(record[field] / total_val, 4)

        return dict(records=records)


@ns.route("/language-statistics-detail")
@respond_with_code
class LanguageStatisticsDetailResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "language_chinese_name", Language.ZH_HANS_CN: "语言"},
        {"field": "balance", Language.ZH_HANS_CN: "总资产"},
        {"field": "new_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "normal_refer_new_user_count", Language.ZH_HANS_CN: "普通refer用户"},
        {"field": "self_reg_new_user_count", Language.ZH_HANS_CN: "自然注册用户"},
        {"field": "ambassador_refer_new_user_count", Language.ZH_HANS_CN: "大使refer用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "increase_trade_user_count", Language.ZH_HANS_CN: "新增交易用户"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "margin_user_count", Language.ZH_HANS_CN: "杠杆用户"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "amm_user_count", Language.ZH_HANS_CN: "AMM用户"},
        {"field": "ambassador_user_count", Language.ZH_HANS_CN: "大使用户"},
        {"field": "cet_new_user_count", Language.ZH_HANS_CN: "CET新增用户"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=fields.String(missing="DAILY"),
            language=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            export=fields.Boolean(missing=False),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-语言分布报表-详情 """
        report_type = kwargs["report_type"]
        start_date: datetime.date = kwargs.get("start_date")
        end_date: datetime.date = kwargs.get("end_date")
        language = kwargs["language"]
        if report_type == "DAILY":
            model = DailyLanguageUserReport
        elif report_type == "MONTHLY":
            model = MonthlyLanguageUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == "QUARTERLY":
            model = QuarterlyLanguageUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        q = model.query.filter(model.language == language)
        if start_date:
            q = q.filter(model.report_date >= start_date)
        if end_date:
            q = q.filter(model.report_date <= end_date)
        q = q.filter(model.language.notin_(('',)))
        q = q.order_by(model.report_date.desc())
        if kwargs['export']:
            items = []
            for row in q.limit(ADMIN_EXPORT_LIMIT).all():
                data = row.to_dict()
                lang_str = data["language"]
                if hasattr(Language, lang_str):
                    lang_enum = getattr(Language, lang_str)
                    language_chinese_name = LANGUAGE_NAMES[lang_enum].chinese
                else:
                    language_chinese_name = lang_str
                data['language_chinese_name'] = language_chinese_name
                if report_type != "DAILY":
                    data['report_date'] = date_to_datetime(data['report_date']).strftime('%Y-%m')
                items.append(data)
            return export_xlsx(
                filename='language_statistic_detail_report',
                data_list=items,
                export_headers=cls.export_headers
            )
        page, limit = kwargs["page"], kwargs["limit"]
        page_rows = q.paginate(page, limit)
        items = [item.to_dict() for item in page_rows.items]
        for language_record in items:
            lang_str = language_record["language"]
            if hasattr(Language, lang_str):
                lang_enum = getattr(Language, lang_str)
                language_chinese_name = LANGUAGE_NAMES[lang_enum].chinese
            else:
                language_chinese_name = lang_str
            language_record["language_chinese_name"] = language_chinese_name
        lang_dict = {k.name: v.chinese for k, v in LANGUAGE_NAMES.items()}
        lang_dict["其他"] = "其他"
        return dict(
            total=page_rows.total,
            items=items,
            extra={
                "language_dict": lang_dict,
            },
        )


@ns.route("/language-series")
@respond_with_code
class LanguageSeriesResource(Resource):
    SeriesType = CountrySeriesResource.SeriesType

    @classmethod
    @ns.use_kwargs(
        dict(
            language=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            series_type=EnumField(SeriesType, required=True),
            report_type=EnumField(ReportType, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 报表-国际化报表-语言分布报表-报表曲线 """
        language = kwargs.get("language")
        series_type = kwargs["series_type"]
        report_type = kwargs["report_type"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyLanguageUserReport
        elif report_type == ReportType.MONTHLY:
            report_model = MonthlyLanguageUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == ReportType.QUARTERLY:
            report_model = QuarterlyLanguageUserReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        series_type_cols_map = {e: [e.name] for e in cls.SeriesType}
        query_cols = [getattr(report_model, col) for col in series_type_cols_map[series_type]]
        query = report_model.query.filter(
            report_model.language == language,
        )
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        query = (
            query
            .with_entities(
                report_model.report_date,
                *query_cols,
            )
            .order_by(report_model.report_date.desc())
            .all()
        )

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = i[1]
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


class CountryLanguageTradeMixin:

    fmt_data_fields = (
        'trade_user_count', 'spot_trade_user_count',
        'perpetual_trade_user_count', 'exchange_trade_user_count',
        'spot_trade_amount','perpetual_trade_amount', 'exchange_trade_amount',
        'spot_fee_amount', 'perpetual_fee_amount', 'exchange_fee_amount',
        'avg_spot_fee_amount', 'avg_perpetual_fee_amount', 'avg_exchange_fee_amount',
        'invitee_fee', 'refer_usd', 'refer_expense_usd',
    )
    data_fields = fmt_data_fields + ('refer_rate', )
    percent_fields = (
        'trade_user_count', 'spot_trade_user_count',
        'perpetual_trade_user_count', 'exchange_trade_user_count',
        'spot_trade_amount', 'perpetual_trade_amount', 'exchange_trade_amount',
        'spot_fee_amount', 'perpetual_fee_amount', 'exchange_fee_amount',
        'invitee_fee', 'refer_usd', 'refer_expense_usd',
    )

    class SeriesType(Enum):
        trade_user_count = "交易人数"
        spot_trade_user_count = "现货交易人数"
        perpetual_trade_user_count = "合约交易人数"
        exchange_trade_user_count = "兑换交易人数"
        spot_trade_amount = "现货交易额"
        perpetual_trade_amount = "合约交易额"
        exchange_trade_amount = "兑换交易额"
        spot_fee_amount = "现货手续费"
        perpetual_fee_amount = "合约手续费"
        exchange_fee_amount = "兑换手续费"
        avg_spot_fee_amount = "现货人均手续费"
        avg_perpetual_fee_amount = "合约人均手续费"
        avg_exchange_fee_amount = "兑换人均手续费"
        invitee_fee = "Refer贡献手续费(USD)"
        refer_usd = "收到返佣总额(USD)"
        refer_expense_usd = "返佣支出总额(USD)"
        refer_rate = "返佣比例"

    series_type_cols_map = {
        SeriesType.trade_user_count: ["trade_user_count"],
        SeriesType.spot_trade_user_count: ["spot_trade_user_count"],
        SeriesType.perpetual_trade_user_count: ["perpetual_trade_user_count"],
        SeriesType.exchange_trade_user_count: ["exchange_trade_user_count"],
        SeriesType.spot_trade_amount: ["spot_trade_amount"],
        SeriesType.perpetual_trade_amount: ["perpetual_trade_amount"],
        SeriesType.exchange_trade_amount: ["exchange_trade_amount"],
        SeriesType.spot_fee_amount: ["spot_fee_amount"],
        SeriesType.perpetual_fee_amount: ["perpetual_fee_amount"],
        SeriesType.exchange_fee_amount: ["exchange_fee_amount"],
        SeriesType.avg_spot_fee_amount: ["avg_spot_fee_amount"],
        SeriesType.avg_perpetual_fee_amount: ["avg_perpetual_fee_amount"],
        SeriesType.avg_exchange_fee_amount: ["avg_exchange_fee_amount"],
        SeriesType.invitee_fee: ["invitee_fee"],
        SeriesType.refer_usd: ["refer_usd"],
        SeriesType.refer_expense_usd: ["refer_expense_usd"],
        SeriesType.refer_rate: ["refer_rate"],
    }

    @classmethod
    def get_static_res(cls, query_model: Type[db.Model], static_category: str, **kwargs) -> (List[Dict], int):
        report_date = kwargs.get('report_date')
        sort_name = kwargs.get('sort_name')
        sort_type = kwargs.get('sort_type')
        if not report_date:
            report_date = cls.get_latest_rec_date(query_model)
            if not report_date:
                return []
        if kwargs['report_type'] == 'MONTHLY':
            report_date = cur_month(report_date.year, report_date.month)
        elif kwargs['report_type'] == 'QUARTERLY':
            report_date = cur_quarter(report_date.year, report_date.month)

        if from_detail := kwargs.get('from_detail'):
            query = query_model.query.filter()
            if start_date := kwargs.get('start_date'):
                query = query.filter(query_model.report_date >= start_date)
            if end_date := kwargs.get('end_date'):
                query = query.filter(query_model.report_date < end_date)
        else:
            query = query_model.query.filter(query_model.report_date == report_date)
        if static_category == 'country':
            if area := kwargs.get("area"):
                area_code_mapping = {
                    k: [i.info.cn_name for i in v]
                    for k, v in AREAS_MAPPING.items()
                }
                query = query.filter(
                    query_model.country.in_(area_code_mapping[area])
                )
        if static_category == 'area':
            if area := kwargs.get("area"):
                query = query.filter(query_model.area == area.name)
        if from_detail:
            query = query.order_by(query_model.report_date.desc())
            page_rows = query.paginate(kwargs["page"], kwargs["limit"])
            items = page_rows.items
        else:
            if sort_name:
                field = getattr(query_model, sort_name)
            else:
                field = query_model.trade_user_count
            if sort_type == 'desc':
                query = query.order_by(field.desc())
            else:
                query = query.order_by(field.asc())
            items = query.all()
        all_ = {'report_date': report_date, static_category: '全部'}
        res = cls.add_all_and_fmt_val_and_percent(items, all_)
        if len(res) > 0:
            res[0]['refer_rate'] = amount_to_str(
                Decimal(res[0]['refer_expense_usd']) / Decimal(res[0]['invitee_fee']) if Decimal(res[0]['invitee_fee']) else Decimal(), 4)
            if from_detail and res:
                res.pop(0)
        return res, query.count()

    @classmethod
    def get_latest_rec_date(cls, query_model: Type[db.Model]):
        rec = query_model.query.order_by(query_model.id.desc()).first()
        if rec:
            return rec.report_date
        return

    @classmethod
    def add_all_and_fmt_val_and_percent(cls, data_lis: List, all_: Dict) -> List:
        res = []
        if not data_lis:
            return res
        for record in data_lis:
            item = record.to_dict()
            res.append(item)
            for field in cls.fmt_data_fields:
                val = item[field]
                if field in all_:
                    all_[field] += val
                else:
                    all_[field] = val
                item[field] = cls._fmt_decimal_val(val)
        all_copy = copy.deepcopy(all_)
        for field in cls.fmt_data_fields:
            all_[field] = cls._fmt_decimal_val(all_[field])
        res.insert(0, all_)

        for row in res:
            for field in cls.percent_fields:
                total_val = all_copy[field]
                val = Decimal(row[field])
                percent_field = f'{field}_percent'
                percent_val = val / total_val if total_val else 0
                row[percent_field] = format_percent(percent_val)
        return res

    @classmethod
    def combine_percent_data(cls, data_list: List) -> List:
        for field in cls.percent_fields:
            for row in data_list:
                percent_field = f'{field}_percent'
                row[field] = f'{row[field]}({row[percent_field]})'
        return data_list

    @classmethod
    def _fmt_decimal_val(cls, val):
        if type(val) is Decimal:
            val = amount_to_str(val, 2)
        return val

    @classmethod
    def fmt_item(cls, row: Dict) -> Dict:
        for k, v in row.items():
            row[k] = cls._fmt_decimal_val(v)
        return row

    @classmethod
    def _format_series_records(cls, records):
        result_series = []
        for i in records:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = cls._fmt_decimal_val(i[1])
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])
        return result_series


@ns.route('/area-trade-statistics')
@respond_with_code
class AreaTradeStatisticsResource(Resource, CountryLanguageTradeMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "area", Language.ZH_HANS_CN: "地区"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货交易人数"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约交易人数"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换交易人数"},
        {"field": "spot_trade_amount", Language.ZH_HANS_CN: "现货交易额(USD)"},
        {"field": "perpetual_trade_amount", Language.ZH_HANS_CN: "合约交易额(USD)"},
        {"field": "exchange_trade_amount", Language.ZH_HANS_CN: "兑换交易额(USD)"},
        {"field": "spot_fee_amount", Language.ZH_HANS_CN: "现货手续费(USD)"},
        {"field": "perpetual_fee_amount", Language.ZH_HANS_CN: "合约手续费(USD)"},
        {"field": "exchange_fee_amount", Language.ZH_HANS_CN: "兑换手续费(USD)"},
        {"field": "avg_spot_fee_amount", Language.ZH_HANS_CN: "现货人均手续费(USD)"},
        {"field": "avg_perpetual_fee_amount", Language.ZH_HANS_CN: "合约人均手续费(USD)"},
        {"field": "avg_exchange_fee_amount", Language.ZH_HANS_CN: "兑换人均手续费(USD)"},
        {"field": "invitee_fee", Language.ZH_HANS_CN: "Refer贡献手续费(USD)"},
        {"field": "refer_expense_usd", Language.ZH_HANS_CN: "返佣支出总额(USD)"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "收到返佣总额(USD)"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(missing='DAILY'),
        report_date=fields.Date,
        start_date=fields.Date,
        end_date=fields.Date,
        from_detail=fields.Boolean(missing=False),
        area=EnumField(AreaInfo),
        sort_name=EnumField(CountryLanguageTradeMixin.data_fields, missing='trade_user_count'),
        sort_type=fields.String(missing='desc'),
        page=PageField(missing=1),
        limit=PageField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-地区交易分布报表"""
        if kwargs['report_type'] == 'DAILY':
            query_model = DailyAreaTradeReport
        elif kwargs['report_type'] == 'MONTHLY':
            query_model = MonthlyAreaTradeReport
        elif kwargs['report_type'] == 'QUARTERLY':
            query_model = QuarterlyAreaTradeReport
        data_list, total = cls.get_static_res(query_model, 'area', **kwargs)
        res = data_list
        if kwargs['export']:
            # data_list = cls.combine_percent_data(res)
            return export_xlsx(
                filename='area_trade_statistics_report',
                data_list=data_list,
                export_headers=cls.export_headers
            )
        return dict(records=res, areas={v.name: v.value for v in AreaInfo}, total=total)


@ns.route('/country-trade-statistics')
@respond_with_code
class CountryTradeStatisticsResource(Resource, CountryLanguageTradeMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货交易人数"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约交易人数"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换交易人数"},
        {"field": "spot_trade_amount", Language.ZH_HANS_CN: "现货交易额(USD)"},
        {"field": "perpetual_trade_amount", Language.ZH_HANS_CN: "合约交易额(USD)"},
        {"field": "exchange_trade_amount", Language.ZH_HANS_CN: "兑换交易额(USD)"},
        {"field": "spot_fee_amount", Language.ZH_HANS_CN: "现货手续费(USD)"},
        {"field": "perpetual_fee_amount", Language.ZH_HANS_CN: "合约手续费(USD)"},
        {"field": "exchange_fee_amount", Language.ZH_HANS_CN: "兑换手续费(USD)"},
        {"field": "avg_spot_fee_amount", Language.ZH_HANS_CN: "现货人均手续费(USD)"},
        {"field": "avg_perpetual_fee_amount", Language.ZH_HANS_CN: "合约人均手续费(USD)"},
        {"field": "avg_exchange_fee_amount", Language.ZH_HANS_CN: "兑换人均手续费(USD)"},
        {"field": "invitee_fee", Language.ZH_HANS_CN: "Refer贡献手续费(USD)"},
        {"field": "refer_expense_usd", Language.ZH_HANS_CN: "返佣支出总额(USD)"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "收到返佣总额(USD)"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(missing='DAILY'),
        report_date=fields.Date,
        area=EnumField(AreaInfo),
        sort_name=EnumField(CountryLanguageTradeMixin.data_fields, missing='trade_user_count'),
        sort_type=fields.String(missing='desc'),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-国家交易分布报表"""
        if kwargs['report_type'] == 'DAILY':
            query_model = DailyCountryTradeReport
        elif kwargs['report_type'] == 'MONTHLY':
            query_model = MonthlyCountryTradeReport
        elif kwargs['report_type'] == 'QUARTERLY':
            query_model = QuarterlyCountryTradeReport
        data_list, _ = cls.get_static_res(query_model, 'country', **kwargs)
        res = [i for i in data_list if i['country'] != '中国']
        if kwargs['export']:
            data_list = cls.combine_percent_data(res)
            return export_xlsx(
                filename='country_trade_statistics_report',
                data_list=data_list,
                export_headers=cls.export_headers
            )
        return dict(records=res, areas={v.name: v.value for v in AreaInfo})


@ns.route('/country-trade-statistics-detail')
@respond_with_code
class CountryTradeStatisticsDetailResource(Resource, CountryLanguageTradeMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货交易人数"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约交易人数"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换交易人数"},
        {"field": "spot_trade_amount", Language.ZH_HANS_CN: "现货交易额(USD)"},
        {"field": "perpetual_trade_amount", Language.ZH_HANS_CN: "合约交易额(USD)"},
        {"field": "exchange_trade_amount", Language.ZH_HANS_CN: "兑换交易额(USD)"},
        {"field": "spot_fee_amount", Language.ZH_HANS_CN: "现货手续费(USD)"},
        {"field": "perpetual_fee_amount", Language.ZH_HANS_CN: "合约手续费(USD)"},
        {"field": "exchange_fee_amount", Language.ZH_HANS_CN: "兑换手续费(USD)"},
        {"field": "avg_spot_fee_amount", Language.ZH_HANS_CN: "现货人均手续费(USD)"},
        {"field": "avg_perpetual_fee_amount", Language.ZH_HANS_CN: "合约人均手续费(USD)"},
        {"field": "avg_exchange_fee_amount", Language.ZH_HANS_CN: "兑换人均手续费(USD)"},
        {"field": "invitee_fee", Language.ZH_HANS_CN: "Refer贡献手续费(USD)"},
        {"field": "refer_expense_usd", Language.ZH_HANS_CN: "返佣支出总额(USD)"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "收到返佣总额(USD)"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(missing='DAILY'),
        country=fields.String(required=True),
        start_date=fields.Date,
        end_date=fields.Date,
        page=PageField(missing=1),
        limit=PageField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-国家交易分布报表-详情"""
        if kwargs['report_type'] == 'DAILY':
            query_model = DailyCountryTradeReport
        elif kwargs['report_type'] == 'MONTHLY':
            query_model = MonthlyCountryTradeReport
        elif kwargs['report_type'] == 'QUARTERLY':
            query_model = QuarterlyCountryTradeReport
        query = query_model.query.filter(
            query_model.country == kwargs['country']
        ).order_by(query_model.id.desc())

        if start_date := kwargs.get('start_date'):
            query = query.filter(query_model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(query_model.report_date <= end_date)
        if kwargs['export']:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            res = cls._fmt_records(records)
            return export_xlsx(
                filename='country_trade_statistics_detail_report',
                data_list=res,
                export_headers=cls.export_headers
            )
        page, limit = kwargs['page'], kwargs['limit']
        paginate = query.paginate(page, limit, error_out=False)
        record_data = cls._fmt_records(paginate.items)
        countries = tuple(set(list_country_cn_name()) - {'中国'})
        return dict(items=record_data,
                    total=paginate.total,
                    extra=dict(country_list=countries))

    @classmethod
    def _fmt_records(cls, records):
        res = []
        for record in records:
            item = record.to_dict()
            if item['country'] == '中国':
                continue
            res.append(cls.fmt_item(item))
        return res


@ns.route("/country-trade-series")
@respond_with_code
class CountryTradeSeriesResource(Resource, CountryLanguageTradeMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            country=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            series_type=EnumField(CountryLanguageTradeMixin.SeriesType, required=True),
            report_type=EnumField(ReportType, required=True),
        )
    )
    def get(cls, **kwargs):
        """报表-国际化报表-国家交易分布报表-报表曲线"""
        country = kwargs.get("country")
        series_type = kwargs["series_type"]
        report_type = kwargs["report_type"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if report_type == ReportType.DAILY:
            report_model = DailyCountryTradeReport
        elif report_type == ReportType.MONTHLY:
            report_model = MonthlyCountryTradeReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == ReportType.QUARTERLY:
            report_model = QuarterlyCountryTradeReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        query_cols = [getattr(report_model, col) for col in
                      cls.series_type_cols_map[series_type]]
        query = report_model.query.filter(
                report_model.country == country,
            )
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        query = (
            query.with_entities(
                report_model.report_date,
                *query_cols,
            ).order_by(
                report_model.report_date.desc()).all()
        )

        result_series = cls._format_series_records(query)
        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


@ns.route('/language-trade-statistics')
@respond_with_code
class LanguageTradeStatisticsResource(Resource, CountryLanguageTradeMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "language", Language.ZH_HANS_CN: "语言"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货交易人数"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约交易人数"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换交易人数"},
        {"field": "spot_trade_amount", Language.ZH_HANS_CN: "现货交易额(USD)"},
        {"field": "perpetual_trade_amount", Language.ZH_HANS_CN: "合约交易额(USD)"},
        {"field": "exchange_trade_amount", Language.ZH_HANS_CN: "兑换交易额(USD)"},
        {"field": "spot_fee_amount", Language.ZH_HANS_CN: "现货手续费(USD)"},
        {"field": "perpetual_fee_amount", Language.ZH_HANS_CN: "合约手续费(USD)"},
        {"field": "exchange_fee_amount", Language.ZH_HANS_CN: "兑换手续费(USD)"},
        {"field": "avg_spot_fee_amount", Language.ZH_HANS_CN: "现货人均手续费(USD)"},
        {"field": "avg_perpetual_fee_amount", Language.ZH_HANS_CN: "合约人均手续费(USD)"},
        {"field": "avg_exchange_fee_amount", Language.ZH_HANS_CN: "兑换人均手续费(USD)"},
        {"field": "invitee_fee", Language.ZH_HANS_CN: "Refer贡献手续费(USD)"},
        {"field": "refer_expense_usd", Language.ZH_HANS_CN: "返佣支出总额(USD)"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "收到返佣总额(USD)"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(missing='DAILY'),
        report_date=fields.Date,
        sort_name=EnumField(CountryLanguageTradeMixin.data_fields,
                            missing='trade_user_count'),
        sort_type=fields.String(missing='desc'),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-语言交易分布报表"""
        if kwargs['report_type'] == 'DAILY':
            query_model = DailyLanguageTradeReport
        elif kwargs['report_type'] == 'MONTHLY':
            query_model = MonthlyLanguageTradeReport
        elif kwargs['report_type'] == 'QUARTERLY':
            query_model = QuarterlyLanguageTradeReport
        data_list, _ = cls.get_static_res(query_model, 'language', **kwargs)
        language_dict = {k.name: v.chinese for k, v in LANGUAGE_NAMES.items()}
        language_dict["其他"] = "其他"
        language_dict["全部"] = "全部"
        res = []
        for row in data_list:
            language = row['language']
            if language == 'ZH_HANS_CN':
                continue
            row['language_chinese_name'] = language_dict[language]
            res.append(row)
        if kwargs['export']:
            data_list = cls.combine_percent_data(res)
            return export_xlsx(
                filename='language_trade_statistics_report',
                data_list=data_list,
                export_headers=cls.export_headers
            )

        return dict(records=res)


@ns.route('/language-trade-statistics-detail')
@respond_with_code
class LanguageTradeStatisticsDetailResource(Resource, CountryLanguageTradeMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "language", Language.ZH_HANS_CN: "语言"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "spot_trade_user_count", Language.ZH_HANS_CN: "现货交易人数"},
        {"field": "perpetual_trade_user_count", Language.ZH_HANS_CN: "合约交易人数"},
        {"field": "exchange_trade_user_count", Language.ZH_HANS_CN: "兑换交易人数"},
        {"field": "spot_trade_amount", Language.ZH_HANS_CN: "现货交易额(USD)"},
        {"field": "perpetual_trade_amount", Language.ZH_HANS_CN: "合约交易额(USD)"},
        {"field": "exchange_trade_amount", Language.ZH_HANS_CN: "兑换交易额(USD)"},
        {"field": "spot_fee_amount", Language.ZH_HANS_CN: "现货手续费(USD)"},
        {"field": "perpetual_fee_amount", Language.ZH_HANS_CN: "合约手续费(USD)"},
        {"field": "exchange_fee_amount", Language.ZH_HANS_CN: "兑换手续费(USD)"},
        {"field": "avg_spot_fee_amount", Language.ZH_HANS_CN: "现货人均手续费(USD)"},
        {"field": "avg_perpetual_fee_amount", Language.ZH_HANS_CN: "合约人均手续费(USD)"},
        {"field": "avg_exchange_fee_amount", Language.ZH_HANS_CN: "兑换人均手续费(USD)"},
        {"field": "invitee_fee", Language.ZH_HANS_CN: "Refer贡献手续费(USD)"},
        {"field": "refer_expense_usd", Language.ZH_HANS_CN: "返佣支出总额(USD)"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "收到返佣总额(USD)"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(missing='DAILY'),
        language=fields.String(required=True),
        start_date=fields.Date,
        end_date=fields.Date,
        page=PageField(missing=1),
        limit=PageField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-语言交易分布报表-详情"""
        if kwargs['report_type'] == 'DAILY':
            query_model = DailyLanguageTradeReport
        elif kwargs['report_type'] == 'MONTHLY':
            query_model = MonthlyLanguageTradeReport
        elif kwargs['report_type'] == 'QUARTERLY':
            query_model = QuarterlyLanguageTradeReport
        query = query_model.query.filter(
            query_model.language == kwargs['language']
        ).order_by(query_model.id.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(query_model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(query_model.report_date <= end_date)
        language_dict = {k.name: v.chinese for k, v in LANGUAGE_NAMES.items() if k != Language.ZH_HANS_CN}
        if kwargs['export']:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            res = cls._fmt_records(records, language_dict)
            return export_xlsx(
                filename='language_trade_statistics_detail_report',
                data_list=res,
                export_headers=cls.export_headers
            )
        page, limit = kwargs['page'], kwargs['limit']
        paginate = query.paginate(page, limit, error_out=False)
        record_data = cls._fmt_records(paginate.items, language_dict)
        return dict(items=record_data,
                    total=paginate.total,
                    extra=dict(language_dict=language_dict))

    @classmethod
    def _fmt_records(cls, records, language_dict):
        res = []
        for record in records:
            item = record.to_dict()
            language = item['language']
            if language == 'ZH_HANS_CN':
                continue
            item['language'] = language_dict[language]
            res.append(cls.fmt_item(item))
        return res


@ns.route("/language-trade-series")
@respond_with_code
class LanguageTradeSeriesResource(Resource, CountryLanguageTradeMixin):

    @classmethod
    @ns.use_kwargs(dict(
        language=fields.String(required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        series_type=EnumField(CountryLanguageTradeMixin.SeriesType, required=True),
        report_type=EnumField(ReportType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-国际化报表-语言交易分布报表-报表曲线"""
        language = kwargs.get("language")
        series_type = kwargs["series_type"]
        report_type = kwargs["report_type"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if report_type == ReportType.DAILY:
            report_model = DailyLanguageTradeReport
        elif report_type == ReportType.MONTHLY:
            report_model = MonthlyLanguageTradeReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        elif report_type == ReportType.QUARTERLY:
            report_model = QuarterlyLanguageTradeReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        query_cols = [getattr(report_model, col) for col in
                      cls.series_type_cols_map[series_type]]
        query = report_model.query.filter(
            report_model.language == language,
        )
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        query = (
            query.with_entities(
                report_model.report_date,
                *query_cols,
            ).order_by(
                report_model.report_date.desc()).all()
        )
        result_series = cls._format_series_records(query)
        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


class FunnelReportMixin:
    model = DailyPublicityChannelUserReport

    sum_field = (
        "register_count",
        "deposit_on_chain_count_7_days",
        "deposit_on_chain_trade_count_7_days",
        "deposit_local_count_7_days",
        "deposit_local_trade_count_7_days",
        "deposit_count_7_days",
        "deposit_count_30_days",
        "trade_count_7_days",

        "spot_trade_count_30_days",
        "perpetual_trade_count_30_days",
        "trade_users_30_days",
        "exchange_count_30_days",
        "margin_count_30_days",
        "investment_count_30_days",
        "amm_count_30_days",

        "all_trade_amount_7_days",
        "all_trade_amount_30_days",
        "all_fee_7_days",
        "all_fee_30_days"
    )

    @classmethod
    def get_publicity_channels(cls, platform):
        """获取宣发渠道漏斗报表所有宣发渠道"""
        if platform == cls.model.Platform.SITE:
            sort_dic = {'全部': 2, '其他': 1}
            channel_query = cls.model.query.filter(
                cls.model.platform == cls.model.Platform.SITE
            ).with_entities(
                cls.model.publicity_channel.distinct(),
            ).all()
            channels = [i[0] for i in channel_query]
            channels.sort(key=lambda x: sort_dic.get(x, 0), reverse=True)  # '全部'排在第一个
        else:
            channels = AfChannelInfoMySQL.get_all_channel() + ["全部"]
        return channels

    @classmethod
    def fmt_channel_export_data(cls, records):
        """格式化各种漏斗报表导出格式"""
        for record in records:
            if 'country' in record and record['country'] == '中国':
                record['country'] = '其他'
            for k, v in record.items():
                if '_percent' in k:
                    continue
                percent_k = f'{k}_percent'
                if percent_k not in record:
                    continue
                fmt_percent_v = format_percent(record[percent_k], 2)
                fmt_data = f'{v} ({fmt_percent_v})'
                record[k] = fmt_data

        return records

    @classmethod
    def add_channel_percent_field(cls, record_data):
        """为各种漏斗报表添加百分比"""
        percent_fields = [
            "deposit_on_chain_count_7_days",
            "deposit_on_chain_trade_count_7_days",
            "deposit_local_count_7_days",
            "deposit_local_trade_count_7_days",
            "deposit_count_7_days",
            "trade_count_7_days",
            "spot_trade_count_30_days",
            "perpetual_trade_count_30_days",
            "trade_users_30_days",
            "exchange_count_30_days",
            "margin_count_30_days",
            "investment_count_30_days",
            "amm_count_30_days",
        ]
        for record in record_data:
            for field in percent_fields:
                p_field = f"{field}_percent"
                base = record['register_count']
                if field == 'deposit_on_chain_trade_count_7_days':
                    base = record['deposit_on_chain_count_7_days']
                if field == 'deposit_local_trade_count_7_days':
                    base = record['deposit_local_count_7_days']

                if base == 0:
                    record[p_field] = 0
                else:
                    record[p_field] = quantize_amount(
                        Decimal(record[field]) / Decimal(base), 4)
        return record_data

    @classmethod
    def get_user_channel_query(cls, st, et, platform, language_area, country, channel):
        query = cls.model.query.filter(
            cls.model.platform == platform,
        )
        if channel:
            query = query.filter(cls.model.publicity_channel == channel)
        if st:
            query = query.filter(cls.model.report_date >= st)
        if et:
            query = query.filter(cls.model.report_date <= et)
        if language_area:
            query = query.filter(cls.model.language == language_area.name)
        if country:
            query = query.filter(cls.model.country_code == country)
        return query

@ns.route('/increase-channel')
@respond_with_code
class IncreaseChannelResource(Resource, FunnelReportMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "channel_type", Language.ZH_HANS_CN: "渠道"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_on_chain_count_7_days", Language.ZH_HANS_CN: "7天内链上充值人数"},
        {"field": "deposit_on_chain_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内链上充值交易人数"},
        {"field": "deposit_local_count_7_days", Language.ZH_HANS_CN: "7天内非链上充值人数"},
        {"field": "deposit_local_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内非链上充值交易人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内总充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内总交易人数"},
        {"field": "spot_trade_count_30_days", Language.ZH_HANS_CN: "30天内币币交易人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内总交易人数"},
        {"field": "perpetual_trade_count_30_days", Language.ZH_HANS_CN: "30天内合约交易人数"},
        {"field": "exchange_count_30_days", Language.ZH_HANS_CN: "30天内兑换人数"},
        {"field": "margin_count_30_days", Language.ZH_HANS_CN: "30天内杠杆借币人数"},
        {"field": "investment_count_30_days", Language.ZH_HANS_CN: "30天内理财人数"},
        {"field": "amm_count_30_days", Language.ZH_HANS_CN: "30天内AMM人数"},
    )

    channel_type_option = {
        DailyChannelIncreaseUserReport.ChannelType.ALL.name: '全部',
        DailyChannelIncreaseUserReport.ChannelType.NONE.name: '自然注册',
        DailyChannelIncreaseUserReport.ChannelType.NORMAL.name: '普通推荐',
        DailyChannelIncreaseUserReport.ChannelType.AMBASSADOR.name: '大使推荐',
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            start_date=fields.Date,
            end_date=fields.Date,
            channel_type=EnumField(DailyChannelIncreaseUserReport.ChannelType,
                                   default=DailyChannelIncreaseUserReport.ChannelType.ALL),
            page=PageField(missing=1),
            limit=LimitField(max_limit=3000),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-渠道漏斗报表 """
        page, limit = kwargs['page'], kwargs['limit']
        channel_type = kwargs['channel_type']
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')

        _query = DailyChannelIncreaseUserReport.query.filter(
            DailyChannelIncreaseUserReport.channel_type == channel_type
        ).order_by(DailyChannelIncreaseUserReport.report_date.desc())

        if start_date:
            _query = _query.filter(
                DailyChannelIncreaseUserReport.report_date >= start_date
            )
        if end_date:
            _query = _query.filter(
                DailyChannelIncreaseUserReport.report_date <= end_date
            )

        paginate = _query.paginate(page, limit, error_out=False)

        record_data = [item.to_dict() for item in paginate.items]
        record_data = cls.add_channel_percent_field(record_data)

        if kwargs['export']:
            return export_xlsx(
                filename='user_increase_channel_report',
                data_list=cls.fmt_channel_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(items=record_data,
                    total=paginate.total,
                    channel_type_option=cls.channel_type_option,
                    )


@ns.route('/increase-channel-async-download')
@respond_with_code
class IncreaseChannelAsyncDownloadResource(Resource, FunnelReportMixin):

    @classmethod
    @ns.use_kwargs(dict(
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """ 报表-用户报表-渠道漏斗报表-异步下载 """
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_increase_channel_report.delay(
            email=g.user.email,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/increase-country')
@respond_with_code
class IncreaseCountryResource(Resource, FunnelReportMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_on_chain_count_7_days", Language.ZH_HANS_CN: "7天内链上充值人数"},
        {"field": "deposit_on_chain_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内链上充值交易人数"},
        {"field": "deposit_local_count_7_days", Language.ZH_HANS_CN: "7天内非链上充值人数"},
        {"field": "deposit_local_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内非链上充值交易人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内总充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内总交易人数"},
        {"field": "spot_trade_count_30_days", Language.ZH_HANS_CN: "30天内币币交易人数"},
        {"field": "perpetual_trade_count_30_days", Language.ZH_HANS_CN: "30天内合约交易人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内总交易人数"},
        {"field": "exchange_count_30_days", Language.ZH_HANS_CN: "30天内兑换人数"},
        {"field": "margin_count_30_days", Language.ZH_HANS_CN: "30天内杠杆借币人数"},
        {"field": "investment_count_30_days", Language.ZH_HANS_CN: "30天内理财人数"},
        {"field": "amm_count_30_days", Language.ZH_HANS_CN: "30天内AMM人数"},
    )

    country_option = {i: i for i in list_country_cn_name()}
    country_option['ALL'] = 'ALL'
    country_option['其他'] = '其他'
    country_option.pop('中国', None)  # delete cn temporary

    @classmethod
    @ns.use_kwargs(
        dict(
            start_date=fields.Date,
            end_date=fields.Date,
            country=fields.String(missing='ALL'),
            page=PageField(missing=1),
            limit=LimitField(max_limit=3000),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-国家漏斗报表 """
        page, limit = kwargs['page'], kwargs['limit']
        country = kwargs['country']
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')

        _query = DailyCountryIncreaseUserReport.query.filter(
            DailyCountryIncreaseUserReport.country == country
        ).order_by(DailyCountryIncreaseUserReport.report_date.desc())

        if start_date:
            _query = _query.filter(
                DailyCountryIncreaseUserReport.report_date >= start_date
            )
        if end_date:
            _query = _query.filter(
                DailyCountryIncreaseUserReport.report_date <= end_date
            )

        paginate = _query.paginate(page, limit, error_out=False)

        record_data = [item.to_dict() for item in paginate.items]
        record_data = cls.add_channel_percent_field(record_data)

        if kwargs['export']:
            return export_xlsx(
                filename='user_increase_channel_report',
                data_list=cls.fmt_channel_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(items=record_data,
                    total=paginate.total,
                    country_option=cls.country_option,
                    )


@ns.route('/increase-channel-detail')
@respond_with_code
class IncreaseChannelDetailResource(Resource, FunnelReportMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "channel_type", Language.ZH_HANS_CN: "渠道"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_on_chain_count_7_days", Language.ZH_HANS_CN: "7天内链上充值人数"},
        {"field": "deposit_on_chain_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内链上充值交易人数"},
        {"field": "deposit_local_count_7_days", Language.ZH_HANS_CN: "7天内非链上充值人数"},
        {"field": "deposit_local_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内非链上充值交易人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内总充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内总交易人数"},
        {"field": "spot_trade_count_30_days", Language.ZH_HANS_CN: "30天内币币交易人数"},
        {"field": "perpetual_trade_count_30_days", Language.ZH_HANS_CN: "30天内合约交易人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内总交易人数"},
        {"field": "exchange_count_30_days", Language.ZH_HANS_CN: "30天内兑换人数"},
        {"field": "margin_count_30_days", Language.ZH_HANS_CN: "30天内杠杆借币人数"},
        {"field": "investment_count_30_days", Language.ZH_HANS_CN: "30天内理财人数"},
        {"field": "amm_count_30_days", Language.ZH_HANS_CN: "30天内AMM人数"},
    )

    channel_type_option = {
        DailyChannelIncreaseUserReport.ChannelType.ALL.name: '全部',
        DailyChannelIncreaseUserReport.ChannelType.NONE.name: '自然注册',
        DailyChannelIncreaseUserReport.ChannelType.NORMAL.name: '普通推荐',
        DailyChannelIncreaseUserReport.ChannelType.AMBASSADOR.name: '大使推荐',
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField(missing=1),
            limit=PageField(missing=50),
            export=fields.Boolean(missing=False),
            report_date=DateField(to_date=True),
            order=fields.String(missing='trade_count_7_days'),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-渠道漏斗报表详情 """
        page, limit = kwargs['page'], kwargs['limit']
        report_date = kwargs['report_date']
        order = kwargs['order']

        _query = DailyChannelIncreaseUserReport.query.filter(
            DailyChannelIncreaseUserReport.report_date == report_date
        ).order_by(getattr(DailyChannelIncreaseUserReport, order).desc())
        paginate = _query.paginate(page, limit, error_out=False)
        record_data = [item.to_dict() for item in paginate.items]
        record_data = cls.add_channel_percent_field(record_data)
        if kwargs['export']:
            return export_xlsx(
                filename='user_increase_channel_report',
                data_list=cls.fmt_channel_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(items=record_data,
                    total=paginate.total,
                    channel_type_option=cls.channel_type_option,
                    )


@ns.route('/increase-country-detail')
@respond_with_code
class IncreaseCountryResourceDetail(Resource, FunnelReportMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_on_chain_count_7_days", Language.ZH_HANS_CN: "7天内链上充值人数"},
        {"field": "deposit_on_chain_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内链上充值交易人数"},
        {"field": "deposit_local_count_7_days", Language.ZH_HANS_CN: "7天内非链上充值人数"},
        {"field": "deposit_local_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内非链上充值交易人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内总充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内总交易人数"},
        {"field": "spot_trade_count_30_days", Language.ZH_HANS_CN: "30天内币币交易人数"},
        {"field": "perpetual_trade_count_30_days", Language.ZH_HANS_CN: "30天内合约交易人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内总交易人数"},
        {"field": "exchange_count_30_days", Language.ZH_HANS_CN: "30天内兑换人数"},
        {"field": "margin_count_30_days", Language.ZH_HANS_CN: "30天内杠杆借币人数"},
        {"field": "investment_count_30_days", Language.ZH_HANS_CN: "30天内理财人数"},
        {"field": "amm_count_30_days", Language.ZH_HANS_CN: "30天内AMM人数"},
    )

    country_option = {i: i for i in list_country_cn_name()}
    country_option['ALL'] = 'ALL'
    country_option['其他'] = '其他'

    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField(missing=1),
            limit=PageField(missing=50),
            export=fields.Boolean(missing=False),
            report_date=DateField(to_date=True),
            order=fields.String(missing='trade_count_7_days'),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-国家漏斗报表详情 """
        page, limit = kwargs['page'], kwargs['limit']
        report_date = kwargs['report_date']
        order = kwargs['order']

        _query = DailyCountryIncreaseUserReport.query.filter(
            DailyCountryIncreaseUserReport.report_date == report_date
        ).order_by(getattr(DailyCountryIncreaseUserReport, order).desc())

        paginate = _query.paginate(page, limit, error_out=False)

        record_data = [item.to_dict() for item in paginate.items]

        # 计算百分比
        record_data = cls.add_channel_percent_field(record_data)
        if kwargs['export']:
            return export_xlsx(
                filename='user_increase_channel_report',
                data_list=cls.fmt_channel_export_data(record_data),
                export_headers=cls.export_headers
            )

        return dict(items=record_data,
                    total=paginate.total,
                    country_option=cls.country_option,
                    )


@ns.route('/publicity-channel')
@respond_with_code
class PublicityChannelUserResource(Resource, FunnelReportMixin):
    @classmethod
    @ns.use_kwargs(
        dict(
            platform=EnumField(DailyPublicityChannelUserReport.Platform, required=True),
            start_date=fields.Date,
            end_date=fields.Date,
            publicity_channel=fields.String(missing='全部'),
            language_area=common_fields.EnumField(LanguageArea),
            country=fields.String(),
            page=PageField(missing=1),
            limit=LimitField(max_limit=3000),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-宣发渠道漏斗报表 """
        page, limit = kwargs['page'], kwargs['limit']
        publicity_channel = kwargs['publicity_channel']
        platform = kwargs["platform"]
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        query = cls.get_user_channel_query(
            start_date, end_date, platform, kwargs.get("language_area"), kwargs.get("country"), publicity_channel)
        query = query.group_by(
            cls.model.report_date
        ).with_entities(
            cls.model.report_date,
            cls.model.publicity_channel,
            *[func.sum(getattr(cls.model, field)).label(field) for field in cls.sum_field]
        ).order_by(cls.model.report_date.desc())
        paginate = query.paginate(page, limit, error_out=False)

        record_data = []
        for item in paginate.items:
            tmp_item = {field: getattr(item, field) for field in cls.sum_field}
            tmp_item.update(
                publicity_channel=item.publicity_channel,
                report_date=item.report_date
            )
            record_data.append(tmp_item)

        record_data = cls.add_channel_percent_field(record_data)
        publicity_channels = cls.get_publicity_channels(platform)
        extra = dict(
            publicity_channels=publicity_channels,
            language_areas={k.name: k.value for k in LanguageArea},
            countries=COUNTRY_CODE_CN_NAME_DIC,
        )

        return dict(
            items=record_data,
            total=paginate.total,
            extra=extra
        )


@ns.route('/publicity-channel-detail')
@respond_with_code
class PublicityChannelUserDetailResource(Resource, FunnelReportMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "publicity_channel", Language.ZH_HANS_CN: "渠道"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_on_chain_count_7_days", Language.ZH_HANS_CN: "7天内链上充值人数"},
        {"field": "deposit_on_chain_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内链上充值交易人数"},
        {"field": "deposit_local_count_7_days", Language.ZH_HANS_CN: "7天内非链上充值人数"},
        {"field": "deposit_local_trade_count_7_days",
         Language.ZH_HANS_CN: "7天内非链上充值交易人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内交易人数"},
        {"field": "spot_trade_count_30_days", Language.ZH_HANS_CN: "30天内币币交易人数"},
        {"field": "perpetual_trade_count_30_days", Language.ZH_HANS_CN: "30天内合约交易人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内交易人数"},
        {"field": "exchange_count_30_days", Language.ZH_HANS_CN: "30天内兑换人数"},
        {"field": "margin_count_30_days", Language.ZH_HANS_CN: "30天内杠杆借币人数"},
        {"field": "investment_count_30_days", Language.ZH_HANS_CN: "30天内理财人数"},
        {"field": "amm_count_30_days", Language.ZH_HANS_CN: "30天内AMM人数"},
    )

    app_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "publicity_channel", Language.ZH_HANS_CN: "渠道"},
        {"field": "register_count", Language.ZH_HANS_CN: "注册人数"},
        {"field": "deposit_count_7_days", Language.ZH_HANS_CN: "7天内充值人数"},
        {"field": "trade_count_7_days", Language.ZH_HANS_CN: "7天内交易人数"},
        {"field": "all_trade_amount_7_days", Language.ZH_HANS_CN: "7天交易金额"},
        {"field": "all_fee_7_days", Language.ZH_HANS_CN: "7天交易手续费"},

        {"field": "deposit_count_30_days", Language.ZH_HANS_CN: "30天内充值人数"},
        {"field": "trade_users_30_days", Language.ZH_HANS_CN: "30天内交易人数"},
        {"field": "all_trade_amount_30_days", Language.ZH_HANS_CN: "30天交易金额"},
        {"field": "all_fee_30_days", Language.ZH_HANS_CN: "30天交易手续费"},
    )

    count_fields = [
        "register_count",
        "deposit_on_chain_count_7_days",
        "deposit_on_chain_trade_count_7_days",
        "deposit_local_count_7_days",
        "deposit_local_trade_count_7_days",
        "deposit_count_7_days",
        "trade_count_7_days",
        "spot_trade_count_30_days",
        "perpetual_trade_count_30_days",
        "trade_users_30_days",
        "exchange_count_30_days",
        "margin_count_30_days",
        "investment_count_30_days",
        "amm_count_30_days",
        "all_trade_amount_7_days",
        "all_fee_7_days",
        "deposit_count_30_days",
        "trade_users_30_days",
        "all_trade_amount_30_days",
        "all_fee_30_days",
    ]

    @classmethod
    @ns.use_kwargs(
        dict(
            platform=EnumField(DailyPublicityChannelUserReport.Platform, required=True),
            publicity_channel=fields.String(),
            page=PageField(missing=1),
            limit=PageField(missing=50),
            export=fields.Boolean(missing=False),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            order=fields.String(missing='register_count'),
        )
    )
    def get(cls, **kwargs):
        """ 报表-用户报表-宣发渠道漏斗报表详情 """
        page, limit = kwargs['page'], kwargs['limit']
        query_model = DailyPublicityChannelUserReport
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        platform = kwargs["platform"]
        publicity_channel = kwargs.get('publicity_channel')
        if not start_date and not end_date:
            start_date = end_date = today() - datetime.timedelta(days=1)
        query = cls.get_user_channel_query(
            start_date, end_date, platform, kwargs.get("language_area"), kwargs.get("country"), publicity_channel)
        query = query.group_by(
            cls.model.publicity_channel,
        ).with_entities(
            query_model.publicity_channel,
            *[func.sum(getattr(query_model, field)).label(field) for field in cls.count_fields]
        )
        records = query.all()
        res = []
        date_interval = f'{start_date}-{end_date}'
        for rec in records:
            item = {field: getattr(rec, field) for field in cls.count_fields}
            item.update(report_date=date_interval)
            item.update(publicity_channel=rec.publicity_channel)
            res.append(item)

        res = cls.add_channel_percent_field(res)
        order = kwargs.get("order", "register_count")
        res.sort(key=lambda x: x.get(order, 0), reverse=True)
        if kwargs['export']:
            return export_xlsx(
                filename='user_increase_channel_report',
                data_list=cls.fmt_channel_export_data(res) if platform == cls.model.Platform.SITE else res,
                export_headers=cls.export_headers if platform == cls.model.Platform.SITE else cls.app_export_headers
            )
        publicity_channels = cls.get_publicity_channels(platform)
        extra = dict(
            publicity_channels=publicity_channels,
            language_areas={k.name: k.value for k in LanguageArea},
            countries=COUNTRY_CODE_CN_NAME_DIC,
        )
        return dict(
            items=res[(page-1)*limit: page*limit],
            total=len(res),
            extra=extra
        )


@ns.route('/active-retained')
@respond_with_code
class UserActiveRetainedReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, missing=ReportType.DAILY),
        start_date=fields.Date,
        end_date=fields.Date,
        origin_type=EnumField(
            [i.name for i in DailyUserActiveRetainedReport.OriginType],
            missing=DailyUserActiveRetainedReport.OriginType.ALL.name
        ),
        export=fields.Boolean(missing=False),
        page=PageField(missing=1),
        limit=PageField(missing=50),
    ))
    def get(cls, **kwargs):
        """用户报表-留存报表-活跃留存"""
        report_type = kwargs["report_type"]
        page, limit = kwargs["page"], kwargs["limit"]
        if report_type == ReportType.DAILY:
            model = DailyUserActiveRetainedReport
            dump_func = cls.dump_daily_report
        else:
            model = MonthlyUserActiveRetainedReport
            dump_func = cls.dump_monthly_report
            page, limit = 1, 24

        query = model.query
        if start_date := kwargs.get("start_date"):
            query = query.filter(
                model.report_date >= start_date
            )
        if end_date := kwargs.get("end_date"):
            query = query.filter(
                model.report_date < end_date
            )
        if origin_type := kwargs.get("origin_type"):
            query = query.filter(
                model.origin_type == origin_type
            )
        query = query.order_by(
            model.report_date.desc()
        )
        pagination = query.paginate(page, limit, error_out=False)
        items = dump_func(pagination.items)
        columns = {}
        if report_type == ReportType.MONTHLY and items:
            report_dates = {i["report_date"] for i in items}
            min_date = min(report_dates)
            report_dates.remove(min_date)
            columns = {
                i.strftime("%Y-%m-%d"): f'{i.strftime("%Y-%m")}留存'
                for i in sorted(report_dates, reverse=True)
            }
        if kwargs.get("export"):
            data = dump_func(query.limit(ADMIN_EXPORT_LIMIT).all())
            return export_xlsx(
                filename="user-active-retained-report",
                data_list=data,
                export_headers=cls.get_export_headers(columns),
            )
        return dict(
            total=pagination.total,
            items=items,
            origin_types={i.name: i.value for i in DailyUserActiveRetainedReport.OriginType},
            total_pages=pagination.pages,
            columns=columns
        )

    @classmethod
    def dump_daily_report(cls, reports):
        result = []
        for report in reports:
            result.append({
                "report_date": report.report_date,
                "id": report.id,
                "origin_type": report.origin_type.value,
                "region_count": report.region_count,
                "next_day_retained_count": report.next_day_retained_count,
                "next_weekly_retained_count": report.next_weekly_retained_count,
                "next_month_retained_count": report.next_month_retained_count
            })
        return result

    @classmethod
    def dump_monthly_report(cls, reports):
        report_ids = {i.id for i in reports}
        report_detail_mapper = defaultdict(list)
        for d in MonthlyUserActiveRetainedDetail.query.filter(
            MonthlyUserActiveRetainedDetail.monthly_report_id.in_(report_ids)
        ).all():
            report_detail_mapper[d.monthly_report_id].append(d)
        result = []
        for report in reports:
            report_info = {
                "report_date": report.report_date,
                "origin_type": report.origin_type.value,
                "region_count": report.region_count,
            }
            details = report_detail_mapper.get(report.id, [])
            date_count_mapper = {d.report_date.strftime("%Y-%m-%d"): d.retained_count for d in details}
            report_info.update(date_count_mapper)
            result.append(report_info)
        return result

    @classmethod
    def get_export_headers(cls, columns):
        if not columns:
            return (
                {"field": "report_date", Language.ZH_HANS_CN: "日期"},
                {"field": "origin_type", Language.ZH_HANS_CN: "注册来源"},
                {"field": "region_count", Language.ZH_HANS_CN: "注册人数"},
                {"field": "next_day_retained_count", Language.ZH_HANS_CN: "次日留存"},
                {"field": "next_weekly_retained_count", Language.ZH_HANS_CN: "7日留存"},
                {"field": "next_month_retained_count", Language.ZH_HANS_CN: "30日留存"}
            )
        else:
            return (
                {"field": "report_date", Language.ZH_HANS_CN: "日期"},
                {"field": "origin_type", Language.ZH_HANS_CN: "注册来源"},
                {"field": "region_count", Language.ZH_HANS_CN: "注册人数"},
                *({"field": k, Language.ZH_HANS_CN: v} for k, v in columns.items())
            )


@ns.route('/active-retained-async-download')
@respond_with_code
class UserActiveRetainedReportAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """用户报表-留存报表-活跃留存-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_active_retained_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route("/trade-retained")
@respond_with_code
class UserTradeRetainedReportResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, missing=ReportType.DAILY),
        start_date=fields.Date,
        end_date=fields.Date,
        trade_type=EnumField([i.name for i in DailyTradeUserRetainedReport.TradeType], required=True),
        export=fields.Boolean(missing=False),
        page=PageField(missing=1),
        limit=PageField(missing=50),
    ))
    def get(cls, **kwargs):
        """用户报表-留存报表-交易留存"""
        report_type = kwargs["report_type"]
        page, limit = kwargs["page"], kwargs["limit"]
        if report_type == ReportType.DAILY:
            model = DailyTradeUserRetainedReport
            dump_func = cls.dump_daily_report
        else:
            model = MonthlyTradeUserRetainedReport
            dump_func = cls.dump_monthly_report
            page, limit = 1, 24

        query = model.query
        if start_date := kwargs.get("start_date"):
            query = query.filter(
                model.report_date >= start_date
            )
        if end_date := kwargs.get("end_date"):
            query = query.filter(
                model.report_date < end_date
            )
        if trade_type := kwargs.get("trade_type"):
            query = query.filter(
                model.trade_type == trade_type
            )
        query = query.order_by(
            model.report_date.desc()
        )
        columns = {}
        pagination = query.paginate(page, limit, error_out=False)
        items = dump_func(pagination.items)
        if report_type == ReportType.MONTHLY and items:
            report_dates = {i["report_date"] for i in items}
            min_date = min(report_dates)
            report_dates.remove(min_date)
            columns = {
                i.strftime("%Y-%m-%d"): f'{i.strftime("%Y-%m")}留存'
                for i in sorted(report_dates, reverse=True)
            }
        if kwargs.get("export"):
            data = dump_func(query.limit(ADMIN_EXPORT_LIMIT).all())
            return export_xlsx(
                filename="user-trade-retained-report",
                data_list=data,
                export_headers=cls.get_export_headers(columns),
            )
        return dict(
            total=pagination.total,
            items=items,
            total_pages=pagination.pages,
            columns=columns
        )

    @classmethod
    def dump_monthly_report(cls, reports):
        report_ids = {i.id for i in reports}
        report_detail_mapper = defaultdict(list)
        for d in MonthlyTradeUserRetainedDetail.query.filter(
            MonthlyTradeUserRetainedDetail.monthly_report_id.in_(report_ids)
        ).all():
            report_detail_mapper[d.monthly_report_id].append(d)
        result = []
        for report in reports:
            report_info = {
                "report_date": report.report_date,
                "trade_count": report.trade_count,
            }
            details = report_detail_mapper.get(report.id, [])
            date_count_mapper = {d.report_date.strftime("%Y-%m-%d"): d.retained_count for d in details}
            report_info.update(date_count_mapper)
            result.append(report_info)
        return result

    @classmethod
    def dump_daily_report(cls, reports):
        result = []
        for report in reports:
            result.append({
                "report_date": report.report_date,
                "id": report.id,
                "trade_count": report.trade_count,
                "next_day_retained_count": report.next_day_retained_count,
                "next_weekly_retained_count": report.next_weekly_retained_count,
                "next_month_retained_count": report.next_month_retained_count
            })
        return result

    @classmethod
    def get_export_headers(cls, columns):
        if not columns:
            return (
                {"field": "report_date", Language.ZH_HANS_CN: "日期"},
                {"field": "trade_count", Language.ZH_HANS_CN: "新增人数"},
                {"field": "next_day_retained_count", Language.ZH_HANS_CN: "次日留存"},
                {"field": "next_weekly_retained_count", Language.ZH_HANS_CN: "7日留存"},
                {"field": "next_month_retained_count", Language.ZH_HANS_CN: "30日留存"}
            )
        else:
            return (
                {"field": "report_date", Language.ZH_HANS_CN: "日期"},
                {"field": "trade_count", Language.ZH_HANS_CN: "新增人数"},
                *({"field": k, Language.ZH_HANS_CN: v} for k, v in columns.items())
            )


class PublicityChannelMixin:

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'

    export_headers_basic = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_user_count", Language.ZH_HANS_CN: "兑换用户"},
    )
    export_headers_daily = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_1d", Language.ZH_HANS_CN: "次日留存"},
        {"field": "retained_count_7d", Language.ZH_HANS_CN: "7日留存"},
        {"field": "retained_count_30d", Language.ZH_HANS_CN: "30日留存"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_user_count", Language.ZH_HANS_CN: "兑换用户"},
    )
    export_headers_weekly = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_7d", Language.ZH_HANS_CN: "7日留存"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_user_count", Language.ZH_HANS_CN: "兑换用户"},
    )
    export_headers_monthly = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_30d", Language.ZH_HANS_CN: "30日留存"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "exchange_user_count", Language.ZH_HANS_CN: "兑换用户"},
    )

    detail_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "active_user_count", Language.ZH_HANS_CN: "活跃用户"},
        {"field": "asset_user_count", Language.ZH_HANS_CN: "资产用户"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "exchange_user_count", Language.ZH_HANS_CN: "兑换用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
    )

    sum_fields = (
        'register_user_count', 'active_user_count', 'asset_user_count',
        'spot_user_count', 'perpetual_user_count', 'trade_user_count',
        'exchange_user_count', 'deposit_user_count', 'trade_usd', 'trade_fee_usd',
         'retained_count_1d', 'retained_count_7d', 'retained_count_30d'
    )
    select_fields = sum_fields + ('report_date', )


class WebPublicityChannelMixin(PublicityChannelMixin):
    web_field = (
        {"field": "visit_count", Language.ZH_HANS_CN: "访问次数"},
        {"field": "ip_count", Language.ZH_HANS_CN: "访问IP数"},
    )

    export_headers_basic = (
        *PublicityChannelMixin.export_headers_daily,
        *web_field
    )
    export_headers_daily = (
        *PublicityChannelMixin.export_headers_daily,
        *web_field
    )
    export_headers_weekly = (
        *PublicityChannelMixin.export_headers_weekly,
        *web_field
    )

    export_headers_monthly = (
        *PublicityChannelMixin.export_headers_monthly,
        *web_field
    )

    detail_export_headers = (
        *PublicityChannelMixin.detail_export_headers,
        *web_field,
        {"field": "short_link", Language.ZH_HANS_CN: "短链接"},
    )

    sum_fields = (
        *PublicityChannelMixin.sum_fields, 'visit_count', 'ip_count',
    )
    select_fields = sum_fields + ('report_date', )


    @classmethod
    def _validate_country(cls, language_area, country):
        if country not in ShortLinkInfo.GROUPED_PC_LA_COUNTRY[language_area]:
            raise InvalidArgument

    @classmethod
    def _validate_publicity_channel(cls, publicity_channel_id):
        row = PublicityChannel.query.filter(PublicityChannel.id == publicity_channel_id).first()
        if not row:
            raise InvalidArgument

    @classmethod
    def _validate_short_link(cls, short_link_id):
        row = ShortLinkInfo.query.filter(
            ShortLinkInfo.id == short_link_id,
            ShortLinkInfo.status == ShortLinkInfo.StatusType.VALID
        ).first()
        if not row:
            raise InvalidArgument

    @classmethod
    def _get_model_and_query(cls, period, report_type, language_area, country, publicity_channel_id,
                             short_link_id, business_segment, category_id, publicity_channel):
        language_area = language_area.name if language_area else language_area
        business_segment = business_segment.name if business_segment else business_segment
        channel_ids = []
        if publicity_channel:
            channels = PublicityChannel.query.with_entities(
                PublicityChannel.id
            ).filter(
                PublicityChannel.channel == publicity_channel,
                PublicityChannel.status == PublicityChannel.Status.VALID
            ).all()
            channel_ids = {row.id for row in channels}
        channel_list = [publicity_channel_id, business_segment, category_id, channel_ids]
        country_list = [language_area, country]
        # 混了多种筛选也查询粒度最小的短连接表
        if short_link_id or (any(channel_list) and any(country_list)):
            model = {
                'DAILY': DailyPublicityShortLinkReport,
                'WEEKLY': WeeklyPublicityShortLinkReport,
                'MONTHLY': MonthlyPublicityShortLinkReport,
                'QUARTERLY': QuarterlyPublicityShortLinkReport,
            }.get(report_type, DailyPublicityShortLinkReport)
            field_map = dict(
                short_link_id=short_link_id,
                publicity_channel_id=publicity_channel_id,
                business_segment=business_segment,
                category_id=category_id,
                language_area=language_area,
                country=country,
            )
            query = model.query.with_entities(
                model.report_date,
                *[func.sum(getattr(model, field)).label(field) for field in cls.sum_fields]
            ).group_by(
                model.report_date
            )
            for name, val in field_map.items():
                if val:
                    query = query.filter(getattr(model, name) == val)
            if channel_ids:
                query = query.filter(model.publicity_channel_id.in_(channel_ids))
        elif publicity_channel_id:
            model = {
                'DAILY': DailyPublicityChannelReport,
                'WEEKLY': WeeklyPublicityChannelReport,
                'MONTHLY': MonthlyPublicityChannelReport,
                'QUARTERLY': QuarterlyPublicityChannelReport,
            }.get(report_type, DailyPublicityChannelReport)
            query = model.query.filter(
                model.publicity_channel_id == publicity_channel_id
            )
        elif publicity_channel:
            model = {
                'DAILY': DailyPublicityChannelReport,
                'WEEKLY': WeeklyPublicityChannelReport,
                'MONTHLY': MonthlyPublicityChannelReport,
                'QUARTERLY': QuarterlyPublicityChannelReport,
            }.get(report_type, DailyPublicityChannelReport)
            query = model.query.with_entities(
                model.report_date,
                *[func.sum(getattr(model, field)).label(field) for field in cls.sum_fields]
            ).filter(
                model.publicity_channel_id.in_(channel_ids),
            ).group_by(
                model.report_date
            )
        elif country:
            model = {
                'DAILY': DailyPublicityCountryReport,
                'WEEKLY': WeeklyPublicityCountryReport,
                'MONTHLY': MonthlyPublicityCountryReport,
                'QUARTERLY': QuarterlyPublicityCountryReport,
            }.get(report_type, DailyPublicityCountryReport)
            query = model.query.filter(
                model.country == country
            )
        elif language_area:
            model = {
                'DAILY': DailyPublicityLanguageAreaReport,
                'WEEKLY': WeeklyPublicityLanguageAreaReport,
                'MONTHLY': MonthlyPublicityLanguageAreaReport,
                'QUARTERLY': QuarterlyPublicityLanguageAreaReport,
            }.get(report_type, DailyPublicityLanguageAreaReport)
            query = model.query.filter(
                model.language_area == language_area
            )
        elif category_id:
            model = {
                'DAILY': DailyPublicityCategoryReport,
                'WEEKLY': WeeklyPublicityCategoryReport,
                'MONTHLY': MonthlyPublicityCategoryReport,
                'QUARTERLY': QuarterlyPublicityCategoryReport,
            }.get(report_type, DailyPublicityCategoryReport)
            query = model.query.filter(
                model.publicity_category_id == category_id
            )
        elif business_segment:
            model = {
                'DAILY': DailyPublicityBusinessSegmentReport,
                'WEEKLY': WeeklyPublicityBusinessSegmentReport,
                'MONTHLY': MonthlyPublicityBusinessSegmentReport,
                'QUARTERLY': QuarterlyPublicityBusinessSegmentReport,
            }.get(report_type, DailyPublicityBusinessSegmentReport)
            query = model.query.filter(
                model.business_segment == business_segment
            )
        else:
            model = {
                'DAILY': DailyPublicityLanguageAreaReport,
                'WEEKLY': WeeklyPublicityLanguageAreaReport,
                'MONTHLY': MonthlyPublicityLanguageAreaReport,
                'QUARTERLY': QuarterlyPublicityLanguageAreaReport,
            }.get(report_type, DailyPublicityLanguageAreaReport)
            query = model.query.filter(
                model.language_area == 'ALL'
            )
        query = query.filter(
            model.period == model.Period[period.name]
        )
        return model, query

    @classmethod
    def _get_publicity_channel_names(cls):
        model = PublicityChannel
        rows = model.query.with_entities(
            model.channel
        ).filter(
            model.status == model.Status.VALID
        ).all()
        return list({row.channel for row in rows})


class AppPublicityChannelMixin(PublicityChannelMixin):

    export_headers_basic = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
    )
    export_headers_daily = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_1d", Language.ZH_HANS_CN: "次日留存"},
        {"field": "retained_count_7d", Language.ZH_HANS_CN: "7日留存"},
        {"field": "retained_count_30d", Language.ZH_HANS_CN: "30日留存"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
    )
    export_headers_weekly = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_7d", Language.ZH_HANS_CN: "7日留存"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
    )
    export_headers_monthly = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "trade_fee_usd", Language.ZH_HANS_CN: "交易手续费（USD）"},
        {"field": "retained_count_30d", Language.ZH_HANS_CN: "30日留存"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
    )

    detail_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "register_user_count", Language.ZH_HANS_CN: "注册用户"},
        {"field": "deposit_user_count", Language.ZH_HANS_CN: "充值用户"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易用户"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "交易金额（USD）"},
        {"field": "spot_user_count", Language.ZH_HANS_CN: "现货用户"},
        {"field": "perpetual_user_count", Language.ZH_HANS_CN: "合约用户"},
        {"field": "channel", Language.ZH_HANS_CN: "渠道"},
    )

    @classmethod
    def _get_model_and_query(cls, period, platform, report_type, language_area, country, channel):
        model = {
            'DAILY': DailyAppPublicityChannelReport,
            'WEEKLY': WeeklyAppPublicityChannelReport,
            'MONTHLY': MonthlyAppPublicityChannelReport,
            'QUARTERLY': QuarterlyAppPublicityChannelReport,
        }[report_type]
        query = model.query.filter(
            model.period == model.Period[period.name],
            model.platform == platform
        )
        if language_area:
            query = query.filter(model.language == language_area.name)
        if country:
            query = query.filter(model.country_code == country)
        if channel:
            query = query.filter(model.channel == channel)
        return model, query


@ns.route('/publicity-channel-statistics')
@respond_with_code
class PublicityChannelStatisticResource(Resource, WebPublicityChannelMixin, PublicityMixin):

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        language_area=common_fields.EnumField(LanguageArea),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        country=fields.String(allow_none=True),
        publicity_channel_id=fields.String(allow_none=True),
        publicity_channel=fields.String(allow_none=True),
        short_link_id=fields.String(allow_none=True),
        export=fields.Boolean(missing=False),
        page=PageField,
        limit=LimitField,
        business_segment=common_fields.EnumField(BusinessSegment),
        category_id=fields.Integer(allow_none=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-宣发渠道统计报表"""
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        period = kwargs['period']
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        business_segment = kwargs.get('business_segment')
        category_id = kwargs.get('category_id')
        publicity_channel = kwargs.get('publicity_channel')
        if language_area and country:
            cls._validate_country(language_area, country)
        if publicity_channel_id := kwargs.get('publicity_channel_id'):
            cls._validate_publicity_channel(publicity_channel_id)
        if short_link_id := kwargs.get('short_link_id'):
            cls._validate_short_link(short_link_id)

        model, query = cls._get_model_and_query(period, report_type, language_area, country, publicity_channel_id,
                                                short_link_id, business_segment, category_id, publicity_channel)
        today_ = today()
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if not start_date:
            start_date = datetime.date(2023, 1, 1)
        if not end_date or end_date > today_:
            end_date = today_
        query = query.filter(
            model.report_date >= start_date,
            model.report_date < end_date,
        )

        res = []
        if not kwargs.get('export'):
            records = query.order_by(model.report_date.desc()).paginate(page, limit)
            for row in records.items:
                new_row = model(**{field: getattr(row, field) for field in cls.select_fields})
                new_row.trade_usd = quantize_amount(new_row.trade_usd, 2)
                new_row.trade_fee_usd = quantize_amount(new_row.trade_fee_usd, 2)
                item = new_row.to_dict()
                res.append(item)

            grouped_language_area_country = {k.name: v for k, v in ShortLinkInfo.GROUPED_PC_LA_COUNTRY.items()}
            c_id_name_dic, b_segment_c_ids_dic, c_id_b_segment_dic, c_id_b_segment_name_dic = cls.get_category_dic()
            business_segments = {item.name: item.value for item in BusinessSegment}
            c_id_pub_channels_dic, pub_channel_id_name_dic = cls.get_category_pub_channels_dic()

            return dict(
                items=res,
                extra=dict(
                    language_areas={k.name: k.value for k in LanguageArea},
                    countries=COUNTRY_CODE_CN_NAME_DIC,
                    grouped_language_area_country=grouped_language_area_country,
                    publicity_channel_names=cls._get_publicity_channel_names(),
                    business_segments=business_segments,
                    c_id_name_dic=c_id_name_dic,
                    b_segment_c_ids_dic=b_segment_c_ids_dic,
                    c_id_pub_channels_dic=c_id_pub_channels_dic,
                    pub_channel_id_name_dic=pub_channel_id_name_dic,
                ),
                total=records.total,
                model=model.__tablename__
            )
        else:
            records = query.order_by(model.report_date.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            for row in records:
                new_row = model(**{field: getattr(row, field) for field in cls.select_fields})
                new_row.trade_usd = quantize_amount(new_row.trade_usd, 2)
                new_row.trade_fee_usd = quantize_amount(new_row.trade_fee_usd, 2)
                item = new_row.to_dict()
                res.append(item)

            if report_type == "DAILY":
                export_headers = cls.export_headers_daily
            elif report_type == "WEEKLY":
                export_headers = cls.export_headers_weekly
            elif report_type == "MONTHLY":
                export_headers = cls.export_headers_monthly
            else:
                export_headers = cls.export_headers_basic
            return export_xlsx(filename='user_publicity_channel_report',
                               data_list=res,
                               export_headers=export_headers)


@ns.route("/publicity-channel-series")
@respond_with_code
class PublicityChannelSeriesResource(Resource, WebPublicityChannelMixin):
    class SeriesType(Enum):
        register_user_count = "注册用户"
        active_user_count = "活跃用户"
        deposit_user_count = "充值用户"
        asset_user_count = "资产用户"
        trade_user_count = "交易用户"
        spot_user_count = "现货用户"
        perpetual_user_count = "合约用户"
        exchange_user_count = "兑换用户"
        trade_usd = "交易金额（USD）"
        trade_fee_usd = "交易手续费（USD）"
        retained_count_1d = "次日留存"
        retained_count_7d = "7日留存"
        retained_count_30d = "30日留存"
        visit_count = "访问次数"
        ip_count = "访问IP数"

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        series_type=EnumField(SeriesType, required=True),
        language_area=common_fields.EnumField(LanguageArea),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        country=fields.String(allow_none=True),
        publicity_channel_id=fields.String(allow_none=True),
        publicity_channel=fields.String(allow_none=True),
        short_link_id=fields.String(allow_none=True),
        business_segment=common_fields.EnumField(BusinessSegment),
        category_id=fields.Integer(allow_none=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-宣发渠道统计报表-报表曲线"""
        today_ = today()
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        report_type = kwargs['report_type']
        period = kwargs['period']
        business_segment = kwargs.get('business_segment')
        category_id = kwargs.get('category_id')
        publicity_channel = kwargs.get('publicity_channel')

        if not start_date:
            start_date = {
                'DAILY': today_ - datetime.timedelta(days=31),
                'WEEKLY': today_ - relativedelta(weeks=10),
                'MONTHLY': date(today_.year, today_.month, 1) - relativedelta(months=6),
                'QUARTERLY': date(today_.year, today_.month, 1) - relativedelta(months=12),
            }.get(report_type, today_ - datetime.timedelta(days=31))
        if not end_date or end_date > today_:
            end_date = today_
        series_type = kwargs["series_type"]
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        if language_area and country:
            cls._validate_country(language_area, country)
        if publicity_channel_id := kwargs.get('publicity_channel_id'):
            cls._validate_publicity_channel(publicity_channel_id)
        if short_link_id := kwargs.get('short_link_id'):
            cls._validate_short_link(short_link_id)

        model, query = cls._get_model_and_query(
            period, report_type, language_area, country, publicity_channel_id,
            short_link_id, business_segment, category_id, publicity_channel)

        series_type_cols_map = {
            cls.SeriesType.register_user_count: ["register_user_count"],
            cls.SeriesType.active_user_count: ["active_user_count"],
            cls.SeriesType.deposit_user_count: ["deposit_user_count"],
            cls.SeriesType.asset_user_count: ["asset_user_count"],
            cls.SeriesType.trade_user_count: ["trade_user_count"],
            cls.SeriesType.spot_user_count: ["spot_user_count"],
            cls.SeriesType.perpetual_user_count: ["perpetual_user_count"],
            cls.SeriesType.exchange_user_count: ["exchange_user_count"],
            cls.SeriesType.trade_usd: ["trade_usd"],
            cls.SeriesType.trade_fee_usd: ["trade_usd"],
            cls.SeriesType.retained_count_1d: ["retained_count_1d"],
            cls.SeriesType.retained_count_7d: ["retained_count_7d"],
            cls.SeriesType.retained_count_30d: ["retained_count_30d"],
            cls.SeriesType.visit_count: ["visit_count"],
            cls.SeriesType.ip_count: ["ip_count"],
        }
        query_cols = [getattr(model, col) for col in
                      series_type_cols_map[series_type]]
        query = query.filter(
            model.report_date >= start_date,
            model.report_date <= end_date,
        ).with_entities(
            model.report_date,
            *query_cols,
        ).order_by(model.report_date.desc()).all()

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = float(quantize_amount(i[1], 2))
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


@ns.route('/publicity-channel-statistics-detail')
@respond_with_code
class PublicityChannelDetailResource(Resource, WebPublicityChannelMixin, PublicityMixin):

    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, required=True),
        report_type=fields.String(missing='DAILY'),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        sort_type=fields.String(missing='register_user_count'),
        language_area=common_fields.EnumField(LanguageArea),
        country=fields.String(allow_none=True),
        publicity_channel_id=fields.String(allow_none=True),
        publicity_channel=fields.String(allow_none=True),
        export=fields.Boolean(missing=False),
        page=PageField,
        limit=LimitField,
        business_segment=common_fields.EnumField(BusinessSegment),
        category_id=fields.Integer(allow_none=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-宣发渠道统计详情"""
        page, limit = kwargs['page'], kwargs['limit']
        report_date = kwargs['report_date']
        report_type = kwargs['report_type']
        sort_type = kwargs['sort_type']
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        period = kwargs['period']
        business_segment = kwargs.get('business_segment')
        category_id = kwargs.get('category_id')
        c_id_name_dic, b_segment_c_ids_dic, c_id_b_segment_dic, c_id_b_segment_name_dic = cls.get_category_dic()
        business_segments = {item.name: item.value for item in BusinessSegment}
        c_id_pub_channels_dic, pub_channel_id_name_dic = cls.get_category_pub_channels_dic()
        grouped_language_area_country = {k.name: v for k, v in ShortLinkInfo.GROUPED_PC_LA_COUNTRY.items()}
        publicity_channel = kwargs.get('publicity_channel')
        if language_area and country:
            cls._validate_country(language_area, country)
        if publicity_channel_id := kwargs.get('publicity_channel_id'):
            cls._validate_publicity_channel(publicity_channel_id)
        short_link_id_dict = cls._get_short_links(
            language_area, country, publicity_channel_id, business_segment,
            category_id, b_segment_c_ids_dic, publicity_channel
        )
        short_link_ids = list(short_link_id_dict.keys())
        model_query, fmt_report_date = cls._get_model_query(period, report_type, report_date, sort_type, short_link_ids)
        if not model_query:
            return dict(
                items=[],
                extra=dict(
                    language_areas={k.name: k.value for k in LanguageArea if
                                    k.name != 'ZH_HANS_CN'},
                    countries=COUNTRY_CODE_CN_NAME_DIC,
                    grouped_language_area_country=grouped_language_area_country,
                    publicity_channel_names=cls._get_publicity_channel_names(),
                    business_segments=business_segments,
                    c_id_name_dic=c_id_name_dic,
                    b_segment_c_ids_dic=b_segment_c_ids_dic,
                    c_id_pub_channels_dic=c_id_pub_channels_dic,
                    pub_channel_id_name_dic=pub_channel_id_name_dic,
                ),
                total=0,
            )
        sum_data = {
            'register_user_count': 0,
            'active_user_count': 0,
            'asset_user_count': 0,
            'spot_user_count': 0,
            'perpetual_user_count': 0,
            'trade_user_count': 0,
            'exchange_user_count': 0,
            'deposit_user_count': 0,
            'trade_usd': Decimal(),
            'trade_fee_usd': Decimal(),
            'visit_count': 0,
            'ip_count': 0,
        }
        res = []
        for record in model_query:
            item = record.to_dict()
            short_link_id = item.pop('short_link_id')
            item['short_link'] = short_link_id_dict[short_link_id]
            for k in sum_data.keys():
                sum_data[k] += item[k]
            item['trade_usd'] = quantize_amount(item['trade_usd'], 2)
            item['trade_fee_usd'] = quantize_amount(item['trade_fee_usd'], 2)
            res.append(item)
        sum_data['trade_usd'] = quantize_amount(sum_data['trade_usd'], 2)
        sum_data['trade_fee_usd'] = quantize_amount(sum_data['trade_fee_usd'], 2)
        sum_data['short_link'] = '--'
        sum_data['report_date'] = '--'
        total_data = cls.get_total_data(period, report_type, fmt_report_date)

        if not kwargs.get('export'):
            total = len(res)
            items = res[(page-1)*limit: page*limit]
            items.insert(0, sum_data)
            items.insert(0, total_data)
            return dict(
                items=items,
                extra=dict(
                    language_areas={k.name: k.value for k in LanguageArea if
                                    k.name != 'ZH_HANS_CN'},
                    countries=COUNTRY_CODE_CN_NAME_DIC,
                    grouped_language_area_country=grouped_language_area_country,
                    publicity_channel_names=cls._get_publicity_channel_names(),
                    business_segments=business_segments,
                    c_id_name_dic=c_id_name_dic,
                    b_segment_c_ids_dic=b_segment_c_ids_dic,
                    c_id_pub_channels_dic=c_id_pub_channels_dic,
                    pub_channel_id_name_dic=pub_channel_id_name_dic,
                ),
                total=total,
            )
        else:
            res.insert(0, sum_data)
            res.insert(0, total_data)
            return export_xlsx(
                filename='user_publicity_channel_report_detail',
                data_list=res,
                export_headers=cls.detail_export_headers
            )

    @classmethod
    def get_total_data(cls, period, report_type, report_date):
        """当天的全部汇总数据"""
        model = {
            'DAILY': DailyPublicityLanguageAreaReport,
            'WEEKLY': WeeklyPublicityLanguageAreaReport,
            'MONTHLY': MonthlyPublicityLanguageAreaReport,
            'QUARTERLY': QuarterlyPublicityLanguageAreaReport,
        }.get(report_type, DailyPublicityLanguageAreaReport)
        total_rec = model.query.filter(
            model.language_area == 'ALL',
            model.period == model.Period[period.name],
            model.report_date == report_date
        ).first()
        total_data = total_rec.to_dict() if total_rec else dict()
        total_data['short_link'] = 'ALL'
        total_data['report_date'] = 'ALL'
        return total_data

    @classmethod
    def _get_short_links(cls, language_area, country, publicity_channel_id, business_segment, category_id,
                         b_segment_c_ids_dic, publicity_channel):
        s_model = ShortLinkInfo
        p_model = PublicityChannel
        query = s_model.query.join(
            p_model, s_model.publicity_channel_id == p_model.id
        ).filter(
            s_model.status == s_model.StatusType.VALID,
            p_model.status == p_model.Status.VALID
        ).with_entities(
            s_model.id,
            s_model.title
        )
        if publicity_channel_id:
            query = query.filter(
                s_model.publicity_channel_id == publicity_channel_id
            )
        if publicity_channel:
            query = query.filter(
                p_model.channel == publicity_channel,
            )
        if language_area:
            query = query.filter(
                s_model.language_area == language_area,
            )
        if country:
            query = query.filter(s_model.country == country)
        if business_segment:
            if category_id:
                query = query.filter(p_model.category_id == category_id)
            else:
                category_ids = b_segment_c_ids_dic[business_segment.name]
                query = query.filter(p_model.category_id.in_(category_ids))
        short_link_dic = {i.id: f'{i.id}-{i.title}' for i in query.all()}
        return short_link_dic

    @classmethod
    def _get_model_query(cls, period, report_type, report_date, sort_type, short_link_ids):

        model = {
            'DAILY': DailyPublicityShortLinkReport,
            'WEEKLY': WeeklyPublicityShortLinkReport,
            'MONTHLY': MonthlyPublicityShortLinkReport,
            'QUARTERLY': QuarterlyPublicityShortLinkReport,
        }.get(report_type, DailyPublicityShortLinkReport)
        first_rec = model.query.filter(
            model.report_date <= report_date,
            model.period == model.Period[period.name]
        ).order_by(model.report_date.desc()).with_entities(
            model.report_date
        ).first()
        if not first_rec:
            return None, report_date
        fmt_report_date = first_rec.report_date
        ret = model.query.filter(
            model.short_link_id.in_(short_link_ids),
            model.report_date == fmt_report_date,
            model.period == model.Period[period.name]
        ).order_by(
            getattr(model, sort_type).desc()
        ).all()
        return ret, fmt_report_date


@ns.route('/app-publicity-channel-statistics')
@respond_with_code
class AppPublicityChannelStatisticResource(Resource, AppPublicityChannelMixin):

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        language_area=common_fields.EnumField(LanguageArea),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        country=fields.String(allow_none=True),
        channel=fields.String(allow_none=True),
        platform=common_fields.EnumField(ReportPlatform, required=True),
        export=fields.Boolean(missing=False),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """报表-用户报表-App宣发渠道统计报表"""
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        channel = kwargs.get('channel')
        model, query = cls._get_model_and_query(
            kwargs['period'], kwargs['platform'], report_type, language_area, country, channel)
        today_ = today()
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if not start_date:
            start_date = datetime.date(2024, 1, 1)
        if not end_date or end_date > today_:
            end_date = today_
        query = query.filter(
            model.report_date >= start_date,
            model.report_date < end_date,
        ).group_by(
            model.report_date
        ).with_entities(
            model.report_date,
            *[func.sum(getattr(model, field)).label(field) for field in cls.sum_fields]
        )

        res = []
        if not kwargs.get('export'):
            records = query.order_by(model.report_date.desc()).paginate(page, limit)
            for row in records.items:
                new_row = model(**{field: getattr(row, field) for field in cls.select_fields})
                new_row.trade_usd = quantize_amount(new_row.trade_usd or Decimal(), 2)
                new_row.trade_fee_usd = quantize_amount(new_row.trade_fee_usd or Decimal(), 2)
                item = new_row.to_dict()
                res.append(item)

            return dict(
                items=res,
                extra=dict(
                    language_areas=language_name_cn_names(),
                    countries=COUNTRY_CODE_CN_NAME_DIC,
                    channels=AfChannelInfoMySQL.get_all_channel(),
                ),
                total=records.total,
            )
        else:
            records = query.order_by(model.report_date.desc()).limit(ADMIN_EXPORT_LIMIT).all()
            for row in records:
                new_row = model(**{field: getattr(row, field) for field in cls.select_fields})
                new_row.trade_usd = quantize_amount(new_row.trade_usd or Decimal(), 2)
                new_row.trade_fee_usd = quantize_amount(new_row.trade_fee_usd or Decimal(), 2)
                item = new_row.to_dict()
                res.append(item)

            if report_type == "DAILY":
                export_headers = cls.export_headers_daily
            elif report_type == "WEEKLY":
                export_headers = cls.export_headers_weekly
            elif report_type == "MONTHLY":
                export_headers = cls.export_headers_monthly
            else:
                export_headers = cls.export_headers_basic
            return export_xlsx(filename='user_app_publicity_channel_report',
                               data_list=res,
                               export_headers=export_headers)


@ns.route("/app-publicity-channel-series")
@respond_with_code
class AppPublicityChannelSeriesResource(Resource, AppPublicityChannelMixin):
    class SeriesType(Enum):
        register_user_count = "注册用户"
        deposit_user_count = "充值用户"
        trade_user_count = "交易用户"
        spot_user_count = "现货用户"
        perpetual_user_count = "合约用户"
        trade_usd = "交易金额（USD）"

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        series_type=EnumField(SeriesType, required=True),
        language_area=common_fields.EnumField(LanguageArea),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        platform=common_fields.EnumField(ReportPlatform, required=True),
        country=fields.String(allow_none=True),
        channel=fields.String(allow_none=True),
    ))
    def get(cls, **kwargs):
        """报表-用户报表-App宣发渠道统计报表-报表曲线"""
        today_ = today()
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        report_type = kwargs['report_type']
        channel = kwargs.get('channel')

        if not start_date:
            start_date = {
                'DAILY': today_ - datetime.timedelta(days=31),
                'WEEKLY': today_ - relativedelta(weeks=10),
                'MONTHLY': date(today_.year, today_.month, 1) - relativedelta(months=6),
                'QUARTERLY': date(today_.year, today_.month, 1) - relativedelta(months=12),
            }.get(report_type, today_ - datetime.timedelta(days=31))
        if not end_date or end_date > today_:
            end_date = today_
        series_type = kwargs["series_type"].name
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        model, query = cls._get_model_and_query(
            kwargs['period'], kwargs['platform'], report_type, language_area, country, channel)
        query = query.filter(
            model.report_date >= start_date,
            model.report_date <= end_date,
        ).group_by(
            model.report_date
        ).with_entities(
            model.report_date,
            func.sum(getattr(model, series_type)).label(series_type),
        ).order_by(model.report_date.desc()).all()

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            value = float(quantize_amount(i[1], 2))
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
            start_date=start_date,
            end_date=end_date,
        )


@ns.route('/app-publicity-channel-statistics-detail')
@respond_with_code
class AppPublicityChannelDetailResource(Resource, AppPublicityChannelMixin):

    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, required=True),
        report_type=fields.String(missing='DAILY'),
        period=common_fields.EnumField(WebPublicityChannelMixin.Period,
                                       missing=WebPublicityChannelMixin.Period.HISTORY),
        sort_type=fields.String(missing='register_user_count'),
        language_area=common_fields.EnumField(LanguageArea),
        country=fields.String(allow_none=True),
        channel=fields.String(allow_none=True),
        platform=common_fields.EnumField(ReportPlatform, required=True),
        export=fields.Boolean(missing=False),
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """报表-用户报表-App宣发渠道统计详情"""
        page, limit = kwargs['page'], kwargs['limit']
        report_date = kwargs['report_date']
        report_type = kwargs['report_type']
        sort_type = kwargs['sort_type']
        channel = kwargs.get('channel')
        language_area = kwargs.get('language_area')
        country = kwargs.get('country')
        model, query = cls._get_model_and_query(
            kwargs['period'], kwargs['platform'], report_type, language_area, country, channel)
        rows = query.filter(
            model.report_date == report_date
        ).group_by(
            model.report_date,
            model.channel,
        ).order_by(
            getattr(model, sort_type).desc()
        ).with_entities(
            model.report_date,
            model.channel,
            *[func.sum(getattr(model, field)).label(field) for field in cls.sum_fields]
        ).all()
        extra = dict(
            language_areas=language_name_cn_names(),
            countries=COUNTRY_CODE_CN_NAME_DIC,
            channels=AfChannelInfoMySQL.get_all_channel()
        )
        if not rows:
            return dict(
                items=[],
                extra=extra,
                total=0,
            )
        sum_data = {
            'register_user_count': 0,
            'active_user_count': 0,
            'asset_user_count': 0,
            'spot_user_count': 0,
            'perpetual_user_count': 0,
            'trade_user_count': 0,
            'exchange_user_count': 0,
            'deposit_user_count': 0,
            'trade_usd': Decimal(),
        }
        res = []
        need_fields = cls.select_fields + ("channel", )
        for record in rows:
            item = model(**{field: getattr(record, field) for field in need_fields})
            for k in sum_data.keys():
                sum_data[k] += getattr(item, k)
            item.trade_usd = quantize_amount(item.trade_usd, 2)
            res.append(item.to_dict())
        sum_data['trade_usd'] = quantize_amount(sum_data['trade_usd'], 2)
        sum_data['channel'] = '--'
        sum_data['report_date'] = '--'

        if not kwargs.get('export'):
            total = len(res)
            items = res[(page-1)*limit: page*limit]
            items.insert(0, sum_data)
            return dict(
                items=items,
                extra=extra,
                total=total,
            )
        else:
            res.insert(0, sum_data)
            return export_xlsx(
                filename='user_publicity_channel_report_detail',
                data_list=res,
                export_headers=cls.detail_export_headers
            )



class ActiveRetainMixin:
    sort_names = ('last_month_active_user_count',
                  'in_three_month_active_user_count',
                  'in_six_month_active_user_count', 'over_six_month_active_user_count')

    sort_dic = {'ALL': 2, '其他': 1}

    @classmethod
    def get_items(cls, model_: db.Model, **kwargs):
        query_date = kwargs.get('query_date')
        if not query_date:
            query_date = cls.get_first_record_date(model_)
            if not query_date:
                return []
        else:
            query_date = datetime.date(query_date.year, query_date.month, 1)
        sort_name = kwargs['sort_name']
        records = model_.query.filter(
            model_.report_date == query_date
        ).order_by(getattr(model_, sort_name).desc()).all()
        res = []
        for record in records:
            item = cls.fmt_item(record)
            res.append(item)
        return res

    @classmethod
    def get_first_record_date(cls, model_):
        rec = model_.query.order_by(model_.report_date.desc()).first()
        return rec.report_date if rec else None

    @classmethod
    def fmt_item(cls, record):
        item = record.to_dict()
        user_count = item['user_count']
        last_month_active_user_percent = format_percent(
            safe_div(item['last_month_active_user_count'], user_count))
        in_three_month_active_user_percent = format_percent(
            safe_div(item['in_three_month_active_user_count'], user_count))
        in_six_month_active_user_percent = format_percent(
            safe_div(item['in_six_month_active_user_count'], user_count))
        over_six_month_active_user_percent = format_percent(
            safe_div(item['over_six_month_active_user_count'], user_count))
        item.update({
            'last_month_active_user_percent': last_month_active_user_percent,
            'in_three_month_active_user_percent': in_three_month_active_user_percent,
            'in_six_month_active_user_percent': in_six_month_active_user_percent,
            'over_six_month_active_user_percent': over_six_month_active_user_percent,
        })
        return item


@ns.route('/language-area-user-active-retain')
@respond_with_code
class LanguageAreaUserActiveRetainResource(Resource, ActiveRetainMixin):

    export_headers = (
        {"field": "language_area", Language.ZH_HANS_CN: "语区"},
        {"field": "user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "last_month_active_user_count", Language.ZH_HANS_CN: "0-1个月"},
        {"field": "last_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_three_month_active_user_count", Language.ZH_HANS_CN: "1-3个月"},
        {"field": "in_three_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_six_month_active_user_count", Language.ZH_HANS_CN: "3-6个月"},
        {"field": "in_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "over_six_month_active_user_count", Language.ZH_HANS_CN: "6个月及以上"},
        {"field": "over_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        sort_name=EnumField(ActiveRetainMixin.sort_names, missing='last_month_active_user_count'),
        query_date=DateField(to_date=True),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国家报表-语区留存报表"""
        items = cls.get_items(LanguageAreaUserActiveRetainReport, **kwargs)
        items = list(filter(lambda x: x['language_area'] != 'ZH_HANS_CN', items))
        items.sort(key=lambda x: cls.sort_dic.get(x['language_area'], 0), reverse=True)
        languages = language_name_cn_names()
        languages.pop('ZH_HANS_CN', None)
        languages.update({
            'ALL': 'ALL',
            '其他': '其他'
        })
        if kwargs['export']:
            for item in items:
                item['language_area'] = languages[item['language_area']]
            return export_xlsx(
                'language_area_user_active_retain_report', items, cls.export_headers)
        else:
            return {
                'items': items,
                'languages': languages
            }


@ns.route('/language-area-user-active-retain-detail')
@respond_with_code
class LanguageAreaUserActiveRetainDetailResource(Resource, ActiveRetainMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "language_area", Language.ZH_HANS_CN: "语区"},
        {"field": "user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "last_month_active_user_count", Language.ZH_HANS_CN: "0-1个月"},
        {"field": "last_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_three_month_active_user_count", Language.ZH_HANS_CN: "1-3个月"},
        {"field": "in_three_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_six_month_active_user_count", Language.ZH_HANS_CN: "3-6个月"},
        {"field": "in_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "over_six_month_active_user_count", Language.ZH_HANS_CN: "6个月及以上"},
        {"field": "over_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        language_area=fields.String(required=True),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """报表-国家报表-语区留存报表详情"""
        language_area = kwargs["language_area"]
        model_ = LanguageAreaUserActiveRetainReport
        records = model_.query.filter(
            model_.language_area == language_area
        ).order_by(model_.id.desc()).all()
        languages = language_name_cn_names()
        languages.pop('ZH_HANS_CN', None)
        languages.update({
            'ALL': 'ALL',
            '其他': '其他'
        })
        res = []
        for record in records:
            if record.language_area == 'ZH_HANS_CN':
                continue
            item = cls.fmt_item(record)
            item['report_date'] = item['report_date'].strftime('%Y-%m')
            res.append(item)
        if not kwargs['export']:
            return dict(
                items=res,
                languages=languages
            )
        else:
            for item in res:
                item['language_area'] = languages[item['language_area']]

            return export_xlsx(
                filename='language_area_user_active_retain_report_detail',
                data_list=res,
                export_headers=cls.export_headers
            )


@ns.route("/language-area-user-active-retain-series")
@respond_with_code
class LanguageAreaUserActiveRetainSeriesResource(Resource):

    class SeriesType(Enum):
        user_count = "总用户"
        last_month_active_user_count = "0-1个月"
        in_three_month_active_user_count = "1-3个月"
        in_six_month_active_user_count = "3-6个月"
        over_six_month_active_user_count = "6个月及以上"
        last_month_active_user_percent = "0-1个月占比"
        in_three_month_active_user_percent = "1-3个月占比"
        in_six_month_active_user_percent = "3-6个月占比"
        over_six_month_active_user_percent = "6个月及以上占比"

    @classmethod
    @ns.use_kwargs(dict(
        language_area=fields.String(required=True),
        series_type=EnumField(SeriesType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-国家报表-语区留存报表-报表曲线"""
        language_area = kwargs.get("language_area")
        series_type = kwargs["series_type"]
        series_type_cols_map = {
            cls.SeriesType.user_count: ["user_count"],
            cls.SeriesType.last_month_active_user_count: ["last_month_active_user_count"],
            cls.SeriesType.in_three_month_active_user_count: ["in_three_month_active_user_count"],
            cls.SeriesType.in_six_month_active_user_count: ["in_six_month_active_user_count"],
            cls.SeriesType.over_six_month_active_user_count: ["over_six_month_active_user_count"],
            cls.SeriesType.last_month_active_user_percent: ["last_month_active_user_count", "user_count"],
            cls.SeriesType.in_three_month_active_user_percent: ["in_three_month_active_user_count", "user_count"],
            cls.SeriesType.in_six_month_active_user_percent: ["in_six_month_active_user_count", "user_count"],
            cls.SeriesType.over_six_month_active_user_percent: ["over_six_month_active_user_count", "user_count"],
        }
        model_ = LanguageAreaUserActiveRetainReport
        query_cols = [getattr(model_, col) for col in
                      series_type_cols_map[series_type]]
        query = (
            model_.query.filter(
                model_.language_area == language_area,
            ).with_entities(
                model_.report_date,
                *query_cols,
            ).order_by(model_.report_date.desc()).all()
        )

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            if 'percent' in series_type.name:
                value = round(safe_div(i[1], i[2]) * 100, 2)
            else:
                value = i[1]
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
        )


@ns.route('/country-user-active-retain')
@respond_with_code
class CountryUserActiveRetainResource(Resource, ActiveRetainMixin):
    export_headers = (
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "last_month_active_user_count", Language.ZH_HANS_CN: "0-1个月"},
        {"field": "last_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_three_month_active_user_count", Language.ZH_HANS_CN: "1-3个月"},
        {"field": "in_three_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_six_month_active_user_count", Language.ZH_HANS_CN: "3-6个月"},
        {"field": "in_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "over_six_month_active_user_count", Language.ZH_HANS_CN: "6个月及以上"},
        {"field": "over_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        sort_name=EnumField(ActiveRetainMixin.sort_names,
                            missing='last_month_active_user_count'),
        query_date=DateField(to_date=True),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-国家报表-国家留存报表"""
        items = cls.get_items(CountryUserActiveRetainReport, **kwargs)
        items = list(filter(lambda x: x['country'] != '中国', items))
        items.sort(key=lambda x: cls.sort_dic.get(x['country'], 0), reverse=True)

        if kwargs['export']:
            return export_xlsx(
                'country_user_active_retain_report', items, cls.export_headers)
        else:
            return {
                'items': items,
            }


@ns.route('/country-user-active-retain-detail')
@respond_with_code
class CountryUserActiveRetainDetailResource(Resource, ActiveRetainMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "country", Language.ZH_HANS_CN: "国家"},
        {"field": "user_count", Language.ZH_HANS_CN: "总用户"},
        {"field": "last_month_active_user_count", Language.ZH_HANS_CN: "0-1个月"},
        {"field": "last_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_three_month_active_user_count", Language.ZH_HANS_CN: "1-3个月"},
        {"field": "in_three_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "in_six_month_active_user_count", Language.ZH_HANS_CN: "3-6个月"},
        {"field": "in_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
        {"field": "over_six_month_active_user_count", Language.ZH_HANS_CN: "6个月及以上"},
        {"field": "over_six_month_active_user_percent", Language.ZH_HANS_CN: "占比"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String(required=True),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """报表-国家报表-国家留存报表详情"""
        country = kwargs["country"]
        res = []
        if country != '中国':
            model_ = CountryUserActiveRetainReport
            records = model_.query.filter(
                model_.country == country
            ).order_by(model_.id.desc()).all()
            for record in records:
                item = cls.fmt_item(record)
                item['report_date'] = item['report_date'].strftime('%Y-%m')
                res.append(item)
        countries = list(set(list_country_cn_name()) - {'中国'})
        if not kwargs['export']:
            return dict(
                items=res,
                countries=countries
            )
        else:
            return export_xlsx(
                filename='country_user_active_retain_report_detail',
                data_list=res,
                export_headers=cls.export_headers
            )


@ns.route("/country-user-active-retain-series")
@respond_with_code
class CountryUserActiveRetainSeriesResource(Resource):
    class SeriesType(Enum):
        user_count = "总用户"
        last_month_active_user_count = "0-1个月"
        in_three_month_active_user_count = "1-3个月"
        in_six_month_active_user_count = "3-6个月"
        over_six_month_active_user_count = "6个月及以上"
        last_month_active_user_percent = "0-1个月占比"
        in_three_month_active_user_percent = "1-3个月占比"
        in_six_month_active_user_percent = "3-6个月占比"
        over_six_month_active_user_percent = "6个月及以上占比"

    @classmethod
    @ns.use_kwargs(dict(
        country=fields.String(required=True),
        series_type=EnumField(SeriesType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-国家报表-国家留存报表-报表曲线"""
        country = kwargs.get("country")
        series_type = kwargs["series_type"]
        series_type_cols_map = {
            cls.SeriesType.user_count: ["user_count"],
            cls.SeriesType.last_month_active_user_count: ["last_month_active_user_count"],
            cls.SeriesType.in_three_month_active_user_count: [
                "in_three_month_active_user_count"],
            cls.SeriesType.in_six_month_active_user_count: [
                "in_six_month_active_user_count"],
            cls.SeriesType.over_six_month_active_user_count: [
                "over_six_month_active_user_count"],
            cls.SeriesType.last_month_active_user_percent: [
                "last_month_active_user_count", "user_count"],
            cls.SeriesType.in_three_month_active_user_percent: [
                "in_three_month_active_user_count", "user_count"],
            cls.SeriesType.in_six_month_active_user_percent: [
                "in_six_month_active_user_count", "user_count"],
            cls.SeriesType.over_six_month_active_user_percent: [
                "over_six_month_active_user_count", "user_count"],
        }
        model_ = CountryUserActiveRetainReport
        query_cols = [getattr(model_, col) for col in
                      series_type_cols_map[series_type]]
        query = (
            model_.query.filter(
                model_.country == country,
            ).with_entities(
                model_.report_date,
                *query_cols,
            ).order_by(model_.report_date.desc()).all()
        )

        result_series = []
        for i in query:
            ts = date_to_datetime(i.report_date).timestamp() * 1000
            if 'percent' in series_type.name:
                value = round(safe_div(i[1], i[2]) * 100, 2)
            else:
                value = i[1]
            result_series.append([ts, value])
        result_series.sort(key=lambda x: x[0])

        return dict(
            series_type=cls.SeriesType,
            series=result_series,
        )

@ns.route("/loss-user")
@respond_with_code
class LossUserReportResource(Resource):

    fields = (
        'id',
        'report_date',
        'user_count',
        'to_be_loss_user_count',
        'new_to_be_loss_user_count',
        'to_be_loss_return_count',
        'to_be_loss_balance_user_count',
        'loss_user_count',
        'new_loss_user_count',
        'loss_return_count',
        'loss_balance_user_count',
        'new_user_count',
        'net_new_user_count'
    )

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(DailyLossUserReport.Type, required=True),
        report_type=EnumField(ReportType, missing=ReportType.DAILY, enum_by_value=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(missing=1),
        limit=PageField(missing=50, unlimited=True),
    ))
    def get(cls, **kwargs):
        """报表-流失报表"""

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyLossUserReport
        else:
            model = MonthlyLossUserReport
        type_ = getattr(model.Type, kwargs['type'].name)
        query = model.query.filter(model.type == type_).order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        pagination = query.paginate(kwargs['page'], kwargs['limit'])

        items = []
        for item in pagination.items:
            item = {k:v for k, v in item.to_dict().items() if k in cls.fields}
            items.append(item)
        return dict(
            total=pagination.total,
            items=items,
        )


@ns.route('/vip-report')
@respond_with_code
class VipReportResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_count", Language.ZH_HANS_CN: "VIP总人数"},
        {"field": "new_count", Language.ZH_HANS_CN: "VIP新增人数"},
        {"field": "vip_1_count", Language.ZH_HANS_CN: "VIP1用户数"},
        {"field": "vip_2_count", Language.ZH_HANS_CN: "VIP2用户数"},
        {"field": "vip_3_count", Language.ZH_HANS_CN: "VIP3用户数"},
        {"field": "vip_4_count", Language.ZH_HANS_CN: "VIP4用户数"},
        {"field": "vip_5_count", Language.ZH_HANS_CN: "VIP5用户数"},
        {"field": "cet_vip_count", Language.ZH_HANS_CN: "CET持仓类VIP用户"},
        {"field": "balance_vip_count", Language.ZH_HANS_CN: "资产总值类VIP用户"},
        {"field": "spot_vip_count", Language.ZH_HANS_CN: "币币交易量类VIP用户"},
        {"field": "perpetual_vip_count", Language.ZH_HANS_CN: "合约交易量类VIP用户"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=EnumField(ReportType, enum_by_value=False, required=True),
        page=PageField,
        limit=LimitField,
        export=fields.Boolean
    ))
    def get(cls, **kwargs):
        """报表-用户报表-VIP报表"""
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if report_type == ReportType.DAILY:
            report_model = DailyVipReport
        else:
            report_model = MonthlyVipReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)
        query = report_model.query.order_by(report_model.report_date.desc())
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        if kwargs.get('export'):
            records = query.all()
            return cls.export_data(records)
        else:
            records = query.paginate(page, limit)
            return dict(
                        items=records.items,
                        total=records.total,
                        )

    @classmethod
    def export_data(cls, records):
        res = []
        for record in records:
            item = record.to_dict()
            user_count = item['user_count']
            new_count = item['new_count']
            last_count = user_count - new_count
            new_count_percent = format_percent(safe_div(new_count, last_count))
            operator = '+' if new_count > 0 else ''
            item['new_count'] = f'{new_count}({operator}{new_count_percent})'
            item['vip_1_count'] = fmt_val(item['vip_1_count'], user_count)
            item['vip_2_count'] = fmt_val(item['vip_2_count'], user_count)
            item['vip_3_count'] = fmt_val(item['vip_3_count'], user_count)
            item['vip_4_count'] = fmt_val(item['vip_4_count'], user_count)
            item['vip_5_count'] = fmt_val(item['vip_5_count'], user_count)
            item['cet_vip_count'] = fmt_val(item['cet_vip_count'], user_count)
            item['balance_vip_count'] = fmt_val(item['balance_vip_count'], user_count)
            item['spot_vip_count'] = fmt_val(item['spot_vip_count'], user_count)
            item['perpetual_vip_count'] = fmt_val(item['perpetual_vip_count'], user_count)
            res.append(item)
        return export_xlsx(
            filename='coinex_ambassador_change_history',
            data_list=res,
            export_headers=cls.export_headers
        )


def fmt_val(val, total_count):
    percent = safe_div(val, total_count)
    return f'{val}({format_percent(percent)})'
