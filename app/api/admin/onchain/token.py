from decimal import Decimal

from collections import defaultdict

from marshmallow import fields

from flask import g
from sqlalchemy import or_

from app.config import config

from app.exceptions import InvalidArgument

from app.assets.asset import get_asset
from app.assets.asset import list_all_assets

from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code
from app.api.common.fields import <PERSON>um<PERSON>ield
from app.api.common.fields import <PERSON><PERSON>ield
from app.api.common.fields import <PERSON>itField
from app.api.common.fields import TimestampField

from app.business.onchain.base import OnchainAddressHelper
from app.business.onchain.token import get_token
from app.business.onchain.token import batch_get_token
from app.business.onchain.token import get_token_base_from_coinex_wallet
from app.business.onchain.token import get_or_create_token_by_token_address

from app.common.onchain import Chain

from app.utils.date_ import now
from app.utils.onchain import decimal_mul
from app.utils.onchain import decimal_add

from app.models import db
from app.models.user import User
from app.models.onchain import OnchainToken
from app.models.onchain import OnchainTokenBalance
from app.models.onchain import OnchainTokenBlocklist
from app.models.onchain import OnchainTokenHotConfig
from app.models.mongo.op_log import OPNamespaceObjectSpot
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog

from app.caches.onchain import OnchainHotTokenCache
from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainHotTokenUpdatedAtCache

ns = Namespace('Onchain - Token')


def _delete_model(model, op, kwargs):
    chain = kwargs['chain']
    contract = OnchainAddressHelper(chain).normalise_address(kwargs['contract'])
    block = model.get_or_create(chain=chain, contract=contract)
    if not block:
        raise InvalidArgument(message=f'未找到Token, chain: {chain}, contract: {contract}')
    op_log_detail = block.to_dict(enum_to_name=True)

    db.session.delete(block)
    db.session.commit()

    AdminOperationLog.new_delete(
        user_id=g.user.id,
        ns_obj=op,
        detail=op_log_detail,
    )


@ns.route('/blocklist')
@respond_with_code
class OnchainTokenBlocklistResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain),
        symbol=fields.String(),
        contract=fields.String(),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """链上交易-代币限制管理-列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = OnchainTokenBlocklist.query.order_by(OnchainTokenBlocklist.id.desc())
        if chain := kwargs.get('chain'):
            query = query.filter(
                OnchainTokenBlocklist.chain == chain,
            )
        if symbol := kwargs.get('symbol'):
            query = query.filter(
                OnchainTokenBlocklist.symbol.ilike(f'%{symbol}%'),
            )
        if contract := kwargs.get('contract'):
            if contract.startswith('0x'):
                contract = OnchainAddressHelper(Chain.ERC20).normalise_address(contract)
            query = query.filter(
                OnchainTokenBlocklist.contract == contract,
            )
        pagination = query.paginate(page, limit, error_out=False)
        user_ids = [item.admin_user_id for item in pagination.items]

        users = User.query.filter(User.id.in_(user_ids)).all()
        user_email_map = {u.id: u.email for u in users}

        return dict(
            items=[
                dict(
                    id=item.id,
                    chain=item.chain.name,
                    contract=item.contract,
                    symbol=item.symbol,
                    name=item.name,
                    block_type=item.block_type.name,
                    notice_type=item.notice_type.name if item.notice_type else None,
                    spot_asset=item.spot_asset,
                    reason=item.reason,
                    admin_user=user_email_map.get(item.admin_user_id, str(item.admin_user_id)),
                    admin_user_id=item.admin_user_id,
                    created_at=item.created_at,
                ) for item in pagination.items
            ],
            total=pagination.total,
            chains=[item.name for item in Chain],
            block_types={item.name: item.value for item in OnchainTokenBlocklist.BlockType},
            notice_types={item.name: item.value for item in OnchainTokenBlocklist.NoticeType},
            assets=list_all_assets(),
        )

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
        block_type=EnumField(OnchainTokenBlocklist.BlockType, required=True),
        notice_type=EnumField(OnchainTokenBlocklist.NoticeType, required=False),
        spot_asset=fields.String(),
        reason=fields.String(),
    ))
    def put(cls, **kwargs):
        """链上交易-代币限制管理-添加/编辑"""
        chain = kwargs['chain']
        contract = OnchainAddressHelper(chain).normalise_address(kwargs['contract'])
        block_type = kwargs['block_type']
        notice_type = kwargs.get('notice_type')
        spot_asset = kwargs.get('spot_asset') or None
        reason = kwargs.get('reason')
        if block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
            if not notice_type:
                raise InvalidArgument(message='禁止交易时需要设置提示分类')
            if notice_type == OnchainTokenBlocklist.NoticeType.ABNORMAL:
                spot_asset = None
        if block_type in OnchainTokenBlocklist.BlockType.can_not_hot_type():
            if OnchainTokenHotConfig.query.filter(
                    OnchainTokenHotConfig.chain == chain,
                    OnchainTokenHotConfig.contract == contract,
                    or_(
                        OnchainTokenHotConfig.expired_at >= now(),
                        OnchainTokenHotConfig.expired_at.is_(None),
                    ),
            ).first() is not None:
                raise InvalidArgument(message=f'Token已被设置手动热门榜, chain: {chain}, contract: {contract}')

        if spot_asset:
            get_asset(spot_asset)  # 检查spot_asset是否有效

        token_base = get_token_base_from_coinex_wallet(chain, contract)
        if not token_base:
            raise InvalidArgument(message=f'未找到Token, chain: {chain}, contract: {contract}')

        block: OnchainTokenBlocklist = OnchainTokenBlocklist.get_or_create(chain=chain, contract=contract)
        old_data = block.to_dict(enum_to_name=True) if block.id else None
        block.symbol = token_base.symbol
        block.name = token_base.name
        block.block_type = block_type
        if block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
            block.notice_type = notice_type
            if notice_type != OnchainTokenBlocklist.NoticeType.ABNORMAL:
                block.spot_asset = spot_asset
        block.reason = reason
        block.admin_user_id = g.user.id
        db.session_add_and_commit(block)

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.OnchainTokenBlocklist,
            old_data=old_data,
            new_data=block.to_dict(enum_to_name=True),
            special_data=dict(chain=chain.name, contract=contract, symbol=block.symbol, name=block.name),
        )
        return dict()

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """链上交易-代币限制管理-删除"""
        _delete_model(OnchainTokenBlocklist, OPNamespaceObjectSpot.OnchainTokenBlocklist, kwargs)
        return dict()


@ns.route('/<int:user_id>/balance')
@respond_with_code
class OnchainTokenBalanceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        contract=fields.String(required=False),
    ))
    def get(cls, user_id, **kwargs):
        """链上交易-链上资产"""
        contract: str = kwargs.get('contract')
        if contract and contract.startswith('0x'):
            contract = OnchainAddressHelper(Chain.ERC20).normalise_address(contract)

        all_balance = OnchainTokenBalance.query.filter(
            OnchainTokenBalance.user_id == user_id,
        ).all()
        token_ids = [item.token_id for item in all_balance]
        token_base_map = {
            item.id: item for item in OnchainToken.query.filter(
                OnchainToken.id.in_(token_ids),
            ).all()
        }
        token_quote_map = OnchainTokenQuoteCache().get_many(token_ids)
        data = []
        total_volume = Decimal()
        for balance in all_balance:
            balance: OnchainTokenBalance
            if balance.token_id not in token_base_map:
                raise InvalidArgument(f'miss token_id: {balance.token_id}')
            token_base: OnchainToken = token_base_map[balance.token_id]
            if contract and token_base.contract != contract:
                continue
            price = Decimal()
            if balance.token_id in token_quote_map:
                price = token_quote_map[balance.token_id]['price'] or Decimal()
            amount = decimal_add(balance.frozen, balance.available)
            volume = decimal_mul(amount, price)
            data.append(dict(
                chain=token_base.chain,
                contract=token_base.contract,
                symbol=token_base.symbol,
                name=token_base.name,
                volume=volume,
                amount=amount,
                frozen=balance.frozen,
                available=balance.available,
            ))
            total_volume = decimal_add(total_volume, volume)
        data.sort(key=lambda x: x['volume'], reverse=True)
        return dict(
            items=data,
            total_volume=total_volume,
        )


@ns.route('/hot/config')
@respond_with_code
class OnchainTokenHotConfigResource(Resource):

    @classmethod
    def get(cls):
        """链上交易-热门榜代币配置-列表"""
        items = OnchainTokenHotConfig.query.filter(
            or_(
                OnchainTokenHotConfig.expired_at >= now(),
                OnchainTokenHotConfig.expired_at.is_(None),
            ),
        ).order_by(OnchainTokenHotConfig.id.desc()).all()
        user_ids = [item.admin_user_id for item in items]

        users = User.query.filter(User.id.in_(user_ids)).all()
        user_email_map = {u.id: u.email for u in users}

        hot_token_count = defaultdict(int)
        for _, token in batch_get_token(OnchainHotTokenCache().all()).items():
            hot_token_count[token.chain.name] += 1

        return dict(
            items=[dict(
                id=item.id,
                chain=item.chain.name,
                contract=item.contract,
                symbol=item.symbol,
                name=item.name,
                is_top=item.is_top,
                expired_at=item.expired_at,
                admin_user=user_email_map.get(item.admin_user_id, str(item.admin_user_id)),
                admin_user_id=item.admin_user_id,
                created_at=item.created_at,
            ) for item in items],
            chains=[item.name for item in Chain],
            hot_token_count=hot_token_count,
            updated_at=int(OnchainHotTokenUpdatedAtCache().get() or 0),
        )

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
        is_top=fields.Boolean(required=True),
        expired_at=TimestampField(is_ms=True),
    ))
    def put(cls, **kwargs):
        """链上交易-热门榜代币配置-添加/编辑"""
        chain = kwargs['chain']
        contract = OnchainAddressHelper(chain).normalise_address(kwargs['contract'])
        is_top = kwargs['is_top']
        expired_at = kwargs.get('expired_at') or None

        block: OnchainTokenBlocklist = OnchainTokenBlocklist.query.filter(
            OnchainTokenBlocklist.chain == chain,
            OnchainTokenBlocklist.contract == contract,
        ).first()
        if block and block.block_type in OnchainTokenBlocklist.BlockType.can_not_hot_type():
            raise InvalidArgument(message=f'Token已被设置禁止上榜, chain: {chain}, contract: {contract}')

        token_id = get_or_create_token_by_token_address(chain, contract)
        if not token_id:
            raise InvalidArgument(message=f'未找到Token, chain: {chain}, contract: {contract}')
        token = get_token(token_id)

        hot_config: OnchainTokenHotConfig = OnchainTokenHotConfig.get_or_create(chain=chain, contract=contract)
        if not hot_config.id:
            max_count = config['ONCHAIN_CONFIGS']['hot_token_count']
            if OnchainTokenHotConfig.query.filter(
                    OnchainTokenHotConfig.chain == chain,
                    or_(
                        OnchainTokenHotConfig.expired_at >= now(),
                        OnchainTokenHotConfig.expired_at.is_(None),
                    ),
            ).count() >= max_count:
                raise InvalidArgument(message=f'设置热榜Token数量超过{max_count}')
        old_data = hot_config.to_dict(enum_to_name=True) if hot_config.id else None
        hot_config.token_id = token_id
        hot_config.symbol = token.symbol
        hot_config.name = token.name
        hot_config.is_top = is_top
        hot_config.expired_at = expired_at
        hot_config.admin_user_id = g.user.id
        db.session_add_and_commit(hot_config)

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.OnchainTokenHotConfig,
            old_data=old_data,
            new_data=hot_config.to_dict(enum_to_name=True),
            special_data=dict(chain=chain.name, contract=contract, symbol=hot_config.symbol, name=hot_config.name),
        )
        return dict()

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """链上交易-热门榜代币配置-删除"""
        _delete_model(OnchainTokenHotConfig, OPNamespaceObjectSpot.OnchainTokenHotConfig, kwargs)
        return dict()
