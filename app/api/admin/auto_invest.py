# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from typing import List, Dict, Optional

from sqlalchemy import or_
from webargs import fields
from marshmallow import Schema, EXCLUDE
from flask import g

from app.api.common.decorators import lock_request
from app.business.clients import ServerClient
from app.business.auto_invest import auto_invest_plan_terminated_task
from app.business.market import MarketOfflineHelper
from app.business.order import Order
from app.common import OrderSideType
from app.models import User, db, Market
from app.models.auto_invest import AutoInvestTask, AutoInvestPlan, AutoInvestPlanLog, AutoInvestSystemOrder, \
    AutoInvestOrderTradeHistory, AutoInvestPlanTimeConfig, AutoInvestPlanStatistic, \
    AutoInvestPlanNoticeConfig, AutoInvestMarket
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSpot
from app.exceptions import InvalidArgument, RecordNotFound
from app.schedules.auto_invest import update_auto_invest_market_cache_schedule, auto_invest_market_terminated_schedule
from app.utils import batch_iter
from app.utils.parser import JsonEncoder
from app.assets import list_all_assets, list_pre_assets
from app.caches import MarketCache
from app.caches.spot import AutoInvestConfigCache, AutoInvestMarketDepthCache
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import LimitField, PageField, EnumField, TimestampField, PositiveDecimalField


ns = Namespace("Auto-Invest-Admin")


@ns.route("/config")
@respond_with_code
class AutoInvestConfigResource(Resource):
    @classmethod
    def get_and_format_configs(cls) -> List[Dict]:
        configs = AutoInvestConfigCache().get_sorted_configs()
        last_min_depth_usd = None
        results = []
        for rank, c in enumerate(configs, 1):
            results.append(
                {
                    "rank": rank,
                    "min_depth_usd": c[0],
                    "max_depth_usd": last_min_depth_usd,
                    "max_usd": c[2],
                    "price_deviation": c[1],
                }
            )
            last_min_depth_usd = c[0]
        return results

    @classmethod
    def get(cls):
        """ 币币-定投-定投档位配置 """
        return cls.get_and_format_configs()

    class ConfigSchema(Schema):
        """ 档位配置 """

        rank = fields.Int(required=False)  # for update
        min_depth_usd = PositiveDecimalField(allow_zero=False, required=True)  # 最小深度市值USD
        max_usd = PositiveDecimalField(allow_zero=False, required=True)  # 最大定投USD
        price_deviation = PositiveDecimalField(allow_zero=False, required=True)  # 目标价格幅度，存小数：5% -> 0.05

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    @ns.use_kwargs(
        dict(
            configs=fields.Nested(ConfigSchema, many=True, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 币币-定投-定投档位配置-修改 """

        cls.update_configs(kwargs["configs"])
        update_auto_invest_market_cache_schedule.delay()  # 修改配置后，更新下市场档位

    @classmethod
    def update_configs(cls, new_configs: List[Dict]):
        # 档位深度不能重复
        min_depth_usds = set()
        data_list = []
        old_configs = AutoInvestConfigCache().get_sorted_configs()
        for c in new_configs:
            if c["min_depth_usd"] in min_depth_usds:
                raise InvalidArgument("min_depth_usd", f"重复的最小档位市值: {c['min_depth_usd']}")
            min_depth_usds.add(c["min_depth_usd"])

            data_list.append(
                {
                    "min_depth_usd": c["min_depth_usd"],
                    "max_usd": c["max_usd"],
                    "price_deviation": c["price_deviation"],
                }
            )
        data_list.sort(key=lambda x: x["min_depth_usd"], reverse=True)
        AutoInvestConfigCache().set(json.dumps(data_list, cls=JsonEncoder))

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AutoInvestConfig,
            old_data=dict(configs=old_configs),
            new_data=dict(configs=data_list),
        )


@ns.route("/market-depths")
@respond_with_code
class AutoInvestMarketsResource(Resource):
    @classmethod
    def find_min_depth_usd(cls, depths: List[Decimal], depth_usd: Decimal) -> Optional[Decimal]:
        # 获得对应 深度档位 的最小深度
        depths = sorted(depths, reverse=True)
        for _depth_usd in depths:
            if depth_usd >= _depth_usd:
                return _depth_usd

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String,
            rank=fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """ 币币-定投-市场定投档位 """
        format_configs = AutoInvestConfigResource.get_and_format_configs()
        min_depth_usd_config_map = {c["min_depth_usd"]: c for c in format_configs}
        min_depth_usds = list(min_depth_usd_config_map.keys())
        market_query = AutoInvestMarket.query.filter(AutoInvestMarket.status != AutoInvestMarket.Status.CLOSE).all()
        result = []
        market_depth_usd_map = {m: Decimal(usd) for m, usd in AutoInvestMarketDepthCache().read().items()}
        market_list = [i.name for i in Market.query.all()]
        for row in market_query:
            market = row.market
            depth_usd = market_depth_usd_map.get(market, 0)
            min_depth_usd = cls.find_min_depth_usd(min_depth_usds, depth_usd)
            format_config = min_depth_usd_config_map.get(min_depth_usd) or {}
            item = {
                "market": market,
                "status": row.status.value,
                "depth_usd": depth_usd,
                "min_depth_usd": min_depth_usd,
                "max_depth_usd": format_config.get("max_depth_usd"),
                "max_usd": format_config.get("max_usd"),
                "price_deviation": format_config.get("price_deviation"),
                "rank": format_config.get("rank", -1),
                "available": bool(format_config),
            }
            if kwargs.get('market') and kwargs.get('market') != item['market']:
                continue
            if kwargs.get('rank') and kwargs.get('rank') != item['rank']:
                continue
            result.append(item)
        result.sort(key=lambda x: x["depth_usd"], reverse=True)

        rank_map = {r["rank"]: r["rank"] for r in format_configs}
        rank_map[-1] = "无档位"

        return {
            "markets": list(market_depth_usd_map.keys()),
            'market_list': sorted(market_list),
            "rank_map": rank_map,
            "items": result,
        }

    @classmethod
    @lock_request(key='auto_invest_market_edit')
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 币币-定投-添加市场定投档位 """
        market = kwargs['market']
        asset = MarketCache(market).dict['base_asset']
        if MarketCache(market).dict['quote_asset'] != 'USDT':
            # 临时添加限制
            raise InvalidArgument(message=f'交易币种非USDT不可配置')
        if asset in list_pre_assets():
            raise InvalidArgument(message=f'币种{asset}为pre token，不可配置')
        market_row: AutoInvestMarket = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == market
        ).first()
        if (not market_row) or market_row.status == AutoInvestMarket.Status.CLOSE:
            MarketOfflineHelper.check_permission(market, MarketOfflineHelper.BusinessType.AUTO_INVEST)
        if market_row and market_row.status == AutoInvestMarket.Status.OPEN:
            raise InvalidArgument(message="该市场已经是定投市场")
        row = AutoInvestMarket.get_or_create(market=market)
        row.status = AutoInvestMarket.Status.OPEN
        db.session_add_and_commit(row)
        update_auto_invest_market_cache_schedule.delay()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AutoInvestMarket,
            detail=row.to_dict(enum_to_name=True),
        )


@ns.route("/market-depths/<string:market>")
@respond_with_code
class AutoInvestMarketDepthDetailResource(Resource):
    @classmethod
    @lock_request(key='auto_invest_market_edit')
    def delete(cls, market):
        """ 币币-定投-删除市场定投档位 """
        market_row: AutoInvestMarket = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == market
        ).first()
        if not market_row:
            raise RecordNotFound
        if market_row.status == AutoInvestMarket.Status.CLOSE:
            raise InvalidArgument(message="该市场已经不是定投市场")
        pending_plans_count = AutoInvestPlan.query.filter(
            AutoInvestPlan.market == market,
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).count()
        if pending_plans_count:
            raise InvalidArgument(message=f"该定投市场还存在{pending_plans_count}个未撤销的策略，无法关闭市场")
        market_row.status = AutoInvestMarket.Status.CLOSE
        db.session.commit()
        update_auto_invest_market_cache_schedule.delay()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AutoInvestMarket,
            detail=dict(market=market),
        )


@ns.route("/orders")
@respond_with_code
class AutoInvestOrderListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start=TimestampField,
            end=TimestampField,
            user_id=fields.Integer,
            plan_id=fields.Integer,
            target_asset=fields.String,
            status=EnumField(AutoInvestTask.Status),
            result=EnumField(AutoInvestTask.Result),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-定投-定投订单列表 """
        model = AutoInvestTask
        query = model.query.filter().order_by(model.id.desc())
        if start_time := kwargs.get("start"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            query = query.filter(model.created_at <= end_time)
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if plan_id := kwargs.get("plan_id"):
            query = query.filter(model.plan_id == plan_id)
        if target_asset := kwargs.get("target_asset"):
            query = query.filter(model.target_asset == target_asset)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)
        if result := kwargs.get("result"):
            query = query.filter(model.result == result)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            d = row.to_dict(enum_to_name=True)
            d["email"] = user_email_map.get(row.user_id, "")
            d["processing_time"] = int(row.updated_at.timestamp() - row.created_at.timestamp())
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in AutoInvestTask.Status},
                result_dict={i.name: i.value for i in AutoInvestTask.Result},
                op_tyoe_dict={i.name: i.value for i in AutoInvestTask.OpType},
                assets=list_all_assets(),
            ),
        )


@ns.route("/plans")
@respond_with_code
class AutoInvestPlanListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start=TimestampField,
            end=TimestampField,
            user_id=fields.Integer,
            plan_id=fields.Integer,
            source_asset=fields.String,
            target_asset=fields.String,
            status=EnumField(AutoInvestPlan.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """币币-定投-定投策略列表"""
        model = AutoInvestPlan
        query = model.query.filter().order_by(model.id.desc())
        if start_time := kwargs.get("start"):
            query = query.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            query = query.filter(model.created_at <= end_time)
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if plan_id := kwargs.get("plan_id"):
            query = query.filter(model.id == plan_id)
        if source_asset := kwargs.get("source_asset"):
            query = query.filter(model.source_asset == source_asset)
        if target_asset := kwargs.get("target_asset"):
            query = query.filter(model.target_asset == target_asset)
        if status := kwargs.get("status"):
            query = query.filter(model.status == status )

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        items = []
        tickers = ServerClient().get_all_market_tickers()
        plan_ids = [i.id for i in rows]
        time_config_query = AutoInvestPlanTimeConfig.query.filter(AutoInvestPlanTimeConfig.plan_id.in_(plan_ids)).all()
        statistic_query = AutoInvestPlanStatistic.query.filter(AutoInvestPlanStatistic.plan_id.in_(plan_ids)).all()
        notice_config_query = AutoInvestPlanNoticeConfig.query.filter(AutoInvestPlanNoticeConfig.plan_id.in_(plan_ids)).all()
        time_config_map = {time_config.plan_id: time_config for time_config in time_config_query}
        statistic_map = {statistic.plan_id: statistic for statistic in statistic_query}
        notice_config_map = {notice_config.plan_id: notice_config for notice_config in notice_config_query}
        for row in rows:
            plan_id = row.id
            time_config = time_config_map[row.id]
            statistic = statistic_map[row.id]
            notice_config = notice_config_map[row.id]
            d = row.to_dict(enum_to_name=True)
            d.update(time_config.to_dict(enum_to_name=True))
            d.update(statistic.to_dict(enum_to_name=True))
            d.update(notice_config.to_dict(enum_to_name=True))
            market = d['market']
            d['id'] = plan_id
            ticker = tickers.get(market)
            last_price = Decimal(ticker['last']) if ticker else 0
            d['end_price'] = d['target_end_price'] if d['target_end_price'] else last_price
            d['last_price'] = last_price
            d['profit_amount'] = d['total_target_amount'] * d['end_price'] - d['total_source_amount']
            d['profit_rate'] = d['profit_amount'] / d['total_source_amount'] if d['total_source_amount'] else 0
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in AutoInvestPlan.Status},
                assets=list_all_assets(),
            ),
        )


@ns.route("/plans/<int:id_>/stop")
@respond_with_code
class AutoInvestPlanStopResource(Resource):
    @classmethod
    def put(cls, id_):
        """币币-定投-终止策略"""
        plan = AutoInvestPlan.query.get(id_)
        if plan.status == AutoInvestPlan.Status.TERMINATED:
            raise InvalidArgument(message='策略已终止，请勿重复操作')
        if plan.is_order_processing:
            raise InvalidArgument(message='该策略存在执行中的订单未完成，请稍后重试')
        auto_invest_plan_terminated_task(id_)

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AutoInvestPlan,
            detail=dict(id=id_, market=plan.market),
            target_user_id=plan.user_id,
        )


@ns.route("/plans/batch-stop")
@respond_with_code
class AutoInvestPlanBatchStopResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String(required=True),
        )
    )
    def put(cls, **kwargs):
        """币币-定投-批量终止策略"""
        market = kwargs['market']
        market_data = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == market
        ).first()
        if not market_data:
            raise RecordNotFound
        if market_data.status == AutoInvestMarket.Status.CLOSING:
            raise InvalidArgument(message='市场正在撤销策略中，请勿重复操作')
        market_data.status = AutoInvestMarket.Status.CLOSING
        db.session.commit()
        plan_list = AutoInvestPlan.query.filter(
            AutoInvestPlan.market == market,
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).all()
        auto_invest_market_terminated_schedule.delay()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.AutoInvestPlan,
            detail=dict(market=market, count=len(plan_list)),
        )
        return dict(
            count=len(plan_list)
        )


@ns.route("/plans/<int:id_>/logs")
@respond_with_code
class AutoInvestPlanLogResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, id_, **kwargs):
        """币币-定投-策略修改记录列表"""
        limit, page = kwargs['limit'], kwargs['page']
        pagination = AutoInvestPlanLog.query.filter(
            AutoInvestPlanLog.plan_id == id_
        ).order_by(
            AutoInvestPlanLog.id.desc()
        ).paginate(page, limit, error_out=False)

        return dict(
            items=pagination.items,
            total=pagination.total,
        )


@ns.route("/sys-orders")
@respond_with_code
class SysAutoInvestOrderListResource(Resource):
    @classmethod
    def get_row(cls, row_id: int) -> AutoInvestTask:
        row = AutoInvestTask.query.filter(AutoInvestTask.id == row_id).first()
        if not row:
            raise InvalidArgument(message=f"定投订单{row_id}不存在")
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            order_id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 币币-定投-系统定投订单列表 """
        order_id = kwargs["order_id"]
        sys_orders = AutoInvestSystemOrder.query.filter(
            AutoInvestSystemOrder.order_id == order_id,
        ).order_by(AutoInvestSystemOrder.id.asc()).all()
        items = []
        for i in sys_orders:
            item = i.to_dict(enum_to_name=True)
            market_info = MarketCache(i.market).dict
            if i.target_asset == market_info["quote_asset"]:
                side = OrderSideType.SELL.value
            else:
                side = OrderSideType.BUY.value
            item["side"] = side
            item["processing_time"] = int(i.updated_at.timestamp() - i.created_at.timestamp())
            items.append(item)

        return dict(
            items=items,
            total=len(items),
            extra=dict(
                result_dict={i.name: i.value for i in AutoInvestSystemOrder.Result},
                order_type_dict={Order.NormalOrderType.LIMIT: "限价", Order.NormalOrderType.MARKET: "市价"},
                order_side_dict={Order.OrderSideType.SELL: "卖出", Order.OrderSideType.BUY: "买入"},
            ),
        )


@ns.route("/sys-trade-orders")
@respond_with_code
class SysAssetAutoInvestTradeOrderListResource(Resource):
    @classmethod
    def get_sys_row(cls, row_id: int) -> AutoInvestSystemOrder:
        row = AutoInvestSystemOrder.query.filter(AutoInvestSystemOrder.id == row_id).first()
        if not row:
            raise InvalidArgument(message=f"定投订单{row_id}不存在")
        return row

    @classmethod
    @ns.use_kwargs(
        dict(
            sys_order_id=fields.Integer(required=True),
            status=EnumField(AutoInvestOrderTradeHistory.Status),
            has_deal=fields.Integer,
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 币币-定投-系统定投订单-挂单历史 """
        sys_order_id = kwargs["sys_order_id"]
        sys_order = cls.get_sys_row(sys_order_id)
        plan_order = SysAutoInvestOrderListResource.get_row(sys_order.order_id)
        query = AutoInvestOrderTradeHistory.query.filter(
            AutoInvestOrderTradeHistory.sys_order_id == sys_order_id,
        ).order_by(AutoInvestOrderTradeHistory.id.asc())
        if status := kwargs.get("status"):
            query = query.filter(AutoInvestOrderTradeHistory.status == status)
        if start_time := kwargs.get("start"):
            query = query.filter(AutoInvestOrderTradeHistory.created_at >= start_time)
        if end_time := kwargs.get("end"):
            query = query.filter(AutoInvestOrderTradeHistory.created_at <= end_time)
        if (has_deal := kwargs.get("has_deal")) is not None:
            # 是否成交
            if has_deal:
                query = query.filter(
                    or_(
                        AutoInvestOrderTradeHistory.base_deal_amount > 0,
                        AutoInvestOrderTradeHistory.quote_deal_amount > 0,
                    )
                )
            else:
                query = query.filter(
                    AutoInvestOrderTradeHistory.base_deal_amount == 0,
                    AutoInvestOrderTradeHistory.quote_deal_amount == 0,
                )
        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        items = []
        for r in rows:
            i = r.to_dict(enum_to_name=True)
            i["processing_time"] = int(r.updated_at.timestamp() - r.created_at.timestamp())
            i["sys_user_id"] = plan_order.sys_user_id
            items.append(i)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.name for i in AutoInvestOrderTradeHistory.Status},
            ),
        )
