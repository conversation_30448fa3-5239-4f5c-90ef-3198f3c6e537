# -*- coding: utf-8 -*-
from flask import g
from webargs import fields

from app import config
from ...caches.admin import RealTimeAssetAdequacyExternalCache
from ..common import Namespace, Resource, respond_with_code, require_api_auth
from ..common.fields import Asset<PERSON><PERSON>
from ...exceptions import OperationDenied, InvalidArgument
from ...models import User, SubAccount, db
from ...business import cached
from ...business.market_maker import MarketMakerHelper
from ...business.external_dbs import PerpetualSummaryDB, PerpetualLogDB
from ...business.clients import WalletClient
from ...utils import amount_to_str

ns = Namespace('Quantification')


@ns.route('/asset-balance')
@respond_with_code
class AssetBalanceResource(Resource):

    @classmethod
    @require_api_auth
    @ns.use_kwargs(dict(
        data=fields.Dict(required=True)
    ))
    def post(cls, **kwargs):
        user: User = g.user
        if user.user_type is not User.UserType.INTERNAL_MAKER:
            raise OperationDenied
        asset_balance = kwargs['data']
        sub_cache = RealTimeAssetAdequacyExternalCache()
        sub_cache.hmset(asset_balance)
        return {}


@ns.route('/hot-wallet-balance')
@respond_with_code
class WalletBalanceResource(Resource):

    @classmethod
    @require_api_auth
    @ns.use_kwargs(dict(
        asset=AssetField
    ))
    def get(cls, **kwargs):
        user: User = g.user
        if user.user_type is not User.UserType.INTERNAL_MAKER:
            raise OperationDenied
        db.session.close()
        asset = kwargs.get('asset')
        rs = WalletClient().get_account_balance()
        result = []
        for x in rs:
            if not asset or asset == x['asset']:
                result.append({
                    'asset': x['asset'],
                    'chain': x['chain'],
                    'balance': x['balances']['HOT']['total']
                })
        return result


@ns.route('/makers')
@respond_with_code
class MakersResource(Resource):

    @classmethod
    @require_api_auth
    @cached(3600)
    def get(cls):
        if g.user.user_type is not User.UserType.INTERNAL_MAKER:
            raise OperationDenied
        users = set()
        for k, v in config.items():
            if k.endswith('USER_ID') and isinstance(v, int) and v > 0:
                users.add(v)
        subs = SubAccount.query.filter(SubAccount.main_user_id.in_(users)).with_entities(SubAccount.user_id).all()
        users.update(x[0] for x in subs)
        users.update(MarketMakerHelper.list_all_maker_ids())
        return users


@ns.route('/perpetual-users')
@respond_with_code
class PerpetualUsersResouce(Resource):

    @classmethod
    @require_api_auth
    @ns.use_kwargs(dict(
        month=fields.Date(required=True)
    ))
    def get(cls, **kwargs):
        if g.user.user_type is not User.UserType.INTERNAL_MAKER:
            raise OperationDenied
        _, month = PerpetualSummaryDB.convert_date(kwargs['month'])
        table = PerpetualSummaryDB.table(f'user_trade_summary_{month}')
        if not table.exists():
            return []
        rows = table.select("distinct user_id")
        return [x for x, in rows]


@ns.route('/perpetual-positions')
@respond_with_code
class PerpetualPositionsResouce(Resource):

    @classmethod
    @require_api_auth
    @ns.use_kwargs(dict(
        market=fields.String,
        cursor=fields.String,
        limit=fields.Integer
    ))
    def get(cls, **kwargs):
        if g.user.user_type is not User.UserType.INTERNAL_MAKER:
            raise OperationDenied
        market = kwargs.get('market')
        if market:
            market = {x.upper() for x in market.split(',')}
        cursor = kwargs.get('cursor')
        limit = kwargs.get('limit', 1000)
        if not 0 < limit <= 10000:
            raise InvalidArgument
        if not cursor:
            ts = PerpetualLogDB.get_slice_history_timestamp(interval=600)
            last = 0
        else:
            try:
                ts, last = cursor.split('-')
                ts = int(ts)
                last = int(last)
            except Exception:
                raise InvalidArgument

        table = PerpetualLogDB.slice_position_table(ts, interval=600)
        fields = ["id", "user_id", "side", "create_time", "update_time", "market", "amount", "open_price"]
        result = []
        inner_limit = 10000
        while True:
            rows = table.select(*fields, where=f"id>{last}", order_by="id", limit=inner_limit)
            end = False
            for row in rows:
                if not market or row[5] in market:
                    result.append(row)
                    if len(result) >= limit:
                        last = row[0]
                        end = True
                        break
            if end:
                break
            if rows:
                last = rows[-1][0]
            if len(rows) < inner_limit:
                break

        return dict(
            cursor=f"{ts}-{last}",
            time=ts,
            data=[dict(
                id=x[0],
                user_id=x[1],
                side=x[2],
                create_time=str(x[3]),
                update_time=str(x[4]),
                market=x[5],
                amount=amount_to_str(x[6]),
                open_price=amount_to_str(x[7])
            ) for x in result]
        )
