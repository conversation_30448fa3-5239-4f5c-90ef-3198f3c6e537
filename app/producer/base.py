import json
from abc import ABC
from enum import Enum
from typing import Dict, List, TypeVar

from flask import current_app
from kafka import KafkaProducer

from app import config
from app.common import ProducerTopics
from app.utils.parser import JsonEncoder

T = TypeVar('T', bound='BaseMessageProducer')


class ProducerMode(Enum):
    AT_MOST_ONCE = 'at_most_once'  # 至多一次
    AT_LEAST_ONCE = 'at_least_once'  # 至少一次
    EXACTLY_ONCE = 'exactly_once'  # 精确一次


MODE_CONFIG_MAPPER = {
    ProducerMode.EXACTLY_ONCE: dict(
        retries=3,
        retry_backoff_ms=1000,
        enable_idempotence=True,
        max_in_flight_requests_per_connection=1,
        acks='all'
    ),
    ProducerMode.AT_MOST_ONCE: dict(
        acks=0,
        retries=0,
        enable_idempotence=False
    ),
    ProducerMode.AT_LEAST_ONCE: dict(
        acks='all',
        retries=3,
        enable_idempotence=False
    )
}


class BaseMessageProducer(ABC):
    """基础消息发送组件"""
    _producers: Dict[ProducerMode, KafkaProducer] = {}

    def __init__(self, mode: ProducerMode = ProducerMode.EXACTLY_ONCE):
        self.mode = mode
        self._producer = None

    def _initialize_producer(self):
        """初始化消息生产者"""
        if self.mode in self._producers and self._producers[self.mode] is not None:
            self._producer = self._producers[self.mode]
            return
        try:
            self._producer = self._create_producer()
            self._producers[self.mode] = self._producer
        except Exception as e:
            self._producers[self.mode] = None
            raise e

    def _create_producer(self) -> KafkaProducer:
        """创建Kafka生产者实例"""
        return KafkaProducer(
            bootstrap_servers=config['PRODUCER_KAFKA_IP'],
            **MODE_CONFIG_MAPPER[self.mode]
        )

    def send_message(self, topic: ProducerTopics, message: dict, is_flush=True):
        """发送单条消息"""
        try:
            if self._producer is None:
                self._initialize_producer()
            self._producer.send(
                topic.value,
                value=json.dumps(message, cls=JsonEncoder).encode('utf-8')
            )
            current_app.logger.warning(f"send topic: {topic.value} msg: {message}")
            if is_flush:
                self.flush(100)
        except Exception as e:
            current_app.logger.warning(f"FAILED send topic: {topic.value} msg: {message} error: {e}")
            self.close(timeout=100)

    def send_multiple_messages(self, topic: ProducerTopics, messages: List[dict]):
        """发送多条消息"""
        if not messages:
            return
        for message in messages:
            self.send_message(topic, message, is_flush=False)
        self.flush(timeout=100)

    def flush(self, timeout=None):
        """刷新消息队列"""
        if self._producer:
            self._producer.flush(timeout)

    def close(self, timeout=None):
        """关闭生产者连接"""
        if self._producer:
            self._producer.close(timeout=timeout)
            self._producers[self.mode] = None
