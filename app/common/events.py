#!/usr/bin/env python3
from enum import IntEnum, unique


@unique
class EventDataType(IntEnum):
    COUNTER = 1  # 次数
    UNIQ_COUNTER = 2  # 去重次数
    GAUGE = 3  # 瞬间值


@unique
class EventMetrics(IntEnum):

    @classmethod
    def event_id(cls):
        return cls.value


class AccountEvent(EventMetrics):
    # account
    REGISTER_COUNT = 1  # 注册次数
    LOGIN_COUNT = 2  # 登录次数
    ONLINE_NUM = 3  # 在线人数


class StopEvent(EventMetrics):
    # STOP
    ORDER_COUNT = 4  # 下单次数
    ORDER_NUM = 5  # 下单人数
    LIMIT_ORDER_COUNT = 6  # 限价下单次数
    MARKET_ORDER_COUNT = 7  # 市价下单次数
    STOP_LIMIT_ORDER_COUNT = 8  # 计划限价下单次数
    STOP_MARKET_ORDER_COUNT = 9  # 计划市价下单次数
    # DEAL_COUNT = 10 #	成交次数


class MarginEvent(EventMetrics):
    # Margin
    ORDER_COUNT = 11  # 下单次数
    ORDER_NUM = 12  # 下单人数
    LIMIT_ORDER_COUNT = 13  # 限价下单次数
    MARKET_ORDER_COUNT = 14  # 市价下单次数
    STOP_LIMIT_ORDER_COUNT = 15  # 计划限价下单次数
    STOP_MARKET_ORDER_COUNT = 16  # 计划市价下单次数
    # DEAL_COUNT = 17 #	成交次数
    LOAN_COUNT = 18  # 借币次数
    LOAN_NUM = 19  # 借币人数
    USER_LOAN_COUNT = 20  # 手动借币次数
    USER_LOAN_NUM = 21  # 手动借币人数
    AUTO_LOAN_COUNT = 22  # 自动借币次数
    AUTO_LOAN_NUM = 23  # 自动借币人数
    FLAT_COUNT = 24  # 还币次数
    FLAT_NUM = 25  # 还币人数
    USER_FLAT_COUNT = 26  # 手动还币次数
    USER_FLAT_NUM = 27  # 手动还币人数
    AUTO_FLAT_COUNT = 28  # 自动还币次数
    AUTO_FLAT_NUM = 29  # 自动还币人数
    LIQUIDATION_COUNT = 30  # 杠杆强平次数


class PerpetualEvent(EventMetrics):
    ORDER_COUNT = 31  # 下单次数
    ORDER_NUM = 32  # 下单人数
    LIMIT_ORDER_COUNT = 33  # 限价下单次数
    MARKET_ORDER_COUNT = 34  # 市价下单次数
    STOP_LIMIT_ORDER_COUNT = 35  # 计划限价下单次数
    STOP_MARKET_ORDER_COUNT = 36  # 计划市价下单次数
    # DEAL_COUNT = 37 #	成交次数
    SET_TAKE_PROFIT = 38  # 止盈设置次数
    # = 39 #	止盈触发次数
    # = 40 #	止盈成功次数
    SET_STOP_LOSS = 41  # 止损设置次数
    # = 42 #	止损触发次数
    # = 43 #	止损成功次数
    CLOSE_ORDER_COUNT = 44  # 平仓次数
    # = 45 #	爆仓次数
    CHANGE_PRICING_BASIS_COUNT = 281    # 计价基准使用次数
    POSITION_NUM = 310  # 持仓人数


class BalanceEvent(EventMetrics):
    DEPOSIT_COUNT = 46  # 充值次数
    DEPOSIT_NUM = 47  # 充值人数
    WITHDRAWAL_COUNT = 48  # 提现次数
    WITHDRAWAL_NUM = 49  # 提现人数
    SPOT_TO_MARGIN = 50  # 现货账户转入杠杆账户次数
    MARGIN_TO_SPOT = 51  # 杠杆账户转入现货账户次数
    SPOT_TO_PERPETUAL = 52  # 现货账户转入合约账户次数
    PERPETUAL_TO_SPOT = 53  # 合约账户转入现货账户次数
    SPOT_TO_INVESTMENT = 54  # 现货账户转入理财账户次数
    INVESTMENT_TO_SPOT = 55  # 理财账户转入现货账户次数
    ADD_LIQUIDITY = 56  # 增加流动性的次数
    REMOVE_LIQUIDITY = 57  # 减少流动性的次数


class IncreaseEvent(EventMetrics):
    """增长类"""
    PUSH_PERMISSION_ENABLE_COUNT = 58  # PUSH权限开关开启的设备数
    PUSH_LOAN_ORDER_EXPIRE = 59  # PUSH发送量-杠杆交易-借币到期提醒
    PUSH_LOAN_LIQUIDATION_WARN = 60  # PUSH发送量-杠杆交易-强平预警
    PUSH_LOAN_RISK_FLAT = 61  # PUSH发送量-杠杆交易-强制还币（触达风险率）
    PUSH_LOAN_EXPIRE_FLAT = 62  # PUSH发送量-杠杆交易-强制还币（订单到期）
    PUSH_LOAN_NOT_ENOUGH_FLAT = 63  # PUSH发送量-杠杆交易-强制还币（借币池余额不足）
    PUSH_LOAN_RENEW_FAIL = 64  # PUSH发送量-杠杆交易-续借失败
    PUSH_PERPETUAL_FLAT_WARN = 65  # PUSH发送量-合约交易-强平预警
    PUSH_PERPETUAL_FLAT_NOTICE = 66  # PUSH发送量-合约交易-强平通知
    PUSH_PERPETUAL_ADL = 67  # PUSH发送量-合约交易-自动减仓
    READ_LOAN_ORDER_EXPIRE = 68  # PUSH打开量-杠杆交易-借币到期提醒
    READ_LOAN_LIQUIDATION_WARN = 69  # PUSH打开量-杠杆交易-强平预警
    READ_LOAN_RISK_FLAT = 70  # PUSH打开量-杠杆交易-强制还币（触达风险率）
    READ_LOAN_EXPIRE_FLAT = 71  # PUSH打开量-杠杆交易-强制还币（订单到期）
    READ_LOAN_NOT_ENOUGH_FLAT = 72  # PUSH打开量-杠杆交易-强制还币（借币池余额不足）
    READ_LOAN_RENEW_FAIL = 73  # PUSH打开量-杠杆交易-续借失败
    READ_PERPETUAL_FLAT_WARN = 74  # PUSH打开量-合约交易-强平预警
    READ_PERPETUAL_FLAT_NOTICE = 75  # PUSH打开量-合约交易-强平通知
    READ_PERPETUAL_ADL = 76  # PUSH打开量-合约交易-自动减仓
    FIRST_SPOT_POP_WIN_ARRIVED_USER_COUNT = 100   # 首次币币成交弹窗触达用户数
    FIRST_PERPETUAL_POP_WIN_ARRIVED_USER_COUNT = 101   # 首次合约完全平仓弹窗触达用户数
    PERPETUAL_PROFIT_RATE_POP_WIN_ARRIVED_COUNT = 102   # 合约收益率突破弹窗触达次数
    PERPETUAL_PROFIT_RATE_POP_WIN_ARRIVED_USER_COUNT = 103   # 合约收益率突破弹窗触达用户数
    FIRST_SPOT_POP_WIN_SHARE_USER_COUNT = 104   # 首次币币成交弹窗分享用户数
    FIRST_PERPETUAL_POP_WIN_SHARE_USER_COUNT = 105   # 首次合约完全平仓弹窗分享用户数
    PERPETUAL_PROFIT_RATE_POP_WIN_SHARE_COUNT = 106   # 合约收益率突破弹窗分享次数
    PERPETUAL_PROFIT_RATE_POP_WIN_SHARE_USER_COUNT = 107   # 合约收益率突破弹窗分享用户数
    FIRST_SPOT_POP_WIN_LANDING_PAGE_CLICK_COUNT = 108   # 首次币币成交弹窗落地页访问次数
    FIRST_PERPETUAL_POP_WIN_LANDING_PAGE_CLICK_COUNT = 109   # 首次合约完全平仓弹窗落地页访问次数
    PERPETUAL_PROFIT_RATE_POP_WIN_LANDING_PAGE_CLICK_COUNT = 110   # 合约收益率突破弹窗落地页访问次数
    PERPETUAL_POSITION_AMOUNT_USD = 146    # 合约-当前持有仓位数量(USD)
    POPUP_WINDOWS_COUNT = 171  # 弹窗次数
    POPUP_WINDOWS_USER_COUNT = 172  # 弹窗新增人数
    POPUP_WINDOWS_FREQUENCY = 173  # 人均弹窗次数
    # app push
    READ_ASSET_NOTICE_RATE_COUNT = 262  # 打开量-行情推送-热门币种大幅涨跌
    READ_ASSET_NOTICE_PRICE_COUNT = 263  # 打开量-行情推送-热门币种价格突破
    READ_PRICE_NOTICE_ALL_COUNT = 266  # 打开量-行情推送-用户自定义价格提醒（总）
    READ_PRICE_NOTICE_SPOT_COUNT = 267  # 打开量-行情推送-用户自定义价格提醒（币币）
    READ_PRICE_NOTICE_PERPETUAL_COUNT = 268  # 打开量-行情推送-用户自定义价格提醒（合约）
    READ_ASSET_NOTICE_CUSTOM_COUNT = 271  # 打开量-行情推送-自选/持仓币种
    SEND_C_BOX_COUNT = 302  # C-Box生成数量
    SEND_EMAIL_C_BOX_COUNT = 303  # 普通C-Box生成数量
    SEND_CODE_C_BOX_COUNT = 304  # 口令C-Box生成次数
    RECEIVE_C_BOX_COUNT = 305  # C-Box领取数量
    SPOT_TO_INVESTMENT_USER_COUNT = 381   # 现货转入理财账户用户数


class ProductEvent(EventMetrics):
    """产品类"""
    PROFIT_LOSS_COUNT = 114  # 盈亏分析开启总人数
    GESTURE_PASSWORD_ENABLE_COUNT = 189  # 手势密码开启设备数
    FINGER_PASSWORD_ENABLE_COUNT = 190  # 指纹密码开启设备数
    PUSH_DEPOSIT_WITHDRAWAL_ENABLE_COUNT = 191  # PUSH开启设备数-充值提现
    PUSH_SMART_TRACK_FAVORITE_ASSET_COUNT = 192  # PUSH开启设备数-智能盯盘-自选币种
    PUSH_SMART_TRACK_HOLD_ASSET_COUNT = 193  # PUSH开启设备数-智能盯盘-持仓币种
    PUSH_SPOT_LIMIT_ORDER_COUNT = 194  # PUSH开启设备数-交易订单-币币交易-限价订单通知
    PUSH_SPOT_STOP_ORDER_COUNT = 195  # PUSH开启设备数-交易订单-币币交易-计划委托通知
    PUSH_PERPETUAL_LIMIT_ORDER_COUNT = 196  # PUSH开启设备数-交易订单-合约交易-限价订单通知
    PUSH_PERPETUAL_STOP_ORDER_COUNT = 197  # PUSH开启设备数-交易订单-合约交易-计划委托通知
    PUSH_PERPETUAL_TAKE_PROFIT_COUNT = 198  # PUSH开启设备数-交易订单-合约交易-止盈止损通知
    PUSH_OPERATION_NEW_ASSET_COUNT = 199  # PUSH开启设备数-运营动态-上新公告
    PUSH_OPERATION_WELLFARE_COUNT = 200  # PUSH开启设备数-运营动态-福利活动
    PUSH_OPERATION_BLOG_COUNT = 201  # PUSH开启设备数-运营动态-博客
    OPEN_HISTORY_ORDER = 243  # K线显示历史委托开启人数
    PERPETUAL_SETTLE_ON_PEOPLE_COUNT = 252  # 合约浮盈结算开启人数
    SIGN_OFF = 244  # 注销人数
    KLINE_BUY_SELL_RECORD_ENABLE_COUNT = 457  # K线图表设置买卖记录点（跟用户账户）

    GESTURE_PASSWORD_ENABLE_USER_COUNT = 407  # 手势密码开启用户数
    GESTURE_PASSWORD_COUNT = 408  # 手势密码使用次数
    GESTURE_PASSWORD_USER_COUNT = 409  # 手势密码使用用户数

    FINGER_PASSWORD_ENABLE_USER_COUNT = 410  # 指纹密码开启用户数
    FINGER_PASSWORD_COUNT = 411  # 指纹密码使用次数
    FINGER_PASSWORD_USER_COUNT = 412  # 指纹密码使用用户数

    FACI_ID_PASSWORD_ENABLE_USER_COUNT = 413  # 面容ID开启用户数
    FACI_ID_PASSWORD_COUNT = 414  # 面容ID使用次数
    FACI_ID_PASSWORD_USER_COUNT = 415  # 面容ID使用用户数

    QRCODE_SIGNIN_COUNT = 466   # 扫码登录使用次数
    QRCODE_SIGNIN_USER_COUNT = 467   # 扫码登录使用用户数

    WEB_AUTHN_ENABLE_COUNT = 468   # 通行密钥开启用户数
    WEB_AUTHN_COUNT = 469   # 通行密钥使用次数
    WEB_AUTHN_USER_COUNT = 470   # 通行密钥使用用户数

    TOTP_ENABLE_COUNT = 471  # TOTP开启用户数
    TOTP_COUNT = 472  # TOTP使用次数
    TOTP_USER_COUNT = 473  # TOTP使用用户数

    MOBILE_ENABLE_COUNT = 474  # 手机验证开启用户数
    MOBILE_COUNT = 475  # 手机验证使用次数
    MOBILE_USER_COUNT = 476  # 手机验证使用用户数

    WITHDRAWAL_PASSWORD_ENABLE_COUNT = 313  # 开启提现密码用户数
    WITHDRAW_PASSWORD_COUNT = 477  # 提现密码使用次数
    WITHDRAW_PASSWORD_USER_COUNT = 478  # 提现密码使用用户数

    PHISHING_CODE_ENABLE_COUNT = 479  # 防钓鱼码开启用户数
    PHISHING_CODE_COUNT = 480  # 防钓鱼码使用次数
    PHISHING_CODE_USER_COUNT = 481  # 防钓鱼码使用用户数

    WITHDRAWAL_APPROVER_ENABLE_COUNT = 482  # 提现多人审核开启用户数
    WITHDRAWAL_APPROVER_COUNT = 483  # 提现多人审核使用次数
    WITHDRAWAL_APPROVER_USER_COUNT = 484  # 提现多人审核使用用户数

    THIRD_PARTY_ACCOUNT_ENABLE_COUNT = 485  # 谷歌账号绑定开启用户数
    THIRD_PARTY_ACCOUNT_COUNT = 486  # 谷歌账号绑定使用次数
    THIRD_PARTY_ACCOUNT_USER_COUNT = 487  # 谷歌账号绑定使用用户数

    FREEZE_ACCOUNT_COUNT = 488  # 冻结账号使用次数
    FREEZE_ACCOUNT_USER_COUNT = 489  # 冻结账号使用用户数

    SIGN_OFF_COUNT = 490  # 注销账号使用次数
    SIGN_OFF_USER_COUNT = 491  # 注销账号使用用户数

    LOGIN_IP_LOCK_ENABLE_COUNT = 492  # 登录IP锁定开启用户数
    SECURITY_LOGIN_ENABLE_COUNT = 493  # 安全登录时长开启用户数

    EMERGENCY_CONTACT_COUNT = 1164    # 紧急联系人使用次数
    EMERGENCY_CONTACT_USER_COUNT = 1165    # 紧急联系人使用用户数



class FifthEvent(EventMetrics):
    """ 5周年相关 """
    IP_COUPON_LIMIT_COUNT = 94  # 五周年-大挑战- IP限制卡券-触发次数
    IP_COUPON_LIMIT_NUM = 95  # 五周年-大挑战- IP限制卡券-触发账户数
    IP_BOX_LIMIT_COUNT = 96  # 五周年-大挑战- IP限制盲盒-触发次数
    IP_BOX_LIMIT_NUM = 97  # 五周年-大挑战- IP限制盲盒-触发账户数
    DEVICE_BOX_LIMIT_COUNT = 98  # 五周年-大挑战- 设备ID盲盒-触发次数
    DEVICE_BOX_LIMIT_NUM = 99  # 五周年-大挑战- 设备ID盲盒-触发账户数


class SixthEvent(EventMetrics):
    """ 6周年相关 """
    USER_GAME_COUNT = 307  # 6周年- 1024小游戏开始次数 触发次数
    USER_COMPLETE_NUM = 308  # 6周年- 周年闯关参与用户数 触发账户数
    USER_RECEIVE_NUM = 309  # 6周年- 周年闯关领奖用户数 触发账户数


class AppPushEvent(EventMetrics):
    """PUSH（APP）"""
    PUSH_PRICE_NOTICE_ALL_COUNT = 259  # 发送量-行情推送-用户自定义价格提醒（总）
    PUSH_PRICE_NOTICE_SPOT_COUNT = 260  # 发送量-行情推送-用户自定义价格提醒（币币）
    PUSH_PRICE_NOTICE_PERPETUAL_COUNT = 261  # 发送量-行情推送-用户自定义价格提醒（合约）
    PUSH_ASSET_NOTICE_CUSTOM_NUM = 269  # 触发次数-行情推送-自选/持仓币种
    PUSH_ASSET_NOTICE_CUSTOM_COUNT = 270  # 发送量-行情推送-自选/持仓币种


class PlatformEventTag(IntEnum):
    API = 1
    WEB = 2
    IOS = 3
    ANDROID = 4
    WEB_COM = 5
    WEB_ZONE = 6


class LanguageEventTag(IntEnum):
    EN_US = 7
    ZH_HANS_CN = 8
    ZH_HANT_HK = 9
    JA_JP = 10
    RU_KZ = 11
    KO_KP = 12
    ID_ID = 13
    ES_ES = 14
    FA_IR = 15
    TR_TR = 16
    VI_VN = 17
    AR_AE = 18
    FR_FR = 19
    PT_PT = 20
    DE_DE = 21
    TH_TH = 22


EventTag = IntEnum('EventTag', list({e.name: e.value for c in [
    PlatformEventTag,
    LanguageEventTag
] for e in c}))
