import json

from decimal import Decimal

from enum import Enum

from app.common.onchain import <PERSON>linePeriod
from app.common.onchain import TokenData
from app.common.onchain import TokenQuote
from app.common.onchain import TokenKline

from app.caches import SetCache
from app.caches import HashCache
from app.caches import StringCache

from app.utils.date_ import current_timestamp
from app.utils.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.utils.amount import amount_to_str, quantize_amount


class OnchainThirdPartyAPICallsCache(HashCache):
    """链上交易第三方数据接口调用次数统计缓存"""

    class ThirdParty(Enum):
        COINGECKO = 'coingecko'
        GO_PLUS = 'go_plus'

    def __init__(self):
        super().__init__(None)

    def add(self, third_party: ThirdParty, path: str):
        self.hincrby(f'{third_party.value}:{path}', 1)

    def get(self) -> dict[str, int]:
        return {k: int(v) for k, v in self.hgetall().items()}

    def clear(self):
        self.delete()


class GoPlusLabsAuthorizationCache(StringCache):
    """GoPlusLabs的Authorization缓存"""

    def __init__(self):
        super().__init__(None)

    def get_authorization(self) -> str | None:
        return self.get()

    def set_authorization(self, authorization: str, ttl: int):
        self.set(authorization, ex=ttl)


class OnchainCoingeckoTokenIDBaseCache(HashCache):
    """Coingecko的TokenID映射缓存基类"""

    def __init__(self):
        super().__init__(None)

    def update(self, data: dict[str, str]):
        self.save(data)

    def get_token_ids(self, token_addresses: list[str]) -> dict[str, str]:
        if len(token_addresses) == 0:
            return {}
        return dict(self.hmget_with_keys([token_address for token_address in token_addresses]))

    def all(self) -> dict[str, str]:
        return self.hgetall()


class OnchainCoingeckoTokenIDSOLCache(OnchainCoingeckoTokenIDBaseCache):
    pass


class OnchainCoingeckoTokenIDETHCache(OnchainCoingeckoTokenIDBaseCache):
    pass


class OnchainCoingeckoTokenIDBSCCache(OnchainCoingeckoTokenIDBaseCache):
    pass


class OnchainCoingeckoLowValueTokenIDCache(HashCache):
    """这里的TokenID是指Coingecko的TokenID, 不是站内的TokenID"""

    TTL = 86400  # 热榜更新一天内不重复获取低价值Token(24H成交额 < 100 USD)

    def __init__(self):
        super().__init__(None)

    def set(self, token_ids: list[str]):
        if not token_ids:
            return
        ts = current_timestamp(to_int=True)
        self.hmset({str(token_id): str(ts) for token_id in token_ids})

    def all(self) -> list[str]:
        curr_ts = current_timestamp(to_int=True)
        expired_ids = []
        all_token_ids = []
        for token_id, v in self.hgetall().items():
            if curr_ts - int(v) > self.TTL:
                expired_ids.append(token_id)
            else:
                all_token_ids.append(token_id)
        if len(expired_ids) > 0:
            self.hdel(*expired_ids)
        return all_token_ids


class OnchainWalletSupportSwapTokenAddressBaseCache(SetCache):
    """钱包支持兑换的Token地址缓存基类"""

    def __init__(self):
        super().__init__(None)

    def update(self, data: set[str]):
        self.save(data)

    def is_support(self, token_address: str) -> bool:
        return self.sismember(token_address)

    def all(self) -> set[str]:
        return self.smembers()


class OnchainWalletSupportSwapTokenAddressSOLCache(OnchainWalletSupportSwapTokenAddressBaseCache):
    pass


class OnchainWalletSupportSwapTokenAddressETHCache(OnchainWalletSupportSwapTokenAddressBaseCache):
    pass


class OnchainWalletSupportSwapTokenAddressBSCCache(OnchainWalletSupportSwapTokenAddressBaseCache):
    pass


class OnchainTokenQuoteCache(HashCache):
    """链上交易Token Quote缓存"""

    def __init__(self):
        super().__init__(None)

    @classmethod
    def _encode_data(cls, data: TokenData) -> str:
        return json.dumps(dict(
            price=data.price,
            change_24h=data.change_24h,
            highest_24h=data.highest_24h,
            lowest_24h=data.lowest_24h,
            amount_24h=data.amount_24h,
        ), cls=JsonEncoder)

    @classmethod
    def _decimal_default(cls, val: str | None) -> Decimal | None:
        if val is None:
            return None
        return Decimal(val)

    @classmethod
    def _decode_data(cls, value: str) -> dict[str, Decimal]:
        raw_data = json.loads(value)
        return dict(
            price=cls._decimal_default(raw_data['price']),
            change_24h=cls._decimal_default(raw_data['change_24h']),
            highest_24h=cls._decimal_default(raw_data['highest_24h']),
            lowest_24h=cls._decimal_default(raw_data['lowest_24h']),
            amount_24h=cls._decimal_default(raw_data['amount_24h']),
        )

    def save_one(
            self, token_id: int, data: TokenData | TokenQuote
    ) -> dict[str, Decimal]:
        val = self._encode_data(data)
        self.hset(str(token_id), val)
        return self._decode_data(val)

    def save_many(self, raw_data: dict[int, TokenData | TokenQuote]):
        if not raw_data:
            return
        self.hmset({
            str(token_id): self._encode_data(item) for token_id, item in raw_data.items()
        })

    def get_one(self, token_id: int) -> dict[str, Decimal]:
        if val := self.hget(str(token_id)):
            return self._decode_data(val)
        return {}

    def get_many(self, token_ids: list[int]) -> dict[int, dict[str, Decimal]]:
        if len(token_ids) == 0:
            return {}
        return {int(k): self._decode_data(v) for k, v in self.hmget_with_keys(list(map(str, token_ids)))}


class OnchainHotTokenCache(SetCache):
    """链上交易热榜Token缓存"""

    def __init__(self):
        super().__init__(None)

    def set(self, token_ids: list[int]):
        self.save(set(map(str, token_ids)))

    def is_hot(self, token_id: int) -> bool:
        return self.sismember(str(token_id))

    def all(self) -> list[int]:
        return list(map(int, self.smembers()))


class OnchainHotTokenUpdatedAtCache(StringCache):
    """链上交易热榜更新时间"""

    def __init__(self):
        super().__init__(None)

    def update(self):
        self.set(str(current_timestamp(to_int=True)))


class OnchainKlineUtils:

    @classmethod
    def encode_data(cls, k: TokenKline) -> str:
        return ','.join([
            str(k.t),
            amount_to_str(k.o),
            amount_to_str(k.h),
            amount_to_str(k.l),
            amount_to_str(k.c),
            amount_to_str(k.v),
        ])

    @classmethod
    def decode_data(cls, value: str) -> TokenKline | None:
        if not value:
            return None
        value_split = value.split(',')
        if len(value_split) != 6:
            return None
        return TokenKline(
            t=int(value_split[0]),
            o=Decimal(value_split[1]),
            h=Decimal(value_split[2]),
            l=Decimal(value_split[3]),
            c=Decimal(value_split[4]),
            v=Decimal(value_split[5]),
        )

    @classmethod
    def get_keys(cls, period: KlinePeriod, start_time: int, end_time: int) -> list[str]:
        period_seconds = period.seconds
        start_time = period.normalise_time(start_time)
        end_time = period.normalise_time(end_time)

        keys = []
        tmp_time = start_time
        while tmp_time <= end_time:
            keys.append(str(tmp_time))
            tmp_time += period_seconds
        return keys


class OnchainKlineCache(HashCache):
    """链上交易的K线缓存"""

    TTL = 86400  # 非热门Token缓存保留1天(更新或查询都可以延长过期时间)

    def __init__(self, token_id: int, period: KlinePeriod):
        self.token_id = token_id
        self.period = period
        super().__init__(f'onchain_kline_{token_id}_{period.value}')

    def _expire(self):
        if not OnchainHotTokenCache().is_hot(self.token_id):
            self.expire(self.TTL)

    def set(self, kline_data: list[TokenKline]):
        if not kline_data:
            return
        self.hmset({
            str(k.t): OnchainKlineUtils.encode_data(k) for k in kline_data
        })
        self._expire()

    def get(self, start_time: int, end_time: int) -> dict[int, TokenKline]:
        keys = OnchainKlineUtils.get_keys(self.period, start_time, end_time)
        if len(keys) == 0:
            return {}

        data = {}
        for item in self.hmget(keys):
            k = OnchainKlineUtils.decode_data(item)
            if k:
                data[k.t] = k

        self._expire()

        return data

    def get_one(self, ts: int) -> TokenKline | None:
        data = self.hget(str(ts))
        if not data:
            return None
        return OnchainKlineUtils.decode_data(data)

    def exist(self) -> bool:
        return self.hlen() > 0

    def all_ts(self) -> list[int]:
        """获取所有时间戳"""
        return list(map(int, self.hkeys()))

    def latest_time(self) -> int | None:
        """获取最新的K线时间"""
        keys = self.all_ts()
        if len(keys) == 0:
            return None
        return max(keys)

    def clear(self):
        """长期不更新时清除所有历史K线数据"""
        self.delete()

    def del_ts(self, ts: list[int]):
        keys = [str(item) for item in ts]
        self.hdel(*keys)


class OnchainCurrencyKlineCache(HashCache):
    """链上交易的定价货币K线缓存"""

    SUPPORT_CURRENCY = {'ETH', 'SOL', 'BNB'}

    def __init__(self, currency: str, period: KlinePeriod):
        if currency not in self.SUPPORT_CURRENCY:
            raise KeyError(f'invalid currency: {currency}')
        self.currency = currency
        self.period = period
        super().__init__(f'onchain_kline_{currency}_{period.value}')

    def set(self, kline_data: list[TokenKline]):
        if not kline_data:
            return
        self.hmset({
            str(k.t): OnchainKlineUtils.encode_data(k) for k in kline_data
        })

    def get(self, start_time: int, end_time: int) -> dict[int, TokenKline]:
        keys = OnchainKlineUtils.get_keys(self.period, start_time, end_time)
        if len(keys) == 0:
            return {}

        data = {}
        for item in self.hmget(keys):
            if k := OnchainKlineUtils.decode_data(item):
                data[k.t] = k

        return data

    def exist(self) -> bool:
        return self.hlen() > 0


class OnchainKlinePenetrationCache(HashCache):
    """K线缓存穿透记录"""

    class Type(Enum):
        HOT = 'hot'
        NOT_HOT = 'not_hot'

    def __init__(self):
        super().__init__(None)

    def add(self, token_id: int):
        t = self.Type.HOT if OnchainHotTokenCache().is_hot(token_id) else self.Type.NOT_HOT
        self.hincrby(t.value, 1)

    def get(self) -> dict[Type, int]:
        return {self.Type(k): int(v) for k, v in self.hgetall().items()}

    def clear(self):
        self.delete()


class OnchainUpdateTokenBaseCache(SetCache):
    """有请求的Token ID记录下来用于定时更新相关Token数据"""

    def __init__(self):
        super().__init__(None)

    def add(self, token_id: int):
        self.sadd(str(token_id))

    def batch_add(self, token_ids: list[int]):
        if not token_ids:
            return
        self.sadd(*[str(i) for i in token_ids])

    def all(self) -> set[int]:
        return {int(val) for val in self.read()}

    def clear(self, token_ids: set[int]):
        if not token_ids:
            return
        self.srem(*map(str, token_ids))

    def has(self, token_id: int) -> bool:
        return self.sismember(str(token_id))


class OnchainUpdateTokenQuoteCache(OnchainUpdateTokenBaseCache):
    """需要更新Quote的Token ID"""
    pass


class OnchainUpdateTokenBaseInfoCache(OnchainUpdateTokenBaseCache):
    """需要更新基础信息的Token ID"""
    pass


class OnchainUpdateTokenInfoCache(OnchainUpdateTokenBaseCache):
    """需要更新Info的Token ID"""
    pass


class OnchainInitKlineCache(OnchainUpdateTokenBaseCache):
    """需要初始化K线的Token ID"""
    pass


class OnchainUpdateKlineMinCache(OnchainUpdateTokenBaseCache):
    """需要更新分钟K线的Token ID"""
    pass


class OnchainUpdateKlineHourCache(OnchainUpdateTokenBaseCache):
    """需要更新小时K线的Token ID"""
    pass


class OnchainUpdateKlineDayCache(OnchainUpdateTokenBaseCache):
    """需要更新天级K线的Token ID"""
    pass


class OnchainTokenUpdatedAtBaseCache(HashCache):
    """记录Token更新时间基类"""

    def __init__(self):
        super().__init__(None)

    @property
    def cycle(self):
        raise NotImplementedError

    def update_one(self, token_id: int):
        self.hset(str(token_id), str(current_timestamp(to_int=True)))

    def update_many(self, token_ids: list[int]):
        if not token_ids:
            return
        val = str(current_timestamp(to_int=True))
        self.hmset({str(token_id): val for token_id in token_ids})

    def get_one(self, token_id: int) -> int:
        if val := self.hget(str(token_id)):
            return int(val)
        return 0

    def get_many(self, token_ids: list[int]) -> dict[int, int]:
        if len(token_ids) == 0:
            return {}
        return {int(k): int(v) for k, v in self.hmget_with_keys(list(map(str, token_ids)))}

    def get_need_update_token_ids(self, token_ids: list[int]) -> list[int]:
        """筛选出到达更新时间需要更新的Token ID列表"""
        update_at_map = self.get_many(token_ids)
        cycle = self.cycle
        need_update_token_ids = []
        cur_time = current_timestamp(to_int=True)
        for token_id in token_ids:
            updated_at = update_at_map.get(token_id, 0)
            if cur_time - updated_at >= cycle:
                need_update_token_ids.append(token_id)
        return need_update_token_ids

    def need_update(self, token_id: int, cycle: int = None) -> bool:
        """判断查询的Token是否需要更新"""
        if not cycle:
            cycle = self.cycle
        updated_at = self.get_one(token_id)
        cur_time = current_timestamp(to_int=True)
        return cur_time - updated_at >= cycle


class OnchainTokenUpdatedAtQuoteCache(OnchainTokenUpdatedAtBaseCache):
    """Quote更新时间"""

    @property
    def cycle(self):
        return 360  # Quote定时任务实时更新, 这里用于接口查询时检查是否需要更新


class OnchainTokenUpdatedAtBaseInfoCache(OnchainTokenUpdatedAtBaseCache):
    """基础信息更新时间"""

    @property
    def cycle(self):
        return 1800  # 半小时更新一次


class OnchainTokenUpdatedAtInfoCache(OnchainTokenUpdatedAtBaseCache):
    """Info信息更新时间"""

    @property
    def cycle(self):
        return 3 * 3600  # 3小时更新一次


class OnchainTokenUpdatedAtKlineMinCache(OnchainTokenUpdatedAtBaseCache):
    """分钟级K线信息更新时间"""

    @property
    def cycle(self):
        return 60  # 1分钟更新一次


class OnchainTokenUpdatedAtKlineHourCache(OnchainTokenUpdatedAtBaseCache):
    """小时级K线信息更新时间"""

    @property
    def cycle(self):
        return 1800  # 30分钟更新一次, 更新时先检查是否已更新最新小时数据


class OnchainTokenUpdatedAtKlineDayCache(OnchainTokenUpdatedAtBaseCache):
    """天级K线信息更新时间"""

    @property
    def cycle(self):
        return 6 * 3600  # 6小时更新一次, 更新时先检查是否已更新最新天数据


class OnchainTokenUpdatedAtAssetLiabilityCache(OnchainTokenUpdatedAtBaseCache):
    """链上交易资产负债预警发送时间"""

    @property
    def cycle(self):
        return 86400  # 1天发送一次


class OnchainTokenVisitAtCache(HashCache):
    """链上交易Token最近访问时间"""

    CYCLE = 86400   # 每次查询其中24H内访问过的Token

    def __init__(self):
        super().__init__(None)

    def update_one(self, token_id: int):
        self.hset(str(token_id), str(current_timestamp(to_int=True)))

    def update_many(self, token_ids: list[int]):
        if not token_ids:
            return
        val = str(current_timestamp(to_int=True))
        self.hmset({str(token_id): val for token_id in token_ids})

    def all(self) -> set[int]:
        curr_ts = current_timestamp(to_int=True)
        data = set()
        delete_keys = []
        for id_str, ts_str in self.hgetall().items():
            if curr_ts - int(ts_str) < self.CYCLE:
                data.add(int(id_str))
            else:
                delete_keys.append(id_str)
        if len(delete_keys) > 0:
            self.hdel(*delete_keys)
        return data


class OnchainGasFeeCache(StringCache):

    def __init__(self):
        super().__init__(None)

    def save_to_cache(self, data: dict):
        self.set(json.dumps({
            'update_time': current_timestamp(to_int=True),
            'data': data,
        }, cls=JsonEncoder))

    def get_chain_gas_fee_mapper(self):
        r = json.loads(self.read())
        if not r:
            return {}
        return r['data']

    def get_update_time(self):
        r = json.loads(self.read())
        if not r:
            return None
        return r['update_time']


class OnchainUserRecent24HBuyCache(StringCache):
    DECIMALS = 8
    ttl = 86400

    def __init__(self, user_id: int):
        key = f'{user_id}'
        super().__init__(key)

    def _get_list(self):
        current_ts = current_timestamp(to_int=True)
        raw = self.read()
        li = json.loads(raw) if raw else []
        return [v for v in li if v['ts'] > current_ts - self.ttl]

    def get_consumed(self) -> Decimal:
        li = self._get_list()
        return sum([Decimal(v['amount']) for v in li]) if li else Decimal()

    def add_amount(self, order_id: int, amount: Decimal):
        amount = quantize_amount(amount, self.DECIMALS)
        li = self._get_list()
        li.append(dict(ts=current_timestamp(to_int=True), order_id=order_id, amount=amount))
        self.set(json.dumps(li, cls=JsonEncoder))

    def cancel_amount(self, order_id: int):
        li = self._get_list()
        li = [v for v in li if v['order_id'] != order_id]
        self.set(json.dumps(li, cls=JsonEncoder))
