# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import date, timedelta, datetime
from decimal import Decimal
from enum import Enum
from itertools import chain
from typing import Mapping, Dict, List, Optional, Tuple

from flask import current_app
from sqlalchemy import func

from app.business.perpetual.position import get_profit_unreal
from . import TimedHash<PERSON>ache, MarginAccountNameCache, PerpetualMarketCache
from .base import <PERSON>h<PERSON><PERSON>, StringCache, BytesCache
from ..business.redshift import PerpetualRedShiftDB
from ..business.utils import query_records_by_time_range
from ..common import PerpetualMarketType, PositionSide, TwoFAType
from ..business import ServerClient, PerpetualServerClient, PriceManager, \
    PerpetualLogDB, mem_cached, PerpetualHistoryDB
from ..models import SubAccount, UserBusinessRecord, MarginLoanOrder
from ..models.exchange import AssetExchangeOrder
from ..utils import (
    current_timestamp, amount_to_str, quantize_amount, today, datetime_to_time, now,
    exhaust, safe_div, timestamp_to_datetime
)
from ..utils.parser import JsonEncoder

MARKET_DATA_UPDATE_TIME = 60 * 10


class AdminUserLoginTokenCache(HashCache):
    DEFAULT_TTL = 86400 * 30
    DEFAULT_RENEW_DURATION = 86400

    def __init__(self, user_id: int):
        self._user_id = user_id
        super().__init__(f'{user_id}')

    @property
    def user_id(self) -> int:
        return self._user_id

    def add_token(self, token: str, ttl: int = DEFAULT_TTL):
        self._clear_outdated()
        self.hset(token, str(current_timestamp() + ttl))
        _AdminLoginTokenOwnerCache(token).set_user(self._user_id, ttl)

    def del_token(self, token: str):
        self.hdel(token)
        _AdminLoginTokenOwnerCache(token).delete()

    def clear_tokens(self, exclude_tokens: List = None) -> List[str]:
        """ exclude_tokens: 用于保留当前登入态的token """
        tokens = list(self.value)
        del_tokens = []
        for token in tokens:
            if exclude_tokens and token in exclude_tokens:
                continue
            self.del_token(token)
            del_tokens.append(token)
        return del_tokens

    @classmethod
    def from_token(cls, token: str) -> Optional['AdminUserLoginTokenCache']:
        user_id = _AdminLoginTokenOwnerCache(token).get_user()
        if user_id is None:
            return None
        cache = cls(user_id)
        expires_at = cache.hget(token)
        if not expires_at or float(expires_at) < current_timestamp():
            return None
        return cache

    def _clear_outdated(self):
        now = current_timestamp()
        exhaust(map(self.hdel,
                    [k for k, v in self.value.items() if float(v) < now]))


class _AdminLoginTokenOwnerCache(StringCache):

    def __init__(self, token: str):
        super().__init__(token)

    def get_user(self) -> Optional[int]:
        value = self.value
        return int(value) if value else None

    def set_user(self, user_id: int, ttl: int):
        self.set(str(user_id), ex=ttl)


class AdminLoginFailureCache(TimedHashCache):

    def __init__(self, account: str, *, ttl: int = None):
        super().__init__(account, interval=ttl)


class AdminWebAuthnOperationType(Enum):
    LOGIN = 'login'
    BINDING_WEBAUTHN = 'binding_webauthn'
    ASSET_UPDATE = 'asset_update'
    CREDIT_USER = 'credit_user'
    CREDIT_FLAT = 'credit_flat'
    CREDIT_RECORD = 'credit_record'
    ASSET_LOCK = 'asset_lock'
    SALARY_PAY = 'salary_pay'
    COMMISSION_PAY = 'commission_pay'
    CHANNEL_ACTIVITY = 'channel_activity'
    CHANNEL_REWARD = 'channel_reward'
    COUPON_APPLY = 'coupon_apply'
    COUPON_DISTRIBUTION = 'coupon_distribution'
    EQUITY_SEND_APPLY = 'equity_send_apply'
    MINING_USERS = 'mining_users'
    STAKING_CONFIG = 'staking_config'
    P2P_MARGIN_AUDIT = 'p2p_margin_audit'
    DEPOSIT_BONUS_AUDIT = 'deposit_bonus_audit'
    AIRDROP_AUDIT = 'airdrop_audit'


class AdminWebAuthnChallengeCache(BytesCache):
    ttl = 300

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def set_data(self, challenge: bytes, host_url: str):
        data = b''.join((len(challenge).to_bytes(1, 'big'), challenge, host_url.encode()))
        self.set(data, ex=self.ttl)

    def get_data(self) -> Tuple[Optional[bytes], Optional[str]]:
        r = self.read()
        if not r:
            return None, None
        i = r[0] + 1
        challenge = r[1:i]
        host_url = r[i:]
        return challenge, host_url.decode()


class AdminUserOperationTokenCache(StringCache):
    ttl = 600

    def __init__(self, token: str,
                 _type: AdminWebAuthnOperationType = AdminWebAuthnOperationType.LOGIN):
        super().__init__(f"{token}:{_type.name}")

    def get_user(self) -> Optional[int]:
        value = self.value
        return int(value) if value else None

    def set_user(self, user_id: int):
        self.set(str(user_id), ex=self.ttl)


class AdminUserWebAuthnOperationTokenCache(StringCache):
    ttl = 300

    def __init__(self,
                 token: str,
                 _type: AdminWebAuthnOperationType = AdminWebAuthnOperationType.LOGIN):
        super().__init__(f"{token}:{_type.name}")

    def get_user(self) -> Optional[int]:
        value = self.value
        if value is None:
            return None
        user_id = value.split(':')[0]
        return int(user_id)

    def get_user_with_2fa_type(self) -> Tuple[Optional[int], Optional[TwoFAType]]:
        value = self.value
        if value is None:
            return None, None
        user_id, tfa_type = value.split(':')
        return int(user_id), TwoFAType(tfa_type)

    def set_user(self, user_id: int, tfa_type: TwoFAType):
        v = f'{user_id}:{tfa_type.value}'
        self.set(v, ex=self.ttl)


class AdminDuplicateRequestCache(StringCache):

    def __init__(self, hash_key: str):
        super().__init__(f"{hash_key}")


class RealTimeAssetAdequacyCache(HashCache):
    """
    实时资金充足率统计
    """

    def __init__(self):
        super().__init__(None)


class RealTimeAssetAdequacyExternalCache(HashCache):
    """
    量化站外资产
    """

    def __init__(self):
        super().__init__(None)


class RealTimeAssetStatisticCache(HashCache):
    """
    实时资产统计
    """

    def __init__(self, account_type: str = "ALL", tag_id: int = None):
        k = None if account_type == "ALL" else account_type
        if tag_id:
            k = f"{k}-{str(tag_id)}"
        super().__init__(k)


class RealtimeUserStatisticCache(StringCache):
    """
    实时用户统计
    """

    def __init__(self):
        super().__init__(None)


class RealtimeDepositWithdrawStatisticCache(StringCache):
    """
    实时充值统计
    """

    def __init__(self, time_type: str):
        super().__init__(time_type)


class RealTimeMarginAssetStatisticCache(HashCache):
    """
    实时杠杆资产-币种纬度
    """

    def __init__(self):
        super().__init__(None)


class RealTimeMarginLiquidationCache(HashCache):
    """
    实时杠杆爆仓
    """

    def __init__(self):
        super().__init__(None)


class RealTimePerpetualLiquidationCache(HashCache):
    """
    实时合约爆仓
    """

    def __init__(self):
        super().__init__(None)


class RealTimeMarginMarketStatisticCache(HashCache):
    """
    实时杠杆资产-市场纬度
    """

    def __init__(self):
        super().__init__(None)


class RealTimeMarginMarketRealLeverageCache(HashCache):
    """
    实时杠杆资产-市场纬度 (每小时的实际杠杆倍数)

    杠杆日报会用到, 存最近1天的数据
    日报-实际杠杆倍数：累加当天每小时的“实际杠杆倍数”快照/当天小时数，单独市场对应单独市场、全站对应全站。
    月报-实际杠杆倍数：累加当月每天的“实际杠杆倍数”/当月天数，单独市场对应单独市场，全站对应全站。
    """

    ttl = 86400 * 3

    def __init__(self, date_: date):
        date_str = date_.strftime("%Y-%m-%d")
        super().__init__(date_str)

    def save_hourly_real_leverages(self, hour_timestamp: int,
                                   market_real_leverage_map: dict) -> None:
        """ 保存每小时的实际杠杆倍数 """
        self.hset(str(hour_timestamp), json.dumps(market_real_leverage_map, cls=JsonEncoder))
        self.expire(self.ttl)

    def get_avg_hourly_real_leverage_map(self) -> dict:
        """ 计算某天平均的实际杠杆倍数：累加某天每小时的“实际杠杆倍数”快照/当天小时数 """
        avg_real_leverage_map = {}
        sum_real_leverage_map = defaultdict(Decimal)
        hourly_market_real_leverage_map = self.value
        if hourly_market_real_leverage_map:
            for hour, _dump_data in hourly_market_real_leverage_map.items():
                market_real_leverage_map = json.loads(_dump_data)
                for market, real_leverage in market_real_leverage_map.items():
                    sum_real_leverage_map[market] += Decimal(real_leverage)
            hour_nums = len(hourly_market_real_leverage_map)
            avg_real_leverage_map = {
                market: quantize_amount(v / hour_nums, 2) for market, v in
                sum_real_leverage_map.items()
            }
        return avg_real_leverage_map


class MarketRealDealContractAmountCache(HashCache):
    """
    合约实时成交统计
    """
    TTL = 3600 * 48
    ALL_AREA = 'ALL'

    def __init__(self):
        super().__init__(None)

    @classmethod
    @mem_cached(300)
    def get_market_list(cls):
        return PerpetualServerClient().market_list()

    def get_trade_areas(self):
        from app.caches import PerpetualMarketCache
        market_list = PerpetualMarketCache().get_market_list()
        ts = current_timestamp(to_int=True)
        end_time = ts
        start_time = ts - 86400
        client = PerpetualServerClient()
        trade_areas = {}
        for market in market_list:
            result = client.query_amount_rank([market], start_time, end_time)
            trade_areas[market] = result['total_users']
        return trade_areas

    @staticmethod
    def get_market_trade_area_dic():
        from app.caches import PerpetualMarketCache
        market_infos = PerpetualMarketCache().read_aside()
        market_area_dic, area_market_lis_dic = dict(), defaultdict(list)
        for market, info_dic in market_infos.items():
            trading_area = info_dic['money']
            market_area_dic[market] = trading_area
            area_market_lis_dic[trading_area].append(market)
        return market_area_dic, area_market_lis_dic

    @classmethod
    def get_market_data(cls, market):
        from app.caches import PerpetualMarketCache

        client = PerpetualServerClient()
        real_market_param = '{market}_REAL'.format(market=market)
        real_result = client.get_market_status(market=real_market_param,
                                               period=86400)
        real_deal = real_result['volume']
        real_amount = real_result['deal']
        total_market_param = '{market}'.format(market=market)
        total_result = client.get_market_status(total_market_param, 86400)
        total_deal = total_result['volume']
        total_amount = total_result['deal']
        # market.status返回的持仓量是刷量后的，改从market.list获取
        mlist = cls.get_market_list()
        minfo = [x for x in mlist if x['name'] == market][0]
        position_amount = minfo['open_interest_volume']
        prices = PriceManager.assets_to_usd()
        prices["USD"] = Decimal("1")

        amount_asset = PerpetualMarketCache().get_amount_asset(market)

        real_leverage_data, position_side_dic, position_user_dic = PerpetualHoursRealLeverageCache. \
            get_market_real_leverage_data(market, prices)

        real_leverage_numerator = real_leverage_data['position_usd']
        real_leverage_denominator = real_leverage_data['position_margin_usd']
        short_profit = real_leverage_data['short_profit']
        long_profit = real_leverage_data['long_profit']
        short_position_count = real_leverage_data['short_position_count']
        long_position_count = real_leverage_data['long_position_count']

        info = PerpetualMarketCache().get_market_info(market)
        asset_rate = prices.get(info['money'], Decimal())
        if info['type'] == PerpetualMarketType.INVERSE:
            real_usd = Decimal(real_deal) * asset_rate
            total_usd = Decimal(total_deal) * asset_rate
        else:
            real_usd = Decimal(real_amount) * asset_rate
            total_usd = Decimal(total_amount) * asset_rate
        data = dict(
            real_usd=real_usd,
            total_usd=total_usd,
            position_amount=Decimal(position_amount) * prices.get(amount_asset, Decimal()),
            real_leverage_numerator=real_leverage_numerator,
            real_leverage_denominator=real_leverage_denominator,
            short_profit=short_profit,
            long_profit=long_profit,
            short_position_count=short_position_count,
            long_position_count=long_position_count,
        )
        return data, position_side_dic, position_user_dic

    def get_trade_data(self, area_market_lis_dic):
        res = defaultdict(int)
        ts = current_timestamp(to_int=True)
        end_time = ts
        start_time = ts - 86400
        client = PerpetualServerClient()
        all_markets = []
        for area, market_lis in area_market_lis_dic.items():
            result = client.query_amount_rank(market_lis, start_time, end_time)
            res[area] = result['total_users']
            all_markets.extend(market_lis)
        all_result = client.query_amount_rank(all_markets, start_time, end_time)
        res[self.ALL_AREA] = all_result['total_users']
        return res

    def get_trade_area_position_users(self, market_area_dic):
        tmp = defaultdict(set)
        all_users = set()
        records = PerpetualLogDB.get_positions('user_id', 'market')
        for user_id, market in records:
            area = market_area_dic.get(market)
            if not area:
                continue
            tmp[area].add(user_id)
            all_users.add(user_id)
        res = defaultdict(int)
        for area, users in tmp.items():
            res[area] = len(users)
        res[self.ALL_AREA] = len(all_users)
        return res

    def reload(self):
        retry = 10
        res = defaultdict(lambda: dict(
            trade_area='',
            market_count=0,
            real_usd=Decimal(),
            total_usd=Decimal(),
            short_profit=Decimal(),
            long_profit=Decimal(),
            position_amount=Decimal(),
            real_leverage_numerator=Decimal(),
            real_leverage_denominator=Decimal(),
            position_user_count=0,
            trade_user_count=0,
            short_position_count=0,
            long_position_count=0,
            real_leverage=Decimal(),
            real_usd_percent=Decimal(),
            position_amount_percent=Decimal(),
            deal_position_percent=Decimal(),
            update_time=ts,
            long_profit_24h=Decimal(),
            short_profit_24h=Decimal(),
        ))
        while retry > 0:
            # noinspection PyBroadException
            try:
                ts = current_timestamp(to_int=True)
                start = ts - 86400
                market_area_dic, area_market_lis_dic = self.get_market_trade_area_dic()
                market_data_dic = defaultdict(dict)
                current_position_side_dic = dict()
                user_ids = set()
                for market, trade_area in market_area_dic.items():
                    data, position_ret, position_user_dic = self.get_market_data(market)
                    market_data_dic[market] = data
                    current_position_side_dic.update(position_ret)
                    user_ids.update(set(position_user_dic.values()))
                trade_users = self.get_trade_users(start, ts)
                history_position_side_dic = self.get_history_position_dic(start, ts, trade_users)
                position_side_dic = {**history_position_side_dic, **current_position_side_dic}
                long_profit_24h_dic, short_profit_24h_dic = self.get_realized_profit_24h(
                    user_ids, trade_users, position_side_dic, start, ts)

                trade_area_trade_users = self.get_trade_data(area_market_lis_dic)
                trade_area_position_users = self.get_trade_area_position_users(market_area_dic)
                for market, trade_area in market_area_dic.items():
                    market_data = market_data_dic[market]
                    res[trade_area]['real_usd'] += market_data['real_usd']
                    res[self.ALL_AREA]['real_usd'] += market_data['real_usd']
                    res[trade_area]['total_usd'] += market_data['total_usd']
                    res[self.ALL_AREA]['total_usd'] += market_data['total_usd']
                    res[trade_area]['position_amount'] += market_data['position_amount']
                    res[self.ALL_AREA]['position_amount'] += market_data['position_amount']
                    res[trade_area]['real_leverage_numerator'] += market_data[
                        'real_leverage_numerator']
                    res[self.ALL_AREA]['real_leverage_numerator'] += market_data[
                        'real_leverage_numerator']
                    res[trade_area]['real_leverage_denominator'] += market_data[
                        'real_leverage_denominator']
                    res[self.ALL_AREA]['real_leverage_denominator'] += market_data[
                        'real_leverage_denominator']

                    res[trade_area]['short_profit'] += market_data['short_profit']
                    res[self.ALL_AREA]['short_profit'] += market_data['short_profit']
                    res[trade_area]['long_profit'] += market_data['long_profit']
                    res[self.ALL_AREA]['long_profit'] += market_data['long_profit']
                    res[trade_area]['short_position_count'] += market_data['short_position_count']
                    res[self.ALL_AREA]['short_position_count'] += market_data['short_position_count']
                    res[trade_area]['long_position_count'] += market_data['long_position_count']
                    res[self.ALL_AREA]['long_position_count'] += market_data['long_position_count']

                    res[trade_area]['long_profit_24h'] += long_profit_24h_dic[market]
                    res[self.ALL_AREA]['long_profit_24h'] += long_profit_24h_dic[market]
                    res[trade_area]['short_profit_24h'] += short_profit_24h_dic[market]
                    res[self.ALL_AREA]['short_profit_24h'] += short_profit_24h_dic[market]

                for trade_area, area_data in res.items():
                    area_data['trade_area'] = trade_area
                    area_data['position_user_count'] = trade_area_position_users[trade_area]
                    area_data['trade_user_count'] = trade_area_trade_users[trade_area]
                    real_leverage_numerator = area_data['real_leverage_numerator']
                    real_leverage_denominator = area_data['real_leverage_denominator']
                    area_data['real_leverage'] = safe_div(real_leverage_numerator,
                                                          real_leverage_denominator)  # 实际杠杆倍数
                    real_usd = area_data['real_usd']
                    all_usd = res[self.ALL_AREA]['real_usd']
                    area_data['real_usd_percent'] = safe_div(real_usd, all_usd)  # 实际成交占比
                    position_amount = area_data['position_amount']
                    all_position_amount = res[self.ALL_AREA]['position_amount']
                    area_data['position_amount_percent'] = safe_div(position_amount,
                                                                    all_position_amount)  # 持仓市值占比
                    area_data['deal_position_percent'] = safe_div(real_usd,
                                                                  position_amount)  # 成交持仓比
                    if trade_area == self.ALL_AREA:
                        area_data['market_count'] = sum(
                            [len(markets) for markets in area_market_lis_dic.values()])
                    else:
                        area_data['market_count'] = len(area_market_lis_dic[trade_area])
                break
            except Exception:
                retry -= 1
        if retry > 0:
            self.delete()
            for k in res:
                res[k] = json.dumps(res[k], cls=JsonEncoder)
            self.hmset(res)
            self.expire(self.TTL)
            return res
        else:
            error_msg = "{} update perpetual MarketRealDealAmountCache error".format(
                current_timestamp())
            current_app.logger.error(error_msg)
            return {}

    @staticmethod
    def get_trade_users(start, end):
        return PerpetualRedShiftDB.get_trade_users_by_time(start, end)

    @staticmethod
    def get_history_position_dic(start, end, trade_users):
        return PerpetualHistoryDB.get_history_position_side_dic(start, end, trade_users)

    def read_aside(self):
        value = self.read()
        if not value:
            value = self.reload()
        return {key: json.loads(v) for key, v in value.items()}

    def get_total_trade_usd(self) -> Decimal:
        """获取全站最近24小时内的合约成交额（USD）"""
        ret = self.read_aside()
        if not ret:
            return Decimal()
        return Decimal(ret[self.ALL_AREA]['total_usd'])

    def get_total_trade_snapshot(self) -> dict:
        """获取实时全站交易快照"""
        ret = self.read_aside()
        return ret[self.ALL_AREA]

    def get_data_lis(self):
        ret = self.read_aside()
        data_lis = [i for i in ret.values()]
        data_lis.sort(key=lambda item: Decimal(item['real_usd']), reverse=True)
        return data_lis

    def get_realized_profit_24h(self, all_user_ids, trade_users, position_side_dic, start, end):
        """最近24h已实现（所有deal的已实现盈亏+浮盈结算的已实现盈亏）"""
        prices = PriceManager.assets_to_usd()
        long_profit_dic = defaultdict(Decimal)
        short_profit_dic = defaultdict(Decimal)
        long_deal_dic, short_deal_dic = self.get_market_deal_dic(trade_users, position_side_dic, start, end)
        long_settle_dic, short_settle_dic = self.get_market_settle_dic(all_user_ids, start, end, position_side_dic)
        for market, profit in chain(long_deal_dic.items(), long_settle_dic.items()):
            balance_asset = PerpetualMarketCache().get_balance_asset(market)
            balance_rate = prices.get(balance_asset, Decimal())
            long_profit_dic[market] += profit * balance_rate
        for market, profit in chain(short_deal_dic.items(), short_settle_dic.items()):
            balance_asset = PerpetualMarketCache().get_balance_asset(market)
            balance_rate = prices.get(balance_asset, Decimal())
            short_profit_dic[market] += profit * balance_rate
        return long_profit_dic, short_profit_dic

    @staticmethod
    def get_market_deal_dic(user_ids, position_side_dic, start, end) -> Tuple[Dict, Dict]:
        long_deal_dic, short_deal_dic = defaultdict(Decimal), defaultdict(Decimal)
        records = PerpetualHistoryDB.get_user_deals(user_ids, start, end)
        for record_dic in records:
            market = record_dic['market']
            position_id = record_dic['position_id']
            position_side = position_side_dic.get(position_id, 1)
            deal_profit, deal_fee, fee_asset = record_dic['deal_profit'], record_dic['deal_fee'], record_dic['fee_asset']
            if fee_asset:
                profit = deal_profit
            else:
                profit = deal_profit - deal_fee
            if position_side == PositionSide.SHORT.value:
                short_deal_dic[market] += profit
            else:
                long_deal_dic[market] += profit
        return long_deal_dic, short_deal_dic

    @staticmethod
    def get_market_settle_dic(user_ids, start, end, position_side_dic) -> Tuple[Dict, Dict]:
        long_deal_dic, short_deal_dic = defaultdict(Decimal), defaultdict(Decimal)
        records = PerpetualHistoryDB.get_realized_settle_profit(user_ids, start, end)
        for record_dic in records:
            market = record_dic['market']
            position_id = record_dic['position_id']
            position_side = position_side_dic.get(position_id, 1)
            if position_side == PositionSide.SHORT.value:
                short_deal_dic[market] += record_dic['margin_change']
            else:
                long_deal_dic[market] += record_dic['margin_change']
        return long_deal_dic, short_deal_dic

    def reload_data(self):
        value = self.reload()
        return {key: json.loads(v) for key, v in value.items()}


class MarketRealDealSpotAmountCache(HashCache):
    """
    币币实时成交统计
    """
    TTL = 3600 * 48

    def __init__(self):
        super().__init__(None)

    @staticmethod
    def get_trade_areas():
        from app.caches import MarketCache

        ts = current_timestamp(to_int=True)
        end_time = ts
        start_time = ts - 86400
        client = ServerClient()
        market_list = MarketCache.list_online_markets()
        areas = defaultdict(list)
        for name in market_list:
            market = MarketCache(name).dict
            areas[market['quote_asset']].append(name)
        total_user_count_dic = {}
        total_market_count_dic = {}
        for area, markets in areas.items():
            r = client.trade_amount_rank(
                market=markets,
                start_time=start_time,
                end_time=end_time)
            total_user_count_dic[area] = r['total_users']
            total_market_count_dic[area] = len(markets)
        return total_user_count_dic, total_market_count_dic

    def get_trade_area_data(self, trade_area):
        ts = current_timestamp(to_int=True)
        client = ServerClient()
        real_market_param = '{trade_area}_ZONE_REAL'.format(
            trade_area=trade_area)
        real_result = client.market_status(market=real_market_param,
                                           period=86400)
        real_deal = real_result['deal']
        total_market_param = '{trade_area}_ZONE'.format(trade_area=trade_area)
        total_result = client.market_status(market=total_market_param,
                                            period=86400)
        total_deal = total_result['deal']
        # 以前汇率从AssetRateCache(is_load=True).value获取
        trade_area_rate_caches = PriceManager.assets_to_usd()
        rate = trade_area_rate_caches.get(trade_area, Decimal('0'))
        return dict(
            real_deal=amount_to_str(real_deal, 8),
            real_usd=amount_to_str(Decimal(real_deal) * rate, 2),
            total_deal=amount_to_str(total_deal, 8),
            total_usd=amount_to_str(Decimal(total_deal) * rate, 2),
            percent=str(Decimal(real_deal) / Decimal(total_deal)) if
            Decimal(total_deal) > 0 else '0',
            rate=str(rate),
            update_time=ts
        )

    @staticmethod
    def get_exchange_data():
        end_time = now()
        start_time = end_time - timedelta(days=1)
        from_list = []
        to_list = []
        for order in query_records_by_time_range(table=AssetExchangeOrder,
                                                       start_time=start_time,
                                                       end_time=end_time,
                                                       limit=5000):
            if order.status != AssetExchangeOrder.Status.FINISHED:
                continue
            from_list.append(dict(
                user_id=order.user_id,
                created_at=order.created_at,
                updated_at=order.updated_at,
                asset=order.source_asset,
                amount=order.source_asset_exchanged_amount,
            ))
            to_list.append(dict(
                user_id=order.user_id,
                created_at=order.created_at,
                updated_at=order.updated_at,
                asset=order.target_asset,
                amount=order.target_asset_exchanged_amount,
            ))
        price_map = PriceManager.assets_to_usd()
        total_data = dict(
            order_count=len(from_list),
            user_count=len({i['user_id'] for i in from_list}),
            avg_time=quantize_amount(
                sum([(i['updated_at'].timestamp() - i['created_at'].timestamp()
                      ) for i in from_list]) / len(from_list) if from_list else 0,
                5
            ),
            amount=quantize_amount(
                sum([(i['amount'] * price_map.get(i['asset'], 0)) for i in from_list]),
                2
            ),
            source_asset_count=len({i['asset'] for i in from_list}),
            target_asset_count=len({i['asset'] for i in to_list}),
        )

        def _get_rank_list(data_list):
            _asset_map = defaultdict(lambda: {
                'user_ids': set(),
                'asset': '',
                'order_count': int(),
                'user_count': int(),
                'time': int(),
                'avg_time': int(),
                'amount_usd': Decimal(),
                'amount': Decimal(),
            })
            for row in data_list:
                _asset_map[row['asset']]['user_ids'].add(row['user_id'])
                _asset_map[row['asset']]['order_count'] += 1
                _asset_map[row['asset']]['amount'] += quantize_amount(row['amount'], 2)
                _asset_map[row['asset']]['amount_usd'] += quantize_amount(
                    row['amount'] * price_map.get(row['asset'], 0),
                    2
                )
                _asset_map[row['asset']]['time'] += (
                            row['updated_at'].timestamp() - row['created_at'].timestamp())
            for k, v in _asset_map.items():
                v['asset'] = k
                v['avg_time'] = quantize_amount(
                    v['time'] / v['order_count'] if v['order_count'] else 0,
                    5
                )
                _user_ids = v.pop('user_ids')
                v['user_count'] = len(_user_ids)
            return sorted(_asset_map.values(), key=lambda x: x["amount_usd"],
                                 reverse=True)[:10]

        return dict(total=[total_data],
                    source_rank=_get_rank_list(from_list),
                    target_rank=_get_rank_list(to_list))

    def reload(self):
        retry = 10
        result = {}
        while retry > 0:
            # noinspection PyBroadException
            try:
                total_user_count_dic, total_market_count_dic = self.get_trade_areas()
                for trade_area, total_user in total_user_count_dic.items():
                    trade_area_data = self.get_trade_area_data(trade_area)
                    trade_area_data['user_count'] = total_user
                    trade_area_data['market_count'] = total_market_count_dic[trade_area]
                    result[trade_area] = json.dumps(trade_area_data)
                break
            except Exception:
                retry -= 1
        if retry > 0:
            result['exchange'] = json.dumps(self.get_exchange_data(), cls=JsonEncoder)
            self.delete()
            self.hmset(result)
            self.expire(self.TTL)
            return result
        else:
            error_msg = "{} update spot MarketRealDealAmountCache error".format(
                current_timestamp())
            current_app.logger.error(error_msg)
            return {}

    def read_aside(self):
        value = self.read()
        if not value:
            value = self.reload()
        spot_data = {key: json.loads(v) for key, v in value.items()}
        exchange_data = spot_data.pop('exchange', {})

        return spot_data, exchange_data

    def reload_data(self):
        value = self.reload()
        return {key: json.loads(v) for key, v in value.items()}


class MarketRealDealSummaryCache(HashCache):
    """
    实时成交-summary统计信息
    """
    TTL = 3600 * 48

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        end_time = current_timestamp(to_int=True)
        start_time = end_time - 86400
        spot_summary = ServerClient().query_deal_summary(start_time, end_time)
        per_summary = PerpetualServerClient().query_deal_summary(start_time, end_time)
        data_map = {
            "spot": json.dumps(spot_summary),
            "perpetual": json.dumps(per_summary),
            "start_time": start_time,
            "end_time": end_time,
        }
        self = cls()
        self.hmset(data_map)
        self.expire(self.TTL)

    def read_aside(self) -> tuple[dict, dict, int, int]:
        value = self.read()
        if not value:
            return {}, {}, 0, 0
        spot_summary = json.loads(value["spot"])
        per_summary = json.loads(value["perpetual"])
        return spot_summary, per_summary, int(value["start_time"]), int(value["end_time"])


class MarketRealDealSpotAmountHistoryCache(HashCache):
    """
    币币实时成交统计
    """
    ttl = 86400 * 2
    retry = 10

    def __init__(self, timestamp):
        super().__init__(timestamp)
        self.timestamp = timestamp

    def reload(self):
        data = MarketRealDealSpotAmountCache().read()
        if not data:
            return
        self.save(data)
        self.expire(self.ttl)

    def read_aside(self):
        value = self.read()
        if value:
            return self.load(value)
        else:
            cur = self.timestamp - MARKET_DATA_UPDATE_TIME
            while cur > self.timestamp - self.retry * MARKET_DATA_UPDATE_TIME:
                last_val = MarketRealDealSpotAmountHistoryCache(cur).read()
                if last_val:
                    return self.load(last_val)
                cur -= MARKET_DATA_UPDATE_TIME
            return {}

    @staticmethod
    def load(value):
        res = {key: json.loads(v) for key, v in value.items()}
        res.pop('exchange', {})
        return res


class AssetSummaryStatisticCache(HashCache):
    """
    资产统计
    """

    def __init__(self):
        super().__init__(None)


class ActiveUserStatisticCache(HashCache):
    """
    近30天活跃用户统计
    """

    def __init__(self):
        super().__init__(None)


class NoActiveUserStatisticCache(HashCache):
    """
    不活跃用户统计
    """

    col_map = {
        "active": "最近一次活跃时间",
        "trade": "交易",
        "spot": "币币",
        "exchange": "兑换",
        "perpetual": "合约",
    }

    interval_list = [7, 30, 60, 90, 180, 365, 730]

    def __init__(self):
        super().__init__(None)

    def read(self) -> Dict[str, str]:
        cache_data = super().read()
        if not cache_data:
            return {}
        cache_data["items"] = json.loads(cache_data["items"])
        return cache_data

    def reload(self):
        """
        区间例子：
            x <= 7,
            7 < x <= 30,
            ...
            350 < x <=730
            x > 730
            总计
        下面几个统计函数中，-1 表示 x > 730, -2 表示全部。
        user_id 需要去重，只能存在一个区间
        """
        from ..business.statistic import get_active_count_map_by_interval, \
            get_business_bitmap_map_by_interval, \
            get_exchange_bitmap_map_by_interval, get_trade_bitmap_map

        interval_list = self.interval_list
        end = today() - timedelta(days=1)
        # end = today()
        sub_rows = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id)
        sub_map = {i.user_id: i.main_user_id for i in sub_rows}
        active_count_map = get_active_count_map_by_interval(interval_list, end, sub_map)
        spot_bitmap_map = get_business_bitmap_map_by_interval(
            interval_list, end, business=UserBusinessRecord.Business.SPOT_TRADE
        )
        perpetual_bitmap_map = get_business_bitmap_map_by_interval(
            interval_list, end, business=UserBusinessRecord.Business.PERPETUAL_TRADE
        )
        exchange_bitmap_map = get_exchange_bitmap_map_by_interval(
            interval_list, end, sub_map
        )
        trade_bitmap_map = get_trade_bitmap_map(interval_list, spot_bitmap_map,
                                                perpetual_bitmap_map,
                                                exchange_bitmap_map)
        cache_data = []
        for interval in chain(interval_list, [-1, -2]):
            item = {
                "interval": interval,
                "active": active_count_map[interval],
                "spot": len(spot_bitmap_map[interval]),
                "perpetual": len(perpetual_bitmap_map[interval]),
                "exchange": len(exchange_bitmap_map[interval]),
                "trade": len(trade_bitmap_map[interval])
            }
            cache_data.append(item)
        data = {
            'timestamp': datetime_to_time(now()),
            'items': json.dumps(cache_data)
        }
        self.save(data)


class RealTimeActiveAssetCache(HashCache):
    """
    实时活跃资产统计
    """

    def __init__(self):
        super().__init__(None)


class ZendeskUserCache(HashCache):
    """
    zendesk用户更新时间
    """

    def __init__(self):
        super().__init__(None)

class ZendeskUserTagRetryCache(StringCache):
    """
    zendesk用户标签更新失败重试缓存
    """
    ttl = 86400 * 3

    def __init__(self, ts: int):
        super().__init__(str(ts))


class ZendeskArticleCache(HashCache):
    """
    zendesk文章更新时间
    """

    def __init__(self):
        super().__init__(None)


class SpotStageDepthCache(HashCache):
    """
    实时现货深度统计
    """

    def __init__(self):
        super().__init__(None)


class PerpetualStageDepthCache(HashCache):
    """
    实时合约深度统计
    """

    def __init__(self):
        super().__init__(None)


class BinancePerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class OKXPerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class HuobiPerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class BybitPerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class KucoinPerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class GatePerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class BitgetPerpetualStageDepthCache(HashCache):

    def __init__(self):
        super().__init__(None)


class MarginHoursUnFlatCache(HashCache):
    """
    杠杆市场小时借币市值
    """

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        _now = current_timestamp(to_int=True)
        end_ts = _now - (_now % 3600)
        end_time = timestamp_to_datetime(end_ts)
        markets = list(MarginAccountNameCache.list_all_markets().values())
        cache = MarginHoursUnFlatCache()
        cache_data = cache.read()
        if cache_data:
            cache_data = {k: json.loads(v) for k, v in cache_data.items()}

        margin_unflat_dict = cls.get_margin_loans(end_time)
        ret = {}
        for market in markets:
            data = cache_data.get(market, [])
            data.append([end_time, amount_to_str(margin_unflat_dict[market])])
            if len(data) > 24 * 90:
                data = data[-24 * 90:]
            ret[market] = json.dumps(data, cls=JsonEncoder)

        old_keys = cache.hkeys()
        cache.hmset(ret)
        del_keys = {x for x in old_keys if x not in ret}
        if del_keys:
            cache.hdel(*del_keys)

    @classmethod
    def get_margin_loans(cls, end_time: datetime) -> Dict[str, Decimal]:
        margin_unflats = MarginLoanOrder.query.filter(
            MarginLoanOrder.status.in_([
                MarginLoanOrder.StatusType.PASS,
                MarginLoanOrder.StatusType.ARREARS,
                MarginLoanOrder.StatusType.BURST
            ]),
            MarginLoanOrder.created_at < end_time
        ).with_entities(
            MarginLoanOrder.market_name,
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount).label(
                'total_unflat_amount')
        ).group_by(
            MarginLoanOrder.market_name,
            MarginLoanOrder.asset
        ).all()
        margin_unflat_dict = defaultdict(Decimal)
        for _v in margin_unflats:
            price = PriceManager.asset_to_usd(_v.asset)
            margin_unflat_dict[_v.market_name] += price * _v.total_unflat_amount

        return margin_unflat_dict


class PerpetualHoursRealLeverageCache(HashCache):
    """
    合约市场小时实时杠杆倍数
    """
    ttl = 86400 * 3

    def __init__(self, date_: date):
        date_str = date_.strftime("%Y-%m-%d")
        super().__init__(date_str)

    def get_avg_hourly_real_leverage_map(self) -> dict:
        """ 计算某天平均的实际杠杆倍数：累加某天每小时的“实际杠杆倍数”快照/当天小时数 """
        avg_real_leverage_map = {}
        sum_real_leverage_map = defaultdict(Decimal)
        hourly_market_real_leverage_map = self.value
        if hourly_market_real_leverage_map:
            for _, _dump_data in hourly_market_real_leverage_map.items():
                market_real_leverage_map = json.loads(_dump_data)
                for market, real_leverage in market_real_leverage_map.items():
                    sum_real_leverage_map[market] += Decimal(real_leverage)
            hour_nums = len(hourly_market_real_leverage_map)
            avg_real_leverage_map = {
                market: quantize_amount(v / hour_nums, 2) for market, v in
                sum_real_leverage_map.items()
            }
        return avg_real_leverage_map

    @classmethod
    def get_market_real_leverage_data(cls, market: str, prices: dict):
        """
        累加【该市场所有仓位的仓位价值】/｛累加【该市场所有逐仓仓位的仓位保证金】+累加【该市场所有全仓仓位的仓位保证金+该市场所有全仓仓位的账户余额（可用+冻结）】｝
        """
        from app.caches import PerpetualMarketCache
        limit = 100
        position_side_dic = dict()
        position_user_dic = dict()
        client = PerpetualServerClient()
        market_status = client.get_market_status(market, 86400)
        market_info = PerpetualMarketCache().get_market_info(market)
        sign_price = Decimal(market_status['sign_price'])
        balance_asset = PerpetualMarketCache().get_balance_asset(market)
        balance_rate = prices.get(balance_asset, Decimal())
        # 逐仓持仓保证金 / 全仓仓位保证金
        position_margin_usd = Decimal()
        # 仓位价值
        position_usd = Decimal()
        # 总盈亏
        short_profit = Decimal()
        long_profit = Decimal()
        short_position_count = int()
        long_position_count = int()
        for side in PositionSide:
            page = 1
            while True:
                result = client.position_list(market, side.value, page, limit)
                positions = result['records']
                for item in positions:
                    profit_unreal = get_profit_unreal(
                        sign_price=sign_price,
                        side=side,
                        settle_price=item['settle_price'],
                        amount=item["amount"],
                        market_type=market_info["type"]
                    )
                    position_side_dic[item['position_id']] = side.value
                    position_user_dic[item['position_id']] = item['user_id']
                    # 正向合约 仓位价值 = 仓位数量 * 标价价格
                    # 反向合约 仓位价值 = 仓位数量 * 合约面值 / 标价价格
                    if market_info["type"] == PerpetualMarketType.INVERSE:
                        position_usd += Decimal(item["amount"]) * Decimal(
                            "1") / sign_price * balance_rate
                    else:
                        position_usd += Decimal(item["amount"]) * sign_price

                    # 仓位保证金 =  起始保障金 + 未实现盈亏
                    position_margin_usd += (Decimal(
                        item['margin_amount']) + profit_unreal) * balance_rate
                    if side.value == PositionSide.SHORT.value:
                        short_profit += (Decimal(item['profit_real'])+profit_unreal) * balance_rate
                        short_position_count += 1
                    else:
                        long_profit += (Decimal(item['profit_real'])+profit_unreal) * balance_rate
                        long_position_count += 1

                if len(positions) < limit:
                    break
                page += 1
        data = dict(
            position_usd=position_usd,
            position_margin_usd=position_margin_usd,
            short_profit=short_profit,
            long_profit=long_profit,
            short_position_count=short_position_count,
            long_position_count=long_position_count,
        )
        return data, position_side_dic, position_user_dic

    def reload(self, hour: int):
        from app.caches import PerpetualMarketCache
        prices = PriceManager.assets_to_usd()
        prices["USD"] = Decimal("1")
        market_list = PerpetualMarketCache().get_market_list()
        all_real_leverage_numerator, all_real_leverage_denominator = Decimal(), Decimal()
        market_real_leverage_mapping = {}
        for market in market_list:
            real_leverage_data, _, _ = self.get_market_real_leverage_data(
                market, prices)
            real_leverage_numerator = real_leverage_data['position_usd']
            real_leverage_denominator = real_leverage_data['position_margin_usd']
            all_real_leverage_numerator += real_leverage_numerator
            all_real_leverage_denominator += real_leverage_denominator
            market_real_leverage_mapping[market] = amount_to_str(
                real_leverage_numerator / real_leverage_denominator if real_leverage_denominator else Decimal(),
                2
            )

        market_real_leverage_mapping["ALL"] = amount_to_str(
            all_real_leverage_numerator / all_real_leverage_denominator
            if all_real_leverage_denominator else Decimal(), 2
        )
        self.hset(str(hour), json.dumps(market_real_leverage_mapping, cls=JsonEncoder))
        self.expire(self.ttl)


class PerpetualHoursTradeCache(HashCache):
    """
    合约24小时交易数据
    """
    ttl = 86400 * 3

    def __init__(self, date_: date):
        date_str = date_.strftime("%Y-%m-%d")
        super().__init__(date_str)

    def reload(self, hour: int):
        p_cache = MarketRealDealContractAmountCache()
        trade_snapshot = p_cache.get_total_trade_snapshot()
        self.hset(str(hour), json.dumps(trade_snapshot, cls=JsonEncoder))
        self.expire(self.ttl)

    def read_by_hour(self, hour: int):
        keys = self.hkeys()
        if not keys:
            return {}
        int_keys = list(map(int, keys))
        closest_hour = min(int_keys, key=lambda x: abs(x - hour))
        return json.loads(self.hget(str(closest_hour)))

    def read_by_hour_contract_usd(self, hour: int) -> Decimal:
        data = self.read_by_hour(hour)
        if not data:
            return Decimal()
        return Decimal(data['total_usd'])


class BlogDateReadCountCache(HashCache):
    """ 博客阅读量分布，按date划分 """

    def __init__(self, date: str):
        super().__init__(date)


class UserFirstNormalReferLastIDCache(StringCache):

    def __init__(self):
        super().__init__(None)


class UserFirstNormalReferTimeCache(HashCache):
    """用户首次普通邀请时间 缓存更新查看 update_user_first_normal_refer_time_cache 函数"""

    def __init__(self):
        super().__init__(None)

    def save(self, mapping: Mapping[str, str]):
        self.hmset(mapping)


class BaseRealTimeMarketMonitorCache(HashCache):
    """市场数据监控缓存"""

    def __init__(self):
        super().__init__(None)

    def read_aside(self):
        dict_obj = {}
        for market, data_str in self.value.items():
            dict_obj[market] = json.loads(data_str)
        return dict_obj


class RealTimePerpetualMonitorCache(BaseRealTimeMarketMonitorCache):
    """合约市场数据监控缓存"""
    pass


class RealTimeMarginMonitorCache(BaseRealTimeMarketMonitorCache):
    """杠杆市场数据监控缓存"""
    pass


class BaseMarketMonitorSpecialFieldCache(HashCache):
    """某些数据需要保留最近8小时的缓存（每分钟一个点）"""
    ttl = 8 * 3600 * 1000  # 毫秒
    field = ''

    def __init__(self):
        super().__init__(None)

    def save_data(self, data: Dict):
        now_ = current_timestamp(to_int=True)
        ts_point = (now_ - now_ % 60) * 1000
        expire_ts = ts_point - self.ttl
        origin_data = self.read_aside()
        res = {}
        for market, val in data.items():
            if market not in origin_data:
                res[market] = json.dumps([(ts_point, val)], cls=JsonEncoder)
            else:
                items = origin_data[market]
                filtered_items = list(filter(lambda x: x[0] >= expire_ts, items))
                filtered_items.append((ts_point, val))
                res[market] = json.dumps(filtered_items, cls=JsonEncoder)
        self.save(res)

    def read_aside(self):
        dict_obj = {}
        for market, data_str in self.value.items():
            dict_obj[market] = json.loads(data_str)
        return dict_obj


class PerpetualMonitorSpecialFieldCache(BaseMarketMonitorSpecialFieldCache):
    pass


class SignPriceBasisRateCache(PerpetualMonitorSpecialFieldCache):
    """标记价格基差率缓存"""
    field = 'sign_price_basis_rate'
    LATEST_DATA_NUM = 5

    def get_latest_markets_data(self, latest_data_num: int = 5):
        records_dic = self.read_aside()
        res = {}
        for market, basis_items in records_dic.items():
            num = min(latest_data_num, len(basis_items))
            if num < self.LATEST_DATA_NUM:
                continue
            res[market] = basis_items[-num:]
        return res


class BidAskDiffRateCache(PerpetualMonitorSpecialFieldCache):
    """盘口价差偏离率缓存"""
    field = 'bid_ask_diff_rate'


class Depth1LongShortRateCache(PerpetualMonitorSpecialFieldCache):
    """0.2%深度多空比"""
    field = 'depth_1_long_short_rate'


class Depth4LongShortRateCache(PerpetualMonitorSpecialFieldCache):
    """2%深度多空比"""
    field = 'depth_4_long_short_rate'


class MarginMonitorSpecialFieldCache(BaseMarketMonitorSpecialFieldCache):
    pass


class MarginIndexPriceBasisRateCache(MarginMonitorSpecialFieldCache):
    """指数价格基差率缓存"""
    field = 'index_price_basis_rate'
    LATEST_DATA_NUM = 5

    def get_latest_markets_data(self, latest_data_num: int = 5):
        records_dic = self.read_aside()
        res = {}
        for market, basis_items in records_dic.items():
            num = min(latest_data_num, len(basis_items))
            if num < self.LATEST_DATA_NUM:
                continue
            res[market] = basis_items[-num:]
        return res


class MarginBidAskDiffRateCache(MarginMonitorSpecialFieldCache):
    """盘口价差偏离率缓存"""
    field = 'bid_ask_diff_rate'


class MarginDepth1LongShortRateCache(MarginMonitorSpecialFieldCache):
    """0.2%深度多空比"""
    field = 'depth_1_long_short_rate'


class MarginDepth4LongShortRateCache(MarginMonitorSpecialFieldCache):
    """2%深度多空比"""
    field = 'depth_4_long_short_rate'


class ExchangesAssetsCache(StringCache):
    def __init__(self, exchange_type_str: str, report_date: date):
        super().__init__(f'{exchange_type_str}:{report_date.strftime("%Y-%m-%d")}')
