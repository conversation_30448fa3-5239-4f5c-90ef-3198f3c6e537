#!/usr/bin/env python3
import json
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app
from sqlalchemy import func

from app.models.custody import CustodySubAccount, CustodyUserDeposit, CustodyUserWithdraw

from ..assets.asset import try_get_asset_chain_config
from ..business import (
    lock_call, LocalTransferReconciliation, TradeLogDB, PerpetualLogDB,
    PriceManager
)
from ..business.asset import AssetAlertHelper
from ..business.monitor import SpotBigBookingOrderMonitor
from ..business.order import PriceVerifySettings
from ..business.risk_control.abnormal_issuance import check_abnormal_issuance
from ..business.risk_control.market import IndexPriceRiskControlHelper
from ..business.risk_control.base import add_risk_event_log, batch_add_risk_user, RiskControlGroupConfig
from ..business.risk_control.pledge import check_pledge_liquidation_task, check_pledge_loan_flat_task
from ..business.risk_control.viabtc_pool import viabtc_risk_control
from ..business.risk_control.bus_online_coin import bus_online_coin_risk_by_market
from ..business.risk_control.wash_sale import check_and_alert_wash_sale
from ..business.risk_control.withdrawal import AccumulatedWithdrawalHelper
from ..business.utils import query_records_by_time_range, yield_query_records_by_time_range
from ..business.voice import send_risk_control_notice, send_accumulated_withdrawal_monitor_call, \
    send_asset_deposit_disabled_notice
from ..caches.kline import AssetQuotesDataCache
from ..caches.order import (
    SpotMarketPriceDeviationCache, PerpetualMarketPriceDeviationCache
)
from ..caches.p2p import P2pAssetConfigCache
from ..caches.risk_control import IndexPriceAlertCache
from ..caches.statistics import AverageAssetDepositStatisticsCache, AverageAssetWithdrawalStatisticsCache
from ..caches.user import CountrySmsRiskCodeCache
from ..config import config
from ..assets import list_all_assets, get_asset_chain_config, asset_to_chains
from ..business.margin.helper import MarginAccountHelper
from ..business.risk_control import (FailOption, check_investment_balance_task,
                                     check_margin_liquidation_task,
                                     check_margin_loan_flat_task,
                                     check_market_volatility_task,
                                     check_perpetual_balance_task,
                                     check_perpetual_position_task,
                                     check_perpetual_liquidation_task,
                                     check_red_packet_remain_amount_task,
                                     check_user_abnormal_profit,
                                     execute_user_check_request_task,
                                     check_p2p_balance_task)
from ..business.risk_control.deposit import AccumulatedDepositHelper, is_temp_asset, get_temp_assets
from ..business.user import filter_active_users

from ..caches import PerpetualCoinTypeCache, PerpetualMarketCache, MarketCache
from ..caches.operation import (
    RiskUserNoticeCache, RiskUserCheckNoticeCache, RiskUserPermissionCheckNoticeCache,
    AccumulatedWithdrawalNoticeCache,
    UserAccumulatedWithdrawalNoticeCache,
)
from ..common import CeleryQueues, TradeBusinessType, PrecisionEnum, OrderSideType, OrderBusinessType
from ..models import (
    AssetInvestmentConfig, InvestmentBalanceCheck, MarginAccount,
    MarginLoanFlatCheck, Market, PerpetualBalanceCheck,
    RedPacketCheck, RiskUser, SubAccount,
    UserCheckRequest, WithdrawalWhitelistUser, db, RiskEventLog, PerpetualMarket, Deposit,
    CoinInformation, AssetAccumulatedDepositConfig, SpecialAssetAccumulatedDepositConfig,
    SpecialAssetAccumulatedWithdrawalConfig, Withdrawal, AssetAccumulatedWithdrawalConfig,
    SpecialAssetAccumulatedDepositConfigProportion, AssetAccumulatedDepositConfigProportion, RiskControlMobileNoticeConfig,
    AssetPrice,
)
from ..models.pledge import LoanAsset
from ..utils import (
    route_module_to_celery_queue, scheduled,
    today, now, celery_task, batch_iter, amount_to_str, quantize_amount, format_percent,
)
from ..business.alert import send_alert_notice
from ..utils.date_ import timestamp_to_datetime, current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.RISK_CONTROL)


@celery_task
@lock_call(with_args=True)
def _do_asset_local_transfer_reconciliation(asset: str):
    LocalTransferReconciliation(asset).run()


@scheduled(crontab(hour='*/2', minute=0))
def do_local_transfer_reconciliation():
    # local transfers include offline assets
    for asset in list_all_assets():
        _do_asset_local_transfer_reconciliation.delay(asset)


@scheduled(120, queue=CeleryQueues.WALLET)
def retry_user_check_request_schedule():
    _now = now() - timedelta(minutes=1)
    records = UserCheckRequest.query.filter(
        UserCheckRequest.status == UserCheckRequest.Status.CREATED,
        UserCheckRequest.created_at < _now
    ).all()
    for record in records:
        execute_user_check_request_task.delay(record.id)


@scheduled(crontab(minute=10, hour=0))
def check_user_abnormal_profit_schedule():
    # 取最近3天的活跃用户
    end = today()
    start = end - timedelta(days=2)
    activeness_users = filter_active_users(start, end)
    # 取主账户id
    sub_ids = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
    sub_ids = dict(sub_ids)
    user_ids = {sub_ids.get(x, x) for x in activeness_users}

    whitelist = WithdrawalWhitelistUser.query.with_entities(WithdrawalWhitelistUser.user_id) \
        .filter(WithdrawalWhitelistUser.status == WithdrawalWhitelistUser.Status.VALID).all()
    whitelist = {x for x, in whitelist}

    user_ids -= whitelist
    for user_id in user_ids:
        try:
            check_user_abnormal_profit(user_id, RiskUser.Reason.PERIOD_ABNORMAL_PROFIT)
        except Exception as e:
            current_app.logger.info('check user %s period abnormal profit error: %s', user_id, e)
        finally:
            db.session.rollback()  # 该任务会长时间执行，结束事务，避免因隔离级别读不到最新的数据。


@scheduled(300)
@lock_call()
def check_market_volatility_schedule():
    batch_limit = 20

    start = now() - timedelta(hours=1)
    s_model = Market
    spot_markets = s_model.query.filter(
        s_model.status == s_model.Status.ONLINE
    ).with_entities(
        s_model.started_at,
        s_model.id
    ).all()
    # 刚开盘市场容易波动过大，不检查
    spot_ids = [i.id for i in spot_markets if not i.started_at or i.started_at <= start]
    for batch_ids in batch_iter(spot_ids, batch_limit):
        check_market_volatility_task.delay(batch_ids, TradeBusinessType.SPOT.name)

    p_model = PerpetualMarket
    perpetual_markets = p_model.query.filter(
        p_model.status == p_model.StatusType.OPEN
    ).all()
    # 刚开盘市场容易波动过大，不检查
    perpetual_ids = [i.id for i in perpetual_markets if not i.created_at or i.created_at <= start]
    for batch_ids in batch_iter(perpetual_ids, batch_limit):
        check_market_volatility_task.delay(batch_ids, TradeBusinessType.PERPETUAL.name)


@scheduled(crontab(hour="*/1", minute="8,38"))
@lock_call()
def check_investment_balance_schedule():
    records = AssetInvestmentConfig.query.filter(AssetInvestmentConfig.status == AssetInvestmentConfig.StatusType.OPEN).all()
    for record in records:
        try:
            check_investment_balance_task(record.asset, FailOption.RETRY)
        except Exception as e:
            current_app.logger.error(f"check investment {record.asset} balance error {e!r}")


@scheduled(600)
@lock_call()
def check_p2p_balance_schedule():
    for asset in P2pAssetConfigCache.get_assets():
        check_p2p_balance_task.delay(asset, FailOption.RETRY)


@scheduled(300)
def check_margin_liquidation_schedule():
    records = MarginAccount.query.filter(MarginAccount.status == MarginAccount.StatusType.OPEN).all()
    for record in records:
        check_margin_liquidation_task.delay(record.id)


@scheduled(600)
def check_margin_loan_flat_schedule():
    records = MarginAccount.query.filter(MarginAccount.status == MarginAccount.StatusType.OPEN).all()
    for record in records:
        info = MarginAccountHelper.get_account_info(record.id)
        check_margin_loan_flat_task.delay(record.id, info['sell_asset_type'], FailOption.RETRY)
        check_margin_loan_flat_task.delay(record.id, info['buy_asset_type'], FailOption.RETRY)


@scheduled(300)
@lock_call()
def check_pledge_liquidation_schedule():
    """质押借贷穿仓风控"""
    check_pledge_liquidation_task()


@scheduled(300)
@lock_call()
def check_pledge_loan_flat_schedule():
    """质押借贷对账不平风控"""
    records = LoanAsset.query.filter(
       LoanAsset.status != LoanAsset.Status.CLOSE
    ).with_entities(
        LoanAsset.asset
    ).all()
    assets = [i.asset for i in records]
    conf_lis = RiskControlGroupConfig().pledge_loan_unflat
    for asset, threshold in conf_lis:
        if asset not in assets:
            continue
        check_pledge_loan_flat_task.delay(asset, threshold, FailOption.RETRY)


@scheduled(600)
def check_perpetual_balance_schedule():
    for asset in PerpetualCoinTypeCache().read_aside():
        check_perpetual_balance_task.delay(asset, FailOption.RETRY)


@scheduled(1800)
def check_perpetual_position_schedule():
    for market in PerpetualMarketCache().get_market_list():
        check_perpetual_position_task.delay(market)


@scheduled(300)
def check_perpetual_liquidation_schedule():
    for market in PerpetualMarketCache().get_market_list():
        check_perpetual_liquidation_task.delay(market)


@scheduled(1200)
def check_red_packet_remain_amount_schedule():
    for asset in list_all_assets():
        check_red_packet_remain_amount_task.delay(asset, FailOption.RETRY)


@scheduled(3600)
def risk_user_audit_notice_schedule():
    if not config.get('MONITOR_ENABLED'):
        return
    count = RiskUser.query.with_entities(func.count('*')).filter(
        RiskUser.status == RiskUser.Status.AUDIT_REQUIRED,
        RiskUser.reason.in_(
            (RiskUser.Reason.ABNORMAL_PROFIT, RiskUser.Reason.WITHDRAWAL_NO_DEPOSIT)
        )).scalar()

    cache = RiskUserNoticeCache()
    cache_val = cache.read()
    old_count: int = int(cache_val) if cache_val else 0

    if count - old_count >= 10:
        send_alert_notice(
            f"当前存在{count}个待审核的风控用户，请尽快审核",
            config["ADMIN_CONTACTS"]["customer_service"],
        )
        cache_count = count
    else:
        cache_count = min(count, old_count)
    cache.set(str(cache_count))


@scheduled(crontab(minute=3, hour=5, day_of_week=1))
def clear_risk_control_record_schedule():
    tables = (RiskUser, RiskEventLog, RedPacketCheck, MarginLoanFlatCheck,
              InvestmentBalanceCheck, PerpetualBalanceCheck, UserCheckRequest)
    for table in tables:
        _clear_table(table)


def _clear_table(table):
    step = 10000
    keep = step * 50
    total = table.query.with_entities(func.count(table.id)).scalar()
    last_id = table.query.with_entities(func.min(table.id)).scalar()
    while total > keep:
        last_id += step
        table.query.filter(table.id < last_id).delete(synchronize_session=False)
        db.session.commit()
        total -= step


@scheduled(crontab(minute='*/5'))
@lock_call()
def viabtc_risk_control_schedule():
    """矿池币种入账风控"""
    viabtc_risk_control()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_risk_event_resume_time_schedule():
    """风控事件记录定时检查更新恢复时间"""
    end_time = now()
    start_time = now() - timedelta(days=10)
    logs = RiskEventLog.query.filter(
        RiskEventLog.resume_time.is_(None),
        RiskEventLog.created_at >= start_time,
        RiskEventLog.created_at <= end_time,
    ).all()

    log_reason_list = [RiskEventLog.Reason.MARKET_VOLATILITY,
                       RiskEventLog.Reason.PERPETUAL_MARKET_VOLATILITY,
                       RiskEventLog.Reason.MARGIN_LIQUIDATION,
                       RiskEventLog.Reason.PERPETUAL_LIQUIDATION,
                       RiskEventLog.Reason.MARGIN_LOAN_FLAT_CHECK,
                       RiskEventLog.Reason.PERPETUAL_BALANCE_CHECK,
                       RiskEventLog.Reason.RED_PACKET_CHECK,
                       RiskEventLog.Reason.INVESTMENT_BALANCE_CHECK,
                       RiskEventLog.Reason.VIABTC_TRANS_BEYOND_THRESHOLD,
                       RiskEventLog.Reason.ABNORMAL_ISSUANCE,
                       RiskEventLog.Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY,
                       RiskEventLog.Reason.BUS_ONLINE_COIN_DEPOSIT_VOLATILITY,
                       RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT,
                       RiskEventLog.Reason.ACCUMULATED_ASSET_WITHDRAWAL,
                       ]
    reverse_map = {v: k for k, v in RiskEventLog.UserToLogReasonMap.items()}
    risk_users = RiskUser.query.filter(
        RiskUser.reason.in_([reverse_map[i] for i in log_reason_list if i in reverse_map]),
        RiskUser.created_at >= start_time,
        RiskUser.created_at <= end_time,
    ).all()

    for row in logs:
        # 这里只处理风控用户的
        if row.reason in log_reason_list and reverse_map.get(row.reason):
            if row.end_time:
                risk_user = [i for i in risk_users if (
                        i.reason == reverse_map[row.reason]
                        and i.source == row.source
                        and row.start_time <= i.created_at <= row.end_time
                )]
            else:
                risk_user = [i for i in risk_users if (
                        i.reason == reverse_map[row.reason]
                        and i.source == row.source
                        and row.start_time <= i.created_at
                )]
            required_list = [i for i in risk_user if i.status == RiskUser.Status.AUDIT_REQUIRED]
            if not required_list:
                row.resume_time = max([i.audited_at for i in risk_user]) if risk_user else now()

        if row.reason == RiskEventLog.Reason.ABNORMAL_ISSUANCE:
            chain = json.loads(row.extra)['chain']
            asset = row.source
            ac_conf = get_asset_chain_config(asset, chain)
            # 目前异常增发无法具体判断恢复时间，只能用RISK_CONTROL大致判断（RISK_CONTROL不一点代表异常增发）
            reason_set = set(ac_conf.withdrawal_suspension_reasons + ac_conf.deposit_suspension_reasons)
            if "RISK_CONTROL" not in reason_set:
                row.resume_time = now()
    db.session.commit()


@scheduled(crontab(hour='*/1', minute="5"))
@lock_call()
def send_sell_order_spot_alert():
    """现货大额挂单监控"""
    if not config.get('MONITOR_ENABLED'):
        return
    SpotBigBookingOrderMonitor.send_all_asset_alert()


@scheduled(crontab(minute='*/5'))
@lock_call()
def check_risk_user_to_alert():
    """风控用户待审核检查通知"""
    if not config.get('MONITOR_ENABLED'):
        return
    mobiles = RiskControlMobileNoticeConfig.get_mobiles(
        RiskControlMobileNoticeConfig.MobileNoticeEventType.RISK_USER_AUDIT_REQUIRED
    )
    if not mobiles:
        return
    model = RiskUser
    curr_cnt = model.query.with_entities(
        func.count('*')
    ).filter(
        model.status == model.Status.AUDIT_REQUIRED,
        model.reason.in_([
            model.Reason.MARKET_VOLATILITY,
            model.Reason.PERPETUAL_MARKET_VOLATILITY,
            model.Reason.MARGIN_LIQUIDATION,
            model.Reason.PERPETUAL_LIQUIDATION,
        ])
    ).scalar() or 0
    cache = RiskUserCheckNoticeCache()
    cache_val = cache.read()
    prev_cnt = int(cache_val) if cache_val else 0
    if curr_cnt - prev_cnt > 100:
        for mobile in mobiles:
            send_risk_control_notice.delay(mobile)
        cache_count = curr_cnt
    else:
        # 可能期间处理过上个检查周期的数据
        cache_count = min(curr_cnt, prev_cnt)
    cache.set(str(cache_count))


@scheduled(crontab(minute='*/10'))
@lock_call()
def check_risk_user_permission_to_alert():
    """风控用户待审核-按权限 检查通知"""
    if not config.get('MONITOR_ENABLED'):
        return
    mobiles = RiskControlMobileNoticeConfig.get_mobiles(
        RiskControlMobileNoticeConfig.MobileNoticeEventType.RISK_USER_AUDIT_REQUIRED
    )
    if not mobiles:
        return

    model = RiskUser
    notice_num_map = {
        "TD_TL": 100,
    }
    check_perms_map = {
        # cache_key: permissions
        "TD_TL": [model.Permission.TRADING_DISABLED, model.Permission.TRADING_LIMITED],
    }
    special_perms_reason_map = {
        # 某些风控原因，在特殊情况下，才会额外禁止某些权限
        model.Permission.TRADING_LIMITED: [model.Reason.ACCUMULATED_ASSET_DEPOSIT],
    }
    filter_reasons = {model.Reason.ACCUMULATED_ASSET_DEPOSIT, }
    for ps in check_perms_map.values():
        for p in ps:
            filter_reasons.update(special_perms_reason_map.get(p, []))
            _reason = model.get_reasons_by_permission(p)
            if _reason:
                filter_reasons.update(_reason)

    rows = model.query.filter(
        model.status == model.Status.AUDIT_REQUIRED,
        model.reason.in_(list(filter_reasons))
    ).with_entities(
        model.id,
        model.user_id,
        model.block_permissions,
    ).all()

    permission_ids_map: dict[str, set] = defaultdict(set)
    for row in rows:
        if not row.block_permissions:
            continue
        bps = json.loads(row.block_permissions)  # list(enum.name)
        for pn in bps:
            permission_ids_map[pn].add(row.id)

    for cache_key, ps in check_perms_map.items():
        pns = [p.name for p in ps]
        _ids = set()
        for pn in pns:
            _ids.update(permission_ids_map[pn])
        cur_count = len(_ids)
        if cur_count < notice_num_map[cache_key]:
            continue
        cache = RiskUserPermissionCheckNoticeCache(cache_key)
        if cache.exists():
            continue
        for mobile in mobiles:
            send_risk_control_notice.delay(mobile)
        cache.gen(cur_count)


@scheduled(crontab(hour="*/1", minute='30'))
@lock_call()
def market_order_price_deviation_schedule():
    slice_time = TradeLogDB.get_slice_history_timestamp()
    perpetual_slice_time = PerpetualLogDB.get_slice_history_timestamp()
    # 总资产
    asset_balance = defaultdict(Decimal)
    assets = list_all_assets()
    prices = PriceManager.assets_to_usd()
    for item in TradeLogDB.get_asset_total_summary(slice_time):
        asset, balance = item
        if asset not in assets:
            continue
        asset_balance[asset] += balance
    for item in PerpetualLogDB.get_asset_total_summary(perpetual_slice_time):
        asset, balance = item
        if asset not in assets:
            continue
        asset_balance[asset] += balance
    usd_asset_balance = [(_asset, _total * prices.get(_asset, Decimal()))
                         for _asset, _total in asset_balance.items()]
    usd_asset_balance.sort(key=lambda x: -x[1])

    rule_list = [
        (
            PriceVerifySettings.market_normal_buy_rate_rule,
            OrderBusinessType.NORMAL_BUSINESS_TYPE,
            OrderSideType.BUY
        ),
        (
            PriceVerifySettings.market_normal_sell_rate_rule,
            OrderBusinessType.NORMAL_BUSINESS_TYPE,
            OrderSideType.SELL
        ),
        (
            PriceVerifySettings.market_margin_buy_rate_rule,
            OrderBusinessType.MARGIN_BUSINESS_TYPE,
            OrderSideType.BUY
        ),
        (
            PriceVerifySettings.market_margin_sell_rate_rule,
            OrderBusinessType.MARGIN_BUSINESS_TYPE,
            OrderSideType.SELL
        )
    ]
    # 现货按照资产市值排名
    # spot_rank_rules = (
    #     # rank, deviation
    #     ((1, 3), Decimal("0.01")),
    #     ((4, 20), Decimal("0.02")),
    #     ((21, 100), Decimal("0.03")),
    #     ((101, 300), Decimal("0.05")),
    #     ((301, 100000), Decimal("0.1")),
    # )
    for rule_detail in rule_list:
        spot_rank_rules, order_business, order_side = rule_detail
        market_asset_mapping = defaultdict(list)
        for _v in Market.query.filter(Market.status == Market.Status.ONLINE).all():
            market_asset_mapping[_v.base_asset].append(_v.name)
        spot_cache = SpotMarketPriceDeviationCache(order_business, order_side)
        all_spot_markets = MarketCache.list_online_markets()
        spot_market_data = {market: amount_to_str(spot_cache.DEFAULT_RATE)
                            for market in all_spot_markets}
        for rule in spot_rank_rules:
            rank_range, _rate = rule
            start, end = rank_range
            for _v in usd_asset_balance[start - 1: end]:
                _asset = _v[0]
                for market in market_asset_mapping[_asset]:
                    spot_market_data[market] = amount_to_str(_rate, PrecisionEnum.RATE_PLACES)
        spot_cache.save(spot_market_data)
        spot_cache.expire(spot_cache.TTL)

    p_rule_list = [
        (
            PriceVerifySettings.market_perpetual_sell_rate_rule,
            OrderSideType.SELL
        ),
        (
            PriceVerifySettings.market_perpetual_buy_rate_rule,
            OrderSideType.BUY
        ),
    ]

    for p_rule_detail in p_rule_list:

        # 合约 按照杠杆倍数
        # perpetual_rules = {100: Decimal('0.01'),
        #                    50: Decimal('0.02'),
        #                    20: Decimal('0.03')}
        perpetual_rule_c, order_side = p_rule_detail
        perpetual_rules = dict(perpetual_rule_c)
        perpetual_default_rate = max(perpetual_rules.values())
        perpetual_market_data = {}

        market_max_leverages = {
            _v.name: max(map(int, _v.leverages.split(',')))
            for _v in PerpetualMarket.query.filter(
                PerpetualMarket.status == PerpetualMarket.StatusType.OPEN
            ).all()}
        for market, max_leverage in market_max_leverages.items():
            _rate = perpetual_rules.get(max_leverage, perpetual_default_rate)
            perpetual_market_data[market] = amount_to_str(_rate, PrecisionEnum.RATE_PLACES)
        p_cache = PerpetualMarketPriceDeviationCache(order_side)
        p_cache.save(perpetual_market_data)
        p_cache.expire(p_cache.TTL)


@scheduled(crontab(minute='10-50/10', hour="1-3"))
@lock_call()
def update_average_asset_deposit_schedule():
    """ 更新币种7日充值均值数据 """
    cache = AverageAssetDepositStatisticsCache()
    last_average_update_ts = int(cache.hget("last_average_update_ts") or 0)
    #  当日更新过之后不再更新
    if (last_average_update_ts > 0 and
            timestamp_to_datetime(last_average_update_ts).date() == today()):
        return

    ret = AccumulatedDepositHelper().get_last_n_day_avg_data()
    if ret:
        dump_data = {asset: json.dumps(avg_data) for asset, avg_data in ret.items()}
        last_average_update_ts = current_timestamp(to_int=True)
        dump_data['last_average_update_ts'] = last_average_update_ts
        cache.save(dump_data)


@scheduled(600)
@lock_call()
def accumulated_deposit_monitor():
    """ 币种累计充值监控 """
    # 用户维度的单笔充值占比风控在充值完成时处理: _user_deposit_by_rc

    accumulated_deposit_monitor_by_cir_range.delay()

    accumulated_deposit_monitor_by_cir_proportion.delay()


@celery_task
@lock_call()
def accumulated_deposit_monitor_by_cir_range():
    """ 币种累计充值监控 - 环比涨幅维度 """
    cache = AverageAssetDepositStatisticsCache()
    cache_data = cache.hgetall()
    avg_data = {}
    for key, value in cache_data.items():
        if key == 'last_average_update_ts':
            continue
        avg_data[key] = json.loads(value)
    if not cache_data:
        return

    def _get_last_24h_deposit():
        prices = AssetPrice.get_yesterday_close_price_map()
        end_time = now()
        start_time = now() - timedelta(days=1)
        _asset_data = defaultdict(lambda: {
            'count': 0,
            'amount': Decimal(),
            'usd': Decimal(),
        })
        _asset_user_data = defaultdict(lambda: defaultdict(Decimal))
        for row in yield_query_records_by_time_range(
                table=Deposit, start_time=start_time, end_time=end_time,
                select_fields=(
                        Deposit.user_id,
                        Deposit.type,
                        Deposit.asset,
                        Deposit.amount,
                        Deposit.status)
        ):
            if row.type == Deposit.Type.LOCAL:
                continue
            if row.status == Deposit.Status.CANCELLED:
                continue
            if row.user_id in whitelist:
                continue
            usd = abs(prices.get(row.asset, 0) * row.amount)
            _asset_user_data[row.asset][row.user_id] += usd
            if row.status in [
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT,
            ]:
                _asset_data[row.asset]['usd'] += usd
                _asset_data[row.asset]['count'] += 1
                _asset_data[row.asset]['amount'] += row.amount
        
        custody_sub_query = CustodySubAccount.query.with_entities(CustodySubAccount.user_id, CustodySubAccount.main_user_id).all()
        sub_map = {sub.user_id: sub.main_user_id for sub in custody_sub_query}

        for row in yield_query_records_by_time_range(
                table=CustodyUserDeposit, start_time=start_time, end_time=end_time,
                select_fields=(
                        CustodyUserDeposit.user_id,
                        CustodyUserDeposit.asset,
                        CustodyUserDeposit.amount,
                        CustodyUserDeposit.status)
        ):
            main_user_id = sub_map.get(row.user_id, row.user_id)
            if row.status == CustodyUserDeposit.Status.FAILED:
                continue
            if main_user_id in whitelist:
                continue
            usd = abs(prices.get(row.asset, 0) * row.amount)
            _asset_user_data[row.asset][main_user_id] += usd
            _asset_data[row.asset]['usd'] += usd
            _asset_data[row.asset]['count'] += 1
            _asset_data[row.asset]['amount'] += row.amount
        return _asset_data, _asset_user_data

    def _get_asset_thresholds():
        model = AssetAccumulatedDepositConfig
        rows = model.query.order_by(model.rank_max.asc()).all()
        ret = []
        for row in rows:
            limit_count_threshold = _get_config_value(
                row.limit_count_threshold,
                row.temp_limit_count_threshold,
                row.temp_limit_expire_at,
            )
            limit_amount_threshold = _get_config_value(
                row.limit_amount_threshold,
                row.temp_limit_amount_threshold,
                row.temp_limit_expire_at,
            )
            if row.deposit_threshold is None:
                continue
            ret.append(
                {
                    'rank_min': row.rank_min * model.UNIT,
                    'rank_max': row.rank_max * model.UNIT,
                    'deposit_threshold': row.deposit_threshold,
                    'user_deposit_threshold': row.user_deposit_threshold,
                    'limit_count_threshold': limit_count_threshold,
                    'limit_amount_threshold': limit_amount_threshold,
                }
            )
        return ret

    def _get_special_asset_thresholds():
        model = SpecialAssetAccumulatedDepositConfig
        rows = model.query.filter(
            model.status == model.Status.OPEN
        ).all()
        ret = {}
        for row in rows:
            limit_count_threshold = _get_config_value(
                row.limit_count_threshold,
                row.temp_limit_count_threshold,
                row.temp_limit_expire_at,
            )
            limit_amount_threshold = _get_config_value(
                row.limit_amount_threshold,
                row.temp_limit_amount_threshold,
                row.temp_limit_expire_at,
            )
            if row.deposit_threshold is None:
                continue
            ret.update({
                row.asset: {
                    'deposit_threshold': row.deposit_threshold,
                    'user_deposit_threshold': row.user_deposit_threshold,
                    'limit_count_threshold': limit_count_threshold,
                    'limit_amount_threshold': limit_amount_threshold,
                }
            })
        return ret

    def _get_config_value(threshold_, temp_threshold, temp_expire_at):
        result = threshold_
        if temp_threshold and temp_expire_at:
            if temp_expire_at > now():
                result = temp_threshold
        return result

    def _calculate_threshold(_asset):
        if _asset in special_thresholds:
            return special_thresholds[_asset], '特殊币种'

        if _asset not in circulations:
            return
        circulation_usd = circulations[_asset] * PriceManager.asset_to_usd(_asset)
        thresholds.sort(key=lambda d: d['rank_max'])
        for idx, _threshold in enumerate(thresholds, start=1):
            if _threshold['rank_min'] <= circulation_usd < _threshold['rank_max']:
                return _threshold, f'{idx}档'
        return

    whitelist = AccumulatedDepositHelper.get_whitelist_users()
    asset_data, asset_user_data = _get_last_24h_deposit()
    circulations = AccumulatedDepositHelper.get_asset_circulations()
    thresholds = _get_asset_thresholds()
    special_thresholds = _get_special_asset_thresholds()
    tmp_exclude_assets = get_temp_assets()
    for asset, value in asset_data.items():
        if is_temp_asset(asset, tmp_exclude_assets):
            continue
        r = _calculate_threshold(asset)
        if not r:
            continue
        threshold, rank_desc = r
        avg = avg_data.get(asset)
        if not avg:
            continue
        avg_count = float(avg['last_7d_avg_count'])
        avg_amount = Decimal(avg['last_7d_avg_amount'])
        if avg_count == 0 or avg_amount == 0:
            continue
        if value['usd'] < threshold['deposit_threshold']:
            continue
        users_data = asset_user_data[asset]
        count_threshold = quantize_amount(Decimal(value['count'] / avg_count), PrecisionEnum.RATE_PLACES)
        amount_threshold = quantize_amount(value['amount'] / avg_amount, PrecisionEnum.RATE_PLACES)
        if count_threshold >= threshold['limit_count_threshold'] or amount_threshold >= threshold[
            'limit_amount_threshold']:
            if RiskEventLog.check_rc_ignore(
                    asset,
                    RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT,
                    ignore_seconds=24 * 60 * 60
            ):
                continue

            count_warning = "已达到阈值" if count_threshold >= threshold['limit_count_threshold'] else "未达到阈值"
            amount_warning = "已达到阈值" if amount_threshold >= threshold['limit_amount_threshold'] else "未达到阈值"
            last_24h_usd = quantize_amount(value['usd'], PrecisionEnum.CASH_PLACES)
            chains = asset_to_chains(asset)
            voice_notice = False

            switch_values = []

            for chain in chains:
                cfg = try_get_asset_chain_config(asset, chain)
                if not cfg:
                    continue
                old_value = cfg.deposits_disabled_by_accumulate_rc_incr
                switch_values.append((chain, asset, old_value))

            if all([t[-1] for t in switch_values]):
                # 所有开关打开，则无需再次进行风控
                continue

            for chain in chains:
                cfg = try_get_asset_chain_config(asset, chain)
                if not cfg:
                    continue
                voice_notice = True
                old_value = cfg.deposits_disabled_by_accumulate_rc_incr
                cfg.deposits_disabled_by_accumulate_rc_incr = True
                AssetAlertHelper.deposit_withdrawal_risk_alert(asset, chain, 'deposits_disabled_by_accumulate_rc_incr',
                                                               old_value, True, '触发币种累计充值风控')
            users_list = []
            display_avg_amount = quantize_amount(avg_amount, 8)
            display_avg_count = quantize_amount(avg_count, 8)
            risk_users = []
            for uid, deposit_usd in users_data.items():
                reason = RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT
                if deposit_usd >= threshold['user_deposit_threshold']:
                    users_list.append((uid, deposit_usd))
                    permissions = RiskUser.PermissionMap[reason]
                    detail = f'''
                                {asset}（最近24H数值/最近7日均值）≥限制阈值，
                                充值数量：{amount_to_str(value['amount'])}/{amount_to_str(display_avg_amount)}≈{amount_to_str(amount_threshold)}，限制阈值为{amount_to_str(threshold['limit_amount_threshold'])}，{amount_warning}；
                                充值笔数：{amount_to_str(value['count'])}/{amount_to_str(display_avg_count)}≈{amount_to_str(count_threshold)}，限制阈值为{amount_to_str(threshold['limit_count_threshold'])}，{count_warning}
                                '''
                    ru = batch_add_risk_user([uid], reason, detail, source=asset, permissions=permissions)
                    risk_users.extend(ru)
            total = len(risk_users)
            msg = f'''
            【币种累计充值告警（环比涨幅）】
            币种：{asset} ({rank_desc})
            告警详情：
            最近24H充值市值（USD）：{last_24h_usd}，允许监控阈值为{amount_to_str(threshold['deposit_threshold'])}
            充值数量：{amount_to_str(value['amount'])}/{amount_to_str(display_avg_amount)}≈{amount_to_str(amount_threshold)}，限制阈值为{amount_to_str(threshold['limit_amount_threshold'])}，{amount_warning}
            充值笔数：{amount_to_str(value['count'])}/{amount_to_str(display_avg_count)}≈{amount_to_str(count_threshold)}，限制阈值为{amount_to_str(threshold['limit_count_threshold'])}，{count_warning}
            风控用户数：{total}
            注：已关闭{asset}的充值，请及时查看处理。
                                    '''
            send_alert_notice(
                msg,
                config["ADMIN_CONTACTS"].get("customer_service"),
                at=config["ADMIN_CONTACTS"]["slack_at"].get("accumulated_deposit_notice")
            )
            if voice_notice:
                mobiles = RiskControlMobileNoticeConfig.get_mobiles(
                    RiskControlMobileNoticeConfig.MobileNoticeEventType.ASSET_ACCUMULATED_DEPOSIT_INCREASE
                )
                for mobile in mobiles:
                    send_asset_deposit_disabled_notice.delay(mobile, asset, total)
            users_list.sort(key=lambda t: t[1], reverse=True)
            users_top10 = users_list[:10]
            extra = dict(
                asset=asset,
                last_24h_usd=value['usd'],
                threshold=threshold['deposit_threshold'],
                count=value['count'],
                avg_count=avg_count,
                count_threshold=count_threshold,
                limit_count_threshold=threshold['limit_count_threshold'],
                amount=amount_to_str(value['amount']),
                avg_amount=amount_to_str(avg_amount),
                amount_threshold=amount_to_str(amount_threshold),
                limit_amount_threshold=threshold['limit_amount_threshold'],
                count_warning=count_warning,
                amount_warning=amount_warning,
                user_count=total,
                limit_plus_cnt=0,
                limit_cnt=total,
                usd_top10={v[0]: amount_to_str(v[1], PrecisionEnum.CASH_PLACES) for v in users_top10},
                rank_desc=rank_desc,
            )

            add_risk_event_log(
                asset,
                RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT,
                current_timestamp(to_int=True),
                0,
                extra,
                0,
                status=RiskEventLog.Status.AUDIT_REQUIRED
            )
        else:
            pass


@celery_task
@lock_call()
def accumulated_deposit_monitor_by_cir_proportion():
    """ 币种累计充值监控 - 流通量占比维度 """

    def _get_asset_thresholds():
        model = AssetAccumulatedDepositConfigProportion
        rows = model.query.order_by(model.rank_max.asc()).all()
        ret = []
        for row in rows:
            if row.user_period is None or row.asset_period is None:
                continue
            ret.append(
                {
                    'rank_min': row.rank_min * model.UNIT,
                    'rank_max': row.rank_max * model.UNIT,
                    'user_period': row.user_period,
                    'user_period_dp': row.user_period_dp,
                    'asset_period': row.asset_period,
                    'asset_period_dp': row.asset_period_dp,
                    'user_deposit_threshold': row.user_deposit_threshold,
                }
            )
        return ret

    def _get_special_asset_thresholds():
        model = SpecialAssetAccumulatedDepositConfigProportion
        rows = model.query.filter(
            model.status == model.Status.OPEN
        ).all()
        ret = {}
        for row in rows:
            ret.update({
                row.asset: {
                    'user_period': row.user_period,
                    'user_period_dp': row.user_period_dp,
                    'asset_period': row.asset_period,
                    'asset_period_dp': row.asset_period_dp,
                    'user_deposit_threshold': row.user_deposit_threshold,
                }
            })
        return ret

    def _calculate_threshold(_asset: str, _prices: dict[str, Decimal]) -> tuple | None:
        if _asset in special_thresholds:
            return special_thresholds[_asset], '特殊币种'

        if _asset not in circulations:
            return None
        circulation_usd = circulations[_asset] * _prices.get(_asset, Decimal())
        thresholds.sort(key=lambda d: d['rank_max'])
        for idx, _threshold in enumerate(thresholds, start=1):
            if _threshold['rank_min'] <= circulation_usd < _threshold['rank_max']:
                return _threshold, f'{idx}档'
        return None

    def _batch_get_period_deposits_by(_asset_configs: list[tuple[str, int, int]]):
        end_time = now()
        _total_max_period = 0
        asset_config_mapping = dict()
        _assets = set()

        for _asset_config in _asset_configs:
            __asset, _user_period, _asset_period = _asset_config
            _assets.add(__asset)
            max_period = max([_user_period, _asset_period])
            _user_start = end_time - timedelta(hours=_user_period)
            _asset_start = end_time - timedelta(hours=_asset_period)
            _total_max_period = max(_total_max_period, max_period)
            asset_config_mapping[__asset] = dict(
                user_start=_user_start,
                asset_start=_asset_start
            )

        total_start_time = end_time - timedelta(hours=_total_max_period)

        __assets_data = defaultdict(lambda: {
            'count': 0,
            'amount': Decimal(),
            'usd': Decimal(),
            'user_data': defaultdict(Decimal),
        })
        __assets_user_data = defaultdict(
            lambda: defaultdict(
                lambda: {
                    'amount': Decimal(),
                    'usd': Decimal(),
                })
        )
        prices = AssetPrice.get_yesterday_close_price_map()

        for row in yield_query_records_by_time_range(
                table=Deposit, start_time=total_start_time, end_time=end_time,
                select_fields=(
                    Deposit.created_at,
                    Deposit.user_id,
                    Deposit.type,
                    Deposit.asset,
                    Deposit.amount,
                    Deposit.status
                )
        ):
            __asset = row.asset
            if row.asset not in _assets:
                continue
            if row.type != Deposit.Type.ON_CHAIN:
                continue

            if row.user_id in whitelist:
                continue
            if __asset not in asset_config_mapping:
                continue
            usd = prices.get(__asset, Decimal()) * row.amount

            user_start = asset_config_mapping[__asset]["user_start"]
            asset_start = asset_config_mapping[__asset]["asset_start"]

            if row.created_at >= user_start:
                __assets_user_data[__asset][row.user_id]['usd'] += usd
                __assets_user_data[__asset][row.user_id]['amount'] += row.amount
            if row.created_at >= asset_start:
                __assets_data[__asset]['user_data'][row.user_id] += usd
                if row.status in [
                    Deposit.Status.FINISHED,
                    Deposit.Status.TO_HOT,
                ]:
                    __assets_data[__asset]['usd'] += usd
                    __assets_data[__asset]['count'] += 1
                    __assets_data[__asset]['amount'] += row.amount

        sub_map = {i.user_id: i.main_user_id for i in CustodySubAccount.query.with_entities(
            CustodySubAccount.user_id, CustodySubAccount.main_user_id).all()}
        for row in yield_query_records_by_time_range(
                table=CustodyUserDeposit, start_time=total_start_time, end_time=end_time,
                select_fields=(
                    CustodyUserDeposit.created_at,
                    CustodyUserDeposit.user_id,
                    CustodyUserDeposit.asset,
                    CustodyUserDeposit.amount,
                    CustodyUserDeposit.status
                )
        ):
            __asset = row.asset
            main_user_id = sub_map.get(row.user_id, row.user_id)
            if row.asset not in _assets:
                continue
            if main_user_id in whitelist:
                continue
            if __asset not in asset_config_mapping:
                continue
            usd = prices.get(__asset, Decimal()) * row.amount

            user_start = asset_config_mapping[__asset]["user_start"]
            asset_start = asset_config_mapping[__asset]["asset_start"]

            if row.created_at >= user_start:
                __assets_user_data[__asset][main_user_id]['usd'] += usd
                __assets_user_data[__asset][main_user_id]['amount'] += row.amount
            if row.created_at >= asset_start:
                __assets_data[__asset]['user_data'][main_user_id] += usd
                if row.status in [
                    CustodyUserDeposit.Status.FINISHED,
                    CustodyUserDeposit.Status.PROCESSING,
                ]:
                    __assets_data[__asset]['usd'] += usd
                    __assets_data[__asset]['count'] += 1
                    __assets_data[__asset]['amount'] += row.amount
        return __assets_data, __assets_user_data

    whitelist = AccumulatedDepositHelper.get_whitelist_users()
    thresholds = _get_asset_thresholds()
    special_thresholds = _get_special_asset_thresholds()
    circulations = AccumulatedDepositHelper.get_asset_circulations()
    tmp_exclude_assets = get_temp_assets()
    _asset_query_configs = []
    assets_prices = AssetPrice.get_yesterday_close_price_map()

    for asset, circulation in circulations.items():
        if is_temp_asset(asset, tmp_exclude_assets):
            continue
        r = _calculate_threshold(asset, assets_prices)
        if not r:
            continue
        if not circulation:
            continue
        threshold, rank_desc = r
        _asset_query_configs.append((asset, threshold['user_period'], threshold['asset_period']))
    _assets_data, _ = _batch_get_period_deposits_by(_asset_query_configs)

    for asset, asset_data in _assets_data.items():
        circulation = circulations[asset]
        r = _calculate_threshold(asset, assets_prices)
        threshold, rank_desc = r
        # 币种维度检查
        if asset_data['amount'] >= circulation * threshold['asset_period_dp']:
            if not RiskEventLog.check_rc_ignore(
                    asset,
                    RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
                    ignore_seconds=threshold['asset_period'] * 60 * 60
            ):
                chains = asset_to_chains(asset)

                switch_values = []
                for chain in chains:
                    cfg = try_get_asset_chain_config(asset, chain)
                    if not cfg:
                        continue
                    old_value = cfg.deposits_disabled_by_accumulate_rc_proportion
                    switch_values.append((chain, asset, old_value))
                if all([t[-1] for t in switch_values]):
                    # 所有开关打开，则无需再次进行风控
                    continue
                for _chain, _asset, _old_value in switch_values:
                    cfg = try_get_asset_chain_config(_asset, _chain)
                    cfg.deposits_disabled_by_accumulate_rc_proportion = True
                    AssetAlertHelper.deposit_withdrawal_risk_alert(
                        _asset,
                        _chain,
                        'deposits_disabled_by_accumulate_rc_proportion',
                        _old_value,
                        True,
                        '触发币种累计充值风控'
                    )
                check_proportion = (asset_data['amount'] / circulation) if circulation else Decimal()
                users_list = []
                value_p = format_percent(check_proportion, 6)
                period_p = format_percent(threshold['asset_period_dp'], 6)
                risk_users = []
                for uid, deposit_usd in asset_data['user_data'].items():
                    if deposit_usd >= threshold['user_deposit_threshold']:
                        users_list.append((uid, deposit_usd))
                        reason = RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION
                        permissions = RiskUser.PermissionMap[reason]
                        detail = f'''
                        该币种最近{threshold['asset_period']}H累计充值数量/币种流通量≈{value_p}（阈值为{period_p}）
                        '''
                        ru = batch_add_risk_user([uid], reason, detail, source=asset, permissions=permissions)
                        risk_users.extend(ru)
                total = len(risk_users)
                msg = f'''
    【币种累计充值监控（流通量占比）】
    币种：{asset} ({rank_desc})
    风控详情：
    1. 该币种最近{threshold['asset_period']}H累计充值数量/币种流通量≈{value_p}（阈值为{period_p}）
    2. 风控用户数：{total}
    3. 注：已限制{asset}充值，需人工处理恢复；
                            '''
                send_alert_notice(
                    msg,
                    config["ADMIN_CONTACTS"].get("customer_service"),
                    at=config["ADMIN_CONTACTS"]["slack_at"].get("accumulated_deposit_notice")
                )
                mobiles = RiskControlMobileNoticeConfig.get_mobiles(
                    RiskControlMobileNoticeConfig.MobileNoticeEventType.ASSET_ACCUMULATED_DEPOSIT_CIRCULATION
                )
                for mobile in mobiles:
                    send_asset_deposit_disabled_notice.delay(mobile, asset, total)
                users_list.sort(key=lambda t: t[1], reverse=True)
                users_top10 = users_list[:10]
                extra = dict(
                    asset=asset,
                    asset_period=threshold['asset_period'],
                    asset_period_dp=threshold['asset_period_dp'],
                    check_proportion=check_proportion,
                    user_count=total,
                    usd_top10={v[0]: amount_to_str(v[1], PrecisionEnum.CASH_PLACES) for v in users_top10},
                    rank_desc=rank_desc,
                )

                add_risk_event_log(
                    asset,
                    RiskEventLog.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
                    current_timestamp(to_int=True),
                    0,
                    extra,
                    0,
                    status=RiskEventLog.Status.AUDIT_REQUIRED
                )


@scheduled(crontab(minute='10-50/10', hour="1-3"))
@lock_call()
def update_average_asset_withdrawal_schedule():
    """ 更新币种7日提现均值数据 """
    cache = AverageAssetWithdrawalStatisticsCache()
    last_average_update_ts = int(cache.hget("last_average_update_ts") or 0)
    #  当日更新过之后不再更新
    if (last_average_update_ts > 0 and
            timestamp_to_datetime(last_average_update_ts).date() == today()):
        return

    ret = AccumulatedWithdrawalHelper().get_last_n_day_avg_data()
    if ret:
        dump_data = {asset: json.dumps(avg_data) for asset, avg_data in ret.items()}
        last_average_update_ts = current_timestamp(to_int=True)
        dump_data['last_average_update_ts'] = str(last_average_update_ts)
        cache.save(dump_data)


@scheduled(600)
@lock_call()
def accumulated_withdrawal_monitor():
    """ 币种累计提现监控 """
    cache = AverageAssetWithdrawalStatisticsCache()
    cache_data = cache.hgetall()
    avg_data = {}
    for key, value in cache_data.items():
        if key == 'last_average_update_ts':
            continue
        avg_data[key] = json.loads(value)
    if not cache_data:
        return

    def _get_asset_circulations():
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
            model.circulation,
            model.status
        ).all()
        offline_assets = [row.code for row in rows if row.status == model.Status.DELETED]
        result = {}
        _data = AssetQuotesDataCache().get_all_data()
        _unit = Decimal('1_000_000')
        _prices = AssetPrice.get_yesterday_close_price_map()
        for _asset_data in _data:
            _asset = _asset_data['asset']
            if _asset in offline_assets:
                continue
            circulation = Decimal(_asset_data['circulation'])
            usd = _prices.get(_asset, Decimal()) * circulation
            result[_asset] = usd / _unit
        return result

    def _get_last_24h_withdrawals():
        _prices = AssetPrice.get_yesterday_close_price_map()
        end_time = now()
        start_time = now() - timedelta(days=1)
        _asset_data = defaultdict(lambda: {
            'count': 0,
            'amount': Decimal(),
            'usd': Decimal(),
        })
        _asset_user_data = defaultdict(lambda: defaultdict(Decimal))
        for row in yield_query_records_by_time_range(
                table=Withdrawal, start_time=start_time, end_time=end_time,
                select_fields=(
                        Withdrawal.user_id,
                        Withdrawal.type,
                        Withdrawal.asset,
                        Withdrawal.amount,
                        Withdrawal.status)
        ):
            if row.type == Withdrawal.Type.LOCAL:
                continue
            if row.status not in (
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FINISHED,
            ):
                continue
            if row.user_id in whitelist:
                continue
            usd = abs(_prices.get(row.asset, 0) * row.amount)
            _asset_user_data[row.asset][row.user_id] += usd
            _asset_data[row.asset]['usd'] += usd
            _asset_data[row.asset]['count'] += 1
            _asset_data[row.asset]['amount'] += row.amount
        
        custody_sub_query = CustodySubAccount.query.with_entities(CustodySubAccount.user_id, CustodySubAccount.main_user_id).all()
        sub_map = {sub.user_id: sub.main_user_id for sub in custody_sub_query}
        for row in yield_query_records_by_time_range(
                table=CustodyUserWithdraw, start_time=start_time, end_time=end_time,
                select_fields=(
                        CustodyUserWithdraw.user_id,
                        CustodyUserWithdraw.asset,
                        CustodyUserWithdraw.amount,
                        CustodyUserWithdraw.status)
        ):
            main_user_id = sub_map.get(row.user_id, row.user_id)
            if row.status not in [
                CustodyUserWithdraw.Status.PROCESSING,
                CustodyUserWithdraw.Status.FINISHED,
            ]:
                continue
            if main_user_id in whitelist:
                continue
            usd = abs(_prices.get(row.asset, 0) * row.amount)
            _asset_user_data[row.asset][main_user_id] += usd
            _asset_data[row.asset]['usd'] += usd
            _asset_data[row.asset]['count'] += 1
            _asset_data[row.asset]['amount'] += row.amount
        return _asset_data, _asset_user_data

    def _get_asset_thresholds():
        model = AssetAccumulatedWithdrawalConfig
        rows = model.query.order_by(model.circulation_min.asc()).all()
        ret = []
        for index, row in enumerate(rows):
            limit_count_threshold = _get_config_value(
                row.limit_count_threshold,
                row.temp_limit_count_threshold,
                row.temp_limit_expire_at,
            )
            limit_amount_threshold = _get_config_value(
                row.limit_amount_threshold,
                row.temp_limit_amount_threshold,
                row.temp_limit_expire_at,
            )
            if row.withdrawal_threshold is None or row.user_withdrawal_threshold is None:
                continue
            ret.append(
                {
                    'circulation_min': row.circulation_min,
                    'circulation_max': row.circulation_max,
                    'index': index+1,
                    'withdrawal_threshold': row.withdrawal_threshold,
                    'user_withdrawal_threshold': row.user_withdrawal_threshold,
                    'limit_count_threshold': limit_count_threshold,
                    'limit_amount_threshold': limit_amount_threshold,
                }
            )
        return ret

    def _get_special_asset_thresholds():
        model = SpecialAssetAccumulatedWithdrawalConfig
        rows = model.query.filter(
            model.status == model.Status.OPEN
        ).all()
        ret = {}
        for row in rows:
            row: SpecialAssetAccumulatedWithdrawalConfig
            limit_count_threshold = _get_config_value(
                row.limit_count_threshold,
                row.temp_limit_count_threshold,
                row.temp_limit_expire_at,
            )
            limit_amount_threshold = _get_config_value(
                row.limit_amount_threshold,
                row.temp_limit_amount_threshold,
                row.temp_limit_expire_at,
            )
            if row.withdrawal_threshold is None or row.user_withdrawal_threshold is None:
                continue
            ret.update({
                row.asset: {
                    'withdrawal_threshold': row.withdrawal_threshold,
                    'user_withdrawal_threshold': row.user_withdrawal_threshold,
                    'limit_count_threshold': limit_count_threshold,
                    'limit_amount_threshold': limit_amount_threshold,
                }
            })
        return ret

    def _get_config_value(threshold_, temp_threshold, temp_expire_at):
        result = threshold_
        if temp_threshold and temp_expire_at:
            if temp_expire_at > now():
                result = temp_threshold
        return result

    def _calculate_threshold(_asset: str, _asset_prices: dict):
        if _asset in special_thresholds:
            return special_thresholds[_asset], '特殊币种'

        if _asset not in asset_circulations:
            return
        circulation_usd = asset_circulations[_asset]
        thresholds.sort(key=lambda d: d['circulation_max'])
        for _threshold in thresholds:
            if _threshold['circulation_min'] <= circulation_usd < _threshold['circulation_max']:
                return _threshold, f"{_threshold['index']}档"
        return

    whitelist = AccumulatedWithdrawalHelper.get_whitelist_users()
    prices = AssetPrice.get_yesterday_close_price_map()
    asset_data, asset_user_data = _get_last_24h_withdrawals()
    asset_circulations = _get_asset_circulations()
    thresholds = _get_asset_thresholds()
    special_thresholds = _get_special_asset_thresholds()
    tmp_exclude_assets = get_temp_assets()
    for asset, value in asset_data.items():
        if is_temp_asset(asset, tmp_exclude_assets):
            continue
        r = _calculate_threshold(asset, prices)
        if not r:
            continue
        threshold, rank_desc = r
        avg = avg_data.get(asset)
        if not avg:
            continue
        avg_count = Decimal(avg['last_7d_avg_count'])
        avg_amount = Decimal(avg['last_7d_avg_amount'])
        if avg_count == 0 or avg_amount == 0:
            continue
        if value['usd'] < threshold['withdrawal_threshold']:
            continue
        users_data = asset_user_data[asset]
        users_list = [(uid, value) for uid, value in users_data.items()]
        users_list.sort(key=lambda t: t[1], reverse=True)
        users_top10 = users_list[:10]
        count_threshold = quantize_amount(Decimal(value['count'] / avg_count), PrecisionEnum.RATE_PLACES)
        amount_threshold = quantize_amount(value['amount'] / avg_amount, PrecisionEnum.RATE_PLACES)
        if amount_threshold >= threshold['limit_amount_threshold']:
            notice_cache = AccumulatedWithdrawalNoticeCache(asset)
            if notice_cache.exists():
                continue

            amount_warning = "已达到阈值" if amount_threshold >= threshold['limit_amount_threshold'] else "未达到阈值"
            last_24h_usd = quantize_amount(value['usd'], PrecisionEnum.CASH_PLACES)
            chains = asset_to_chains(asset)
            for chain in chains:
                cfg = try_get_asset_chain_config(asset, chain)
                if not cfg:
                    continue
                old_value = cfg.withdrawals_disabled_by_risk_control
                cfg.withdrawals_disabled_by_accumulate_rc = True
                cfg.withdrawals_disabled_by_rc_updated_at = now()
                AssetAlertHelper.deposit_withdrawal_risk_alert(asset, chain, 'withdrawals_disabled_by_accumulate_rc',
                                                               old_value, True, '触发币种累计提现风控')
            risk_users = []
            for uid, w_usd in users_data.items():
                if w_usd < threshold['user_withdrawal_threshold']:
                    continue
                user_notice_cache = UserAccumulatedWithdrawalNoticeCache(uid, asset)
                if user_notice_cache.exists():
                    continue
                reason = RiskUser.Reason.ACCUMULATED_ASSET_WITHDRAWAL
                permissions = RiskUser.PermissionMap[reason]
                detail = f'''
                            {asset}（最近24H提现数量/最近7日均值）：{amount_to_str(value['amount'])}/{amount_to_str(avg_amount)}≈{amount_to_str(amount_threshold)}，阈值为{amount_to_str(threshold['limit_amount_threshold'])}，{amount_warning}；
                '''
                ru = batch_add_risk_user([uid], reason, detail, source=asset, permissions=permissions)
                risk_users.extend(ru)
                user_notice_cache.gen()
            total = len(risk_users)
            msg = f'''
【币种累计提现告警】
币种：{asset} ({rank_desc})
告警详情：
最近24H提现市值（USD）：{last_24h_usd}，允许监控阈值为{amount_to_str(threshold['withdrawal_threshold'])}
最近24H提现数量/7日均值：{amount_to_str(value['amount'])}/{amount_to_str(avg_amount)}≈{amount_to_str(amount_threshold)}，阈值
为{amount_to_str(threshold['limit_amount_threshold'])}，{amount_warning}
风控用户数：{total}
注：已关闭{asset}的提现，请及时查看处理。
                                '''
            send_alert_notice(
                msg,
                config["ADMIN_CONTACTS"].get("customer_service"),
                at=config["ADMIN_CONTACTS"]["slack_at"].get("accumulated_withdrawal_notice")
            )
            mobiles = RiskControlMobileNoticeConfig.get_mobiles(
                RiskControlMobileNoticeConfig.MobileNoticeEventType.ASSET_ACCUMULATED_WITHDRAWAL
            )
            for mobile in mobiles:
                send_accumulated_withdrawal_monitor_call.delay(mobile, asset, total)
            extra = dict(
                asset=asset,
                last_24h_usd=value['usd'],
                threshold=threshold['withdrawal_threshold'],
                count=value['count'],
                avg_count=avg_count,
                count_threshold=count_threshold,
                limit_count_threshold=threshold['limit_count_threshold'],
                amount=amount_to_str(value['amount']),
                avg_amount=amount_to_str(avg_amount),
                amount_threshold=amount_to_str(amount_threshold),
                limit_amount_threshold=threshold['limit_amount_threshold'],
                amount_warning=amount_warning,
                user_count=total,
                limit_plus_cnt=0,
                limit_cnt=total,
                usd_top10={v[0]: amount_to_str(v[1], PrecisionEnum.CASH_PLACES) for v in users_top10},
                rank_desc=rank_desc,
            )

            add_risk_event_log(
                asset,
                RiskEventLog.Reason.ACCUMULATED_ASSET_WITHDRAWAL,
                current_timestamp(to_int=True),
                0,
                extra,
                0
            )
            notice_cache.gen()
        else:
            pass


@scheduled(300)
@lock_call()
def bus_online_coin_risk_by_market_monitor():
    """商务上币市场维度风控"""
    bus_online_coin_risk_by_market()


@scheduled(crontab(minute='*/5'))
@lock_call()
def country_sms_risk_code_schedule():
    """国家短信区号风控"""
    CountrySmsRiskCodeCache.reload()


@scheduled(crontab(minute='*/15'))
@lock_call()
def wash_deal_risk_control_schedule():
    """防止限制提现用户在现货市场对敲风控"""
    check_and_alert_wash_sale(block_permissions=True)


@scheduled(60 * 5)
@lock_call()
def abnormal_issuance_monitor():
    check_abnormal_issuance()


@scheduled(crontab(minute='*/1'))
@lock_call()
def margin_index_data_update_monitor():
    IndexPriceRiskControlHelper.run(
        IndexPriceAlertCache.IndexType.MARGIN,
    )


@scheduled(crontab(minute='*/1'))
@lock_call()
def perpetual_index_data_update_monitor():
    IndexPriceRiskControlHelper.run(
        IndexPriceAlertCache.IndexType.PERPETUAL,
    )


@scheduled(crontab(minute='*/10'))
@lock_call(with_args=True)
def caculate_small_amount_withdrawal_event_schedule(record_id: int = None):

    def _get_top_user_ids(start_time, end_time, asset=None):
        _prices = PriceManager.assets_to_usd()
        _asset_user_data = defaultdict(Decimal)
        whitelist = AccumulatedWithdrawalHelper.get_whitelist_users()
        for row in yield_query_records_by_time_range(
                table=Withdrawal, start_time=start_time, end_time=end_time,
                select_fields=(
                        Withdrawal.user_id,
                        Withdrawal.type,
                        Withdrawal.asset,
                        Withdrawal.amount,
                        Withdrawal.status)
        ):
            if row.type == Withdrawal.Type.LOCAL:
                continue
            if row.status not in (
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FINISHED,
            ):
                continue
            if row.user_id in whitelist:
                continue
            if asset and row.asset != asset:
                continue
            if not asset:
                # site
                usd = abs(_prices.get(row.asset, 0) * row.amount)
                _asset_user_data[row.user_id] += usd
            else:
                # asset
                _asset_user_data[row.user_id] += row.amount
        result = sorted(_asset_user_data.items(), key=lambda x: x[1], reverse=True)[:10]
        return [i[0] for i in result]

    if record_id:
        rows = RiskEventLog.query.filter(RiskEventLog.id == record_id).all()
    else:
        now_ = now()
        start_time = now_ - timedelta(days=1)
        asset_records = query_records_by_time_range(RiskEventLog,
                                                    start_time=start_time,
                                                    end_time=now_,
                                                    filters={'reason': RiskEventLog.Reason.ASSET_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT})
        site_records = query_records_by_time_range(RiskEventLog,
                                                   start_time=start_time,
                                                   end_time=now_,
                                                   filters={'reason': RiskEventLog.Reason.SITE_WITHDRAWALS_FUSED_BY_SMALL_AMOUNT})
        rows = asset_records + site_records

    for row in rows:
        extra = json.loads(row.extra)
        if extra.get('top_user_ids'):
            continue
        event_time = timestamp_to_datetime(extra['event_time'])
        start = event_time - timedelta(days=1)
        end = event_time
        top_user_ids = _get_top_user_ids(start, end, extra.get('asset'))
        extra['top_user_ids'] = top_user_ids
        row.extra = json.dumps(extra)
    db.session.commit()