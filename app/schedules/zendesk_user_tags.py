# -*- coding: utf-8 -*-
import json
import time
from datetime import datetime, date, timedelta
from flask import current_app
import requests
from collections import defaultdict

from celery.schedules import crontab
from sqlalchemy import func

from app.utils.date_ import date_to_datetime

from .. import config
from ..common import CeleryQueues
from ..caches.admin import ZendeskUser<PERSON>ache, ZendeskUserTagRetryCache
from ..models import (db, VipUser, MarketMaker, Ambassador,
                      ZendeskUserTags, User)
from ..utils import (today, today_datetime, batch_iter, scheduled, timestamp_to_datetime,
                     route_module_to_celery_queue)


route_module_to_celery_queue(__name__, CeleryQueues.DAILY)

LIMIT = 100
ZENDESK_CONFIG = config['ZENDESK_CONFIG']


def insert_incremental_user(start_time):
    inc_user_url = ZENDESK_CONFIG['inc_user_url']
    def _get_inc_user_list(start_time):
        url = f'{inc_user_url}?start_time={start_time}'
        return requests.get(url, auth=(ZENDESK_CONFIG['user'], ZENDESK_CONFIG['token'])).json()

    yesterday = today_datetime() - timedelta(days=1)
    start_time = start_time or int(yesterday.timestamp())
    zendesk_user_list = []
    end_of_stream = False  # True表示最后一页
    while not end_of_stream:
        data = _get_inc_user_list(start_time)
        # You can make up to 10 requests per minute to these endpoints.
        time.sleep(10)
        end_of_stream = data['end_of_stream']
        start_time = data['end_time']
        zendesk_user_list.extend(
            [dict(id=i['id'], email=str.lower(i['email'])) for i in data['users'] if i['email']]
        )
    email_zendesk_id_map = {i['email']: i['id'] for i in zendesk_user_list}
    user_data_list = []
    for _email_list in batch_iter(list(email_zendesk_id_map.keys()), 1000):
        if _email_list:
            user_data = User.query.filter(User.email.in_(_email_list)
            ).with_entities(User.id, User.email).all()
            user_data_list.extend(user_data)
    row_list = []
    zend_user_ids = {i.user_id for i in ZendeskUserTags.query.with_entities(ZendeskUserTags.user_id).all()}
    for user in user_data_list:
        if user.id not in zend_user_ids:
            row_list.append(ZendeskUserTags(
                user_id=user.id, zendesk_id=email_zendesk_id_map[str.lower(user.email)],
                updated_at=date.min))  # 表示没更新过
    for rows in batch_iter(row_list, 5000):
        db.session.bulk_save_objects(rows)
        db.session.flush()
    db.session.commit()


def update_model_user_tags():
    vip_data = VipUser.query.filter(VipUser.status == VipUser.StatusType.PASS).all()
    marketmaker_data = MarketMaker.query.filter(
        MarketMaker.status == MarketMaker.StatusType.PASS
    ).group_by(
        MarketMaker.user_id
    ).with_entities(
        MarketMaker.user_id,
        func.max(MarketMaker.level).label('level'),
    ).all()
    ambassador_data = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID).all()
    user_tag_map = defaultdict(list)
    for item in vip_data:
        user_id = item.user_id
        user_tag_map[user_id].append(f'VIP用户-VIP{item.level}')
    for item in marketmaker_data:
        user_id = item.user_id
        user_tag_map[user_id].append(f'做市商-LV{item.level}')
    for item in ambassador_data:
        user_id = item.user_id
        user_tag_map[user_id].append(f'大使-{Ambassador.LEVEL_NAMES[item.level]}')
    # user_tag_map {123: ['做市商-LV1', '大使-白银']}
    zend_user_map = {i.user_id: i for i in ZendeskUserTags.query.all()}
    has_tags_user = {user_id for user_id, tags in zend_user_map.items() if tags != ''}
    delete_user_list = has_tags_user - set(user_tag_map.keys())  # 最新的数据没有表情代表需要删除用户的标签
    update_list = []
    for user_id, tag_list in user_tag_map.items():
        if user_id in zend_user_map:
            tags = ";".join(tag_list)
            if tags != zend_user_map[user_id].tags:
                update_list.append({'id': zend_user_map[user_id].id, 'tags': tags})
    for user_id in delete_user_list:
        update_list.append({'id': zend_user_map[user_id].id, 'tags': ''})
    for rows in batch_iter(update_list, 5000):
        db.session.bulk_update_mappings(ZendeskUserTags, rows)
        db.session.flush()
    db.session.commit()


def update_zendesk_user_tags(start_time: int):
    start_time = timestamp_to_datetime(start_time)
    result = ZendeskUserTags.query.filter(ZendeskUserTags.updated_at >= start_time).all()
    tmp_update_list = [dict(id=i.zendesk_id, tags=i.tags) for i in result]
    failed_data = []
    for update_data in batch_iter(tmp_update_list, LIMIT):
        try:
            requests.put(ZENDESK_CONFIG['update_url'],
                        json=update_data,
                        auth=(ZENDESK_CONFIG['user'], ZENDESK_CONFIG['token'])).json()
        except Exception as e:
            current_app.logger.error(f'update zendesk user tags failed: {e!r}')
            failed_data.extend(update_data)
    if failed_data:
        cache = ZendeskUserTagRetryCache(int(start_time.timestamp()))
        cache.set(json.dumps(failed_data))
        cache.expire(cache.ttl)


def retry_update_zendesk_user_tags(start_time: int):
    cache = ZendeskUserTagRetryCache(start_time)
    failed_data = cache.read()
    if not failed_data:
        return
    failed_data = json.loads(failed_data)
    new_failed_data = []
    for update_data in batch_iter(failed_data, LIMIT):
        try:
            requests.put(ZENDESK_CONFIG['update_url'],
                        json=update_data,
                        auth=(ZENDESK_CONFIG['user'], ZENDESK_CONFIG['token'])).json()
        except Exception as e:
            current_app.logger.error(f'retry update zendesk user tags failed: {e!r}')
            new_failed_data.extend(update_data)
    if new_failed_data:
        cache.set(json.dumps(new_failed_data))
    else:
        cache.delete()


@scheduled(crontab(hour='1,3', minute='0'))
def update_zendesk_user_data():
    if not ZENDESK_CONFIG:
        return
    today_ = today()
    redis_client = ZendeskUserCache()
    cache_ = redis_client.read()
    update_time = int(datetime(today_.year, today_.month, today_.day).timestamp())
    yesterday = update_time - 86400

    insert_user_time = int(cache_['insert_user_time']) if cache_.get('insert_user_time') else yesterday
    if update_time != insert_user_time:
        insert_incremental_user(insert_user_time)
        redis_client.hset('insert_user_time', update_time)

    update_model_time = int(cache_['update_model_time']) if cache_.get('update_model_time') else yesterday
    if update_time != update_model_time:
        update_model_user_tags()
        redis_client.hset('update_model_time', update_time)

    update_zendesk_time = int(cache_['update_zendesk_time']) if cache_.get('update_zendesk_time') else yesterday
    if update_time != update_zendesk_time:
        update_zendesk_user_tags(update_zendesk_time)
        redis_client.hset('update_zendesk_time', update_time)


@scheduled(crontab(hour='1-3', minute='0,30'))
def retry_update_zendesk_user_data():
    update_time = int(date_to_datetime(today()).timestamp())
    if ZendeskUserTagRetryCache(update_time).exists():
        retry_update_zendesk_user_tags(update_time)

