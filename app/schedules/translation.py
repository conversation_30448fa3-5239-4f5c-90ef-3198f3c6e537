import time
from collections import defaultdict
import json
from datetime import timed<PERSON><PERSON>
from io import Bytes<PERSON>
from typing import List
from celery.schedules import crontab
from flask import current_app
from app.business.clients.ai_translate import AITranslateClient
from app.business.lock import lock_call
from app.common.constants import CeleryQueues, Language
from app.models.mongo.insight import (
    CoinExInsightContentMySQL as CoinExInsightContent,
    ArticleType,
)
from app.models.mongo.translation import TranslationTaskMySQL
from app.models.quotes import CoinInformationTrans
from app.models.support import SelfServiceQAQuestionContent
from app.models.onchain import OnchainTokenAboutSource
from app.models.onchain import OnchainTokenAboutTranslation
from app.business.onchain.token import get_token_about_source
from ..business.academy import AcademyBusiness
from ..business.file import upload_subtitle_file
from ..business.insight import get_participles
from ..models import db, KlineAnalysisContent, AirdropActivityDetail, AirdropActivityQuestionBank, Blog, BlogCategory, \
    File
from ..models.academy import AcademyArticleContent
from ..models.media import VideoSubtitle, Video
from ..models.user_education import UserEducationContent, Content
from ..utils import scheduled, route_module_to_celery_queue, celery_task, now

route_module_to_celery_queue(__name__, CeleryQueues.TRANSLATION)

manual_post_business = (TranslationTaskMySQL.Business.INSIGHT, TranslationTaskMySQL.Business.ACADEMY,
                        TranslationTaskMySQL.Business.AIRDROP, TranslationTaskMySQL.Business.AIRDROP_QUESTION,
                        TranslationTaskMySQL.Business.BLOG, TranslationTaskMySQL.Business.VIDEO_SUBTITLE,
                        TranslationTaskMySQL.Business.CUSTOMER_CHATBOT
                        )


@scheduled(crontab(minute='*/5'))
@lock_call()
def check_translation():
    q1_start = time.time()
    manual_post_records: List[TranslationTaskMySQL] = list(TranslationTaskMySQL.query.filter(
        TranslationTaskMySQL.business.in_(manual_post_business),
        TranslationTaskMySQL.status == TranslationTaskMySQL.Status.CREATED
    ).order_by(TranslationTaskMySQL.created_at.desc()).limit(2000))
    q1_duration = time.time() - q1_start

    q2_start = time.time()
    auto_post_records: List[TranslationTaskMySQL] = list(TranslationTaskMySQL.query.filter(
        TranslationTaskMySQL.business.notin_(manual_post_business),
        TranslationTaskMySQL.status == TranslationTaskMySQL.Status.CREATED
    ).order_by(TranslationTaskMySQL.created_at.desc()).limit(2000))
    q2_duration = time.time() - q2_start

    records = manual_post_records + auto_post_records

    translator = AITranslateClient()

    if len(records) == 0:
        return

    current_app.logger.warning(f"Handle translation task start: {len(records)}, {records[0].id}, "
                               f"oldest record date: {records[-1].created_at}, "
                               f"q1_duration: {q1_duration:.3f}, q2_duration: {q2_duration:.3f}")

    completed_tasks = []
    batch_size = 20

    for batch_idx in range(0, len(records), batch_size):
        task_batch = records[batch_idx:batch_idx + batch_size]

        try:
            Status = TranslationTaskMySQL.Status
            datas = translator.get_async_result([task.task_id for task in task_batch])

            if len(datas) != len(task_batch):
                current_app.logger.error(
                    f"Batch {batch_idx // batch_size}: data count mismatch, "
                    f"tasks={len(task_batch)}, datas={len(datas)}"
                )
                continue

            datas_map = {}
            for index, data in enumerate(datas):
                if getattr(data, 'task_id', None) is not None:
                    datas_map[data.task_id] = data
                datas_map[index] = data

            for index,task in enumerate(task_batch):
                task: TranslationTaskMySQL
                data = datas_map.get(task.task_id) or datas_map.get(index)
                if not data:
                    continue
                if data.status in {Status.CREATED.name, Status.PROCESSING.name, Status.RETRYING.name}:
                    continue

                if data.status == Status.FAILED.name:
                    task.status = Status.FAILED
                    db.session.commit()
                    continue

                try:
                    task.input_tokens = data.input_tokens
                    task.output_tokens = data.output_tokens
                    task.content = data.content
                    task.status = Status.FINISHED
                    db.session.commit()
                    process_business_task(task)
                    completed_tasks.append(task)
                except Exception as e:
                    task.status = Status.FAILED
                    db.session.commit()
                    current_app.logger.error(f"Failed to process task {task.id}: {e}")

        except Exception as e:
            current_app.logger.exception(f"Batch {batch_idx // batch_size} failed: {e}")
            db.session.rollback()

    current_app.logger.warning(f"Handle translation task complete: {len(completed_tasks)}, "
                               f"first_completed id: {completed_tasks[0].mongo_id if completed_tasks else 'none'}")


# 业务处理
def process_business_task(task):
    business = task.business
    task_id = str(task.mongo_id)
    if business == TranslationTaskMySQL.Business.INSIGHT:
        insight_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.KLINE_ANALYSIS:
        kline_analysis_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.AIRDROP:
        airdrop_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.AIRDROP_QUESTION:
        airdrop_question_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.ACADEMY:
        academy_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.COIN_INFO:
        coin_info_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.BLOG:
        blog_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.VIDEO_SUBTITLE:
        video_subtitle_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.CUSTOMER_CHATBOT:
        self_service_translated.delay(task_id)
    elif business == TranslationTaskMySQL.Business.ONCHAIN:
        onchain_token_about_translated(task_id)
    elif business == TranslationTaskMySQL.Business.USER_EDUCATION:
        user_education_translated.delay(task_id)
    else:
        current_app.logger.warning(f"Handle translation task warning unknown business: {business}")


def _extract_task_with_content(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()

    business_info = task.business_info
    if business_info:
        raise ValueError(f"Old async trans task, with business_info: {business_info}")
    try:
        return task, json.loads(task.content)
    except json.JSONDecodeError:
        raise ValueError(f"Failed to parse task content in JSON format: {task.content}")


def handle_translation_error(func):
    """
    捕获翻译业务处理中的异常，并将任务状态更新为失败
    """
    from functools import wraps

    @wraps(func)
    def wrapper(task_id, *args, **kwargs):
        try:
            return func(task_id, *args, **kwargs)
        except Exception as e:
            # 获取任务更新为失败
            task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
            if task:
                task.status = TranslationTaskMySQL.Status.FAILED
                task.save()

            current_app.logger.error(f"{func.__name__} 处理失败，task_id: {task_id}, 错误: {str(e)}")

            raise

    return wrapper


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def insight_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = CoinExInsightContent.get_or_create(task.business_id, task.target)

    # 一些字段如果为空，需要从英文版拷贝过来
    need_copy_fields = ['cover']
    if business_record.article_type == ArticleType.REPORT:
        need_copy_fields.append('report')
    source_content = None
    for need_copy_field in need_copy_fields:
        if getattr(business_record, need_copy_field):
            continue
        if not source_content:
            source_content = CoinExInsightContent.query.filter_by(
                insight_id=task.business_id,
                lang=task.source
            ).first()  # 在循环内获取英文版内容，是因为正常情况下，应该不需要复制
        if not source_content:
            # 英文版内容如果不存在，说明数据有问题
            current_app.logger.error(f"Failed to find {task.source} content for insight: {task.business_id}")
            continue
        setattr(business_record, need_copy_field, getattr(source_content, need_copy_field))

    translatedFields = ['title', 'abstract', 'content_html']
    for key, value in task_content.items():
        if key in translatedFields:
            setattr(business_record, key, value)

    participle = get_participles(Language[task.target], business_record.title, business_record.content_html)
    business_record.content_text = participle['text']
    business_record.participles = participle['participles']
    business_record.title_participles = participle['title_participles']
    business_record.content_participles = participle['content_participles']

    db.session.commit()
    current_app.logger.warning(f'insight task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def academy_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)
    current_app.logger.warning(f'academy task start: {task_id}, {task_content}')

    business_record: AcademyArticleContent = AcademyArticleContent.get_or_create_by_article(task.business_id,
                                                                                            task.target)

    AcademyBusiness.update_parsed_result(business_record, Language[task.target], task_content)
    db.session.commit()
    current_app.logger.warning(f'academy task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def airdrop_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = AirdropActivityDetail.get_or_create(
        auto_commit=True,
        airdrop_activity_id=task.business_id,
        lang=task.target
    )

    for key, value in task_content.items():
        if key in ['title', 'summary', 'introductions']:
            setattr(business_record, key, value)
        else:
            current_app.logger.error(f"Unknown key in task content: {key}. task.content: {task.content}")
            continue

    db.session.commit()
    current_app.logger.warning(f'airdrop task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def airdrop_question_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)

    business_record = AirdropActivityQuestionBank.query.get(task.business_id)

    for key, value in task_content.items():
        if key in ['question', 'answer', 'answer_analysis', 'A', 'B', 'C']:
            if key in ['A', 'B', 'C']:
                options = json.loads(business_record.options) or {}
                options[key] = value
                business_record.options = json.dumps(options)
            elif key in ['question', 'answer', 'answer_analysis']:
                setattr(business_record, key, value)
            else:
                current_app.logger.error(f"Unknown key in task content: {key}. task.content: {task.content}")
                continue

    db.session.commit()
    current_app.logger.warning(f'airdrop_question task complete: {task_id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def kline_analysis_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    analysis_id = task.business_id
    lang = task.target
    content = KlineAnalysisContent(
        analysis_id=analysis_id,
        lang=lang,
        text=content,
        input_token_count=task.input_tokens,
        output_token_count=task.output_tokens,
    )
    db.session.add(content)
    db.session.commit()


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def coin_info_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    if not content:
        current_app.logger.error(f'coin info translation task content is empty: {task_id}')
        raise ValueError
    content = json.loads(content)

    business_id = task.business_id
    lang = task.target
    record = CoinInformationTrans.get_or_create(
        coin_information_id=business_id,
        lang=lang
    )
    record.description = content.get('description', '')
    record.is_auto_translation = True
    introduce_map = defaultdict(lambda: defaultdict(str))

    # 发送给翻译服务的字段content是平铺的，而存储的字段introduces是一个列表，在此处把字典还原成列表
    for k, v in content.items():
        if k.startswith('transIntroduces'):
            _, field, index = k.split(':')
            introduce_map[int(index)][field] = v
    max_index = max(introduce_map)
    introduces = []
    for i in range(max_index + 1):
        introduces.append(introduce_map[i])
    record.introduces = json.dumps(introduces)
    db.session_add_and_commit(record)


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def blog_translated(task_id: str):
    task, task_content = _extract_task_with_content(task_id)
    current_app.logger.warning(f'blog translation task start: {task_id}')

    # 获取原始博客
    source_blog = Blog.query.get(task.business_id)
    if not source_blog:
        current_app.logger.error(f"Source blog not found: {task.business_id}")
        return

    # 获取源博客的分类信息
    source_category = BlogCategory.query.get(source_blog.category_id) if source_blog.category_id else None

    # 查找目标语言中与源分类备注相同的分类
    target_category_id = None  # 默认设置为无分类
    if source_category:
        target_category = BlogCategory.query.filter(
            BlogCategory.status != BlogCategory.Status.DELETED,
            BlogCategory.lang == task.target,
            BlogCategory.remark == source_category.remark
        ).first()
        if target_category:
            target_category_id = target_category.id

    # 检查目标语言的博客是否已存在
    existing_blog = Blog.query.filter_by(
        blueprint_id=source_blog.id if not source_blog.blueprint_id else source_blog.blueprint_id,
        lang=task.target,
    ).first()

    if existing_blog:
        # 如果已存在，则更新内容
        blog = existing_blog
        blog.status = Blog.Status.DRAFT
        blog.category_id = target_category_id  # 更新为找到的对应分类
        # 只有在目标博客封面为空时才继承源博客封面
        if not existing_blog.cover:
            existing_blog.cover = source_blog.cover if source_blog.cover else ""
        if not existing_blog.app_cover:
            existing_blog.app_cover = source_blog.app_cover if source_blog.app_cover else ""
        current_app.logger.warning(f'Updating existing blog: {blog.id}')
    else:
        # 创建新博客
        blog = Blog(
            remark=source_blog.remark,
            lang=task.target,
            category_id=target_category_id,  # 使用找到的对应分类
            published_at=source_blog.published_at,
            status=Blog.Status.DRAFT,  # 翻译后的博客默认为草稿状态
            seo_url_keyword=source_blog.seo_url_keyword,
            seo_title=source_blog.seo_title,
            title="",
            content="",
            abstract="",
            draft_title="",
            draft_content="",
            draft_abstract="",
            cover=source_blog.cover if source_blog.cover else "",
            app_cover=source_blog.app_cover if source_blog.app_cover else "",
            is_blueprint=False,  # 翻译后的博客不是主文章
            blueprint_id=source_blog.id if not source_blog.blueprint_id else source_blog.blueprint_id,
            is_top=source_blog.is_top,
            last_updated_at=now(),
        )
        db.session.add(blog)
        current_app.logger.warning(f'Creating new blog for translation')

    # 根据原博客的状态更新翻译后的博客
    if source_blog.status == Blog.Status.DRAFT:
        # 如果原博客是草稿，更新草稿字段
        blog.draft_title = task_content.get('title', '')
        blog.draft_abstract = task_content.get('abstract', '')
        blog.draft_content = task_content.get('content', '')
    else:
        # 如果原博客已发布，同时更新正式字段和草稿字段
        blog.draft_title = task_content.get('title', '')
        blog.draft_abstract = task_content.get('abstract', '')
        blog.draft_content = task_content.get('content', '')

        # 已发布的博客也需要更新正式字段，但保持草稿状态
        blog.title = task_content.get('title', '')
        blog.abstract = task_content.get('abstract', '')
        blog.content = task_content.get('content', '')

    # 更新最后更新时间
    blog.last_updated_at = now()

    db.session.commit()
    current_app.logger.warning(f'blog translation task complete: {task_id}, blog_id: {blog.id}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def self_service_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    if not content:
        current_app.logger.error(f'self service translation task content is empty: {task_id}')
        raise ValueError
    content = json.loads(content)

    business_id = task.business_id
    lang = task.target
    record = SelfServiceQAQuestionContent.get_or_create(
        question_id=business_id,
        lang=lang,
    )
    record.title = content.get('title', '')
    record.content = content.get('content', '')
    db.session_add_and_commit(record)


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def user_education_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    if not content:
        current_app.logger.error(f'user education translation task content is empty: {task_id}')
        raise ValueError
    content = json.loads(content)

    business_id = task.business_id
    lang = task.target

    # business_id f"{user_education_id}-{content_type}"
    user_education_id, content_type = business_id.split('-')

    record = UserEducationContent.get_or_create(
        user_education_id=user_education_id,
        content_type=UserEducationContent.ContentType[content_type],
        lang=lang,
    )

    content_map = defaultdict(lambda: defaultdict(str))
    for k, v in content.items():
        if k.startswith('transUserEducation'):
            _, _, field, index = k.split(':')
            content_map[int(index)][field] = v

    updated_contents = []
    for idx, old_content in enumerate(record.contents):
        new_content_dict = old_content.dict()

        if idx in content_map:
            trans_content = content_map[idx]
            for k, v in trans_content.items():
                new_content_dict[k] = v
        updated_contents.append(Content(**new_content_dict))

    record.contents = updated_contents
    db.session.commit()


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def video_subtitle_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    if not task:
        current_app.logger.error(f"Translation task not found: {task_id}")
        return

    task_content = task.content
    if not task_content:
        current_app.logger.error(f"Translation task content is empty: {task_id}")
        return

    video = Video.query.get(task.business_id)
    if not video:
        current_app.logger.error(f"Video not found for task: {task_id}, business_id: {task.business_id}")
        return

    video_file_key = video.file_key

    upload_result = upload_subtitle_file(BytesIO(task_content.encode("utf-8")), 'vtt', video_file_key, task.target)
    new_file: File = File.new(
        None, upload_result['file_key'], upload_result['file_name'], mime_type=upload_result['mime_type']
    )
    db.session.add(new_file)
    db.session.flush()

    subtitle = VideoSubtitle.query.filter_by(
        video_id=video.id,
        lang=Language[task.target]
    ).first()
    if subtitle:
        # 如果已经存在字幕，更新文件信息
        subtitle.file_id = new_file.id
        subtitle.file_key = new_file.key
        subtitle.is_translated = True
    else:
        subtitle = VideoSubtitle(
            video_id=video.id,
            lang=Language[task.target],
            file_id=new_file.id,
            file_key=new_file.key,
            is_translated=True
        )
        db.session.add(subtitle)
    db.session.commit()
    current_app.logger.warning(f'video subtitle task complete: {task_id}, video: {video.name}, lang: {task.target}')


@celery_task()
@lock_call(with_args=True)
@handle_translation_error
def onchain_token_about_translated(task_id: str):
    task = TranslationTaskMySQL.query.filter_by(mongo_id=task_id).first()
    content = task.content
    if not content:
        current_app.logger.error(f'onchain translation task content is empty: {task_id}')
        raise ValueError

    business_id = task.business_id
    lang = task.target
    token_about_source: OnchainTokenAboutSource = OnchainTokenAboutSource.query.filter(
        OnchainTokenAboutSource.id == business_id,
    ).first()
    if not token_about_source:
        return
    new_token_about_source = get_token_about_source(token_about_source.token_id)
    if token_about_source.id != new_token_about_source.id:
        return

    token_about_translation: OnchainTokenAboutTranslation = OnchainTokenAboutTranslation.get_or_create(
        token_id=token_about_source.token_id,
        lang=lang,
    )
    token_about_translation.source_id = token_about_source.id
    token_about_translation.about = content
    db.session_add_and_commit(token_about_translation)


@scheduled(crontab(minute='20', hour='7'))
@lock_call(with_args=False, wait=True)
def clear_old_data_schedule():
    cutoff_date = now() - timedelta(days=7)
    delete_count = TranslationTaskMySQL.query.filter(
        TranslationTaskMySQL.created_at <= cutoff_date
    ).delete()

    db.session.commit()
    current_app.logger.warning(f"Clear old translation tasks: [{delete_count}]")
