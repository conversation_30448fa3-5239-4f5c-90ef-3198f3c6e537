# -*- coding: utf-8 -*-
import datetime

from celery.schedules import crontab

from app.business import (
    route_module_to_celery_queue,
    CeleryQueues,
    lock_call,
)
from app.business.report.investment import (
    InvestmentReporter,
    SiteInvestmentReporter,
    update_monthly_site_investment_report,
    update_monthly_asset_investment_report,
)
from app.caches.investment import (
    InterestRankCache,
    InvIncEquityYesIncomeCache,
    Investment7DaysEARCache,
    InvestmentBalanceRankCache,
    InvestmentConfigCache,
    InvestmentYesterdayARRCache,
    InvestmentStatisticCache,
    UserInvSummaryCache,
    UserYesInvSummaryCache,
)
from app.models.daily import DailyInvestmentReport, DailySiteInvestmentReport
from app.models.monthly import MonthlyInvestmentReport, MonthlySiteInvestmentReport
from app.schedules.reports.utils import get_monthly_report_date
from app.business.investment import InvestmentSchedule
from app.utils import scheduled
from app.utils.date_ import next_month


route_module_to_celery_queue(__name__, CeleryQueues.INVESTMENT)


@scheduled(crontab(minute="5,10,15", hour="*/1"))
@lock_call()
def investment_hourly_schedule():
    """理财小时任务调度"""
    InvestmentSchedule().hour_interest_schedule()


@scheduled(crontab(minute="10,20,30", hour="*/1"))
@lock_call()
def investment_asset_rate_schedule():
    """理财小时利率更新"""
    InvestmentSchedule().update_conifg_rate_schedule()


@scheduled(crontab(minute="10,20", hour="0-3"))
@lock_call()
def investment_day_schedule():
    """理财日任务调度"""
    InvestmentSchedule().day_interest_schedule()


@scheduled(crontab(minute="20,40", hour="0-3"))
@lock_call()
def investment_day_payout_schedule():
    """理财日利息发放调度"""
    InvestmentSchedule().day_payout_schedule()


@scheduled(crontab(minute="30,50", hour="1-3"))
@lock_call()
def investment_clear_fragment_data_schedule():
    """理财碎片数据清理"""
    InvestmentSchedule().clear_fragment_data_schedule()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_investment_config_schedule():
    InvestmentConfigCache.reload()


@scheduled(crontab(hour="*/1", minute="10"))
@lock_call()
def update_investment_balance_rank_schedule():
    InvestmentBalanceRankCache.reload()


@scheduled(crontab(hour="*", minute="30"))
@lock_call()
def update_investment_yes_summary_schedule():
    UserInvSummaryCache.reload()
    UserYesInvSummaryCache.reload()
    InvIncEquityYesIncomeCache.reload()


@scheduled(crontab(minute="10", hour="1-3"))
@lock_call()
def update_investment_interest_rank_schedule():
    InterestRankCache.reload()


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_day_rate_schedule():
    Investment7DaysEARCache().reload()
    InvestmentYesterdayARRCache().reload()


@scheduled(crontab(minute='20,40', hour="0-3"))
@lock_call()
def run_daily_investment_report_schedule():
    InvestmentReporter().dispatch()
    SiteInvestmentReporter().dispatch()


@scheduled(crontab(minute=30, hour="4-6", day_of_month=1))
@lock_call()
def update_monthly_asset_investment_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlyInvestmentReport, DailyInvestmentReport)

    if not start_month:
        return

    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_asset_investment_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute=30, hour="4-6", day_of_month=1))
@lock_call()
def update_monthly_investment_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlySiteInvestmentReport, DailySiteInvestmentReport)

    if not start_month:
        return

    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_site_investment_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute="*/30"))
@lock_call()
def update_investment_statistic_cache_schedule():
    InvestmentStatisticCache.reload_all()