# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
import json

from flask import current_app
from sqlalchemy import func

from app import config
from app.assets.asset import asset_to_default_chain, get_asset_config
from app.business.alert import send_alert_notice
from app.business.clients.server import Server<PERSON>lient
from app.business.clients.wallet import WalletClient
from app.business.email import send_staking_remove_success_email, send_staking_add_notice_email
from app.business.lock import CacheLock, LockKeys, lock_call
from app.business.staking import StakingOperation
from app.caches.staking import StakingRewardRetryCache
from app.caches.statistics import StakingStatisticsCache
from app.common.constants import BalanceBusiness, CeleryQueues, PrecisionEnum
from app.models import db
from app.models.daily import DailyStakingReport
from app.models.staking import (StakingAccount, StakingHistory, StakingIncomeRecord, StakingPool, 
                                StakingRewardHistory, StakingSyncRecord, 
                                StakingSystemSummary, StakingUser<PERSON>ummary)
from app.models.wallet import AssetPrice
from app.utils import route_module_to_celery_queue
from app.utils.amount import amount_to_str, quantize_amount
from app.utils.celery_ import celery_task, scheduled
from celery.schedules import crontab

from app.utils.date_ import current_timestamp, date_to_datetime, now, timestamp_to_datetime, today

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)

@scheduled(crontab(hour='*/3', minute='10'))
@lock_call()
def update_staking_pool_schedule():

    # 确保资金池状态是最新
    proccess_unstaking_history_schedule()
    proccess_staking_history_schedule()
    current_ts = current_timestamp(to_int=True)
    current_ts = current_ts - current_ts % 3600
    report_at = timestamp_to_datetime(current_ts)
    pools = StakingPool.query.all()
    staking_client = WalletClient()
    for pool in pools:
        with CacheLock(LockKeys.staking_operation(pool.asset)):
            db.session.rollback()
            r = StakingSyncRecord.query.filter(
                StakingSyncRecord.asset == pool.asset,
                StakingSyncRecord.report_at == report_at,
            ).first()
            if r:
                continue
            if not r:
                r = StakingSyncRecord(report_at=report_at, asset=pool.asset)
            pool = StakingPool.query.filter(StakingPool.asset == pool.asset).first()
            r.amount = r.amount or StakingOperation(pool.asset).get_stake_or_unstake_amount(pool)
            if r.amount == Decimal():
                continue
            db.session_add_and_commit(r)
            chain = asset_to_default_chain(pool.asset)
            staking_client.update_staking_status(pool.asset,
                                                 chain,
                                                 r.amount, 
                                                 str(r.id))


@scheduled(crontab(hour='*/6', minute='10'))
@lock_call()
def update_staking_sync_record_schedule():
    records = StakingSyncRecord.query.filter(
        StakingSyncRecord.status == StakingSyncRecord.Status.CREATED,
    ).all()
    client = WalletClient()
    is_commit = False
    for r in records:
        business_id = str(r.id)
        try:
            result = client.get_staking_status(r.asset, business_id)
        except Exception:
            current_app.logger.error(f"get staking status error business_id: {business_id}")
            continue
        if result['status'] == 'FINISHED':
            r.status = StakingSyncRecord.Status.FINISHED
            is_commit = True
    if is_commit:
        db.session.commit()


@scheduled(crontab(hour='*/1', minute='*/20'))
@lock_call()
def proccess_unstaking_history_schedule():
    records = StakingHistory.query.filter(
        StakingHistory.type == StakingHistory.Type.UNSTAKE,
        StakingHistory.status == StakingHistory.Status.QUEUED).order_by(
            StakingHistory.id.asc()
        ).all()
    accounts = StakingAccount.query.filter(
        StakingAccount.status == StakingAccount.Status.OPEN).all()
    config_map = {item.asset: get_asset_config(item.asset) for item in accounts}
    client = ServerClient()
    for record in records:
        with CacheLock(LockKeys.staking_operation(record.asset), wait=False):
            record: StakingHistory

            db.session.rollback()
            conf = config_map[record.asset]
            now_ = now()
            proccess_at = record.created_at + timedelta(hours=int(conf.staking_withdraw_period))
            if proccess_at >= now_:
                continue
            if record.transfer_status == StakingHistory.TransferStatus.CREATED \
                    and now_ - proccess_at >= timedelta(days=7):
                current_app.logger.error(f"process unstaking history timeout record_id: {record.id} user_id: {record.user_id}")
                continue
            
            pool = StakingPool.query.filter(StakingPool.asset == record.asset).first()
            summary = StakingUserSummary.query.filter(
                StakingUserSummary.user_id == record.user_id,
                StakingUserSummary.asset == record.asset,
            ).first()
            db.session.commit()
            try:
                if record.transfer_status == StakingHistory.TransferStatus.CREATED:
                    result = client.asset_query_business(
                        user_id=record.user_id,
                        asset=record.asset,
                        business=BalanceBusiness.STAKING_REMOVE,
                        business_id=record.id,
                        account_id=StakingAccount.ACCOUNT_ID,
                    )
                    if result:
                        assert summary.unstaking_amount >= record.amount
                        summary.unstaking_amount -= record.amount
                        pool.amount -= record.amount
                        pool.unstaking_amount -= record.amount
                        record.transfer_status = StakingHistory.TransferStatus.DEDUCTED
                        db.session.commit()
                result = StakingOperation(record.asset).do_transfer_out(record, pool, summary)
            except Exception as e:
                current_app.logger.error(f"process unstaking history error record_id: {record.id} {e!r}")
                continue
            if result:
                send_staking_remove_success_email.delay(record.user_id, amount_to_str(record.amount), record.asset)


@scheduled(crontab(hour=0, minute='*/20'))
@lock_call()
def proccess_staking_history_schedule():

    records = StakingHistory.query.filter(
        StakingHistory.type == StakingHistory.Type.STAKE,
        StakingHistory.status == StakingHistory.Status.QUEUED,
        StakingHistory.transfer_status == StakingHistory.TransferStatus.FINISHED).order_by(
            StakingHistory.id.asc()
        ).all()
    server_client = ServerClient()
    for record in records:
        with CacheLock(LockKeys.staking_operation(record.asset), wait=False):
            db.session.rollback()
            sys_amount = WalletClient().get_staking_summary(record.asset)['activated_staking_amount']
            pool = StakingPool.query.filter(StakingPool.asset == record.asset).first()
            if record.amount > sys_amount - pool.amount + pool.unstaking_amount:
                continue
            summary = StakingUserSummary.query.filter(
                StakingUserSummary.user_id == record.user_id,
                StakingUserSummary.asset == record.asset,
            ).first()
            server_client.unlock_user_balance(record.user_id, record.asset,
                                              str(record.amount),
                                              BalanceBusiness.STAKING_ADD, record.id,
                                              account_id=StakingAccount.ACCOUNT_ID)
            assert summary.staking_amount >= record.amount
            summary.staking_amount -= record.amount
            pool.amount += record.amount
            record.status = StakingHistory.Status.FINISHED
            record.finished_at = now()
            db.session.commit()
            send_staking_add_notice_email.delay(record.user_id, amount_to_str(record.amount), record.asset)


@scheduled(crontab(hour='*/1', minute='0,30,50'))
@lock_call()
def sync_system_staking_schedule():
    current_ts = current_timestamp(to_int=True)
    current_ts -= current_ts % 3600
    report_at = timestamp_to_datetime(current_ts)
    r = StakingSystemSummary.query.filter(
        StakingSystemSummary.report_at == report_at).first()
    if r:
        return
    client = WalletClient()
    for account in StakingAccount.query.filter(
            StakingAccount.status == StakingAccount.Status.OPEN).all():
        resp = client.get_staking_summary(account.asset)
        r = StakingSystemSummary(
            report_at=report_at,
            asset=account.asset,
            staking_amount=resp['activated_staking_amount'],
        )
        db.session.add(r)
    db.session.commit()


@scheduled(crontab(hour='0,1', minute='20,40'))
@lock_call()
def send_staking_reward_schedule():
    today_ = today() 
    end_time = date_to_datetime(today_)
    start_time = end_time - timedelta(days=1)
    for account in StakingAccount.query.filter(
            StakingAccount.status == StakingAccount.Status.OPEN).all():
        account: StakingAccount
        asset = account.asset
        operation = StakingOperation(asset)
        operation.send_reward(start_time, end_time)
        update_staking_user_summary_task.delay(asset, int(end_time.timestamp()))


@scheduled(crontab(hour='1,2', minute='30,50'))
@lock_call()
def retry_send_staking_reward_schedule():
    cache = StakingRewardRetryCache()
    record_ids = cache.smembers()
    if not record_ids:
        return

    record_ids = [int(i) for i in record_ids]
    records = StakingRewardHistory.query.filter(
        StakingRewardHistory.id.in_(record_ids)
    ).all()
    failed_assets = set()
    ended_ids = []
    for r in records:
        r: StakingRewardHistory
        if now() - r.reward_at > timedelta(days=2):
            current_app.logger.error(f"staking reward retry transfer timeout user_id: {r.user_id} asset: {r.asset}. set failed.")
            r.status = StakingRewardHistory.Status.FAILED
            db.session.commit()
            ended_ids.append(r.id)
            continue

        if not StakingOperation(r.asset).retry_send_reward(r):
            failed_assets.add(r.asset)
        else:
            ended_ids.append(r.id)
    if ended_ids:
        cache.srem(*ended_ids)
    if failed_assets:
        msg = f"staking reward send error, still failed after retry. please check. failed assets: {failed_assets}"
        current_app.logger.error(msg)
        send_alert_notice(msg, config["ADMIN_CONTACTS"]["web_notice"])


@celery_task()
@lock_call(with_args=True)
def update_staking_user_summary_task(asset, ts):
    reward_at = timestamp_to_datetime(ts)
    last_record = StakingUserSummary.query.filter(
        StakingUserSummary.last_reward_at == reward_at,
        StakingUserSummary.asset == asset).first()
    if last_record:
        return
    records = StakingRewardHistory.query.filter(
        StakingRewardHistory.reward_at == reward_at,
        StakingRewardHistory.asset == asset,
    ).all()
    sum_records = StakingUserSummary.query.filter(
        StakingUserSummary.asset == asset).all()
    record_map = {item.user_id: item for item in sum_records}
    price_map = AssetPrice.get_close_price_map((reward_at - timedelta(days=1)).date())
    for item in records:
        item: StakingRewardHistory
        summary: StakingUserSummary
        summary = record_map.get(item.user_id)
        if not summary:
            continue
        reward = item.amount
        reward_usd = quantize_amount(item.amount * price_map.get(asset, 0), PrecisionEnum.CASH_PLACES)
        summary.daily_reward = reward
        summary.daily_reward_usd = reward_usd
        summary.total_reward += reward
        summary.total_reward_usd += reward_usd
        summary.last_reward_at = reward_at
    db.session.commit()

def _update_staking_statistics(asset: str, report_at: datetime):
    
    def _get_income_rate_map() -> dict[str, Decimal]:
        
        last_record = DailyStakingReport.query.order_by(
            DailyStakingReport.report_date.desc()
        ).first()
        last_report_at = last_record.report_date
        records = DailyStakingReport.query.filter(
            DailyStakingReport.report_date == last_report_at,
        ) .with_entities(
            DailyStakingReport.asset,
            DailyStakingReport.income_rate
        ).all()
        return dict(records)

    data = StakingStatisticsCache(asset).read()
    if data:
        data = json.loads(data)
        last_report_at = timestamp_to_datetime(data['report_at'])
        if last_report_at == report_at:
            return
    client = WalletClient()
    summary = client.get_staking_summary(asset)
    balance_ts = int(report_at.timestamp())
    balances = StakingOperation(asset).get_snapshot_balance(balance_ts)
    balances = [item for item in balances if item['asset'] == asset]
    balance_user_ids = set(item['user_id'] for item in balances)
    staking_users = StakingUserSummary.query.filter(
        StakingUserSummary.asset == asset,
    ).with_entities(
        StakingUserSummary.user_id,
        StakingUserSummary.staking_amount,
        StakingUserSummary.unstaking_amount
    ).all()
    pending_staking_user_ids = {item.user_id for item in staking_users if item.staking_amount > 0}
    pending_unstaking_user_ids = {item.user_id for item in staking_users if item.unstaking_amount > 0}
    user_balance_map = defaultdict(Decimal)
    for item in balances:
        user_balance_map[item['user_id']] = item['balance']
    for item in staking_users:
        user_balance_map[item.user_id] -= item.unstaking_amount
    user_balance_map = {k: v for k, v in user_balance_map.items() if v > 0}
    effective_system_amount = summary['activated_staking_amount']
    pool = StakingPool.query.filter(StakingPool.asset == asset).first()

    user_pending_unstaking = sum(item.unstaking_amount for item in staking_users)
    user_pending_staking = sum(item.staking_amount for item in staking_users)
    bufsize = effective_system_amount + summary['pending_staking_amount'] - summary['pending_unstaking_amount'] \
        - (pool.amount + user_pending_staking - pool.unstaking_amount)
    target_bufsize = get_asset_config(asset).staking_bufsize
    income_rate_map = _get_income_rate_map()
    data = dict(
        asset=asset,
        report_at=int(report_at.timestamp()),
        income_rate=income_rate_map.get(asset, '0'),
        effective_user_amount=pool.amount - user_pending_unstaking,
        total_user_amount=pool.amount + user_pending_staking,
        system_pending_staking=summary['pending_staking_amount'],
        system_pending_unstaking=summary['pending_unstaking_amount'],
        system_staking=summary['staking_amount'],
        user_pending_staking=user_pending_staking,
        user_pending_unstaking=user_pending_unstaking,
        total_user_count=len(balance_user_ids | pending_staking_user_ids),
        effective_user_count=len(user_balance_map),
        staking_user_count=len(pending_staking_user_ids),
        unstaking_user_count=len(pending_unstaking_user_ids),
        system_effective_amount=summary['activated_staking_amount'],
        bufsize=bufsize,
        target_bufsize=target_bufsize,
    )
    for k, v in data.items():
        if isinstance(v, Decimal):
            data[k] = amount_to_str(v)
    StakingStatisticsCache(asset).save(json.dumps(data))

@scheduled(crontab(hour='*/1', minute='10,20,40,50'), queue=CeleryQueues.STATISTIC)
@lock_call()
def update_staking_statistics_schedule():
    accounts = StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN).all()
    current_ts = current_timestamp(to_int=True)
    report_ts = current_ts - current_ts % 1800
    report_at = timestamp_to_datetime(report_ts)
    for account in accounts:
        _update_staking_statistics(account.asset, report_at)


@scheduled(crontab(hour='1,2', minute='10,20,40,50'))
@lock_call()
def update_staking_income_record_schedule():
    
    PROCCESS_DAYS = 7

    def _proccess_income(all_assets):
        def _update_income_record(asset, report_date):
            start_time = date_to_datetime(report_date) - timedelta(days=1)
            end_time = date_to_datetime(report_date)
            try:
                amount = client.get_staking_reward(asset, start_time, end_time)['amount']
            except Exception as e:
                current_app.logger.error(f"update staking income record get staking reward error: {asset} {e!r}")
                return
            if amount == 0:
                current_app.logger.warning(f"update staking income record, income equals zero {report_date} {asset}")
                return
            record = StakingIncomeRecord(
                report_date=report_date,
                settle_date=today(),
                asset=asset,
                amount=amount,
                type=StakingIncomeRecord.Type.INCOME,
            )
            db.session.add(record)

        client = WalletClient()
        today_ = today()
        history_date = today_ - timedelta(days=PROCCESS_DAYS)
        records = StakingIncomeRecord.query.filter(
            StakingIncomeRecord.report_date >= history_date,
            StakingIncomeRecord.report_date <= today_,
            StakingIncomeRecord.type == StakingIncomeRecord.Type.INCOME
        ).with_entities(
            StakingIncomeRecord.report_date,
            StakingIncomeRecord.asset,
        ).all()
        infos = set()
        missing_infos = set()
        for r in records:
            infos.add((r.report_date, r.asset))
        
        curr_date = history_date
        while curr_date <= today_:
            for asset in all_assets:
                if (curr_date, asset) not in infos:
                    missing_infos.add((curr_date, asset))
            curr_date += timedelta(days=1)
        for report_date, asset in missing_infos:
            _update_income_record(asset, report_date)
        db.session.commit()

    def _proccess_payment(all_assets):
        today_ = today()
        history_date = today_ - timedelta(days=PROCCESS_DAYS)
        records = StakingIncomeRecord.query.filter(
            StakingIncomeRecord.report_date >= history_date,
            StakingIncomeRecord.report_date <= today_,
            StakingIncomeRecord.type == StakingIncomeRecord.Type.PAYMENT
        ).with_entities(
            StakingIncomeRecord.report_date,
            StakingIncomeRecord.asset
        ).all()

        reward_history = StakingRewardHistory.query.filter(
            StakingRewardHistory.reward_at >= history_date,
            StakingRewardHistory.reward_at <= today_,
        ).group_by(
            StakingRewardHistory.reward_at,
            StakingRewardHistory.asset,
        ).with_entities(
            StakingRewardHistory.reward_at,
            StakingRewardHistory.asset,
            func.sum(StakingRewardHistory.amount).label('amount')
        ).all()
        reward_map = defaultdict(Decimal)
        for item in reward_history:
            reward_map[(item.reward_at.date(), item.asset)] = item.amount
        infos = set()
        missing_infos = set()
        for r in records:
            infos.add((r.report_date, r.asset))
        
        curr_date = history_date
        while curr_date <= today_:
            for asset in all_assets:
                if (curr_date, asset) not in infos:
                    missing_infos.add((curr_date, asset))
            curr_date += timedelta(days=1)
        for report_date, asset in missing_infos:
            amount = reward_map[(report_date, asset)]
            if amount == 0:
                current_app.logger.warning(f"proccess staking payment, no reward history found for {report_date} {asset}")
                continue
            record = StakingIncomeRecord(
                report_date=report_date,
                settle_date=today_,
                asset=asset,
                amount=amount,
                type=StakingIncomeRecord.Type.PAYMENT,
            )
            db.session.add(record)
        db.session.commit()
        

    assets = [account.asset for account in StakingAccount.query.filter(StakingAccount.status == StakingAccount.Status.OPEN).all()]
    _proccess_income(assets)
    _proccess_payment(assets)

