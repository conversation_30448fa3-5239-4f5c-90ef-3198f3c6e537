# -*- coding: utf-8 -*-
from datetime import timedelta
from typing import List

from celery.schedules import crontab
from flask import current_app

from app.common import CeleryQueues
from app.business import lock_call, LockAssetHelper
from app.models import LockedAssetBalance
from app.utils import scheduled, now, route_module_to_celery_queue


route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


@scheduled(crontab(minute='*/1'))
@lock_call()
def lock_asset_schedule():
    model = LockedAssetBalance
    day_limit = 15
    time_delta = timedelta(minutes=3)
    rows: List[model] = model.get_to_lock_rows(
        start_time=now() - timedelta(days=day_limit),
        end_time=now() - time_delta,
        businesses=[model.Business.ADMIN, model.Business.ONCHAIN]
    )
    for r in rows:
        try:
            if r.lock_type == model.OpType.LOCK:
                LockAssetHelper.lock(r.business, r.business_id, r.user_id, r.asset, r.amount, callback=True)
            elif r.lock_type == model.OpType.ADD_AND_LOCK:
                LockAssetHelper.add_and_lock(r.business, r.business_id, r.user_id, r.asset, r.amount, callback=True)
        except Exception as e:
            current_app.logger.error(f"lock asset error: {e}")


@scheduled(crontab(minute='*/1'))
@lock_call()
def unlock_asset_schedule():
    model = LockedAssetBalance
    day_limit = 15
    time_delta = timedelta(minutes=3)
    rows: List[model] = model.get_to_unlock_rows(
        start_time=now() - timedelta(days=day_limit),
        end_time=now() - time_delta,
    )
    for r in rows:
        try:
            if r.unlock_type == model.OpType.UNLOCK_AND_SUB:
                LockAssetHelper.unlock_and_sub(r.business, r.business_id, r.user_id, callback=True)
            else:
                LockAssetHelper.unlock(r.business, r.business_id, r.user_id, callback=True)
        except Exception as e:
            current_app.logger.error(f"unlock asset error: {e}")
