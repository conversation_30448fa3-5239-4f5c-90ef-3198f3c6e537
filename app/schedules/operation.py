# -*- coding: utf-8 -*-
import datetime
from collections import defaultdict
from datetime import timedelta, date
from decimal import Decimal
from typing import Dict, Iterable, List, Set, Callable
from urllib.parse import urlparse, urlunparse
import json
from bs4 import BeautifulSoup
from celery.schedules import crontab
from flask import current_app
from flask_babel import gettext
from sqlalchemy import func

from app.business.utils import yield_query_records_by_time_range
from app.business.activity.trade import TradeActivityBusiness
from app.business.external_dbs import PerpetualSummaryDB, TradeSummaryDB
from app.business.pledge.helper import get_loan_asset_info
from app.business.push_statistic import EmailPushUserParser, \
    PUSH_REPORT_MAP
from app.caches.push import AppAutoPushReadCache, StrategyPushReadCache
from app.common.constants import PerpetualMarketType, TradeBusinessType
from app.models.amm import UserLiquidity
from app.models.daily import DailyPerpetualTradeReport, DailySpotTradeReport, DailyPushStrategyReport
from app.models.margin import MarginLoanOrder
from app.models.operation import (
    ActivityBlackList,
    AppAutoPushHistory,
    AssetMarketOfflineEmailPush,
    TradeRankActivity,
    TradeRankActivityJoinUser,
    TradeRankActivityStatistics,
    AutoPushHistory,
    TradeRankActivityUserInfo,
    AutoPushStrategySendHistory,
    AutoPushStrategy,
    P2pActivity, OperationTemplate, OperationTemplateContent, TradeRankActivityDetail
)
from app.models.perpetual import PerpetualMarket
from app.models.pledge import PledgePosition
from app.models.spot import Market
from app.models.user import SubAccount
from app.models.user_tag import UserTag
from app.models.wallet import AssetPrice, DepositWithdrawalPopupWindow, \
    DepositWithdrawalPopupWindowContent, EDDAudit, DepositAudit
from app.models.message import Message, MessagePush
from app.models.mongo.announcement import AnnouncementArticleMySQL as AnnouncementArticle
from app.utils.amount import amount_to_str, quantize_amount
from app.utils.date_ import current_timestamp, date_to_datetime, timestamp_to_datetime, today

from ..assets import asset_to_chains, get_asset_chain_config, chain_to_assets
from ..business import lock_call, ServerClient, TradeLogDB, PerpetualLogDB, CacheLock, \
    LockKeys
from ..business.activity.notice import ActivityAutoNotice
from ..business.clients.biz_monitor import BizMonitorClient, biz_monitor
from ..business.operation import close_protect_duration, ActivityAutoPushHelper
from ..business.deposit_audit import DepositAuditBusiness
from ..business.push import send_resume_deposit_withdrawal, send_mobile_push_to_user_ids, get_url_users_mapping
from ..business.email import batch_send_big_push_email, batch_send_push_email, send_resume_deposit_withdrawal_notice_email, \
    send_internal_user_email, batch_send_system_business_push_email, send_edd_audit_email
from ..business.alert import send_telegram_message
from ..business.push_base import PushBusinessHandler, PushTaskIdManager
from ..business.push_strategy import AutoPushStrategyDelegate
from ..business.user_tag import get_tag_read_table
from ..business.zendesk import get_zendesk_config_list, upload_zendesk_img_link
from ..caches import MarketCache
from ..caches.admin import ZendeskArticleCache
from ..caches.deposits import (
    DepositMaintainInfoCache,
    DepositMaintainSubscriberCache,
    WithdrawalMaintainInfoCache,
    WithdrawalMaintainSubscriberCache,
)
from ..caches.operation import AnnouncementCache, \
    AppQuickEntranceListCache, \
    ShortLinkInfoCache, BlogDisplayCache, \
    NewerGuideCache, CurrentPortraitHashCache, NotificationBarCache, CharityBannerCache, \
    CharityActivitiesCache, \
    TipBarCache, \
    TelegramPushedAnnouncementCache, MarketBannerCache, AssetCirculationHistoryCache, \
    PopularBlogCache, UserRelationAsyncExportCache, PageInsetCache, \
    CharityVideoCache, AppActivateCache, \
    TradeRankActivityNoticeCache, CharityFootprintCache, PerpetualActivityCache, InsetConfigCache, \
    NewAppActivateCache, MessagePushContentCache, \
    ReferralActivityBannerCache, DynamicFallingCache, P2pActivityBannerCache, SNSConfCache, CBoxPromotionCache, \
    CBoxThemeCache, AssetMaintainConfigCache
from ..caches.spot import OnlineMarketAssetCache
from ..caches.system import TempMaintainCache, MarketMaintainCache
from ..caches.user import UserConfigKeyCache, UserVisitPermissionCache
from ..common import Language, CeleryQueues, WebPushChannelType, WebPushMessageType, IncreaseEvent, EventTag
from ..common.push import AppPushBusiness
from ..config import config
from ..exceptions import InvalidArgument
from ..models import User, db, EmailPush, AmmMarket, MarginAccount, AssetTagRelation, EmailPushUnsubscription, \
    AssetTag, CoinInformation, TelegramGroupBot, AppPush, AppPushContent, TempMaintain, PeriodType, MonitorEvent
from ..models import UserPreference as UserPreferenceModel
from ..utils import (
    RESTClient, str_to_datetime, datetime_to_str, batch_iter
)
from ..utils import scheduled, now, route_module_to_celery_queue, celery_task
from ..utils.push import MobilePusher, PushType
from ..utils.date_ import convert_datetime
from ..utils.parser import JsonEncoder
from ..utils.export import export_xlsx_with_sheet

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


@scheduled(crontab(minute='*/5'))
@lock_call()
def send_email_pushes():
    """
    如果邮件推送 卡在推送中 或 虽然已经推送完，但是有大量用户没收到推送。

    则只需要将邮件状态修改为 AUDITED 即可，会自动过滤掉已经推送的用户，避免重复推送。
    """
    pushes = EmailPush.list_pushable()
    for row in pushes:
        row.status = EmailPush.Status.PROCESSING
    db.session.commit()

    login_disabled_user_ids = UserConfigKeyCache.get_login_disabled_by_admin_ids()
    visit_permission_user_ids = UserVisitPermissionCache().hgetall()
    
    user_lang_map = {}
    for row in pushes:
        row: EmailPush
        user_ids = row.get_target_users()
        row.update_users()
        if not user_ids:
            if row.type == EmailPush.Type.OFFLINE_ASSET:
                row.status = EmailPush.Status.AUDITED
            else:
                row.status = EmailPush.Status.FAILED
            row.updated_at = now()
            db.session.commit()
            continue
        row.set_user_ids(list(row.users))
        user_id_to_email = _get_user_emails(user_ids)
        contents = {c.lang: c for c in row.contents}

        risk_kwargs = row.get_system_template_kwargs()
        lang_pushes = defaultdict(set)
        system_push_ids = set()
        
        # 批量查询尚未查询过的用户语言偏好
        uncached_user_ids = [uid for uid in user_ids if uid not in user_lang_map]
        if uncached_user_ids:
            for ch_ids in batch_iter(uncached_user_ids, 10000):
                ch_rows = UserPreferenceModel.query.filter(
                    UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
                    UserPreferenceModel.user_id.in_(ch_ids),
                    UserPreferenceModel.key == 'language',
                ).with_entities(
                    UserPreferenceModel.user_id,
                    UserPreferenceModel.value,
                ).all()
                for r in ch_rows:
                    user_lang_map[r.user_id] = r.value
        
        for user_id in user_ids:
            if user_id not in user_id_to_email:
                continue
            if user_id in login_disabled_user_ids:
                continue
            if permission_user := visit_permission_user_ids.get(str(user_id)):
                if permission_user != UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
                    continue
            
            if risk_kwargs:
                if risk_kwargs['business'] != 'RISK_SCREEN':
                    # 当前只支持风险筛查
                    continue
                if not risk_kwargs['template_kwargs'].get(user_id):
                    continue
                system_push_ids.add(user_id)
                continue
            
            lang_name = user_lang_map.get(user_id)
            if not lang_name:
                continue
            lang = getattr(Language, lang_name)
            content = contents.get(lang)
            if not content or not content.title or not content.content:
                continue
            if not user_id_to_email.get(user_id):
                continue

            lang_pushes[lang].add(user_id)
        row.status = EmailPush.Status.FINISHED
        row.updated_at = now()
        row.finished_at = now()
        db.session.commit()
        
        # 如果推送人数超过1万，则使用慢速推送队列
        BIG_LIMIT = 10000
        is_big = sum(len(i) for i in lang_pushes.values()) >= BIG_LIMIT
        BATCH_SIZE = 1000
        for lang, user_ids in lang_pushes.items():
            for batch_ids in batch_iter(user_ids, BATCH_SIZE):
                try:
                    if is_big:
                        batch_send_big_push_email.delay(row.id, batch_ids, lang.value)
                    else:
                        batch_send_push_email.delay(row.id, batch_ids, lang.value)
                except Exception as e:
                    current_app.logger.error(f"{row.id} send_push_email {lang} error: {e}")

        for batch_ids in batch_iter(system_push_ids, 10000):
            try:
                batch_send_system_business_push_email.delay(row.id, batch_ids)
            except Exception as e:
                current_app.logger.error(f"{row.id} send_push_email_by_system_business error: {e}")


def _get_user_emails(user_ids: Iterable[int]) -> Dict[int, str]:
    step = 10000
    user_ids = list(user_ids)
    id_to_email = {}
    for start in range(0, len(user_ids), step):
        id_to_email.update(
            User.query
                .filter(User.id.in_(user_ids[start:start + step]))
                .with_entities(User.id, User.email))
    return id_to_email


@scheduled(crontab(minute=37, hour=6))
@lock_call()
def update_expired_email_pushes():
    """把太长时间仍未推送完成的记录标记为失败"""
    rows = EmailPush.query.filter(
        EmailPush.push_time < (now() - timedelta(days=7)),
        EmailPush.push_type == EmailPush.Type.NORMAL_PUSH,
        EmailPush.status.in_((
            EmailPush.Status.AUDITED,
            EmailPush.Status.PROCESSING,
        ))).with_entities(EmailPush.id).all()
    for row in rows:
        EmailPush.query.filter(EmailPush.id == row.id).update({'status': EmailPush.Status.FAILED},
                                                              synchronize_session=False)
    db.session.commit()


@scheduled(crontab(hour='1', minute='0'))
@lock_call()
def update_offline_asset_market_push_status(record_id=None, refresh=False, force=False):

    def _get_user_ids(push) -> List[int]:
        ts = current_timestamp(to_int=True)
        key = json.loads(push.key)
        ts -= ts % 3600
        
        def _asset_user_ids(ts):
            table = TradeLogDB.slice_balance_table(ts)
            if not table:
                ts -= 3600
                table = TradeLogDB.slice_balance_table(ts)
            
            asset_chains = asset_to_chains()
            min_amount_map = defaultdict(Decimal)
            for k in key:
                min_amount = Decimal('inf')
                for c in asset_chains[k]:
                    conf = get_asset_chain_config(k, c)
                    min_amount_map[k] = min(conf.min_withdrawal_amount, min_amount)
            user_ids = set()
            key_str = '","'.join(key)
            where = f'asset in ("{key_str}")'
            records = table.select('user_id, asset, balance', where=where)
            for r in records:
                user_id, asset, balance = r
                if balance >= min_amount_map[asset]:
                    user_ids.add(user_id)
            markets = Market.query.filter(
                Market.base_asset.in_(key)).with_entities(Market.name).all()
            markets = [m.name for m in markets]
            market_str = '","'.join(markets)
            order_table = TradeLogDB.slice_order_table(ts)
            records = []
            records.extend(order_table.select('distinct user_id', 
                                              where=f'market in ("{market_str}")'))
            stop_table = TradeLogDB.slice_stop_order_table(ts)
            records.extend(stop_table.select('distinct user_id', 
                                              where=f'market in ("{market_str}")'))
            user_ids |= {r[0] for r in records}

            markets = PerpetualMarket.query.filter(
                PerpetualMarket.base_asset.in_(key)).with_entities(PerpetualMarket.name).all()
            if not markets:
                # server special user id
                user_ids -= {0}
                return list(user_ids)
            markets = [m.name for m in markets]
            market_str = '","'.join(markets)
            ts = current_timestamp(to_int=True)
            ts -= ts % 3600
            table = PerpetualLogDB.slice_balance_table(ts)
            if not table:
                ts -= 3600
                table = PerpetualLogDB.slice_balance_table(ts)
            records = table.select('distinct user_id', 
                                    where=where)
            records = list(records)
            position_table = PerpetualLogDB.slice_position_table(ts)
            records.extend(position_table.select('distinct user_id', 
                                                 where=f'market in ("{market_str}")'))
            order_table = PerpetualLogDB.slice_order_table(ts)
            records.extend(order_table.select('distinct user_id', 
                                              where=f'market in ("{market_str}")'))
            stop_table = PerpetualLogDB.slice_stop_order_table(ts)
            records.extend(stop_table.select('distinct user_id', 
                                             where=f'market in ("{market_str}")'))
            user_ids |= {r[0] for r in records}
            
            # server special user id
            user_ids -= {0}
            return list(user_ids)

        def _perpetual_market_user_ids(ts):
            key_str = '","'.join(key)

            position_table = PerpetualLogDB.slice_position_table(ts)
            if not position_table:
                ts -= 3600
                position_table = PerpetualLogDB.slice_position_table(ts)
            records = position_table.select('distinct user_id', 
                                            where=f'market in ("{key_str}")')
            records = list(records)
            order_table = PerpetualLogDB.slice_order_table(ts)
            records.extend(order_table.select('distinct user_id', 
                                              where=f'market in ("{key_str}")'))
            stop_table = PerpetualLogDB.slice_stop_order_table(ts)
            records.extend(stop_table.select('distinct user_id', 
                                             where=f'market in ("{key_str}")'))
            user_ids = {r[0] for r in records}

            # server special user id
            user_ids -= {0}
            return list(user_ids)

        def _margin_market_user_ids(ts):
            key_str = '","'.join(key)
            user_ids = MarginLoanOrder.query.filter(
                MarginLoanOrder.market_name.in_(key),
                MarginLoanOrder.status.in_([MarginLoanOrder.StatusType.PASS,
                                            MarginLoanOrder.StatusType.ARREARS,
                                            MarginLoanOrder.StatusType.BURST])
            ).with_entities(
                MarginLoanOrder.user_id.distinct().label('user_id')).all()
            user_ids = {item.user_id for item in user_ids}
            order_table = TradeLogDB.slice_order_table(ts)
            if not order_table or not order_table.exists():
                ts -= 3600
                order_table = TradeLogDB.slice_order_table(ts)
            records = []
            if order_table:
                records = order_table.select('distinct user_id', 
                                            where=f'market in ("{key_str}") AND account != 0')
                records = list(records)
            stop_table = TradeLogDB.slice_stop_order_table(ts)
            if stop_table:
                records.extend(stop_table.select('distinct user_id', 
                                                where=f'market in ("{key_str}") AND account != 0'))
                user_ids |= {r[0] for r in records}

            # server special user id
            user_ids -= {0}
            return list(user_ids)
            
        def _amm_market_user_ids(ts):
            user_ids = UserLiquidity.query.filter(
                UserLiquidity.liquidity > 0,
                UserLiquidity.market.in_(key),
            ).with_entities(UserLiquidity.user_id.distinct().label('user_id')).all()
            return [item.user_id for item in user_ids]
        

        def _pledge_asset_user_ids(ts):
            """ 下架质押币 """
            import json
            from collections import defaultdict
            from app.models.pledge import PledgeLoanHistory, PledgeHistory, PledgePosition
            from app.utils import batch_iter

            def _get_balance(ts, account_id, assets):
                ts = ts - ts % 3600
                table = TradeLogDB.slice_balance_table(ts)
                if not table:
                    ts -= 3600
                balances = TradeLogDB.get_user_balances(ts, account_id, assets)
                res = []
                for item in balances:
                    user_id, asset, _, amount = item
                    res.append(dict(
                        user_id=user_id,
                        asset=asset,
                        amount=amount,
                    ))
                return res

            borrowing_pos_rows = PledgePosition.query.filter(
                PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
            ).with_entities(
                PledgePosition.id,
                PledgePosition.user_id,
                PledgePosition.loan_asset,
            ).all()
            borrowing_pos_map = {i.id: i for i in borrowing_pos_rows}
            borrowing_pos_ids = set(borrowing_pos_map.keys())

            match_pos_ids = set()
            for ch_ids in batch_iter(borrowing_pos_ids, 2000):
                ch_loan_his_rows = PledgeLoanHistory.query.filter(
                    PledgeLoanHistory.position_id.in_(ch_ids),
                ).with_entities(
                    PledgeLoanHistory.position_id,
                    PledgeLoanHistory.pledge_data,
                ).all()
                for r in ch_loan_his_rows:
                    pledge_data = json.loads(r.pledge_data)
                    if set(key) & set(pledge_data):
                        match_pos_ids.add(r.position_id)

            for ch_ids in batch_iter(borrowing_pos_ids - match_pos_ids, 2000):
                ch_pledge_his_rows = PledgeHistory.query.filter(
                    PledgeHistory.position_id.in_(ch_ids),
                ).with_entities(
                    PledgeHistory.position_id,
                    PledgeHistory.pledge_data,
                ).all()
                for r in ch_pledge_his_rows:
                    pledge_data = json.loads(r.pledge_data)
                    if set(key) & set(pledge_data):
                        match_pos_ids.add(r.position_id)

            loan_asset_pos_rows_map = defaultdict(set)
            for pos_id in match_pos_ids:
                _pos = borrowing_pos_map[pos_id]
                loan_asset_pos_rows_map[_pos.loan_asset].add(_pos)
            match_user_ids = set()
            for _loan_asset, pos_rows in loan_asset_pos_rows_map.items():
                user_ids = {i.user_id for i in pos_rows}
                account_id = get_loan_asset_info(_loan_asset).account_id
                balances = _get_balance(ts, account_id, key)
                for item in balances:
                    if item['user_id'] in user_ids and item['amount'] > 0 and item['asset'] in key:
                        match_user_ids.add(item['user_id'])
            return list(match_user_ids)

        def _pledge_loan_asset_user_ids(ts):
            records = PledgePosition.query.filter(
                PledgePosition.loan_asset.in_(key),
                PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES)
            ).with_entities(
                PledgePosition.user_id
            ).all()
            user_ids = {item.user_id for item in records}

            return list(user_ids)

        user_ids = locals()[f'_{push.type.name.lower()}_user_ids'](ts)
        res = set(user_ids)
        for ids in batch_iter(user_ids, 1000):
            tmp = SubAccount.query.filter(
                SubAccount.user_id.in_(ids),
            ).with_entities(SubAccount.main_user_id, SubAccount.user_id).all()
            res |= {item.main_user_id for item in tmp}
            res -= {item.user_id for item in tmp}
        return list(res)

    now_ = now()
    if record_id:
        pushes = AssetMarketOfflineEmailPush.query.filter(
                    AssetMarketOfflineEmailPush.id == record_id).all()
    else:
        pushes = AssetMarketOfflineEmailPush.query.filter(
            AssetMarketOfflineEmailPush.started_at <= now_ + timedelta(hours=23),
            AssetMarketOfflineEmailPush.ended_at > now_ - timedelta(hours=1)
        ).all()

    for p in pushes:
        p: AssetMarketOfflineEmailPush
        email_push = EmailPush.query.get(p.push_id)
        if email_push.status == EmailPush.Status.DELETED:
            continue
        if p.type == AssetMarketOfflineEmailPush.Type.ASSET and not force:
            if _not_in_check_days(now_, p.ended_at):
                continue
            if _is_first_attempt_close_to_started_at(p.started_at, p.ended_at):
                continue
        user_ids = set(_get_user_ids(p))
        login_disabled_users = UserConfigKeyCache.get_login_disabled_by_admin_ids()
        user_ids -= {item for item in login_disabled_users}
        unsubscribed_user_ids = EmailPushUserParser.get_unsubscribed_user_ids()
        if email_push.push_scope_type == EmailPush.PushScopeType.EXCLUDE_UNSUBSCRIBE:
            user_ids -= unsubscribed_user_ids
        if email_push.whitelist_enabled:
            user_whitelist = email_push.cached_user_whitelist
            user_ids -= set(user_whitelist)
        all_user_ids = push_user_ids = user_ids
        if not refresh:
            all_user_ids = set(p.cached_user_ids) | user_ids
            if p.type != AssetMarketOfflineEmailPush.Type.ASSET:   # 下架资产时对user_ids全量推送
                # 下面的代码，利用 set_user_ids 函数对 cached_user_ids 进行重复更新来实现防重复
                # 定时任务一天执行一次 第一次 cached_user_ids 为空，添加 user_ids，
                # 第二次 user_ids - set(email_push.cached_user_ids) 会将 cached_user_ids 置为其他用户
                # 第三天又会重新加入第一天的 user_ids
                push_user_ids = user_ids - set(email_push.cached_user_ids)  # 下架其他时，48小时内已推送的不再推送
        email_push.set_user_ids(push_user_ids)
        p.set_user_ids(list(all_user_ids))
        email_push.user_count = len(all_user_ids)
        email_push.subscribe_count = len(set(p.cached_user_ids) - unsubscribed_user_ids)
        if push_user_ids and email_push.status in (EmailPush.Status.FINISHED, EmailPush.Status.FAILED):
            email_push.status = EmailPush.Status.AUDITED
        email_push.push_time = max(now(), p.started_at)
        db.session.commit()


CHECK_DAYS = [90, 60, 30, 15, 7, 5, 3, 2, 1]


def _not_in_check_days(now_, ended_at):
    end_date = date(ended_at.year, ended_at.month, ended_at.day)
    now_date = date(now_.year, now_.month, now_.day)
    return (end_date-now_date).days not in CHECK_DAYS


def _is_first_attempt_close_to_started_at(started_at, ended_at):
    """
    下架币种推送创建时就会推送一次, 因此如果首次推送时间和创建时间相隔不到15天, 则取消第一次推送
    """
    if started_at >= ended_at or not CHECK_DAYS:
        return False
    start_date = started_at.date()
    end_date = ended_at.date()

    today_ = today()
    today_delta_ = (end_date - today_).days
    if today_delta_ < CHECK_DAYS[0]:
        return False

    delta_ = (end_date - start_date).days
    if abs(delta_ - CHECK_DAYS[0]) <= 15:
        return True
    return False


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_announcement_articles():
    for lang, article_lang in AnnouncementArticle.LANG_LOCALE_DICT.items():
        articles = AnnouncementArticle.latest_n(
            lang=Language(lang),
            num=30,
            only=['article_id', 'title', 'html_url', 'created_at', 'updated_at'],
        )
        if not articles:
            continue
        cache = AnnouncementCache(lang)
        cached_articles = cache.get_top_announcements()
        cached_articles = {x['id']: x for x in cached_articles}
        result = []
        for article in articles:
            # 公告首次出现的时间作为发布时间
            cached_article = cached_articles.get(article.article_id)
            if cached_article:
                created_at = str_to_datetime(cached_article['created_at'])
            else:
                _now = now()
                article_created_at = convert_datetime(article.created_at, "second")  # set tz
                # 防止过早的公告误当做新公告
                if cached_articles and _now - article_created_at < timedelta(days=7):
                    created_at = _now
                else:
                    created_at = article_created_at

            result.append(dict(
                id=article.article_id,
                title=article.title,
                url=article.html_url,
                created_at=created_at,
            ))
        result.sort(key=lambda x: x['created_at'], reverse=True)
        for x in result:
            x['created_at'] = datetime_to_str(x['created_at'], fmt="%Y-%m-%dT%H:%M:%SZ")
        cache.save_announcements(result)


def update_zendesk_article_category_schedule():
    """ 更新公告/帮助中心文章类别 """

    zendesk_config_list = get_zendesk_config_list()
    for zendesk_config in zendesk_config_list:
        _url = zendesk_config['url']
        _db = zendesk_config['db']
        _cache = zendesk_config['category_cache']
        client = RESTClient(_url)
        for lang, article_lang in _db.LANG_LOCALE_DICT.items():
            category_id_sections_map = defaultdict(list)
            sec_r = client.get(f'{article_lang}/sections', per_page=100)
            for _sec in sec_r["sections"]:
                category_id_sections_map[_sec['category_id']].append(
                    dict(
                        id=_sec['id'],
                        name=_sec['name'],
                        url=_sec['html_url'],
                        parent_section_id=_sec['parent_section_id'],
                    )
                )

            data_list = []
            cat_r = client.get(f'{article_lang}/categories', per_page=100)
            for _cty in cat_r['categories']:
                _c_data = dict(
                    id=_cty['id'],
                    name=_cty['name'],
                    url=_cty['html_url'],
                    sections=category_id_sections_map[_cty['id']],
                )
                data_list.append(_c_data)
            _cache(lang).save(json.dumps(data_list, cls=JsonEncoder))


def sync_zendesk_article_schedule(full_sync: bool = False):
    """ 同步Zendesk公告文章到MongoDB，full_sync：全量同步 """
    def _get_category_map(_lang: str):
        category_data = json.loads(_category_cache(lang).read())
        return {
            int(sec["id"]): int(first["id"])
            for first in category_data for sec in first["sections"]
        }

    def _fetch_inc_articles(_start_time: int, _locale: str):
        return client.get(api=inc_url, start_time=_start_time, locale=_locale, per_page=per_page)

    def _format_articles(_articles: list,
                         _sec_first_map: dict,
                         _lang: str,
                         _img_url_replace: bool = False):
        _res = []
        _lang_enum = Language(_lang)
        for _r in _articles:
            section_id = int(_r['section_id'])
            category_id = int(_sec_first_map.get(section_id, 0))
            if _r['draft']:
                status = _db.Status.DRAFT
            elif not category_id:  # 无有效分类不展示
                status = _db.Status.DRAFT
            elif _r.get('user_segment_id'):  # 不针对所有用户不展示
                status = _db.Status.DRAFT
            else:
                status = _db.Status.VALID
            body_html = _r['body'] or ''
            if _img_url_replace:
                body_html = BeautifulSoup(_r['body'] or '', 'html.parser')
                for img in body_html.find_all('img', attrs={'src': True}):
                    img['src'] = upload_zendesk_img_link(img['src'])

            _res.append(
                dict(
                    article_id=int(_r['id']),
                    position=int(_r['position']),
                    section_id=section_id,
                    category_id=category_id,
                    lang=_lang_enum,
                    title=_r['title'] or '',
                    body_html=str(body_html),
                    html_url=_r['html_url'],
                    is_top=_r['promoted'],
                    status=status,
                    created_at=str_to_datetime(_r['created_at']),
                    updated_at=str_to_datetime(_r['edited_at']),  # edited_at 内容修改时间
                )
            )
        return _res

    per_page = 1000
    inc_url = "incremental/articles"

    zendesk_config_list = get_zendesk_config_list()
    for zendesk_config in zendesk_config_list:
        auth = (zendesk_config['user'], zendesk_config['token'])
        client = RESTClient(zendesk_config['url'], auth=auth)
        _db = zendesk_config['db']
        img_url_replace = zendesk_config['img_url_replace']
        _category_cache = zendesk_config['category_cache']
        for lang, article_lang in _db.LANG_LOCALE_DICT.items():
            if full_sync:
                start_time = 0
            else:
                last_update_row = _db.latest_update(Language(lang), ['updated_at'])
                start_time = int(last_update_row.updated_at.timestamp()) if last_update_row else 0
            sec_first_map = _get_category_map(lang)
            inc_articles = []
            for _ in range(10):  # max 10 * 1000 article
                data = _fetch_inc_articles(start_time, article_lang)
                articles = data['articles']
                inc_articles.extend(_format_articles(articles, sec_first_map, lang, img_url_replace))
                start_time = data['end_time']
                if len(articles) < per_page or not data["next_page"]:
                    break
            for r in inc_articles:
                _db.create_or_update(r)
            current_app.logger.warning(f"sync_zendesk db {_db} lang {lang} save {len(inc_articles)} inc_articles")
    cache_ = ZendeskArticleCache()
    cache_.hmset({
        'timestamp': current_timestamp(to_int=True),
    })


@scheduled(crontab(minute="*/20"))
@lock_call()
def update_zendesk_article_schedule():
    # 先更新分类避免文章无分类不展示
    update_zendesk_article_category_schedule()
    sync_zendesk_article_schedule()


@scheduled(crontab(hour="*/1", minute='12'))
@lock_call()
def sync_zendesk_deleted_article_schedule():
    """ 同步被删除的Zendesk公告/帮助中心文章 """
    ts = current_timestamp(to_int=True) - 86400  # 查一天内的删除事件
    zendesk_config_list = get_zendesk_config_list()
    for zendesk_config in zendesk_config_list:
        auth = (zendesk_config['user'], zendesk_config['token'])
        base_url = urlunparse(urlparse(zendesk_config['url'])._replace(path='/'))  # noqa
        client = RESTClient(base_url, auth=auth)
        # https://support.zendesk.com/hc/en-us/articles/6120898366490-Where-can-I-see-my-deleted-articles-
        path = f"hc/api/v2/knowledge_events?start_time={ts}&types=article_removed&per_page=100"
        data = client.get(path)
        if data["knowledge_events"]:
            del_article_ids = [int(i['aggregate_id']) for i in data["knowledge_events"]]
            _db = zendesk_config['db']
            _db.delete_by_article_ids(del_article_ids)


@scheduled(crontab(minute='*/3'))
def send_telegram_announcement():
    rows = TelegramGroupBot.query.all()
    bot_configs = {}
    for row in rows:
        bot_configs.setdefault(row.lang.value, []).append(row)
    cache_cls = TelegramPushedAnnouncementCache
    now_ = now()
    for lang in cache_cls.LANGS:
        lang = lang.value
        bots = bot_configs.get(lang)
        if not bots:
            continue

        cache = AnnouncementCache(lang)
        articles = cache.get_top_announcements(limit=10)
        if not articles:
            continue

        pushed_cache = cache_cls(lang)
        pushed_article_ids = [int(id_) for id_ in pushed_cache.read()]
        for article in articles[::-1]:
            article_id = int(article['id'])
            created_at = str_to_datetime(article['created_at'])
            if now_ - created_at > timedelta(days=1):  # 防止上线时，推送以前的公告
                continue
            if article_id in pushed_article_ids:
                continue

            content = article['title'] + '\n' + article['url']
            for bot in bots:
                send_telegram_message.delay(content, bot.send_msg_url, bot.chat_id)
            pushed_article_ids.append(article_id)
        pushed_cache.save(pushed_article_ids[-10:])


class PushLimitHelper:

    def __init__(self, row: AppPush):
        business = self.get_push_business(row.push_type)
        self.handler = PushBusinessHandler(business)

    def get_limit_users(self) -> Set:
        return self.handler.get_limit_users()

    @staticmethod
    def get_push_business(push_type: AppPush.PushType) -> AppPushBusiness:
        if push_type == AppPush.PushType.ANNOUNCEMENT:
            push_business = AppPushBusiness.AnnouncementSubscribe
        elif push_type == AppPush.PushType.ACTIVITY:
            push_business = AppPushBusiness.ActivitySubscribe
        elif push_type == AppPush.PushType.BLOG:
            push_business = AppPushBusiness.BlogSubscribe
        else:
            push_business = None
        return push_business

    def set_pushed(self, user_ids: Set):
        self.handler.set_pushed(user_ids)


@scheduled(crontab(minute='*/5'))
@lock_call(ttl=24*60*60)
def send_app_pushes():
    row: AppPush
    user_slice_number = 20000
    uniq_key = 'app push:{push_id}'
    # 定向群组
    pushes = AppPush.list_pushable()
    for row in pushes:
        row.status = AppPush.Status.PROCESSING
    db.session.commit()

    pushes = AppPush.list_processing_pushable()
    for row in pushes:
        user_ids = row.subscribe_users
        row.subscribe_count = len(user_ids)
        if not user_ids:
            row.user_count = len(row.users)
            row.status = AppPush.Status.FAILED
            row.updated_at = now()
            db.session.commit()
            continue
        row.set_user_ids(list(row.users))
        contents = {c.lang: c for c in row.contents}
        user_lang_mapping = defaultdict(set)
        helper = PushLimitHelper(row)
        send_limit_users = helper.get_limit_users()
        user_ids -= send_limit_users
        for u_ids in batch_iter(user_ids, user_slice_number):
            lang_query = UserPreferenceModel.query.filter(
                UserPreferenceModel.key.in_(('language', 'app_language')),
                UserPreferenceModel.status == UserPreferenceModel.Status.VALID,
                UserPreferenceModel.user_id.in_(u_ids)
            ).with_entities(
                UserPreferenceModel.user_id,
                UserPreferenceModel.key,
                UserPreferenceModel.value
            ).all()
            user_lang_dic = defaultdict(dict)
            for v in lang_query:
                user_lang_dic[v.user_id][v.key] = v.value
            for user_id, lang_dic in user_lang_dic.items():
                if 'app_language' in lang_dic:
                    user_lang_mapping[getattr(Language, lang_dic['app_language'])].add(user_id)
                else:
                    user_lang_mapping[getattr(Language, lang_dic['language'])].add(user_id)
        final_lang_mapping = {
            lang: set()
            for lang in AppPushContent.AVAILABLE_LANGS if lang in contents
        }
        for lang, lang_user_ids in user_lang_mapping.items():
            if lang not in AppPushContent.AVAILABLE_LANGS:
                continue
            else:
                if lang in contents:
                    final_lang_mapping[lang] |= lang_user_ids
        row.user_count = len(row.users)
        db.session.commit()
        business_sent_users = set()
        send_user_ids = set(row.list_send_user_ids)
        push_task_manager = PushTaskIdManager.for_app_push(row.id)
        for lang, lang_user_ids in final_lang_mapping.items():
            lang_user_ids = set(lang_user_ids) - send_user_ids
            if len(lang_user_ids) == 0:
                continue
            if not push_task_manager.is_push_task_active():
                row.status = AppPush.Status.SUSPENDED
                db.session.commit()
                break
            content = contents[lang]
            url = content.url or row.url
            url_users_mapping = get_url_users_mapping(url, lang_user_ids)
            for r_url, _u_ids in url_users_mapping.items():
                for _lang_user_ids in batch_iter(_u_ids, 2000):
                    try:
                        # 其他的逻辑不宜放入到这个 try-catch 代码块中，因为如果其他的代码逻辑失败，会导致这里重复发送推送
                        result = send_mobile_push_to_user_ids(
                            [str(v) for v in _lang_user_ids],
                            content.content, content.title,
                            url=r_url,
                            ttl=row.ttl,
                            uniq_key=uniq_key.format(push_id=row.id),
                            extras=dict(business_push_id=row.id, push_type=PushType.MANUAL_PUSH.name),
                        )
                    except Exception as e:
                        current_app.logger.error(f"send app push error : {e!r}")
                        continue
                    if not result:
                        continue
                    task_id = result['data']['task_id']
                    push_task_manager.add_task_id(task_id)
                    avail_user_ids = {int(i) for i in result['data']['avail_user_ids']}
                    if avail_user_ids:
                        send_user_ids.update(avail_user_ids)
                        row.set_send_user_ids(list(send_user_ids))
                        row.send_user_count = len(send_user_ids)
                        db.session.commit()
                        business_sent_users.update(avail_user_ids)
        row.status = AppPush.Status.FINISHED
        db.session.commit()
        helper.set_pushed(business_sent_users)


@scheduled(crontab(minute='*/5'), queue=CeleryQueues.PUSH)
@lock_call()
def send_app_strategy_auto_push():
    """发送app自动策略推送"""
    AutoPushStrategyDelegate.run()


@scheduled(crontab(hour='2', minute='9'))
@lock_call()
def update_daily_strategy_push_send_report_schedule():
    """更新每日自动推送策略PUSH数据"""
    rec = DailyPushStrategyReport.query.order_by(DailyPushStrategyReport.report_date.desc()).first()
    if rec:
        start_date = rec.report_date - datetime.timedelta(days=10)  # 因广播发送数据会存在采集延迟，因此需要往前推10天，
        # 确保更新到最新数据
    else:
        start_date = datetime.date(2024, 7, 29)

    today_ = today()
    while start_date < today_:
        update_daily_strategy_push_send_report(start_date)
        start_date += datetime.timedelta(days=1)


def update_daily_strategy_push_send_report(report_date):
    start = date_to_datetime(report_date)
    end = start + datetime.timedelta(days=1)
    records = AutoPushStrategySendHistory.query.filter(
        AutoPushStrategySendHistory.send_at >= start,
        AutoPushStrategySendHistory.send_at < end
    ).with_entities(
        AutoPushStrategySendHistory.strategy_id,
        AutoPushStrategySendHistory.send_user_count,
        AutoPushStrategySendHistory.user_count,
        AutoPushStrategySendHistory.user_read_count,
    ).all()
    trigger_count_dic, send_user_count_dic, user_count_dic, user_read_count_dic = (
        defaultdict(int), defaultdict(int), defaultdict(int), defaultdict(int))
    strategies = set()
    for record in records:
        strategy_id = record.strategy_id
        strategies.add(strategy_id)
        trigger_count_dic[strategy_id] += 1
        send_user_count = record.send_user_count or 0   # 以下所有的值均为加总值，不去重
        send_user_count_dic[strategy_id] += send_user_count
        user_count = record.user_count or 0
        user_count_dic[strategy_id] += user_count
        user_read_count = record.user_read_count or 0
        user_read_count_dic[strategy_id] += user_read_count
    for strategy_id in strategies:
        row = DailyPushStrategyReport.get_or_create(strategy_id=strategy_id, report_date=report_date)
        row.trigger_count = trigger_count_dic[strategy_id]
        row.send_count = trigger_count_dic[strategy_id]
        row.user_count = user_count_dic[strategy_id]
        row.send_user_count = send_user_count_dic[strategy_id]
        row.user_read_count = user_read_count_dic[strategy_id]
        db.session.add(row)
    db.session.commit()


@scheduled(crontab(hour='1', minute='51'))
@lock_call()
def update_broadcast_records():
    """将fcm广播数据更新进发送历史（fcm数据更新有5天左右的延迟）"""
    report_map = MobilePusher().get_push_report_detail()
    history_dic = dict()
    for business_id, report_info in report_map.items():
        if 'auto_push_strategy_send_history' not in business_id:
            continue
        business_parse_lis = business_id.split(':')
        if len(business_parse_lis) != 2:
            continue
        history_dic[int(business_parse_lis[1])] = report_info
    history_ids = list(history_dic.keys())
    start = now() - datetime.timedelta(days=10)
    history_records = AutoPushStrategySendHistory.query.filter(
        AutoPushStrategySendHistory.id.in_(history_ids),
        AutoPushStrategySendHistory.send_at >= start
    ).all()   # 只更新最近10天的广播记录即可
    strategy_ids = {i.strategy_id for i in history_records}
    broadcast_strategies = AutoPushStrategy.query.filter(
        AutoPushStrategy.id.in_(strategy_ids),
        AutoPushStrategy.type == AutoPushStrategy.Type.PRICE_BROADCAST
    ).all()
    broadcast_strategy_ids = {i.id for i in broadcast_strategies}
    for history in history_records:
        history_id = history.id
        strategy_id = history.strategy_id
        if strategy_id not in broadcast_strategy_ids:
            continue
        map_ = history_dic[history_id]
        if not map_:
            continue
        send_count = sum(map_.values())
        history.send_user_count = send_count
        history.user_count = send_count
    db.session.commit()


@scheduled(crontab(hour='1', minute='41'))
@lock_call()
def update_strategy_push_read_schedule():
    """更新策略push打开数量"""
    cache = StrategyPushReadCache()
    history_count_dic = cache.get_recs()
    if not history_count_dic:
        return
    cache.clear()
    history_ids = list(history_count_dic.keys())
    histories = AutoPushStrategySendHistory.query.filter(
        AutoPushStrategySendHistory.id.in_(history_ids)
    ).all()
    for history in histories:
        count = history.user_read_count or 0
        new_count = history_count_dic[history.id]
        history.user_read_count = count + new_count
    db.session.commit()



@scheduled(crontab(minute='*/5'))
@lock_call()
def send_message_pushes():
    """ 执行站内信手动推送 """
    _now = now()
    _push_start = _now - timedelta(days=3)
    msg_pushes: List[MessagePush] = MessagePush.query.filter(
        MessagePush.status.in_([MessagePush.Status.AUDITED, MessagePush.Status.PROCESSING]),
        MessagePush.push_time <= _now,
        MessagePush.push_time >= _push_start,
    ).all()
    for row in msg_pushes:
        send_processing_message_push(row)


def send_processing_message_push(row: MessagePush):
    """ 执行站内信推送 """
    sbc_user_ids = row.subscribe_users
    row.subscribe_count = len(sbc_user_ids)
    if not sbc_user_ids:
        row.user_count = len(row.users)
        row.status = MessagePush.Status.FAILED
        row.updated_at = now()
        db.session.commit()
        return

    if row.whitelist_enabled:
        user_whitelist = row.cached_user_whitelist
        sbc_user_ids -= set(user_whitelist)

    MessagePushContentCache.reload_one(row.id)

    chunk_size = 10000
    send_user_ids = set(row.list_send_user_ids)
    for ch_user_ids in batch_iter(sbc_user_ids, chunk_size):
        ch_final_user_ids = set(ch_user_ids) - send_user_ids
        if len(ch_final_user_ids) == 0:
            continue

        for user_id in ch_final_user_ids:
            if not user_id:
                continue
            msg_row = Message.new_push_message(
                push_row=row,
                user_id=user_id,
                **row.jump_link_map,
            )
            db.session.add(msg_row)
        send_user_ids.update(ch_final_user_ids)
        row.set_send_user_ids(list(send_user_ids))
        row.send_user_count = len(send_user_ids)
        db.session.commit()

    group_users = row.users
    row.set_user_ids(list(group_users))
    row.user_count = len(group_users)
    row.status = MessagePush.Status.FINISHED
    row.finished_at = now()
    db.session.commit()
    current_app.logger.info(f"send_processing_message_push push_id:{row.id} total:{row.user_count}")


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_push_statistic():
    cls_list = PUSH_REPORT_MAP.values()
    for cls in cls_list:
        cls.refresh_report_field_to_db()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_app_auto_push_history_schedule():
    records = AppAutoPushHistory.query.filter(
        AppAutoPushHistory.created_at >= now() - timedelta(days=1)
    ).all()
    for item in records:
        if count:= AppAutoPushReadCache(item.id).pfcount():
            item.read_count = count
    db.session.commit()
    if now().hour != 1:
        return
    report_map = MobilePusher().get_push_report_detail()
    report_map = {int(k): v for k, v in report_map.items() if str(k).isdigit()}
    ids = list(report_map)
    records = AppAutoPushHistory.query.filter(    
        AppAutoPushHistory.id.in_(ids),
    ).all()
    for record in records:
        map_ = report_map.get(record.id)
        if not map_:
            continue
        count = sum(map_.values())
        record.send_count = max(record.send_count, count)
    db.session.commit()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_app_quick_entrance_list_cache():
    AppQuickEntranceListCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_app_activate_cache():
    AppActivateCache.reload()
    NewAppActivateCache.reload()


@scheduled(crontab(minute='*/10'))
def update_short_link_info_cache_schedule():
    """ 短链接信息缓存 """
    ShortLinkInfoCache.reload()


@scheduled(crontab(hour='*/6', minute=0))
def update_blog_display():
    BlogDisplayCache.reload()


@scheduled(crontab(hour='*/2', minute=0))
def update_popular_blog():
    PopularBlogCache.reload()


@scheduled(crontab(hour='*/6', minute=0))
def update_newer_guide_schedule():
    NewerGuideCache.reload()


@scheduled(crontab(minute="*/1"))
def update_market_maintain_info():
    MarketMaintainCache().reload()


@scheduled(crontab(minute="*/1"))
def update_temp_maintain_info():
    TempMaintainCache().reload()
    close_protect_status_of_expired_maintain()


def close_protect_status_of_expired_maintain():
    now_ = now()
    recs = TempMaintain.query.filter(
        TempMaintain.status == TempMaintain.Status.VALID,
        TempMaintain.protect_duration_updated.is_(False)
    ).all()
    for row in recs:
        if protect_duration := row.protect_duration:
            deadline = row.ended_at + datetime.timedelta(minutes=protect_duration)
        else:
            deadline = row.ended_at
        if now_ >= deadline:
            close_protect_duration(row.scope)
            row.protect_duration_updated = True
    db.session.commit()


@scheduled(crontab(minute="*/1"))
def notice_temp_maintain_info():
    """临时维护前定时广播"""
    start_time = now()
    end_time = start_time + timedelta(minutes=10)
    rows = TempMaintain.query.filter(
        TempMaintain.status == TempMaintain.Status.VALID,
        TempMaintain.started_at > start_time,
        TempMaintain.started_at < end_time,
    ).all()
    client = ServerClient()
    for row in rows:
        client.notice_user_message(0, WebPushChannelType.NORMAL.value,
                                   dict(id=row.id, msg_type=WebPushMessageType.MAINTAIN.value))

        notice_api_users(client, row)   # 用于api用户广播，与web区分开channel_id


def notice_api_users(client, row):
    protect_duration = row.protect_duration or 0
    ended_at = row.ended_at
    protect_duration_start = ended_at
    protect_duration_end = ended_at + datetime.timedelta(minutes=protect_duration)
    scope = [row.scope.name] if row.scope != TempMaintain.MaintainScope.ALL else [
        TempMaintain.MaintainScope.SPOT.name, TempMaintain.MaintainScope.PERPETUAL.name]
    client.notice_user_message(0, WebPushChannelType.API_MAINTAIN.value,
                               dict(
                                   started_at=int(row.started_at.timestamp()),
                                   ended_at=int(ended_at.timestamp()),
                                   scope=scope,
                                   protect_duration_start=int(protect_duration_start.timestamp()),
                                   protect_duration_end=int(protect_duration_end.timestamp()),
                               ))


@scheduled(crontab(hour='*/1', minute=0))
def auto_update_tag():
    """自动更新AMM/杠杆标签"""
    info_assets = CoinInformation.query.filter(CoinInformation.status == CoinInformation.Status.VALID) \
        .with_entities(CoinInformation.code).all()  # 有币种资料的币种才能加入标签
    info_assets_set = {x[0] for x in info_assets}
    amm_assets_set = _get_data_from_amm_market(info_assets_set)
    margin_assets_set = _get_data_from_margin_market(info_assets_set)
    amm_tag_id, ori_amm_tag_set = _get_tag_set(AssetTag.AMMTag)
    margin_tag_id, ori_margin_set = _get_tag_set(AssetTag.MarginTag)
    _update_tag(amm_assets_set, ori_amm_tag_set, amm_tag_id)
    _update_tag(margin_assets_set, ori_margin_set, margin_tag_id)


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_ton_deposit_withdrawal_popup_window():
    """自动添加TON链充提弹窗"""

    chain = 'TON'
    asset_list = set(chain_to_assets(chain))
    exist_asset_query = DepositWithdrawalPopupWindow.query.filter(
        DepositWithdrawalPopupWindow.asset.in_(asset_list),
        DepositWithdrawalPopupWindow.chain == chain,
    ).with_entities(
        DepositWithdrawalPopupWindow.asset
    ).all()
    exist_asset_list = {i.asset for i in exist_asset_query}

    need_popup_asset = asset_list - exist_asset_list
    if not need_popup_asset:
        return

    coin_query = CoinInformation.query.filter(
        CoinInformation.status == CoinInformation.Status.VALID,
        CoinInformation.code.in_(need_popup_asset),
    ).with_entities(
        CoinInformation.code,
        CoinInformation.online_time,
    ).all()
    template_name = 'TON链新币充值须知'
    template_row = OperationTemplate.query.filter(
        OperationTemplate.status == OperationTemplate.Status.VALID,
        OperationTemplate.title == template_name,
        OperationTemplate.business == OperationTemplate.Business.DEPOSIT_WITHDRAW_WINDOW
    ).first()
    if not template_row:
        current_app.logger.warning(f"{chain} template content not found")
        return
    content_query = OperationTemplateContent.query.filter(
        OperationTemplateContent.template_id == template_row.id
    ).all()
    content_lang_map = {i.lang: i for i in content_query}
    for row in coin_query:
        if not row.online_time:
            continue
        sort_id = (
                DepositWithdrawalPopupWindow.query.filter(
                    DepositWithdrawalPopupWindow.status == DepositWithdrawalPopupWindow.Status.VALID,
                )
                .with_entities(func.max(DepositWithdrawalPopupWindow.sort_id))
                .scalar()
                or 0
        )
        popup_window_row = DepositWithdrawalPopupWindow(
            name=f'{row.code}币未充值须知',
            chain=chain,
            asset=row.code,
            started_at=row.online_time,
            ended_at=row.online_time+timedelta(days=365*2),  # 按币种资料上线时间+2年
            platform=DepositWithdrawalPopupWindow.Platform.ALL,
            trigger_page=DepositWithdrawalPopupWindow.TriggerPage.DEPOSIT,
            frequency=DepositWithdrawalPopupWindow.Frequency.EVERY_TIME,
            sort_id=sort_id+1,
            status=DepositWithdrawalPopupWindow.Status.VALID,
        )
        db.session.add(popup_window_row)
        db.session.flush()
        for _lang, _content in content_lang_map.items():
            db.session.add(
                DepositWithdrawalPopupWindowContent(
                    popup_window_id=popup_window_row.id,
                    lang=_lang,
                    url=_content.url,
                    title=_content.title,
                    content=_content.content,
                )
            )
        db.session.commit()


def _get_data_from_amm_market(info_assets_set):
    markets = AmmMarket.query.filter(AmmMarket.status == AmmMarket.Status.ONLINE).with_entities(AmmMarket.name).all()
    amm_assets_set = get_assets_from_markets(markets)
    return info_assets_set & amm_assets_set


def _get_data_from_margin_market(info_assets_set):
    markets = MarginAccount.query.filter(MarginAccount.status == MarginAccount.StatusType.OPEN).with_entities(
        MarginAccount.name).all()
    margin_assets_set = get_assets_from_markets(markets)
    return info_assets_set & margin_assets_set


def get_assets_from_markets(markets):
    assets_set = set()
    for market in markets:
        m = MarketCache(market[0]).dict
        asset = m['base_asset']
        assets_set.add(asset)
    return assets_set


def _get_tag_set(tag):
    tag_id = AssetTag.query.filter(AssetTag.display_name == tag).with_entities(AssetTag.id).first()
    if not tag_id:
        raise InvalidArgument(message=f"{tag}标签不存在，无法自动更新标签下的币种！")
    tag_id = tag_id[0]
    tag_items = AssetTagRelation.query.filter(AssetTagRelation.tag_id == tag_id,
                                              AssetTagRelation.status == AssetTagRelation.StatusType.PASSED).all()
    tag_set = {i.asset for i in tag_items}
    return tag_id, tag_set


def _update_tag(assets_set, ori_tag_set, tag_id):
    add_sets = assets_set - ori_tag_set
    del_sets = ori_tag_set - assets_set
    for asset in add_sets:
        row = AssetTagRelation.query.filter(AssetTagRelation.tag_id == tag_id, AssetTagRelation.asset == asset).first()
        if row:
            row.status = AssetTagRelation.StatusType.PASSED
        else:
            at = AssetTagRelation(tag_id=tag_id, asset=asset)
            db.session.add(at)
    for asset in del_sets:
        row = AssetTagRelation.query.filter(AssetTagRelation.tag_id == tag_id, AssetTagRelation.asset == asset).first()
        row.status = AssetTagRelation.StatusType.DELETED
    db.session.commit()


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_current_portrait():
    CurrentPortraitHashCache().reload()


@scheduled(crontab(minute='*/1'))
@lock_call()
def update_notification_bar_cache_schedule():
    NotificationBarCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_tip_bar_cache_schedule():
    TipBarCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_asset_maintain_cache_schedule():
    AssetMaintainConfigCache.reload()


@scheduled(crontab(hour="*/1", minute=15))
@lock_call()
def send_deposit_withdrawal_maintain_resume_notice_schedule():
    # 对于开启充提的币种，5小时后发送订阅通知
    from app.assets import get_chain

    def _batch_send_notice(_asset: str, _asset_chain_str: str, _type: str, _user_ids: List[int]):
        for _chunk_user_ids in batch_iter(_user_ids, 1000):
            send_resume_deposit_withdrawal.delay(_chunk_user_ids, asset, _type, current_timestamp(to_int=True))
            send_resume_deposit_withdrawal_notice_email.delay(
                _chunk_user_ids,
                _type,
                _asset,
                _asset_chain_str,
            )

    deadline_at = now() - timedelta(hours=5)
    for asset, chains in asset_to_chains().items():
        for chain_ in chains:
            ac_conf = get_asset_chain_config(asset, chain_)
            asset_chain_str = asset
            if len(chains) > 1:
                chain_name = get_chain(chain_).display_name
                if chain_name != asset:
                    asset_chain_str = f"{asset}-{chain_name}"

            if ac_conf.deposits_all_enabled and ac_conf.deposits_updated_at and ac_conf.deposits_updated_at < deadline_at:
                subs_cache = DepositMaintainSubscriberCache(asset, chain_)
                user_ids = subs_cache.smembers()
                subs_cache.delete()
                DepositMaintainInfoCache(asset, chain_).delete()
                if user_ids:
                    user_ids = [int(i) for i in user_ids]
                    _batch_send_notice(asset, asset_chain_str, "DEPOSIT", user_ids)

            if ac_conf.withdrawals_all_enabled and ac_conf.withdrawals_updated_at and ac_conf.withdrawals_updated_at < deadline_at:
                subs_cache = WithdrawalMaintainSubscriberCache(asset, chain_)
                user_ids = subs_cache.smembers()
                subs_cache.delete()
                WithdrawalMaintainInfoCache(asset, chain_).delete()
                if user_ids:
                    user_ids = [int(i) for i in user_ids]
                    _batch_send_notice(asset, asset_chain_str, "WITHDRAWAL", user_ids)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_charity_cache_schedule():
    CharityBannerCache.reload()
    CharityActivitiesCache.reload()
    CharityVideoCache.reload()
    CharityFootprintCache.reload()


@scheduled(crontab(minute='*/5'))
def update_market_banner_cache_schedule():
    MarketBannerCache.reload()


@scheduled(crontab(hour='*/8', minute='0'))
def update_asset_circulation_history_cache_schedule():
    AssetCirculationHistoryCache().reload()


@scheduled(crontab(minute='*/5'))
def update_online_market_asset_cache_schedule():
    """ 更新有上架市场的币种 """
    OnlineMarketAssetCache().reload()


@scheduled(crontab(minute='*/5'))
def update_sns_conf_schedule():
    SNSConfCache.reload()


@scheduled(crontab(minute='*/1'))
@lock_call()
def update_popup_windows_biz_schedule():
    """更新人均弹窗指标"""
    # 产品创建的 id
    pop_counter_id = IncreaseEvent.POPUP_WINDOWS_COUNT.value    # 弹窗次数
    pop_user_id = IncreaseEvent.POPUP_WINDOWS_USER_COUNT.value   # 弹窗新增人数
    model = MonitorEvent
    rows = model.query.filter(
        model.id.in_([pop_counter_id, pop_user_id])
    )
    event_type_map = {
        i.id: i.data_type.value for i in rows
    }
    client = BizMonitorClient()
    _now = current_timestamp(to_int=True)
    now_minute = _now - _now % 60
    last_minute = now_minute - 60
    counter_map = client.get_metrics(
        last_minute, now_minute, pop_counter_id, event_type_map[pop_counter_id], PeriodType.MINUTE.value
    )
    user_map = client.get_metrics(
        last_minute, now_minute, pop_user_id, event_type_map[pop_user_id], PeriodType.MINUTE.value
    )

    tag_map = {str(i.value): i for i in EventTag}
    for tag, count_data in counter_map.items():
        if not count_data:
            continue
        if user_data := user_map.get(tag):
            user_time, user_count = user_data[0]
            if user_count == 0:
                continue
            count_time, count = count_data[0]
            freq = round(count / user_count, 2)
            biz_monitor.increase_guage(
                IncreaseEvent.POPUP_WINDOWS_FREQUENCY,
                value=freq,
                tag=tag_map[tag],
            )

@scheduled(crontab(hour='*/1', minute=30))
@lock_call()
def send_trade_rank_activity_notice_schedule():
    """交易排名活动通知"""
    PUSH_NAME = 'trade rank auto push'

    now_ = now()
    activities = TradeRankActivity.query.filter(
        TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
    ).with_entities(TradeRankActivity.id,
                    TradeRankActivity.type,
                    TradeRankActivity.activity_id, 
                    TradeRankActivity.started_at,
                    TradeRankActivity.gift_amount,
                    TradeRankActivity.gift_asset,
                    TradeRankActivity.gift_rules,
                    TradeRankActivity.started_at,
                    TradeRankActivity.ended_at,
                    TradeRankActivity.least_trade_amount).all()

    for item in activities:
        cache = TradeRankActivityNoticeCache(item.id)
        if cache.exists():
            continue
        reward_user_ids, rank_user_ids, no_rank_user_ids = [], [], []
        if item.ended_at.date() - now_.date() != timedelta(days=3):
            continue
        buz = TradeActivityBusiness(item.id)
        user_ids = buz.get_valid_users()
        user_info_map = dict()
        gift_rules = json.loads(item.gift_rules)
        max_gift_rank = gift_rules[-1]['rank_max']
        max_gift_amount = gift_rules[0]['rank_amount']
        for ids in batch_iter(user_ids, 5000):
            infos = TradeRankActivityUserInfo.query.filter(
                TradeRankActivityUserInfo.user_id.in_(ids)
            ).with_entities(
                TradeRankActivityUserInfo.user_id,
                TradeRankActivityUserInfo.trade_amount,
                TradeRankActivityUserInfo.rank
            ).all()
            user_info_map.update({i.user_id: i for i in infos})
        for user_id in user_ids:
            info = user_info_map.get(user_id)
            rank, trade_amount = None, 0
            if info:
                rank = info.rank
                trade_amount = info.trade_amount
            if rank and rank <= max_gift_rank:
                reward_user_ids.append(user_id)
            elif rank and trade_amount >= item.least_trade_amount:
                rank_user_ids.append(user_id)
            else:
                no_rank_user_ids.append(user_id)
            cache.pfadd(user_id)

        details = TradeRankActivityDetail.query.filter(
            TradeRankActivityDetail.trade_activity_id == item.id,
        ).all()
        titles = {d.lang: d.title for d in details}
        data = dict(
            least_trade_amount=amount_to_str(item.least_trade_amount, 8),
            max_gift_amount=amount_to_str(max_gift_amount, 8),
            max_gift_rank=max_gift_rank,
            total_gift_amount=amount_to_str(item.gift_amount, 8),
            trade_asset=buz.get_trade_asset(),
            gift_asset=item.gift_asset,
            started_at=item.started_at.strftime("%Y-%m-%d %H:%M:%S"),
            ended_at=item.ended_at.strftime("%Y-%m-%d %H:%M:%S"),
            site_url=f"{config['SITE_URL']}/activity/trade-rank/{item.id}",
            title=titles,
            type=item.type.name,
        )
        if reward_user_ids:
            data['send_type'] = 'REWARD'
            if item.type in TradeRankActivity.SPOT_TYPES:
                gettext('现货排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}')
                title = '现货排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}'
            else:
                gettext('合约排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}')
                title = '合约排位赛：恭喜进入排名！冲刺赢得{{ max_gift_amount }} {{ gift_asset }}'
            ActivityAutoPushHelper.create_email_push(
                reward_user_ids,
                title,
                'notice',
                'trade_rank_activity_notice',
                data,
                name=PUSH_NAME
            )
        if rank_user_ids:
            data['send_type'] = 'RANK'
            if item.type in TradeRankActivity.SPOT_TYPES:
                gettext('现货排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池')
                title = '现货排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池'
            else:
                gettext('合约排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池')
                title = '合约排位赛：恭喜达标！继续交易，瓜分{{ total_gift_amount }} {{ gift_asset }}奖池'
            ActivityAutoPushHelper.create_email_push(
                rank_user_ids,
                title,
                'notice',
                'trade_rank_activity_notice',
                data,
                name=PUSH_NAME
            )
        if no_rank_user_ids:
            data['send_type'] = 'NO_RANK'
            if item.type in TradeRankActivity.SPOT_TYPES:
                gettext('现货排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步')
                title = '现货排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步'
            else:
                gettext('合约排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步')
                title = '合约排位赛：你离瓜分{{ total_gift_amount }} {{ gift_asset }}仅差一步'
            ActivityAutoPushHelper.create_email_push(
                no_rank_user_ids,
                title,
                'notice',
                'trade_rank_activity_notice',
                data,
                name=PUSH_NAME
            )
    # 删除一些旧的key
    old_activities = TradeRankActivity.query.filter(
        TradeRankActivity.ended_at >= now_ - timedelta(days=7),
        TradeRankActivity.ended_at < now_
    ).with_entities(TradeRankActivity.id).all()
    for item in old_activities:
        TradeRankActivityNoticeCache(item.id).delete()


def _update_trade_rank_activity_statistics(activity: TradeRankActivity):
    black_list_user_ids = ActivityBlackList.query.filter(
        ActivityBlackList.activity_id == activity.activity_id,
        ActivityBlackList.status == ActivityBlackList.Status.PASSED
    ).with_entities(ActivityBlackList.user_id).all()
    black_list_user_ids = [item.user_id for item in black_list_user_ids]
    join_users = TradeRankActivityJoinUser.query.filter(
        TradeRankActivityJoinUser.trade_activity_id == activity.id,
        TradeRankActivityJoinUser.user_id.notin_(black_list_user_ids)
    ).with_entities(TradeRankActivityJoinUser.user_id,
                    TradeRankActivityJoinUser.created_at).all()
    join_user_map = dict(join_users)
    business_type = TradeActivityBusiness.trade_business_types[activity.type]
    if business_type == TradeBusinessType.SPOT:
        tag = UserTag.FIRST_SPOT_TRADE_TIME
    else:
        tag = UserTag.FIRST_PERPETUAL_TRADE_TIME
    join_user_ids = [item.user_id for item in join_users]
    not_trade_user_count = new_user_count = new_trade_user_count = 0
    new_trade_user_ids = set()
    read_model = get_tag_read_table(tag)
    for ids in batch_iter(join_user_ids, 5000):
        tag_records = read_model.query.filter(read_model.tag == tag.name,
                                    read_model.user_id.in_(ids)).with_entities(
                                    read_model.user_id, read_model.value
                                    ).all()
        
        first_trade_date_map = {item.user_id: timestamp_to_datetime(int(item.value)) for item in tag_records}
        user_records = User.query.filter(User.id.in_(ids)).with_entities(
            User.id, User.created_at
        ).all()
        user_created_at_map = dict(user_records)
        for id_ in ids:
            joined_at = join_user_map[id_]
            first_trade_date = first_trade_date_map.get(id_)
            if not first_trade_date or first_trade_date >= joined_at:
                not_trade_user_count += 1
            if user_created_at_map[id_] >= activity.started_at:
                new_user_count += 1
            if first_trade_date and first_trade_date.date() >= joined_at.date():
                new_trade_user_count += 1
                new_trade_user_ids.add(id_)
    
    today_, start_date = today(), activity.started_at.date()
    yday = today_ - timedelta(days=1)
    delta_ = yday - start_date + timedelta(days=1)
    if business_type == TradeBusinessType.SPOT:
        trade_reports = DailySpotTradeReport.query.filter(
            DailySpotTradeReport.report_date >= start_date - delta_,
        ).with_entities(
            DailySpotTradeReport.report_date,
            DailySpotTradeReport.increase_trade_user_count.label('new_user_count')).all()
    else:
        trade_reports = DailyPerpetualTradeReport.query.filter(
            DailyPerpetualTradeReport.report_date >= start_date - delta_,
        ).with_entities(
            DailyPerpetualTradeReport.report_date,
            DailyPerpetualTradeReport.increase_trade_user.label('new_user_count')).all()
    increase_count = last_increase_count = 0
    for r in trade_reports:
        if r.report_date >= start_date:
            increase_count += r.new_user_count
        else:
            last_increase_count += r.new_user_count
    trade_relative_ratio = (increase_count - last_increase_count) / last_increase_count \
                            if last_increase_count else 0
    trade_relative_ratio = quantize_amount(trade_relative_ratio, 4)

    user_infos = TradeRankActivityUserInfo.query.filter(
        TradeRankActivityUserInfo.trade_activity_id == activity.id,
        TradeRankActivityUserInfo.user_id.notin_(black_list_user_ids)
    ).with_entities(
        TradeRankActivityUserInfo.user_id,
        TradeRankActivityUserInfo.trade_amount,
        TradeRankActivityUserInfo.market_fee_usd,
        TradeRankActivityUserInfo.fee_usd,
    ).all()
    qualified_user_count = len([u for u in user_infos if u.trade_amount >= activity.least_trade_amount])
    trade_user_count = len([u for u in user_infos if u.trade_amount > 0])
    total_trade_amount = total_market_fee_usd = total_fee_usd = new_trade_user_fee_usd = 0
    if user_infos:
        total_trade_amount = sum(u.trade_amount for u in user_infos)
        total_market_fee_usd = sum(u.market_fee_usd for u in user_infos)
        total_fee_usd = sum(u.fee_usd for u in user_infos)
        new_trade_user_fee_usd = sum(u.fee_usd for u in user_infos if u.user_id in new_trade_user_ids)
    sub_user_map = dict()
    for ids in batch_iter(join_user_ids, 5000):
        tmp = SubAccount.query.filter(
            SubAccount.main_user_id.in_(ids),
        ).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_user_map.update(dict(tmp))
    all_join_user_ids = join_user_ids + list(sub_user_map)
    price_range_map = AssetPrice.get_close_price_range_map(activity.started_at.date(), 
                                                           min(today(), activity.ended_at.date()))
    activity_markets = TradeActivityBusiness(activity.id).get_markets()
    activity_markets = set(activity_markets)
    total_trade_usd = market_trade_usd = 0
    
    # trade_user_set只为现货净买入活动设置
    trade_user_set = set()
    if business_type == TradeBusinessType.SPOT:
        trade_records = []
        for ids in batch_iter(all_join_user_ids, 5000):
            r = TradeSummaryDB.get_user_trade_summary_data(
                activity.started_at.date(),
                min(activity.ended_at.date(), today()) + timedelta(days=1),
                None,
                ids,
                ('trade_date', 'user_id', 'money_asset', 'deal_volume', 'market')
            )
            trade_records.extend(r)
        for r in trade_records:
            user_id = sub_user_map.get(r['user_id'], r['user_id'])
            if r['trade_date'] < join_user_map[user_id].date():
                continue
            price_map = price_range_map[r['trade_date']]
            usd = price_map.get(r['money_asset'], 0) * r['deal_volume']
            if r['market'] in activity_markets:
                market_trade_usd += usd
                trade_user_set.add(user_id)
            total_trade_usd += usd
    else:
        trade_records = []
        for ids in batch_iter(all_join_user_ids, 5000):
            tmp = PerpetualSummaryDB.get_user_trade_summary_data(
                activity.started_at.date(),
                min(activity.ended_at.date(), today()) + timedelta(days=1),
                None,
                ids,
                None
            )
            trade_records.extend(tmp)
        for r in trade_records:
            user_id = sub_user_map.get(r['user_id'], r['user_id'])
            if r['trade_date'] < join_user_map[user_id].date():
                continue
            price_map = price_range_map[r['trade_date']]
            price_map['USD'] = 1
            if r['market_type'] == PerpetualMarketType.DIRECT:
                usd = price_map.get(r['money_asset'], 0) * r['deal_volume']
            else:
                usd = price_map.get(r['money_asset'], 0) * r['deal_amount']
            if r['market'] in activity_markets:
                market_trade_usd += usd
                trade_user_set.add(user_id)
            total_trade_usd += usd
    current_ts = current_timestamp(to_int=True)
    current_ts -= current_ts % 3600
    report_at = timestamp_to_datetime(current_ts)
    record = TradeRankActivityStatistics.get_or_create(trade_activity_id=activity.id)
    record.report_at = report_at
    record.join_user_count = len(join_users)
    record.trade_user_count = trade_user_count
    record.qualified_user_count = qualified_user_count
    record.not_trade_user_count = not_trade_user_count
    record.new_user_count = new_user_count
    # 全站新增交易用户
    record.new_trade_user_count = increase_count
    record.trade_user_relative_ratio = trade_relative_ratio

    # 转化新用户
    record.new_join_user_trade_count = new_trade_user_count
    record.market_trade_usd = quantize_amount(total_trade_amount, 2)
    record.total_fee_usd = quantize_amount(total_fee_usd, 2)
    record.market_fee_usd = quantize_amount(total_market_fee_usd, 2)
    record.new_user_fee_usd = new_trade_user_fee_usd
    record.total_trade_usd = quantize_amount(total_trade_usd, 2)
    
    # 净买入活动的特殊值
    if activity.type == TradeRankActivity.Type.SPOT_NET_BUY:
        record.market_trade_usd = quantize_amount(market_trade_usd, 2)
        record.trade_user_count = len(trade_user_set)
        record.total_trade_usd = quantize_amount(total_trade_usd, 2)
    db.session.add(record)


@scheduled(crontab(hour='*/1', minute=30))
@lock_call()
def update_trade_rank_activity_statistics_schedule():
    """更新交易排名活动统计数据"""
    activities = TradeRankActivity.query.filter(
        TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
        TradeRankActivity.started_at <= now(),
        TradeRankActivity.ended_at > now() - timedelta(hours=1)
    ).with_entities(TradeRankActivity.id, 
                    TradeRankActivity.activity_id,
                    TradeRankActivity.type,
                    TradeRankActivity.least_trade_amount,
                    TradeRankActivity.started_at,
                    TradeRankActivity.ended_at).all()
    for item in activities:
        _update_trade_rank_activity_statistics(item)
    db.session.commit()


@celery_task
@lock_call(with_args=True)
def user_relation_async_export_task(exporter_id: int):
    """ 用户IP/设备账号关联查询 异步导出 """
    from app.api.admin.users import UserRelationListResource, UserRelationAsyncExportResource

    cache = UserRelationAsyncExportCache(exporter_id)
    user_ids = cache.smembers()
    if not user_ids:
        return

    exp_user = User.query.get(exporter_id)
    items = []
    for ch_user_ids in batch_iter(user_ids, 1000):
        ch_user_ids = [int(i) for i in ch_user_ids]
        ch_users = User.query.filter(
            User.id.in_(ch_user_ids),
        ).all()
        items.extend(UserRelationListResource.format_records(ch_users, is_export=True))

    header_mapper = {
        i["field"]: i[Language.ZH_HANS_CN]
        for i in UserRelationAsyncExportResource.export_headers
    }
    sheet_datas = {
        'IP-设备ID查询导出': {
            'header_mapper': header_mapper,
            'data': items,
        }
    }
    file_url = export_xlsx_with_sheet(sheet_data=sheet_datas)
    send_internal_user_email(
        email=exp_user.email,
        email_content=f'IP-设备ID查询导出 excel 下载链接：{file_url}',
        subject=f'IP-设备ID查询导出',
        lang=Language.ZH_HANS_CN.value
    )

    cache.delete()


@scheduled(crontab(minute='*/5'))
def update_inset_schedule():
    """更新首页插画配置"""
    InsetConfigCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_page_inset_cache_schedule():
    """更新页面插画缓存"""
    PageInsetCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_perpetual_activity_cache():
    """更新合约交易页活动"""
    PerpetualActivityCache().reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_referral_activity_banner_cache():
    ReferralActivityBannerCache.reload()


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_dynamic_falling_cache():
    DynamicFallingCache.reload()


@scheduled(crontab(minute='*/1'))
@lock_call()
def cancel_suspending_app_push_tasks():
    """取消处于暂停中状态的 APP推送任务"""
    app_pushes = AppPush.query.filter(
        AppPush.status == AppPush.Status.SUSPENDING
    ).all()
    
    pusher = MobilePusher()
    for push in app_pushes:
        push_task_manager = PushTaskIdManager.for_app_push(push.id)
        task_ids = push_task_manager.get_task_ids()
        if not task_ids:
            push.status = AppPush.Status.SUSPENDED
            continue

        for _iter_task_ids in batch_iter(task_ids, 1000):
            try:
                pusher.cancel_push_tasks(_iter_task_ids)
            except Exception as e:
                current_app.logger.error(f"取消 AppPush 任务 {push.id} 失败: {e}")
        push.status = AppPush.Status.SUSPENDED
    db.session.commit()


@scheduled(crontab(minute='*/1'))
@lock_call()
def cancel_suspending_auto_push_tasks():
    """取消处于暂停中状态的 APP自动推送任务"""
    auto_strategies = AutoPushStrategy.query.filter(
        AutoPushStrategy.status == AutoPushStrategy.Status.SUSPENDING
    ).all()
    
    pusher = MobilePusher()
    for strategy in auto_strategies:
        push_task_manager = PushTaskIdManager.for_auto_push_strategy(strategy.id)
        task_ids = push_task_manager.get_task_ids()
        if not task_ids:
            strategy.status = AutoPushStrategy.Status.SUSPENDED
            continue
        for _iter_task_ids in batch_iter(task_ids, 1000):
            try:
                pusher.cancel_push_tasks(_iter_task_ids)
            except Exception as e:
                current_app.logger.error(f"取消 AutoPushStrategy 任务 {strategy.id} 失败: {e}")
        strategy.status = AutoPushStrategy.Status.SUSPENDED
    db.session.commit()
    

@scheduled(crontab(minute='*/5'))
@lock_call()
def update_edd_audit_status_and_send_email():
    model = EDDAudit
    rows = model.query.filter(
        model.status == model.Status.CREATED
    ).all()
    for row in rows:
        row.status = model.Status.INFO_REQUIRED
        db.session.commit()
        send_edd_audit_email.delay(row.id)


@scheduled(crontab(minute='*/10'))
@lock_call()
def activity_create_auto_notice_schedule():
    ActivityAutoNotice.notice()


@scheduled(crontab(minute='*/5'))
@lock_call()
def send_auto_notice():
    now_ = now()
    rows = AutoPushHistory.query.filter(
        AutoPushHistory.status == AutoPushHistory.Status.CREATED
    ).all()
    for r in rows:
        if now_ >= r.expired_at:
            r.status = AutoPushHistory.Status.FAILED
    db.session.commit()

    rows = AutoPushHistory.query.filter(
        AutoPushHistory.status == AutoPushHistory.Status.CREATED
    ).all()
    for r in rows:
        create_auto_push_task.delay(r.push_type.name, r.business.name, r.business_key)


@celery_task
@lock_call(with_args=True)
def create_auto_push_task(push_type: str, business: str, business_key: str):
    model = AutoPushHistory
    push_type = model.PushType[push_type]
    business = model.Business[business]
    func_map = {
        (model.PushType.EmailPush, model.Business.TradeRankStart): ActivityAutoNotice.create_trade_rank_start_email_push,
        (model.PushType.AppPush, model.Business.TradeRankStart): ActivityAutoNotice.create_trade_rank_start_app_push,
        (model.PushType.AppPush, model.Business.TradeRankEnd): ActivityAutoNotice.create_trade_rank_end_app_push,
        (model.PushType.EmailPush, model.Business.AirdropStart): ActivityAutoNotice.create_airdrop_start_email_push,
        (model.PushType.AppPush, model.Business.AirdropEnd): ActivityAutoNotice.create_airdrop_end_app_push,
        (model.PushType.EmailPush, model.Business.DiscountStart): ActivityAutoNotice.create_discount_start_email_push,
        (model.PushType.AppPush, model.Business.DiscountEnd): ActivityAutoNotice.create_discount_end_app_push,
        (model.PushType.AppPush, model.Business.LaunchPoolMiningPre): ActivityAutoNotice.create_launch_pool_pre_app_push,
        (model.PushType.NotificationBar, model.Business.LaunchPoolMiningPre): ActivityAutoNotice.create_launch_pool_pre_notification_bar,
        (model.PushType.EmailPush, model.Business.LaunchPoolMiningStart): ActivityAutoNotice.create_launch_pool_start_email_push,
        (model.PushType.AppPush, model.Business.LaunchPoolMiningStart): ActivityAutoNotice.create_launch_pool_start_app_push,
        (model.PushType.AppPush, model.Business.LaunchPoolMiningEnd): ActivityAutoNotice.create_launch_pool_end_app_push,
    }
    func_: Callable = func_map.get((push_type, business))
    if not func_:
        current_app.logger.error(f"create_auto_push_task {push_type} {business} {business_key} not_func")
        return

    push_id = func_(business_key)
    if push_id:
        row = model.query.filter(
            model.push_type == push_type,
            model.business == business,
            model.business_key == business_key
        ).first()
        row.status = model.Status.SUCCESS
        row.push_id = push_id
        db.session.commit()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_p2p_activity_status_and_cache():
    model = P2pActivity
    now_ = now()
    pending_query = model.query.filter(
        model.status == model.Status.PENDING,
        model.start_at <= now_
    ).all()
    for row in pending_query:
        row.status = model.Status.ONLINE
    oline_query = model.query.filter(
        model.status == model.Status.ONLINE,
        model.end_at <= now_
    ).all()
    for data in oline_query:
        data.status = model.Status.OFFLINE
    db.session.commit()
    P2pActivityBannerCache.reload()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_c_box_promotion_schedule():
    CBoxPromotionCache.reload()


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_c_box_theme_schedule():
    CBoxThemeCache.reload()


@scheduled(crontab(minute='17', hour='9'))
@lock_call()
def update_email_push_unsubscribe_count_schedule():
    end = now()
    start = end - timedelta(days=30)
    rows = yield_query_records_by_time_range(EmailPush,
                                            start_time=start,
                                            end_time=end,
                                            select_fields=[EmailPush.id, EmailPush.status],
                                            limit=200)
    for row in rows:
        if row.status != EmailPush.Status.FINISHED:
            continue
        count = EmailPushUnsubscription.query.filter(
                    EmailPushUnsubscription.push_id == row.id,
                    EmailPushUnsubscription.status == EmailPushUnsubscription.Status.FINISHED
            ).with_entities(func.count()).scalar() or 0
        EmailPush.query.filter(EmailPush.id == row.id).update({EmailPush.unsubscribe_count: count}, synchronize_session=False)
        db.session.commit()


@scheduled(crontab(minute='*/5'))
@lock_call()
def sync_deposit_audit_status_schedule():
    sync_deposit_audit_status(check_days=30)


@scheduled(crontab(hour='*/1', minute='0'))
@lock_call()
def slowly_sync_deposit_audit_status_schedule():
    sync_deposit_audit_status(check_days=180)


def sync_deposit_audit_status(check_days: int):
    rows = DepositAudit.query.with_entities(
        DepositAudit.deposit_id.distinct().label("deposit_id"),
    ).filter(
        DepositAudit.created_at > now() - timedelta(days=check_days)
    ).order_by(
        DepositAudit.id.desc()
    ).all()
    for row in rows:
        with CacheLock(LockKeys.deposit_audit(row.deposit_id)):
            db.session.rollback()
            DepositAuditBusiness.sync_status_by_audit(row.deposit_id, auto_commit=True)


@celery_task
@lock_call(with_args='edd_id')
def notify_edd_status_changed_task(edd_id: int):
    row = EDDAudit.query.get(edd_id)
    if not row:
        return
    if row.source != EDDAudit.Source.DEPOSIT_AUDIT:
        return
    assert row.deposit_id
    with CacheLock(LockKeys.deposit_audit(row.deposit_id)):
        db.session.rollback()
        DepositAuditBusiness.sync_status_by_edd(row, auto_commit=True)
