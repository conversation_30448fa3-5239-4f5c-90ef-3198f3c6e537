import datetime
import json
from collections import defaultdict
from decimal import Decimal
from enum import Enum
from functools import cached_property

from sqlalchemy import func, and_, or_, select

from app import Language
from app.business import BusinessSettings, crontab, lock_call, PerpetualSysHistoryDB, PerpetualHistoryDB, PriceManager
from app.business.email import send_internal_user_email
from app.caches import PerpetualMarketCache, PerpetualOfflineMarketCache
from app.caches.report import HistoryRiskReportCache
from app.common import CeleryQueues, get_country, list_country_codes_3_admin, PerpetualMarketType
from app.models import DailyMarginFundReport, DailyCountryReachReport, \
    DailySpotMarginBurstReport, MarginLiquidationHistory, DailyPerpetualInsuranceReport, MarginLiquidationFeeHistory, \
    AssetPrice, PerpetualMarket, DailyPerpetualMarginBurstReport, KycVerification, KYCInstitution, UserRiskScreenCase, User, \
    WithdrawalWhitelistUser, WithdrawalPrivilegedUser, ClearedUser,\
    UserSetting, \
    SecurityResetApplication, SecurityResetAnswerHistory, SignOffUser
from app.models.wallet import AbnormalDepositApplication
from app.utils import route_module_to_celery_queue, scheduled, last_month, format_percent, amount_to_str, safe_div
from app.utils.parser import JsonEncoder

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


@scheduled(crontab(minute=15, hour=2, day_of_month=1))
@lock_call()
def push_risk_report_monthly():
    end_dt = datetime.datetime.now()
    last_month_date = last_month(end_dt.year, end_dt.month)
    start_dt = datetime.datetime(last_month_date.year, last_month_date.month, 1)
    RiskReportHandler(start_dt, end_dt).push_to_users(RiskReportHandler.Period.MONTHLY)


@scheduled(crontab(minute=15, hour=2))
@lock_call()
def push_risk_report_daily():
    end_dt = datetime.datetime.now()
    start_dt = end_dt - datetime.timedelta(days=1)
    RiskReportHandler(start_dt, end_dt).push_to_users(RiskReportHandler.Period.DAILY)


class RiskReportHandler:
    TMPL = """
    Dear all：
以下是 CoinEx {report_date} 的风控{period}报：<br>
{desc}
一、市场维度<br>
1、风控触发记录相关数据<br>
（1）用户异常盈利（实时&定期）：{user_abnormal_profit}<br><br>
（2）市场异常波动（币币+合约）：{event_market_volatility}<br><br>
（3）异常穿仓：<br>
       {event_liquidation}<br><br>
（4）对账不平：<br>
       {event_account_checking}<br><br>
（5）币种异常增发(ERC-20，BSC)：<br>
       {event_abnormal_issuance}
{sms}<br>
  <br><br>
{event_no_deals}
<br><br>
{user_big_booking}
<br>
<br>
2、币币市场相关数据<br>
1）{margin_burst}<br><br>
2）{margin_fund}<br><br>
3）杠杆穿仓金额>0的市场排名1至5，展示市场及穿仓金额仅统计USDT的数据
{margin_liquidation}
<br>
<br>
3、合约市场相关数据<br>
1）{perp_auto_deleveraging}<br><br>
2）{perp_liquidation}<br><br>
3）{perp_insurance}<br>
4）合约穿仓金额>0的市场排名1至5，展示市场及穿仓金额<br>
{perp_burst}<br>
<br><br>
二、账户维度<br>
1、KYC相关数据<br>
（1）个人KYC：<br>
      A. {admin_kyc}
      B. {auto_kyc}
      C. {all_kyc}
<br><br>
（2）{kyc_institution}<br><br>
（3）{kyc_change_rate}<br><br>
（4）{kyc_rate}<br>
<br>
2、风险筛查相关数据<br>
（1）{screen_auto}<br>
（2）{screen_admin}<br><br>
（3）{screen_total}<br><br>
（4）{screen_kyc}
<br>
{sp_config}
<br>
{user_large_asset}
<br>
{2fa}
<br>
{deposit_found}
    """

    class Period(Enum):
        DAILY = '每日'
        MONTHLY = '每月'

    def __init__(self, start_dt: datetime.datetime, end_dt: datetime.datetime):
        start_ts = int(start_dt.timestamp())
        end_ts = int(end_dt.timestamp())
        self.start_ts = start_ts - start_ts % (60 * 60 * 24)
        self.end_ts = end_ts - end_ts % (60 * 60 * 24)  # 今日零点

        self.start_dt = datetime.datetime.fromtimestamp(self.start_ts)
        self.end_dt = datetime.datetime.fromtimestamp(self.end_ts)

        self.period = None

    def push_to_users(self, period: Period):
        self.set_period(period)
        emails = BusinessSettings.risk_report_emails
        if not emails:
            return
        if self.period is self.Period.DAILY:
            report_date = self.start_dt.strftime('%Y-%m-%d')
            period_str = '日报'
        else:
            report_date = self.start_dt.strftime('%Y-%m')
            period_str = '月报'
        for email in emails:
            send_internal_user_email.delay(
                email=email,
                email_content=self._content,
                subject=f'CoinEx {report_date} 风控{period_str}',
                lang=Language.ZH_HANS_CN.value
            )

    def set_period(self, period: Period):
        self.period = period

    @cached_property
    def _content(self):
        return self._build()

    def _build(self):
        map1 = self._build_risk_user()
        map2 = self._build_risk_event()
        map3 = self._build_risk_sms()
        map4 = self._build_risk_margin_burst()
        map5 = self._build_risk_margin_fund()
        map6 = self._build_risk_margin_liquidation()
        map7 = self._build_risk_perp_auto_deleveraging()
        map8 = self._build_risk_perp_liquidation()
        map9 = self._build_risk_perp_insurance()
        map10 = self._build_risk_perp_burst()
        map11 = self._build_risk_kyc()
        map12 = self._build_risk_screen()
        map13 = self._build_risk_sp_config()
        map14 = self._build_risk_2fa()
        map15 = self._build_risk_deposit_found()
        mapping = {
            **map1, **map2, **map3, **map4, **map5, **map6,
            **map7, **map8, **map9, **map10, **map11, **map12,
            **map13, **map14, **map15
        }
        period_begin = self.start_dt.strftime('%Y-%m-%d')
        period_end = self.end_dt.strftime('%Y-%m-%d')
        if self.period is self.Period.DAILY:
            report_date = self.start_dt.strftime('%Y-%m-%d')
            period = '日'
        else:
            report_date = self.start_dt.strftime('%Y-%m')
            period = '月'
        _desc = f'{period_begin} 08:00—{period_end} 08:00'
        desc = f'数据周期（UTC+8）：{label_u_b(_desc)} <br>'
        titles = {'report_date': report_date, 'period': period, 'desc': desc}
        content = self.TMPL.format(**titles, **mapping)
        self._save(content, extra=mapping)
        return content

    def _build_risk_user(self):
        str1 = self.__build_risk_user_abnormal_profit()
        str2 = self.__build_risk_user_large_asset()
        str3 = self.__build_risk_user_big_booking()
        return {
            'user_abnormal_profit': str1,
            'user_large_asset': str2,
            'user_big_booking': str3,
        }

    def __build_risk_user_abnormal_profit(self):
        from app.models import RiskUser
        model = RiskUser
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.reason.in_([
                model.Reason.ABNORMAL_PROFIT,
                model.Reason.PERIOD_ABNORMAL_PROFIT,
            ])
        ).all()
        audited, rejected = [], []
        for row in rows:
            if row.status is model.Status.AUDITED:
                audited.append(row)
            elif row.status is model.Status.AUDIT_REJECTED:
                rejected.append(row)
        a, b, c = f'{len(rows)}', f'{len(audited)}', f'{len(rejected)}'
        return f'触发记录共{label_u_b(a)}条，审核通过{label_u_b(b)}条，审核不通过{label_u_b(c)}条'

    def __build_risk_user_large_asset(self):
        return ''

    def __build_risk_user_big_booking(self):
        from app.models import RiskUser
        model = RiskUser
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.reason == model.Reason.USER_ASSET_PENDING_ORDER_THRESHOLD
        ).all()
        down_str = f'（7）大额挂单监控：<br>' if self.period is self.Period.DAILY else f'（8）大额挂单监控：<br>'
        asset_count_dic = defaultdict(int)
        audited, rejected = [], []
        for row in rows:
            asset_count_dic[row.source] += 1
            if row.status is model.Status.AUDITED:
                audited.append(row)
            elif row.status is model.Status.AUDIT_REJECTED:
                rejected.append(row)

        assets = [k + str(v) + '次' for k, v in asset_count_dic.items()]
        assets_str = '、'.join(assets) or '无'
        a, b, c, d = f'{len(rows)}', f'{len(audited)}', f'{len(rejected)}', f'{assets_str}'
        down_str += f'触发记录共{label_u_b(a)}条，审核通过{label_u_b(b)}条，审核不通过{label_u_b(c)}条，触发币种：{label_u_b(d)}<br>'
        return down_str

    def _build_risk_event(self):
        str1 = self.__build_risk_event_market_volatility()
        str2 = self.__build_risk_event_liquidation()
        str3 = self.__build_risk_event_account_checking()
        str4 = self.__build_risk_event_abnormal_issuance()
        str5 = self.__build_risk_event_no_deals()
        return {
            'event_market_volatility': str1,
            'event_liquidation': str2,
            'event_account_checking': str3,
            'event_abnormal_issuance': str4,
            'event_no_deals': str5,
        }

    def __build_risk_event_market_volatility(self):
        from app.models import RiskEventLog, RiskUser
        model = RiskEventLog
        reason_map = {
            model.Reason.MARKET_VOLATILITY: {
                "reason": RiskUser.Reason.MARKET_VOLATILITY,
                "content": "币币市场波动"
            },
            model.Reason.PERPETUAL_MARKET_VOLATILITY: {
                "reason": RiskUser.Reason.PERPETUAL_MARKET_VOLATILITY,
                "content": "合约市场波动"
            },
        }
        content_list = []
        for event, detail in reason_map.items():
            rows = model.query.filter(
                model.reason == event,
                model.created_at >= self.start_dt,
                model.created_at < self.end_dt,
            ).all()
            volatility_cnt = len(rows)
            markets = set([row.source for row in rows])
            risks = self.__get_user_risks(events=rows, reason=detail['reason'])
            rejected = []
            distinct_users = set()
            for risk in risks:
                distinct_users.add(risk.user_id)
                if risk.status is RiskUser.Status.AUDIT_REJECTED:
                    rejected.append(risk)
            market_str = ', '.join(list(markets)) or '无'
            a, b, c, d = f'{volatility_cnt}', f'{market_str}', f'{len(distinct_users)}', f'{len(rejected)}'
            content_list.append(f'{detail["content"]}触发事件共{label_u_b(a)}个，触发市场{label_u_b(b)}，'
                               f'受影响用户数{label_u_b(c)}， 审核不通过用户数{label_u_b(d)}')
        return "<br>".join(content_list)

    def __build_risk_event_liquidation(self):
        from app.models import RiskEventLog
        from app.models import RiskUser

        model = RiskEventLog
        rows = model.query.filter(
            model.reason.in_([
                model.Reason.MARGIN_LIQUIDATION,
                model.Reason.PERPETUAL_LIQUIDATION
            ]),
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).all()
        margin_rows, perp_rows = [], []
        margin_amount, perp_amount = 0, 0
        margin_markets, perp_markets = defaultdict(int), defaultdict(int)
        for row in rows:
            extra = json.loads(row.extra)
            amount = Decimal(extra['amount'])
            if row.reason is model.Reason.MARGIN_LIQUIDATION:
                margin_rows.append(row)
                margin_amount += amount
                margin_markets[row.source] += 1
            else:
                perp_rows.append(row)
                perp_amount += amount
                perp_markets[row.source] += 1

        def _get_items(_risks):
            rejected, users = [], set()
            remarks = set()
            for risk in _risks:
                users.add(risk.user_id)
                remarks.add(risk.remark or '')
                if risk.status is RiskUser.Status.AUDIT_REJECTED:
                    rejected.append(risk)
            return rejected, users, remarks

        margin_risks = self.__get_user_risks(events=margin_rows, reason=RiskUser.Reason.MARGIN_LIQUIDATION)
        margin_rejected, margin_users, margin_remarks = _get_items(margin_risks)
        perp_risks = self.__get_user_risks(events=perp_rows, reason=RiskUser.Reason.PERPETUAL_LIQUIDATION)
        perp_rejected, perp_users, perp_remarks = _get_items(perp_risks)

        def _format_market(d):
            liq_market = ''
            for k, v in d.items():
                liq_market += f'{k}: {v}次、'
            if liq_market:
                liq_market = liq_market[:-1]
            return liq_market or '无'

        a, b, c, d = f'{len(perp_rows)}', f'{_format_market(perp_markets)}', f'{len(perp_users)}', f'{amount_to_str(perp_amount, 2)}'
        e, f, g, h = f'{len(perp_rejected)}', f'{len(margin_rows)}', f'{_format_market(margin_markets)}', f'{len(margin_users)}'
        i, j = f'{amount_to_str(margin_amount, 2)}', f'{len(margin_rejected)}'

        def _format_remark(r):
            if self.period is self.Period.MONTHLY:
                return ''
            s = '; '.join(list(r)) or '无'
            return f'备注：{s}<br>'

        return f'''
        （合约）触发事件共{label_u_b(a)}个，触发市场{label_u_b(b)}，
        受影响用户数{label_u_b(c)}，穿仓金额{label_u_b(d)}，审核不通过用户数{label_u_b(e)}
        <br>{_format_remark(perp_remarks)}
        （杠杆）触发事件共{label_u_b(f)}个，触发市场{label_u_b(g)}，
        受影响用户数{label_u_b(h)}，穿仓金额{label_u_b(i)}，审核不通过用户数{label_u_b(j)}
        <br>{_format_remark(margin_remarks)}
        '''

    def __build_risk_event_account_checking(self):
        from app.models import RiskEventLog
        from app.models import RiskUser

        model = RiskEventLog
        rows = model.query.filter(
            model.reason.in_([
                model.Reason.MARGIN_LOAN_FLAT_CHECK,
                model.Reason.PERPETUAL_BALANCE_CHECK,
                model.Reason.RED_PACKET_CHECK,
                model.Reason.INVESTMENT_BALANCE_CHECK,
            ]),
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).all()
        m_checks, p_checks, r_checks, i_checks = [], [], [], []
        for row in rows:
            if row.reason is model.Reason.MARGIN_LOAN_FLAT_CHECK:
                m_checks.append(row)
            elif row.reason is model.Reason.PERPETUAL_BALANCE_CHECK:
                p_checks.append(row)
            elif row.reason is model.Reason.RED_PACKET_CHECK:
                r_checks.append(row)
            else:
                i_checks.append(row)

        def _get_users(_risks):
            users = set()
            for risk in _risks:
                users.add(risk.user_id)
            return users

        m_risks = self.__get_user_risks(events=m_checks, reason=RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK)
        p_risks = self.__get_user_risks(events=p_checks, reason=RiskUser.Reason.PERPETUAL_BALANCE_CHECK)
        r_risks = self.__get_user_risks(events=r_checks, reason=RiskUser.Reason.RED_PACKET_CHECK)
        i_risks = self.__get_user_risks(events=i_checks, reason=RiskUser.Reason.INVESTMENT_BALANCE_CHECK)
        a, b, c, d = f'{len(m_checks)}', f'{len(_get_users(m_risks))}', f'{len(p_checks)}', f'{len(_get_users(p_risks))}'
        e, f, g, h = f'{len(r_checks)}', f'{len(_get_users(r_risks))}', f'{len(i_checks)}', f'{len(_get_users(i_risks))}'
        return f'''
        a. 杠杆对账不平事件共{label_u_b(a)}个，受影响用户数{label_u_b(b)}<br>
        b. 合约对账不平事件共{label_u_b(c)}个，受影响用户数{label_u_b(d)}<br>
        c. 红包对账不平事件共{label_u_b(e)}个，受影响用户数{label_u_b(f)}<br>
        d. 理财对账不平事件共{label_u_b(g)}个，受影响用户数{label_u_b(h)}
        '''

    def __build_risk_event_abnormal_issuance(self):
        from app.models import RiskEventLog

        model = RiskEventLog
        rows = model.query.filter(
            model.reason.in_([
                model.Reason.ABNORMAL_ISSUANCE
            ]),
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).all()
        if self.period is self.Period.DAILY:
            ab_str = ''
            for row in rows:
                asset = row.source
                extra = json.loads(row.extra)
                chain = extra['chain']
                asset_str = f'{asset}-{chain}'
                delta_issue_amount = Decimal(extra['cur_issue_amount']) - Decimal(extra['his_issue_amount'])
                rate_str = format_percent(delta_issue_amount / Decimal(extra['his_issue_amount']), 2) \
                    if Decimal(extra['his_issue_amount']) else 0
                ab_str += f'币种{label_u_b(asset_str)}，增量{label_u_b(rate_str)}<br>'
            if not rows:
                ab_str += '无'
        else:
            pass
            monthly = defaultdict(int)
            ab_str = ''
            for row in rows:
                asset = row.source
                extra = json.loads(row.extra)
                chain = extra['chain']
                asset_str = f'{asset}-{chain}'
                monthly[asset_str] += 1
            for k, v in monthly.items():
                ab_str += f'{label_u_b(k)}, 增发{label_u_b(v)}次<br>'
            if not rows:
                ab_str += '无<br><br>'
        return ab_str

    def __build_risk_event_no_deals(self):
        from app.models import RiskEventLog

        model = RiskEventLog
        rows = model.query.filter(
            model.reason.in_([
                model.Reason.NO_DEALS
            ]),
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).all()
        if self.period is self.Period.DAILY:
            down_str = f'（6）币币/合约交易撮合（宕机）<br>'
            for row in rows:
                server_name_str = '现货' if row.source == 'server' else '合约'
                extra = json.loads(row.extra)
                time_str = row.start_time.strftime('%Y-%m-%d %H:%M:%S')
                down_str += f'（{server_name_str}）宕机时间点：{label_u_b(time_str)}，持续时间：{label_u_b(extra["minutes"])}分钟<br>'
            else:
                if rows:
                    down_str = down_str[:-4]
                else:
                    down_str += '无'
        else:
            down_str = f'（7）币币/合约交易撮合（宕机）<br>'
            server_minutes = defaultdict(lambda: {'minutes': 0, 'cnt': 0})
            for row in rows:
                if row.source == 'server':
                    server_name_str = '现货'
                else:
                    server_name_str = '合约'
                extra = json.loads(row.extra)
                server_minutes[server_name_str]['minutes'] += Decimal(extra['minutes'])
                server_minutes[server_name_str]['cnt'] += 1
            for k, v in server_minutes.items():
                down_str += f'（{k}）宕机次数({label_u_b(v["cnt"])})：{label_u_b(v["minutes"])}分钟<br>'
            else:
                if server_minutes:
                    down_str = down_str[:-4]
                else:
                    down_str += '无'
        return down_str

    @classmethod
    def __get_user_risks(cls, events, reason):
        from app.models import RiskUser

        time_list = []
        for row in events:
            time_list.append(row.start_time)
            time_list.append(row.end_time)
        max_time, min_time = None, None
        if len(time_list) > 1:
            max_time, min_time = max(time_list), min(time_list)
        risks = []
        model = RiskUser
        if max_time and min_time:
            risks = model.query.filter(
                model.created_at >= min_time,
                model.created_at < max_time,
                model.reason == reason
            ).all()
        return risks

    def _build_risk_sms(self):
        if self.period is self.Period.DAILY:
            return {'sms': ''}
        model = DailyCountryReachReport
        yesterday = self.start_dt.date()
        _30days_ago = yesterday - datetime.timedelta(days=30)
        _60days_ago = yesterday - datetime.timedelta(days=60)
        rows = model.query.filter(
            model.report_date >= _60days_ago,
            model.report_date <= yesterday,
        ).all()
        date_to_rows = {}
        cur_date = _30days_ago
        while cur_date <= yesterday:
            range_begin = cur_date - datetime.timedelta(days=30)
            range_end = cur_date
            for row in rows:
                if range_begin <= row.report_date <= range_end:
                    date_to_rows.setdefault(cur_date, []).append(row)
            cur_date += datetime.timedelta(days=1)

        def _update_alerts(_date, _rows):
            date_cnt_map = {}
            country_30days_cnt_map = defaultdict(int)  # 短信发送数
            country_30days_map = defaultdict(int)  # 有效发送天数
            for _row in _rows:
                if _row.country == "":
                    # 忽略 全部国家的汇总
                    continue
                if _row.report_date == _date:
                    date_cnt_map[_row.country] = _row.sms_send_count
                else:
                    country_30days_cnt_map[_row.country] += _row.sms_send_count
                    country_30days_map[_row.country] += 1

            basic_count = 100  # 【昨日发送数】大于这个数才告警
            multiple = 1.5  # 【昨日发送数】大于30天平均发送数的 multiple 倍才告警
            for country, date_count in date_cnt_map.items():
                if date_count <= basic_count:
                    continue
                _30days_count = country_30days_cnt_map[country]
                days = country_30days_map[country]
                if days == 0:
                    continue
                _30days_avg_count = int(_30days_count / days)
                if date_count >= _30days_avg_count * multiple:
                    country_alerts[country] += 1

        country_alerts = defaultdict(int)
        for date_, date_rows in date_to_rows.items():
            _update_alerts(date_, date_rows)
        country_name_map = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        alert_str = '（6）短信异常用量告警：<br>'
        for country, alerts_cnt in country_alerts.items():
            country_name = country_name_map.get(country, country)
            if alerts_cnt < 4:  # filter
                continue
            alert_str += f'{country_name}({country})，告警次数{label_u_b(alerts_cnt)}；<br>'
        if alert_str:
            alert_str = alert_str[:-4]
        if not country_alerts:
            alert_str += '无'
        return {'sms': alert_str}

    def _build_risk_margin_burst(self):
        model = MarginLiquidationHistory
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt
        ).all()
        date_to_rows = {}
        liq_ids = set()
        for row in rows:
            liq_ids.add(row.id)
            date_to_rows.setdefault(row.created_at.date(), []).append(row)
        fee_model = MarginLiquidationFeeHistory
        fee_rows = fee_model.query.filter(
            fee_model.liq_id.in_(liq_ids)
        ).all()
        fee_mapping = {v.liq_id: v for v in fee_rows}
        price_range_map = {}
        if date_to_rows:
            min_date, max_date = min(date_to_rows.keys()), max(date_to_rows.keys())
            price_range_map = AssetPrice.get_close_price_range_map(min_date, max_date)
        burst_usd = insurance_usd = 0
        for date_, date_rows in date_to_rows.items():
            for date_row in date_rows:
                base, quote = date_row.base_asset, date_row.quote_asset
                price_map = price_range_map.get(date_) or {}
                base_rate, quote_rate = price_map.get(base, 1), price_map.get(quote, 1)
                insurance_base_fee = date_row.loan_base_asset_amount
                insurance_quote_fee = date_row.loan_quote_asset_amount
                liq_fee = fee_mapping.get(date_row.id)
                if liq_fee:
                    base_fee, quote_fee = liq_fee.deduct_base_fee, liq_fee.deduct_quote_fee
                else:
                    base_fee, quote_fee = 0, 0
                burst_usd += base_fee * base_rate + quote_fee * quote_rate
                insurance_usd += insurance_base_fee * base_rate + insurance_quote_fee * quote_rate
        return {
            'margin_burst': f'杠杆市场强平订单{label_u_b(len(rows))}个，'
                            f'爆仓费收入{label_u_b(amount_to_str(burst_usd, 2))}，穿仓亏损{label_u_b(amount_to_str(insurance_usd, 2))}'
        }

    def _build_risk_margin_fund(self):
        model = DailyMarginFundReport
        rows = model.query.filter(
            model.report_date >= self.start_dt.date(),
            model.report_date < self.end_dt.date(),
            model.asset.in_(['USDT', 'ETH', 'BTC'])
        ).all()
        total_mapping = defaultdict(lambda: {'amount': 0, 'balance': 0})
        group_by_asset = defaultdict(list)
        for row in rows:
            total_mapping[row.asset]['amount'] += row.amount
            group_by_asset[row.asset].append(row)
        for asset, row_list in group_by_asset.items():
            sorted_rows = sorted(row_list, key=lambda r: r.report_date, reverse=True)
            if sorted_rows:
                last_row = sorted_rows[0]
                total_mapping[last_row.asset]['balance'] = last_row.balance
        content = '杠杆保险基金变动: <br>'
        for k, v in total_mapping.items():
            if v['amount'] >= 0:
                color = 'red'
            else:
                color = 'green'
            content += f'{k}:变动{label_font(label_u_b(amount_to_str(v["amount"], 2)), color=color)}，' \
                       f'总数{label_u_b(amount_to_str(v["balance"], 2))}<br>'
        else:
            if total_mapping:
                content = content[:-4]
        return {'margin_fund': content}

    def _build_risk_margin_liquidation(self):
        model = DailySpotMarginBurstReport
        rows = model.query.filter(
            model.report_date >= self.start_dt.date(),
            model.report_date < self.end_dt.date(),
        ).all()
        date_to_rows = {}
        for row in rows:
            date_to_rows.setdefault(row.report_date, []).append(row)
        price_range_mapping = {}
        if date_to_rows:
            min_date, max_date = min(date_to_rows.keys()), max(date_to_rows.keys())
            price_range_mapping = AssetPrice.get_close_price_range_map(min_date, max_date)
        market_mapping = defaultdict(Decimal)
        for date_, date_rows in date_to_rows.items():
            for row in date_rows:
                base, quote = row.base_asset, row.quote_asset
                base_fund, quote_fund = row.base_asset_fund_amount, row.quote_asset_fund_amount
                price_map = price_range_mapping.get(date_) or {}
                base_rate, quote_rate = price_map.get(base, 1), price_map.get(quote, 1)
                base_usd = base_fund * base_rate
                quote_usd = quote_fund * quote_rate
                if base_usd + quote_usd > 0:
                    market_mapping[row.market] += base_usd + quote_usd
        ret = sorted([(k, v) for k, v in market_mapping.items()], key=lambda t: t[1], reverse=True)
        ret5 = ret[:5]
        content = ''
        for i, (market, amount) in enumerate(ret5, start=1):
            if i == len(ret5):
                content += f'市场 {market}，穿仓损失：{label_u_b(amount_to_str(amount, 2))} USD'
            else:
                content += f'市场 {market}，穿仓损失：{label_u_b(amount_to_str(amount, 2))} USD<br>'
        if not ret5:
            content += '无'
        return {'margin_liquidation': content}

    def _build_risk_perp_auto_deleveraging(self):
        rows = PerpetualSysHistoryDB.get_auto_deleveraging_records(
            self.start_ts,
            self.end_ts
        )
        return {'perp_auto_deleveraging': f'自动减仓用户{label_u_b(len(rows))}个'}

    def _build_risk_perp_liquidation(self):
        _, records = PerpetualSysHistoryDB.get_liquidation_history(
            self.start_ts,
            self.end_ts,
            insurance_change=PerpetualSysHistoryDB.InsuranceChange.LT_0,
            export=True
        )
        market_list = {i['market'] for i in records}
        market_query = PerpetualMarket.query.filter(
            PerpetualMarket.name.in_(market_list)).with_entities(
            PerpetualMarket.name,
            PerpetualMarket.market_type,
            PerpetualMarket.status,
        ).all()
        market_type_map = {i.name: i.market_type.value for i in market_query}
        online_market_list = {i.name for i in market_query if i.status == PerpetualMarket.StatusType.OPEN}
        for item in records:
            all_deal_stock, all_insurance = PerpetualHistoryDB.sum_deal_history(
                item['user_id'], item['position_id'])
            all_deal_stock = all_deal_stock or 0
            all_insurance = all_insurance or 0
            item['average_deal_price'] = Decimal()
            if all_deal_stock > Decimal():
                if market_type_map[item['market']] == PerpetualMarketType.DIRECT.value:
                    item['average_deal_price'] = all_deal_stock / item['liq_amount']
                else:
                    item['average_deal_price'] = item['liq_amount'] / all_deal_stock
            item['insurance_change'] = all_insurance

            asset = PerpetualMarketCache.get_balance_asset(item['market']) \
                if item['market'] in online_market_list else PerpetualOfflineMarketCache.get_balance_asset(
                item['market'])
            asset_rate = Decimal('1') if asset == 'USD' else PriceManager.asset_to_usd(asset)

            insurance = all_insurance * asset_rate
            item['insurance_change_value'] = insurance
        total_amount = sum([i['insurance_change_value'] for i in records])
        return {
            'perp_liquidation': f'全站合约市场爆仓次数之和: {label_u_b(len(records))}，'
                                f'穿仓垫付总金额: {label_u_b(amount_to_str(abs(total_amount), 2))} USD'
        }

    def _build_risk_perp_insurance(self):
        model = DailyPerpetualInsuranceReport
        rows = model.query.filter(
            model.report_date >= self.start_dt.date(),
            model.report_date < self.end_dt.date(),
            model.asset.in_(['USDT', 'ETH', 'BTC'])
        ).all()
        total_mapping = defaultdict(lambda: {'amount': 0, 'balance': 0})
        group_by_asset = defaultdict(list)
        for row in rows:
            total_mapping[row.asset]['amount'] += row.increase_amount - row.decrease_amount
            group_by_asset[row.asset].append(row)
        for asset, row_list in group_by_asset.items():
            sorted_rows = sorted(row_list, key=lambda r: r.report_date, reverse=True)
            if sorted_rows:
                last_row = sorted_rows[0]
                total_mapping[last_row.asset]['balance'] = last_row.total_balance
        content = '保险基金变动: <br>'
        for k, v in total_mapping.items():
            if v['amount'] >= 0:
                color = 'red'
            else:
                color = 'green'
            content += f'{k}:变动{label_font(label_u_b(amount_to_str(v["amount"], 2)), color=color)}，' \
                       f'总数{label_u_b(amount_to_str(v["balance"], 2))}<br>'
        return {'perp_insurance': content}

    def _build_risk_perp_burst(self):
        inverse_markets = [
            row.name
            for row in PerpetualMarket.query.with_entities(
                PerpetualMarket.name
            ).filter(
                PerpetualMarket.market_type == PerpetualMarketType.INVERSE
            ).all()
        ]
        model = DailyPerpetualMarginBurstReport
        rows = model.query.filter(
            model.report_date >= self.start_dt.date(),
            model.report_date < self.end_dt.date(),
            model.market.notin_(inverse_markets)
        ).all()
        market_mapping = defaultdict(lambda: {'amount': 0, 'usd': 0})
        for row in rows:
            market_mapping[row.market]['amount'] += row.liquidation_insurance_amount
            market_mapping[row.market]['usd'] += row.liquidation_insurance_usd
        ret = sorted([(k, v['amount'], v['usd']) for k, v in market_mapping.items()], key=lambda t: t[-1], reverse=True)
        ret5 = ret[:5]
        content = ''
        online_market_list = set(PerpetualMarketCache().get_market_list())
        for market, amount, _ in ret5:
            _cache = PerpetualMarketCache if market in online_market_list else PerpetualOfflineMarketCache
            balance_asset = _cache.get_balance_asset(market)
            content += f'市场 {market}, 穿仓损失: {label_u_b(amount_to_str(amount, 2))} {balance_asset}<br>'
        else:
            if ret5:
                content = content[:-4]
        if not ret5:
            content += '无'
        return {'perp_burst': content}

    def _build_risk_kyc(self):
        map1 = self.__build_risk_kyc_person()
        map2 = self.__build_risk_kyc_institution()
        map3 = self.__build_risk_kyc_change_rate()
        return {**map1, **map2, **map3}

    def __build_risk_kyc_person(self):
        model = KycVerification
        rows = model.query.filter(
            model.updated_at >= self.start_dt,
            model.updated_at < self.end_dt,
            model.status.in_([
                model.Status.SCREENING,
                model.Status.PASSED,
                model.Status.REJECTED,
                model.Status.CANCELLED  # 二次提交后会重写为 cancelled
            ])
        ).all()
        auto, admin = [], []
        for row in rows:
            if row.auditor_id is None:
                auto.append(row)
            else:
                admin.append(row)

        def _cal_approved_rejected_cnt(_rows):
            approved, rejected = 0, 0
            for _row in _rows:
                if _row.status in [
                    model.Status.SCREENING,
                    model.Status.PASSED
                ]:
                    approved += 1
                else:
                    rejected += 1
            return approved, rejected

        def _cal_rate(x1, x2):
            if x2 != 0:
                return format_percent(Decimal(x1) / Decimal(x2))
            return '0%'

        auto_approved, auto_rejected = _cal_approved_rejected_cnt(auto)
        auto_kyc = f'个人KYC自动审核共{label_u_b(len(auto))}条，' \
                   f'其中审核通过{label_u_b(auto_approved)}条,' \
                   f'审核拒绝{label_u_b(auto_rejected)}条' \
                   f'，通过率{label_u_b(_cal_rate(auto_approved, len(auto)))}<br>'
        admin_approved, admin_rejected = _cal_approved_rejected_cnt(admin)
        admin_kyc = f'个人KYC人工审核共{label_u_b(len(admin))}条，' \
                    f'其中审核通过{label_u_b(admin_approved)}条,' \
                    f'审核拒绝{label_u_b(admin_rejected)}条，' \
                    f'通过率{label_u_b(_cal_rate(admin_approved, len(admin)))}<br>'

        all_kyc = f'个人KYC通过率{label_u_b(_cal_rate(auto_approved + admin_approved, len(rows)))}'
        return {'auto_kyc': auto_kyc, 'admin_kyc': admin_kyc, 'all_kyc': all_kyc}

    def __build_risk_kyc_institution(self):
        model = KYCInstitution
        rows = model.query.filter(
            model.updated_at >= self.start_dt,
            model.updated_at < self.end_dt,
            model.status.in_([
                model.Status.PASSED,
                model.Status.REJECTED,
            ])
        ).all()
        approved = rejected = 0
        for row in rows:
            if row.status is model.Status.PASSED:
                approved += 1
            else:
                rejected += 1
        total = len(rows)
        if total > 0:
            rate = format_percent(Decimal(approved) / Decimal(total))
        else:
            rate = '0%'
        content = f'机构KYC共审核{label_u_b(total)}条，' \
                  f'其中审核通过{label_u_b(approved)}条审核拒绝{label_u_b(rejected)}条，通过率{label_u_b(rate)}'
        return {'kyc_institution': content}

    def __build_risk_kyc_change_rate(self):
        pre_end = self.start_dt
        if self.period is self.Period.DAILY:
            pre_start = self.start_dt - datetime.timedelta(days=1)
        else:
            pre_start = self.start_dt - datetime.timedelta(days=30)

        def get_kyc_person_info(st=None, ed=None):
            model = KycVerification
            query = model.query.filter(
                model.status.in_([
                    model.Status.SCREENING,
                    model.Status.PASSED,
                ])
            )
            if st:
                query = query.filter(model.updated_at >= st)
            if ed:
                query = query.filter(model.updated_at < ed)
            rows = query.all()
            return len(rows)

        cur_pcnt = get_kyc_person_info(self.start_dt, self.end_dt)
        pre_pcnt = get_kyc_person_info(pre_start, pre_end)
        total_pcnt = get_kyc_person_info(None, None)

        def get_kyc_ins_info(st=None, ed=None):
            model = KYCInstitution
            query = model.query.filter(
                model.status.in_([
                    model.Status.PASSED,
                ])
            )
            if st:
                query = query.filter(model.created_at >= st)
            if ed:
                query = query.filter(model.created_at < ed)
            rows = query.all()
            return len(rows)

        cur_icnt = get_kyc_ins_info(self.start_dt, self.end_dt)
        pre_icnt = get_kyc_ins_info(pre_start, pre_end)
        total_icnt = get_kyc_ins_info(None, None)
        rate = '0%'
        if pre_pcnt + pre_icnt != 0:
            rate = Decimal((cur_pcnt + cur_icnt) - (pre_pcnt + pre_icnt)) / Decimal(pre_pcnt + pre_icnt)
            rate = format_percent(rate)
        irate = format_percent(safe_div(cur_icnt - pre_icnt, pre_icnt))
        prate = format_percent(safe_div(cur_pcnt - pre_pcnt, pre_pcnt))
        c1 = f'周期内新增通过实名认证用户数{label_u_b(cur_pcnt + cur_icnt)}（本周期对比上周期的变动率{label_u_b(rate)}，' \
             f'其中通过个人KYC用户{label_u_b(cur_pcnt)}个变动率{label_u_b(prate)}，' \
             f'通过机构KYC用户{label_u_b(cur_icnt)}个 变动率{label_u_b(irate)}）'

        c2 = f'当前周期内平台全部用户（个人+机构）' \
             f'实名认证率{label_u_b(format_percent(safe_div(total_pcnt + total_icnt, self.site_user_count)))}'
        return {'kyc_change_rate': c1, 'kyc_rate': c2}

    @cached_property
    def site_user_count(self):
        model = User
        return model.query.with_entities(func.count(model.id)).scalar() or 0

    def _build_risk_screen(self):
        model = UserRiskScreenCase
        rows = model.query.filter(
            or_(
                and_(
                    model.auditor_id.is_(None),
                    model.created_at >= self.start_dt,
                    model.created_at < self.end_dt,
                ),
                and_(
                    model.auditor_id.is_not(None),
                    model.audited_at >= self.start_dt,
                    model.audited_at < self.end_dt,
                )
            ),
            model.status.in_([
                model.Status.PASSED,
                model.Status.RISKED,
            ])
        ).all()
        auto, admin = [], []
        for row in rows:
            if row.auditor_id is None:
                auto.append(row)
            else:
                admin.append(row)

        def _cal_approved_rejected(_rows):
            approved = rejected = 0
            for _row in _rows:
                if _row.status is model.Status.PASSED:
                    approved += 1
                else:
                    rejected += 1
            return approved, rejected

        auto_approved, auto_rejected = _cal_approved_rejected(auto)
        admin_approved, admin_rejected = _cal_approved_rejected(admin)
        c1 = f'自动审核共{label_u_b(len(auto))}条，通过率{label_u_b(format_percent(safe_div(auto_approved, len(rows))))}<br>'
        c2 = f'人工审核共{label_u_b(len(admin))}条，' \
             f'其中审核通过{label_u_b(admin_approved)}条审核拒绝{label_u_b(admin_rejected)}条，' \
             f'通过率{label_u_b(format_percent(safe_div(admin_approved, len(admin))))}'

        sub_query = select(SignOffUser.query.with_entities(SignOffUser.user_id).subquery())
        total_rows = model.query.filter(
            model.status.in_([
                model.Status.RISKED,
                model.Status.PASSED,
            ]),
            model.country.notin_(['CHN', 'USA']),
            model.user_id.notin_(sub_query),
        ).all()
        total_approved_cnt, total_rejected_cnt = _cal_approved_rejected(total_rows)
        c3 = f'当前累计清退用户共{label_u_b(total_rejected_cnt)}个（审核拒绝记录总数，不含大陆清退用户、美国清退用户、已注销用户），' \
             f'清退占比{label_u_b(format_percent(safe_div(total_rejected_cnt, len(total_rows))))}（审核拒绝记录总数/总审核数）'

        kyc_pcnt = KycVerification.query.with_entities(
            func.count()
        ).filter(
            KycVerification.status.in_([
                KycVerification.Status.PASSED,
            ]),
            KycVerification.country.notin_(['CHN', 'USA']),
            KycVerification.user_id.notin_(sub_query),
        ).scalar() or 0
        kyc_icnt = KYCInstitution.query.with_entities(
            func.count()
        ).filter(
            KYCInstitution.status.in_([
                KYCInstitution.Status.PASSED,
            ])
        ).scalar() or 0
        c4 = f'周期内当前平台用户风险筛查比例' \
             f'{label_u_b(format_percent(safe_div(total_approved_cnt, kyc_pcnt + kyc_icnt)))}'
        return {'screen_auto': c1, 'screen_admin': c2, 'screen_total': c3, 'screen_kyc': c4}

    def _build_risk_sp_config(self):
        if self.period is self.Period.DAILY:
            return {'sp_config': ''}
        str1 = self.__build_risk_withdrawal_whitelist()
        str2 = self.__build_risk_withdrawal_privileged_users()
        str3 = self.__build_risk_withdrawal_only_whitelist()
        str4 = self.__build_risk_withdrawal_only_users()
        str5 = self.__build_risk_daily_withdrawal_limit()
        return {'sp_config': f'<br>3、特殊配置相关数据<br>{str1}<br>{str2}<br>{str3}<br>{str4}<br>{str5}'}

    def __build_risk_withdrawal_whitelist(self):
        if self.period is self.Period.DAILY:
            return ''
        model = WithdrawalWhitelistUser
        new, cnt = self._get_cnt(model)
        c = f'（1）免风控提现白名单：新增用户{label_u_b(new)}个，当前名单内用户共{label_u_b(cnt)}个<br>'
        return c

    def __build_risk_withdrawal_privileged_users(self):
        if self.period is self.Period.DAILY:
            return ''
        model = WithdrawalPrivilegedUser
        new, cnt = self._get_cnt(model)
        c = f'（2）特殊提现白名单：新增用户{label_u_b(new)}个，当前名单内用户共{label_u_b(cnt)}个<br>'
        return c

    def __build_risk_withdrawal_only_whitelist(self):
        from app.business.admin_tag import TagType, AdminTagUser
        if self.period is self.Period.DAILY:
            return ''
        new = AdminTagUser.query.filter(
            AdminTagUser.created_at >= self.start_dt,
            AdminTagUser.created_at < self.end_dt,
            AdminTagUser.tag_id == TagType.CLEAR_WHITELIST.value,
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).count()
        cnt = AdminTagUser.query.filter(
            AdminTagUser.tag_id == TagType.CLEAR_WHITELIST.value,
            AdminTagUser.status == AdminTagUser.Status.PASSED
        ).count()
        c = f'（3）仅支持提现白名单：新增用户{label_u_b(new)}个，当前名单内用户共{label_u_b(cnt)}个<br>'
        return c

    def __build_risk_withdrawal_only_users(self):
        if self.period is self.Period.DAILY:
            return ''
        model = ClearedUser
        cnt = len(model.get_user_ids(ClearedUser.Status.WITHDRAWAL_ONLY))
        new = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.status == model.Status.WITHDRAWAL_ONLY,
            model.valid.is_(True),
        ).count()
        c = f'（4）仅支持提现用户列表：新增用户{label_u_b(new)}个，当前名单内用户共{label_u_b(cnt)}个<br>'
        return c

    def __build_risk_daily_withdrawal_limit(self):
        if self.period is self.Period.DAILY:
            return ''
        model = UserSetting
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.status == model.Status.VALID,
            model.key == func.binary('daily_withdrawal_limit')
        ).all()
        cnt = model.query.with_entities(func.count()).filter(
            model.status == model.Status.VALID,
            model.key == func.binary('daily_withdrawal_limit')
        ).scalar() or 0
        c = f'（5）特殊提现额度列表：新增用户{label_u_b(len(rows))}个，当前名单内用户共{label_u_b(cnt)}个<br>'
        return c

    def _get_cnt(self, model):
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.status == model.Status.VALID
        ).all()
        cnt = model.query.with_entities(func.count()).filter(
            model.status == model.Status.VALID
        ).scalar() or 0
        return len(rows), cnt

    def _build_risk_2fa(self):
        if self.period is self.Period.DAILY:
            return {'2fa': ''}
        str1 = self.__build_risk_2fa()
        str2 = self.__build_risk_2fa_answer()
        return {'2fa': f'<br>5、重置安全工具相关数据<br>{str1}<br>{str2}'}

    def __build_risk_2fa(self):
        if self.period is self.Period.DAILY:
            return ''
        model = SecurityResetApplication
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.status != model.StatusType.CREATED
        ).all()
        approved = 0
        for row in rows:
            if row.status is model.StatusType.PASSED:
                approved += 1
        return f'1)重置安全工具用户总数{label_u_b(len(rows))}（人工重置＋系统重置）、' \
               f'重置安全工具通过率{label_u_b(format_percent(safe_div(approved, len(rows))))}（通过数/审核总数）<br>'

    def __build_risk_2fa_answer(self):
        if self.period is self.Period.DAILY:
            return ''
        model = SecurityResetAnswerHistory
        rows = model.query.filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).all()
        succeed = failed = 0
        failed_mapping = defaultdict(int)
        for row in rows:
            if row.status is model.Status.SUCCEEDED:
                succeed += 1
            else:
                failed_mapping[row.user_id] += 1
        for _, cnt in failed_mapping.items():
            if cnt >= 3:
                failed += 1
        c = f'2)通过答题成功重置用户共{label_u_b(succeed)}个<br>'
        c += f'三次答题失败用户共{label_u_b(failed)}个<br>'
        return c

    def _build_risk_deposit_found(self):
        if self.period is self.Period.DAILY:
            return {'deposit_found': ''}
        model = AbnormalDepositApplication
        found = model.query.with_entities(
            func.count()
        ).filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
            model.status == model.Status.FINISHED
        ).scalar() or 0

        total = model.query.with_entities(
            func.count()
        ).filter(
            model.created_at >= self.start_dt,
            model.created_at < self.end_dt,
        ).scalar() or 0
        c = f'找回成功记录共{label_u_b(found)}条<br>'
        c += f'找回成功比例{label_u_b(format_percent(safe_div(found, total), 2))}（找回成功数/记录总数）<br>'
        return {'deposit_found': f'6、充值自助找回相关数据<br>{c}'}

    def _save(self, content, extra):
        if self.period is self.Period.DAILY:
            return
        cache = HistoryRiskReportCache(self.start_dt.date())
        data = {
            'content': content,
            'extra': json.dumps(extra, cls=JsonEncoder)
        }
        cache.hmset(mapping=data)


def label_u_b(content) -> str:
    return label_u(label_b(content))


def label_u(content) -> str:
    return f'<u>{content}</u>'


def label_b(content) -> str:
    return f'<b>{content}</b>'


def label_font(content, color: str = 'red') -> str:
    return f'<font color="{color}">{content}</font>'
