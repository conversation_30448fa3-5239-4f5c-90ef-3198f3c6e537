# -*- coding: utf-8 -*-
from datetime import timed<PERSON>ta
from typing import List

from celery.schedules import crontab
from flask import current_app
from sqlalchemy import and_, or_
from app.business.activity.trade import TradeActivityBusiness
from app.business.lock import lock_call
from .. import config

from ..assets import asset_to_chains, get_asset_chain_config
from ..business import LockAssetHelper
from ..business.activity.ambassador import ActivityBusiness
from ..business.activity.deposit_bonus import DepositBonusActivityBusiness
from ..business.gift import update_gift_history_task, update_deposit_activity_rank_task, \
    send_deposit_activity_gift_task, gift_history_revoke, admin_gift_history_revoke
from ..business.p2p.utils import send_p2p_alert

from ..common import CeleryQueues, BalanceBusiness
from ..models import (
    db, Activity, DepositActivity, TradeRankActivity, DepositActivityRank, OperationAmbassadorActivity, GiftHistory,
    LockedAssetBalance, AdminGiftBalanceHistory, DepositBonusActivity, DepositBonusActivityConfig
)
from ..utils import (scheduled, route_module_to_celery_queue, now)

route_module_to_celery_queue(__name__, CeleryQueues.GIFT)


@scheduled(crontab(minute='*/5'))
def update_gift_history_schedule():
    update_gift_history_task.delay(
        Activity.REFERRAL_ID, BalanceBusiness.REFERRAL.value, pay_from_admin=False)
    update_gift_history_task.delay(
        Activity.get_or_create_broker_referral_id(),
        BalanceBusiness.BROKER_REFERRAL.value, pay_from_admin=False)
    update_gift_history_task.delay(
        Activity.get_or_create_indirect_referral_id(),
        BalanceBusiness.INDIRECT_REFERRAL.value,
        pay_from_admin=False,
    )

    def _do_update_task(activities, pay_from_admin_user_id=0):
        for record in activities:
            update_gift_history_task.delay(
                record.activity_id, BalanceBusiness.GIFT.value,
                pay_from_admin_user_id=pay_from_admin_user_id,
            )

    # 充值送币活动(定额、比例进行中，瓜分、排名结束一天内)派奖
    deposit_activities: List[DepositActivity] = DepositActivity.query.filter(
        or_(
            and_(
                DepositActivity.status == DepositActivity.Status.PASSED,
                DepositActivity.type.in_(
                    [DepositActivity.Type.FIX, DepositActivity.Type.RATE]),
                DepositActivity.started_at <= now(),
                DepositActivity.ended_at > now()
            ),
            and_(
                DepositActivity.status == DepositActivity.Status.FINISHED,
                DepositActivity.type.in_([DepositActivity.Type.PARTITION,
                                          DepositActivity.Type.RANK]),
                DepositActivity.ended_at <= now(),
                DepositActivity.ended_at > now() + timedelta(days=-5)
            )
        )
    ).all()
    _do_update_task(activities=deposit_activities)

    trade_activities: List[TradeRankActivity] = TradeRankActivity.query.filter(
        TradeRankActivity.status == TradeRankActivity.Status.FINISHED,
        TradeRankActivity.ended_at <= now(),
        TradeRankActivity.ended_at > now() + timedelta(days=-30)
    ).all()
    spot_trade_activities = []
    perpetual_trade_activities = []
    for r in trade_activities:
        if r.type in [TradeRankActivity.Type.SPOT_TRADE, TradeRankActivity.Type.SPOT_NET_BUY]:
            spot_trade_activities.append(r)
        else:
            perpetual_trade_activities.append(r)
    _do_update_task(activities=spot_trade_activities, pay_from_admin_user_id=config["SPOT_TRADE_RANK_ADMIN_USER_ID"])
    _do_update_task(activities=perpetual_trade_activities, pay_from_admin_user_id=config["PERPETUAL_TRADE_RANK_ADMIN_USER_ID"])

    ambassador_activities: List[OperationAmbassadorActivity] = OperationAmbassadorActivity.query.filter(
        OperationAmbassadorActivity.status == OperationAmbassadorActivity.Status.FINISHED,
        OperationAmbassadorActivity.ended_at <= now(),
        OperationAmbassadorActivity.ended_at > now() + timedelta(days=-30)
    ).all()
    _do_update_task(activities=ambassador_activities, pay_from_admin_user_id=config["AMBASSADOR_ACTIVITY_ADMIN_USER_ID"])
    deposit_bonus_activities: List[DepositBonusActivity] = DepositBonusActivity.query.with_entities(
        DepositBonusActivity.id
    ).filter(
        DepositBonusActivity.status == DepositBonusActivity.StatusType.FINISHED,
        DepositBonusActivity.end_time <= now(),
        DepositBonusActivity.end_time > now() + timedelta(days=-30)
    ).all()
    deposit_bonus_cfgs = DepositBonusActivityConfig.query.filter(
        DepositBonusActivityConfig.deposit_bonus_id.in_([x.id for x in deposit_bonus_activities])
    ).all()
    _do_update_task(activities=deposit_bonus_cfgs, pay_from_admin_user_id=config['DEPOSIT_BONUS_ADMIN_USER_ID'])

    # 激励包结算奖励
    activity_id = Activity.get_or_create_ambassador_package_activity_id()
    update_gift_history_task.delay(
        activity_id, BalanceBusiness.AMBASSADOR_PACKAGE_SETTLEMENT.value,
        pay_from_admin_user_id=config['AMBASSADOR_PACKAGE_USER_ID']
    )


@scheduled(crontab(hour='*/1', minute=0))
def fix_add_deposit_gift_schedule():
    """充值送币(瓜分、排行)活动重试"""
    deposit_activities: List[DepositActivity] = DepositActivity.query.filter(
        DepositActivity.type.in_([DepositActivity.Type.PARTITION,
                                  DepositActivity.Type.RANK]),
        DepositActivity.status == DepositActivity.Status.FINISHED,
        DepositActivity.ended_at <= now(),
        DepositActivity.ended_at > now() + timedelta(days=-1)
    ).all()
    for record in deposit_activities:
        query = DepositActivityRank.query.filter(
            DepositActivityRank.activity_id == record.activity_id
        ).order_by(
            DepositActivityRank.rank
        )
        for v in query:
            send_deposit_activity_gift_task.delay(record.id, v.user_id)


@scheduled(crontab(hour='*/1', minute='20,50'))
@lock_call(wait=False)
def update_hourly_trade_value_schedule():
    activity_list: List[TradeRankActivity] = TradeRankActivity.query.filter(
        TradeRankActivity.status == TradeRankActivity.Status.ONLINE,
        TradeRankActivity.started_at < now(),
        TradeRankActivity.ended_at > now() + timedelta(hours=-1)
    ).order_by(TradeRankActivity.started_at).all()
    for activity in activity_list:
        TradeActivityBusiness(activity.id).update_user_values()


@scheduled(crontab(hour='*/1', minute='5'))
@lock_call(wait=False)
def update_ambassador_activity_hourly_schedule():
    model = OperationAmbassadorActivity
    activity_list: List[OperationAmbassadorActivity] = model.query.filter(
        model.status == model.Status.ONLINE,
        model.started_at < now(),
        model.ended_at > now() + timedelta(hours=-1)
    ).order_by(model.started_at).all()
    for activity in activity_list:
        ActivityBusiness(activity.id).update_user_infos()


@scheduled(crontab(hour='*/1', minute=0))
def turn_withdraw_switch_during_deposit_activity_schedule():
    records: List[DepositActivity] = DepositActivity.query.filter(
        DepositActivity.started_at <= now(),
        DepositActivity.ended_at > now(),
        DepositActivity.status == DepositActivity.Status.PASSED
    ).all()
    for item in records:
        for chain in asset_to_chains(item.deposit_asset):
            c = get_asset_chain_config(item.deposit_asset, chain)
            c.withdrawals_disabled_due_to_deposit_activity = True

    records = DepositActivity.query.filter(
        DepositActivity.ended_at <= now(),
        DepositActivity.ended_at >= now() + timedelta(hours=-1),
        DepositActivity.status != DepositActivity.Status.DELETED,
    ).all()
    for item in records:
        if item.type in [DepositActivity.Type.RATE, DepositActivity.Type.FIX]:
            if item.status != DepositActivity.Status.FINISHED:
                item.status = DepositActivity.Status.FINISHED
                db.session.commit()
        for chain in asset_to_chains(item.deposit_asset):
            c = get_asset_chain_config(item.deposit_asset, chain)
            c.withdrawals_disabled_due_to_deposit_activity = False


@scheduled(crontab(minute='*/10'))
def deposit_activity_rank_schedule():
    query = DepositActivity.query.filter(
        DepositActivity.type.in_([DepositActivity.Type.RANK,
                                  DepositActivity.Type.PARTITION]),
        DepositActivity.status == DepositActivity.Status.PASSED
    ).with_entities(DepositActivity.id)
    for v in query:
        update_deposit_activity_rank_task.delay(v.id)


@scheduled(crontab(minute='*/5'))
def fix_gift_history_to_revoke():
    # 补偿最近5天的 gift to revoke 记录
    model = GiftHistory
    l_model = LockedAssetBalance
    gift_rows = model.query.filter(
        model.status == model.Status.TO_REVOKE,
        model.updated_at >= now() - timedelta(days=1),
        model.updated_at < now() - timedelta(minutes=30),
    ).all()
    # 获取所有的 Lock 记录
    for gift_row in gift_rows:
        lock_row = LockAssetHelper.get_uni_row(l_model.Business.GIFT, gift_row.id, gift_row.user_id)
        # lock_row 记录已经解锁, 查看解锁的操作类型
        if lock_row.status == l_model.Status.UNLOCKED and lock_row.unlock_type == l_model.OpType.UNLOCK:
            # 到解锁时间，没有撤回，而是走了自动解锁行为
            gift_row.status = model.Status.FINISHED
            db.session.commit()
        else:
            # lock_row 记录还没有解锁
            try:
                gift_history_revoke(gift_row)
            except Exception as e:
                current_app.logger.error(f"fix revoke gift_id: {gift_row.id} reward error: {e}")


@scheduled(crontab(minute='*/5'))
def fix_admin_gift_history_revoke():
    # 补偿最近5天的 admin gift to revoke 记录
    a_model = AdminGiftBalanceHistory
    rows: [a_model] = a_model.query.filter(
        a_model.status == a_model.Status.TO_REVOKE,
        a_model.updated_at >= now() - timedelta(days=1),
        a_model.updated_at < now() - timedelta(minutes=30)
    ).all()
    for row in rows:
        try:
            admin_gift_history_revoke(row)
        except Exception as e:
            current_app.logger.error(f"fix revoke admin_gift_id: {row.id} reward error: {e}")


@scheduled(crontab(minute='*/10'))
def alter_gift_revoke_error():
    # 查看那些 revoke 补偿大于1天
    model = GiftHistory
    gift_rows = model.query.filter(
        model.status == model.Status.TO_REVOKE,
        model.updated_at <= now() - timedelta(days=1),
    ).all()
    if gift_rows:
        err_msg = f"礼物撤回补偿超过1天未处理, 请及时处理, 礼物 id 为 {[i.id for i in gift_rows]}"
        send_p2p_alert(err_msg)

    a_model = AdminGiftBalanceHistory
    admin_rows = a_model.query.filter(
        a_model.status == a_model.Status.TO_REVOKE,
        a_model.updated_at <= now() - timedelta(days=1),
    ).all()
    if admin_rows:
        admin_msg = (f"admin礼物撤回补偿超过1天未处理, 请及时处理, admin 记录 id 为 {[i.id for i in admin_rows]}, "
                     f"admin账户 {[i.user_id for i in admin_rows]}")
        send_p2p_alert(admin_msg)


@scheduled(crontab(hour='*/1', minute='5'))
@lock_call(wait=False)
def update_deposit_bonus_activity_hourly_schedule():
    model = DepositBonusActivity
    activity_list: List[DepositBonusActivity] = model.query.filter(
        model.status == model.StatusType.ONLINE,
        model.start_time < now(),
        model.end_time > now() + timedelta(hours=-1)
    ).order_by(model.start_time).all()
    for activity in activity_list:
        DepositBonusActivityBusiness(activity.id).update_user_infos()
