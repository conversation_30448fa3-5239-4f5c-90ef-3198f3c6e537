# -*- coding: utf-8 -*-

import re
import time
from datetime import <PERSON><PERSON><PERSON>
from typing import BinaryIO, Callable, List, NamedTuple, Optional, Tuple

from celery.schedules import crontab
from flask import current_app

from app.business import PerpetualLogDB, TradeLogDB
from app.business.alert import send_alert_notice
from app.business.amm import get_all_user_amm_assets
from app.business.margin.helper import MarginHelper
from app.common import CeleryQueues
from app.config import config
from app.models import CreditBalance, SubAccount, User
from app.models.onchain import OnchainTokenBalance, OnchainToken
from app.models.pledge import PledgePosition
from app.utils import (AWSBackupBucket, AzureBackupBlob, current_timestamp,
                       route_module_to_celery_queue, scheduled,
                       str_to_datetime, timestamp_to_date, today)
from app.utils.onchain import decimal_add
from app.utils.date_ import date_to_datetime

from .base import gzip_compress, txt_file, put_file

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


class _Group(NamedTuple):
    name : str
    order: int
    require: Optional[Callable]
    processors: List[Tuple[str, Callable[[], BinaryIO]]]

    def backup(self, name: str):
        def deco(fn):
            self.processors.append((name, fn))
            return fn
        return deco

_groups: List[_Group] = []

def group(name: str, *, order: int, require: Callable = None) -> _Group:
    grp = _Group(
        name=name,
        order=order,
        require=require,
        processors=[]
    )
    _groups.append(grp)
    return grp


def require_balance_snapshot():
    # wait for snapshot, up to 10 minutes.
    _now = current_timestamp(to_int=True)
    ts = _now - _now % 3600
    for _ in range(30):
        t1 = TradeLogDB.get_slice_history_timestamp(ts)
        t2 = PerpetualLogDB.get_slice_history_timestamp(ts)
        if t1 and t2:
            break
        time.sleep(20)
    else:
        raise RuntimeError('no balance snpashot.')


web_balance = group("web_balance", order=1)
server_balance = group("server_balance", order=2, require=require_balance_snapshot)
user_data = group("user", order=3)


@server_balance.backup("spot_balance")
def get_spot_balance():
    table = TradeLogDB.slice_balance_table()
    rows = table.select(
        'user_id', 'asset', 'account', 'SUM(balance)',
        group_by='user_id, asset, account'
    )
    header = ('user_id', 'asset', 'account', 'balance')
    return txt_file(header, rows)


@server_balance.backup("perpetual_balance")
def get_perpetual_balance():
    table = PerpetualLogDB.slice_balance_table()
    # 包含账户权益
    rows = table.select(
        'user_id', 'asset', 'SUM(balance)',
        group_by='user_id, asset'
    )
    header = ('user_id', 'asset', 'balance')
    return txt_file(header, rows)


@web_balance.backup("amm_balance")
def get_amm_balance():
    rows = []
    for user_id, assets in get_all_user_amm_assets().items():
        for asset, balance in assets.items():
            rows.append((user_id, asset, balance))
    header = ('user_id', 'asset', 'balance')
    return txt_file(header, rows)


@web_balance.backup("margin_liability")
def get_margin_liability():
    result = MarginHelper.group_margin_loan_order()
    rows = [(user, asset, account, v) for (account, user, asset), v in result.items()]
    header = ('user_id', 'asset', 'account', 'liability')
    return txt_file(header, rows)


@web_balance.backup("credit_liability")
def get_credit_liability():
    rows = CreditBalance.query.filter(
        CreditBalance.unflat_amount > 0
    ).with_entities(
        CreditBalance.user_id,
        CreditBalance.asset,
        CreditBalance.unflat_amount + CreditBalance.interest_amount
    ).all()
    headers = ('user_id', 'asset', 'liability')
    return txt_file(headers, rows)


@web_balance.backup("pledge_liability")
def get_pledge_liability():
    positions = PledgePosition.query.filter(
        PledgePosition.status == PledgePosition.Status.BORROWING
    ).with_entities(
        PledgePosition.user_id, PledgePosition.loan_asset, PledgePosition.debt_amount + PledgePosition.interest_amount
    ).all()
    headers = ('user_id', 'asset', 'liability')
    return txt_file(headers, positions)


@web_balance.backup("onchain_token")
def get_onchain_token_balance():
    rows = OnchainTokenBalance.query.join(OnchainToken, OnchainTokenBalance.token_id == OnchainToken.id) \
        .with_entities(
            OnchainTokenBalance.user_id,
            OnchainTokenBalance.available,
            OnchainTokenBalance.frozen,
            OnchainToken.chain,
            OnchainToken.contract,
            OnchainToken.symbol
    ).all()
    data = []
    for row in rows:
        balance = decimal_add(row.available, row.frozen)
        if balance <= 0:
            continue
        data.append((
            row.user_id,
            row.chain,
            row.contract,
            row.symbol,
            balance
        ))
    headers = ('user_id', 'chain', 'contract','symbol', 'balance')
    return txt_file(headers, data)


@user_data.backup("user")
def get_users():
    users = User.query.with_entities(
        User.id,
        User.email,
        User.mobile_country_code,
        User.mobile_num,
        User.login_password_hash,
    ).all()
    subs = SubAccount.query.with_entities(
        SubAccount.user_id,
        SubAccount.main_user_id
    ).all()
    subs = dict(subs)
    users = [(*x, subs.get(x[0])) for x in users]
    headers = ('id', 'email', 'mobile_country_code', 'mobile_num', 
               'login_password_hash', 'parent_user_id')
    return txt_file(headers, users)


@scheduled(crontab(minute=0, hour='*/1'))
def backup_core_data_schedule():
    if not config.get('BACKUP_ENABLED'):
        return
    alert_url = config["ADMIN_CONTACTS"]["web_notice"]
    try:
        # read all data in same group, then backup them.
        ts = current_timestamp(to_int=True) // 3600 * 3600
        _date = timestamp_to_date(ts).strftime('%Y%m%d')
        grp: _Group
        for grp in sorted(_groups, key=lambda x: x.order):
            if grp.require:
                grp.require()
            files = []
            for name, func in grp.processors:
                files.append((name, func()))

            for (name, file) in files:
                file = gzip_compress(file)
                key = f'{_date}/{name}_{ts}.gz'
                if not put_file(key, file):
                    msg = f"backup data {name} failed"
                    current_app.logger.error(msg)
                    send_alert_notice(msg, alert_url)
            current_app.logger.info(f'backup {grp.name} data success.')
    except Exception as e:
        msg = str(e.args[0] if e.args else e)
        current_app.logger.exception('backup data error')
        send_alert_notice(f'backup core data error: {e}', alert_url)


@scheduled(crontab(minute=10, hour='7'))
def clear_core_backup_data_schedule():
    if not config.get('BACKUP_ENABLED'):
        return
    regex = re.compile(r'^(\d{8})/(\w+)_\d+\.gz$')
    names = set(sum([[a for a, _ in x.processors] for x in _groups], []))
    earlist = date_to_datetime(today() - timedelta(days=14))
    for oss in (AWSBackupBucket, AzureBackupBlob):
        for key in oss.iter_objects():
            if not (m := regex.findall(key)):
                continue
            file_time, file_name = m[0]
            if file_name not in names:
                continue
            file_time = str_to_datetime(file_time)
            if file_time < earlist:
                oss.delete_file(key)
                current_app.logger.info(f"delete core backup data {key}")
