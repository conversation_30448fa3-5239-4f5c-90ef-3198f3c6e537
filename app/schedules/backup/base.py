# -*- coding: utf-8 -*-

import zlib
from datetime import date, datetime
from decimal import Decimal
from io import BytesIO
from typing import BinaryIO, Iterator, List, Tuple, IO
from enum import Enum

from dateutil.relativedelta import relativedelta
from flask import current_app

from app.models import db
from app.utils import AWSBackupBucket, AzureBackupBlob, amount_to_str, datetime_to_time


class _BytesFile:

    def __init__(self, it: Iterator[bytes]):
        self._it = it

    def read(self, size: int = -1):
        if size < 0:
            return self._read_to_end()

        buf = bytearray(size)
        n = self.readinto(buf)
        return buf[:n]

    def readinto(self, b: bytearray) -> int:
        n, size = 0, len(b)
        while n < size:
            try:
                b[n] = next(self._it)
            except StopIteration:
                break
            n += 1
        return n

    def _read_to_end(self):
        buf = bytearray()
        while True:
            try:
                v = next(self._it)
            except StopIteration:
                break
            buf.append(v)
        return buf


def txt_file(header: List[str], data: List[Tuple]) -> _BytesFile:
    def _format(v):
        if v is None:
            return ''
        if isinstance(v, str):
            return v
        if isinstance(v, Decimal):
            return amount_to_str(v)
        if isinstance(v, Enum):
            return v.name
        if isinstance(v, (date, datetime)):
            return str(datetime_to_time(v))
        return str(v)

    def _it():
        line = ','.join(header)
        yield from f"{line}\n".encode('utf8')
        for row in data:
            line = ','.join(_format(v) for v in row)
            yield from f"{line}\n".encode('utf8')
    # use iterator as file to save memory.
    return _BytesFile(_it())


def gzip_compress(file: BinaryIO) -> BinaryIO:
    zfile = BytesIO()
    # set wbits to 15+16, include a basic gzip header and trailing checksum in the output.
    z = zlib.compressobj(wbits=31)
    size = 32768
    # readinto avoid alloc buf everytime.
    if hasattr(file, 'readinto'):
        buf = bytearray(size)
        while n := file.readinto(buf):
            zfile.write(z.compress(buf[:n]))
    else:
        while buf := file.read(size):
            zfile.write(z.compress(buf))
    zfile.write(z.flush())
    zfile.seek(0)
    return zfile


def gzip_decompress(file: BinaryIO) -> BinaryIO:
    zfile = BytesIO()
    z = zlib.decompressobj(wbits=31)
    while buf := file.read(32768):
        zfile.write(z.decompress(buf))
    zfile.write(z.flush())
    zfile.seek(0)
    return zfile


def backup_data_history(model, month=12):
    """
    model 需要备份的model
    month 从当前时间前month个月
    """
    def back_once():
        end_date = date.today() - relativedelta(months=month)
        limit = 10000
        min_size = 100000
        max_size = 3000000

        last_id = 0
        records = []

        while True:
            result = model.query.filter(model.id > last_id).order_by(model.id).limit(limit).all()
            if any(row.created_at.date() >= end_date for row in result):
                break
            records.extend(result)
            if len(result) > 0:
                last_id = records[-1].id
            if len(result) != limit:
                break
            if len(records) >= max_size:
                break

        if len(records) < min_size:
            return False

        if not upload_file(model, records):
            return False
        
        model.query.filter(model.id <= last_id).delete()
        db.session.commit()
        db.session.expunge_all()
        return True

    while back_once():
        pass


def upload_file(model, records):
    columns = [i.name for i in model.__table__.columns]
    upload_data_list = []
    first_data_id = records[0].id
    report_date = records[0].created_at.date()
    last_data_id = records[-1].id
    for record in records:
        item = [getattr(record, i) for i in columns]
        upload_data_list.append(item)

    file = gzip_compress(txt_file(columns, upload_data_list))
    # like 'margin_insurance_history/2020-10-18/160278-200000.gz',
    key = f'{model.__table__.name}/{report_date}-{first_data_id}-{last_data_id}.gz'

    if not put_file(key, file):
        current_app.logger.error(f"backup data {key} failed")
        return False
    else:
        current_app.logger.info(f"backup data {key} succeed")
        return True


def put_file(key: str, file: IO) -> bool:
    data = file.read()
    r = AWSBackupBucket.put_file(key, data)
    try:
        AzureBackupBlob.put_file(key, data)
    except Exception as e:
        current_app.logger.error("azure put file failed: %s", e)
        return False
    return r
