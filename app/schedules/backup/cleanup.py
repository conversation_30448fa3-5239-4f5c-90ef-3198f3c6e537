# -*- coding: utf-8 -*-

from datetime import timedelta
from typing import List, Union, Literal
from flask import current_app
from celery.schedules import crontab

from app.config import config
from app.common import CeleryQueues
from app.business import lock_call, TradeSummaryDB, TradeHistoryDB, PerpetualSummaryDB, PerpetualHistoryDB
from app.utils import scheduled, route_module_to_celery_queue
from app.utils.date_ import (
    now,
    date_to_datetime,
    datetime_to_time,
    last_month,
)
from app.utils.external_db import connect

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


def master(db_cls):
    """替换只读实例的连接uri为主库，并返回一个继承的ExternalDB类，其db属性连接到主库"""
    conf = config[db_cls.config].copy()
    conf['host'] = conf['host'].replace('-ro-', '-')
    db = connect(**conf)
    db.autocommit(True)
    return type('Master' + db_cls.__name__, (db_cls, ), {'_db': db})

def masters(db_cls):
    """替换HisotryDB的每个分库为主库"""
    dbs = tuple(master(x) for x in db_cls.DBS)
    return type('Master' + db_cls.__name__, (db_cls, ), {'DBS': dbs})

class ExchangeHistoryDataCleanup(object):
    typ: Literal['spot', 'perpetual'] = None
    SUMMARY_DB: Union[TradeSummaryDB, PerpetualSummaryDB] = None
    HISTORY_DB: Union[TradeHistoryDB, PerpetualHistoryDB] = None

    CHUNK_SIZE = 50000
    ORDER_COUNT_THRESHOLD = 50000  # 用户订单数阈值
    DATA_STORAGE_DAYS = 30  # 数据保存天数

    log_func = None  # builtin print
    log_prefix = ""

    @classmethod
    def log(cls, msg):
        msg = f"{cls.log_prefix}{msg}"
        if cls.log_func:
            cls.log_func(msg)
        else:
            current_app.logger.info(msg)

    @classmethod
    def run(cls):
        today = now().date()
        clean_end_date = today - timedelta(days=cls.DATA_STORAGE_DAYS)
        clean_end_timestamp = datetime_to_time(date_to_datetime(clean_end_date))
        cls.log("clean_end_date: {}, clean_end_timestamp: {}".format(clean_end_date, clean_end_timestamp))

        user_ids = cls.get_recently_deal_user_ids()
        for user_id in user_ids:
            # step2.1 stop_history, order_history
            cls.delete_user_order_history_data(user_id, "stop_history", clean_end_timestamp)
            max_delete_order_id = cls.delete_user_order_history_data(user_id, "order_history", clean_end_timestamp)
            if not max_delete_order_id:
                continue

            # step2.2 deal_history
            max_time = cls.delete_user_deal_history_data(user_id, max_delete_order_id)
            if not max_time:
                continue

            # step2.3 balance_history
            cls.delete_user_balance_history_data(user_id, max_time)

    @classmethod
    def get_user_trade_summary_tables(cls) -> List[str]:
        # （当前月和上个月）
        today = now().date()
        last_month_first_day = last_month(today.year, today.month, 1)
        table_names = [
            "user_trade_summary_{}".format(last_month_first_day.strftime("%Y%m")),
            "user_trade_summary_{}".format(today.strftime("%Y%m")),
        ]
        return table_names

    @classmethod
    def get_recently_deal_user_ids(cls) -> List[int]:
        """获取最近周期内（当前月和上个月）有成交记录用户
        trade_summary.user_trade_summary_{date}
        """
        table_names = cls.get_user_trade_summary_tables()
        result = []
        db_name = cls.SUMMARY_DB.db.db.decode()
        for table_name in table_names:
            rows = cls.SUMMARY_DB.table(table_name).select("DISTINCT(`user_id`)")
            user_ids = [i[0] for i in rows]
            result.extend(user_ids)
            cls.log("from <{}.{}> get {} deal users".format(db_name, table_name, len(user_ids)))
        result = list(set(result))
        cls.log("get_recently_deal_user_ids total {} user_ids".format(len(result)))
        return result

    @classmethod
    def delete_user_order_history_data(cls, user_id: int, table_name_template: str, clean_end_timestamp: int) -> int:
        db_, table_name = cls.HISTORY_DB.user_to_db_and_table(user_id, table_name_template)
        table = db_.table(table_name)
        db_name = db_.db.db.decode()

        # INDEX `idx_user_time` (`user_id`, `create_time`)
        query_sql = (
            "SELECT order_id, create_time "
            "FROM {table_name} "
            "WHERE user_id={user_id} "
            "AND create_time<{clean_end_timestamp} "
            "ORDER BY create_time DESC "
            "LIMIT {threshold_nums},1 ; "
        ).format(
            table_name=table_name,
            user_id=user_id,
            clean_end_timestamp=clean_end_timestamp,
            threshold_nums=cls.ORDER_COUNT_THRESHOLD,
        )  # 找到时间在一个月前, 最新的第5w条order, 早于该order的数据都删除
        table.execute(query_sql)
        rows = table.cursor.fetchall()  # maybe 0 rows, ( if rows < 5w )
        if not rows:
            return 0

        row = rows[0]
        max_delete_order_id, delete_order_time = row[0], row[1]
        msg_prefix = "【clean <{}.{} user:{}>】".format(db_name, table_name, user_id)
        if not max_delete_order_id or not delete_order_time:
            return 0

        delete_sql = (
            "DELETE FROM {table_name} "
            "WHERE user_id={user_id} "
            "AND create_time<{min_create_time} "
            "LIMIT {chunk_size} ; "
        ).format(
            table_name=table_name,
            user_id=user_id,
            min_create_time=delete_order_time,
            chunk_size=cls.CHUNK_SIZE,
        )  # 只删除时间在一个月前, 并且数量超过5万的部分
        cls.chunk_delete_rows(
            table=table,
            delete_sql=delete_sql,
            msg_prefix=msg_prefix,
            chunk_delete_nums=cls.CHUNK_SIZE,
        )
        return max_delete_order_id

    @classmethod
    def delete_user_deal_history_data(cls, user_id: int, max_delete_order_id: int) -> Union[int, float]:
        if cls.typ == 'spot':
            _table_template_name = "user_deal_history"
        else:
            # 合约的表名不同
            _table_template_name = "deal_history"

        _db, _table_name = cls.HISTORY_DB.user_to_db_and_table(user_id, _table_template_name)
        deal_history_table = _db.table(_table_name)
        # 根据max_delete_order_id, 查出deal表中的记录, 获取记录的最小时间
        sql = "SELECT MIN(`time`) FROM {table_name} WHERE order_id={max_delete_order_id}; ".format(
            table_name=_table_name,
            max_delete_order_id=max_delete_order_id,
        )
        deal_history_table.execute(sql)
        rows = deal_history_table.cursor.fetchall()
        if not rows:
            return 0
        min_time = rows[0][0]
        msg_prefix = "【clean <{}.{} user:{}>】".format(_db.db.db.decode(), _table_name, user_id)
        if not min_time:
            return 0

        # INDEX `idx_user_time` (`user_id`, `time`)
        delete_sql = (
            "DELETE FROM {table_name} WHERE user_id={user_id} AND `time`<{min_time} LIMIT {chunk_size}; "
        ).format(
            table_name=_table_name,
            user_id=user_id,
            min_time=min_time,
            chunk_size=cls.CHUNK_SIZE,
        )
        cls.chunk_delete_rows(
            table=deal_history_table,
            delete_sql=delete_sql,
            msg_prefix=msg_prefix,
            chunk_delete_nums=cls.CHUNK_SIZE,
        )
        return min_time

    @classmethod
    def delete_user_balance_history_data(cls, user_id: int, max_time: Union[int, float]) -> None:
        if cls.typ == 'spot':
            cls._trade_delete_user_balance_history_data(user_id, max_time)
        else:
            cls._perpetual_delete_user_balance_history_data(user_id, max_time)

    @classmethod
    def _trade_delete_user_balance_history_data(cls, user_id: int, max_time: Union[int, float]) -> None:
        _db, _table_name = cls.HISTORY_DB.user_to_db_and_table(user_id, "balance_history")
        db_name = _db.db.db.decode()
        balance_table = _db.table(_table_name)
        sql = (
            "SELECT account, "
            "COUNT(*) as account_row_nums "
            "FROM {table_name} "
            "WHERE user_id={user_id} "
            "AND `time`<{max_time} "
            "GROUP BY account; "
        ).format(
            table_name=_table_name,
            user_id=user_id,
            max_time=max_time,
        )
        balance_table.execute(sql)
        result = balance_table.cursor.fetchall()

        for account, account_row_nums in result:
            if not account_row_nums:
                continue

            # INDEX `idx_user_account_time` (`user_id`, `account`, `time`)
            delete_sql = (
                "DELETE FROM {table_name} "
                "WHERE user_id={user_id}  "
                "AND account={account}    "
                "AND `time`<{max_time}    "
                "LIMIT {chunk_size};      "
            ).format(
                table_name=_table_name,
                user_id=user_id,
                account=account,
                max_time=max_time,
                chunk_size=cls.CHUNK_SIZE,
            )
            msg_prefix = "【clean <{}.{} user:{}, account:{}>】".format(db_name, _table_name, user_id, account)
            cls.chunk_delete_rows(
                table=balance_table,
                delete_sql=delete_sql,
                msg_prefix=msg_prefix,
                chunk_delete_nums=cls.CHUNK_SIZE,
            )

    @classmethod
    def _perpetual_delete_user_balance_history_data(cls, user_id: int, max_time: Union[int, float]) -> None:
        _db, _table_name = cls.HISTORY_DB.user_to_db_and_table(user_id, "balance_history")
        db_name = _db.db.db.decode()
        balance_table = _db.table(_table_name)

        # INDEX `idx_user_time` (`user_id`, `time`)
        delete_sql = (
            "DELETE FROM {table_name} "
            "WHERE user_id={user_id}  "
            "AND `time`<{max_time}    "
            "LIMIT {chunk_size};      "
        ).format(
            table_name=_table_name,
            user_id=user_id,
            max_time=max_time,
            chunk_size=cls.CHUNK_SIZE,
        )
        msg_prefix = "【clean <{}.{} user:{}>】".format(db_name, _table_name, user_id)
        cls.chunk_delete_rows(
            table=balance_table,
            delete_sql=delete_sql,
            msg_prefix=msg_prefix,
            chunk_delete_nums=cls.CHUNK_SIZE,
        )

    @classmethod
    def chunk_delete_rows(cls, table, delete_sql, msg_prefix, chunk_delete_nums):
        deleted_nums = 0
        while True:
            affected = table.execute(delete_sql)
            deleted_nums += affected
            if affected < chunk_delete_nums:
                break
        cls.log("{} clean finished, sql: {} total_deleted {} rows".format(msg_prefix, delete_sql, deleted_nums))


class SpotHistoryDataCleanup(ExchangeHistoryDataCleanup):
    typ = "spot"
    SUMMARY_DB = master(TradeSummaryDB)
    HISTORY_DB = masters(TradeHistoryDB)
    log_prefix = "<Spot> "


class PerpetualHistoryDataCleanup(ExchangeHistoryDataCleanup):
    typ = "perpetual"
    SUMMARY_DB = master(PerpetualSummaryDB)
    HISTORY_DB = masters(PerpetualHistoryDB)
    log_prefix = "<Perpetual> "


@scheduled(crontab(day_of_week=0, hour=2, minute=30))
@lock_call()
def delete_exchange_history_data_schedule():
    SpotHistoryDataCleanup.run()
    PerpetualHistoryDataCleanup.run()
