import datetime
from decimal import Decimal

from dateutil.relativedelta import relativedelta
from flask import current_app
from sqlalchemy import func
from pyroaring import BitMap
from app.business import scheduled, crontab, route_module_to_celery_queue, \
    CeleryQueues, lock_call
from app.business.clients.wallet import WalletClient
from app.business.external_dbs import TradeLogDB
from app.business.staking import StakingOperation
from app.common.constants import PrecisionEnum, ReportType
from app.models import db
from app.models.daily import DailyStakingReport, DailyStakingSiteReport
from app.models.monthly import MonthlyStakingReport, MonthlyStakingSiteReport
from app.models.spot import WalletAssetRewardHistory
from app.models.staking import StakingAccount, StakingHistory, StakingRewardHistory, StakingUserSummary
from app.models.wallet import AssetPrice
from app.utils.amount import quantize_amount
from app.utils.date_ import date_to_datetime, last_month, next_month, today

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _update_daily_staking_report(report_date, asset):
    reward_at = date_to_datetime(report_date) + datetime.timedelta(days=1)
    reward_history = StakingRewardHistory.query.filter(
        StakingRewardHistory.asset == asset,
        StakingRewardHistory.reward_at == reward_at,
    ).first()
    if not reward_history:
        return
    unfinished_reward = StakingRewardHistory.query.filter(
        StakingRewardHistory.asset == asset,
        StakingRewardHistory.reward_at == reward_at,
        StakingRewardHistory.status.in_((StakingRewardHistory.Status.FAILED,
                                         StakingRewardHistory.Status.CREATED))
    ).first()
    if unfinished_reward:
        current_app.logger.error(f"staking report unfinished reward {asset} {report_date}")
        return
    report: DailyStakingReport = DailyStakingReport(report_date=report_date, asset=asset)
    last_report = DailyStakingReport.query.filter(
        DailyStakingReport.asset == asset,
        DailyStakingReport.report_date == report_date - datetime.timedelta(days=1)).first()
    
    if last_report:
        history_user_ids = set(BitMap.deserialize(last_report.history_user_bitmap))
        history_income_user_ids = set(BitMap.deserialize(last_report.history_income_user_bitmap))
    else:
        history_user_ids = set()
        history_income_user_ids = set()
    reward_history = StakingRewardHistory.query.filter(
        StakingRewardHistory.asset == asset,
        StakingRewardHistory.reward_at == reward_at
    ).with_entities(StakingRewardHistory.user_id, 
                    StakingRewardHistory.amount).all()
    income_user_ids = {item.user_id for item in reward_history}
    staking_history = StakingHistory.query.filter(
        StakingHistory.asset == asset,
        StakingHistory.created_at >= report_date,
        StakingHistory.created_at < report_date + datetime.timedelta(days=1),
        StakingHistory.type == StakingHistory.Type.STAKE,
        StakingHistory.status != StakingHistory.Status.FAILED,
    ).with_entities(StakingHistory.user_id, StakingHistory.amount).all()
    staking_user_ids = {item.user_id for item in staking_history}
    new_staking_amount = sum(item.amount for item in staking_history)
    unstaking_history = StakingHistory.query.filter(
        StakingHistory.asset == asset,
        StakingHistory.created_at >= report_date,
        StakingHistory.created_at < report_date + datetime.timedelta(days=1),
        StakingHistory.type == StakingHistory.Type.UNSTAKE,
    ).with_entities(StakingHistory.user_id, StakingHistory.amount).all()
    new_unstaking_amount = sum(item.amount for item in unstaking_history)
    unstaking_amount = StakingUserSummary.query.filter(
        StakingUserSummary.asset == asset,
    ).with_entities(func.sum(StakingUserSummary.unstaking_amount)).scalar() or 0
    operation = StakingOperation(asset)
    balances = operation.get_snapshot_balance(int(date_to_datetime(report_date + datetime.timedelta(days=1)).timestamp()))
    if balances is None:
        balances = {}
    min_staking_amount = operation.get_configs()['min_staking_amount']
    balance_map = {item['user_id']: item['balance'] for item in balances \
                   if item['asset'] == asset and item['balance'] >= min_staking_amount}
    report.user_count = len(balance_map)
    balance_ts = int(date_to_datetime(report_date + datetime.timedelta(days=1)).timestamp())
    balances = TradeLogDB.get_user_balances(balance_ts, account_id=StakingAccount.ACCOUNT_ID)
    report.staking_amount = sum(item[3] for item in balances if item[1] == asset)
    report.reward_user_count = len(income_user_ids)
    report.new_reward_user_count = len(income_user_ids - history_income_user_ids)
    report.new_user_count = len(staking_user_ids - history_user_ids)
    report.unstaking_amount = unstaking_amount
    report.new_staking_amount = new_staking_amount
    report.new_unstaking_amount = new_unstaking_amount
    report.net_staking_amount = new_staking_amount - new_unstaking_amount
    try:
        total_reward_amount = WalletClient().get_staking_reward(asset,
                                                                date_to_datetime(report_date), 
                                                                date_to_datetime(report_date + datetime.timedelta(days=1)))['amount']
    except Exception as e:
        current_app.logger.error(f"get staking reward error: {asset} {report_date} {e!r}")
        return
    report.user_reward_amount = sum(item.amount for item in reward_history) # 用户收益
    report.reward_amount = total_reward_amount
    report.system_reward_amount = total_reward_amount - report.user_reward_amount
    report.income_user_bitmap = BitMap(income_user_ids).serialize()
    report.user_bitmap = BitMap(set(balance_map.keys())).serialize()
    report.history_income_user_bitmap = BitMap(set(income_user_ids) | set(history_income_user_ids)).serialize()
    report.history_user_bitmap = BitMap(set(balance_map.keys()) | set(history_user_ids)).serialize()
    income_rate = 0
    operation = StakingOperation(asset)
    summary = WalletClient().get_staking_summary(asset)
    income_rate = summary['apr']
    income_rate = income_rate * (1 - operation.get_configs()["system_sharing_ratio"])
    report.income_rate = quantize_amount(income_rate, PrecisionEnum.RATE_PLACES)
    report_at = date_to_datetime(report_date + datetime.timedelta(days=1))
    report.system_staking_amount = operation.get_total_staking_amount(report_at)
    db.session.add(report)


def _update_monthly_staking_report(report_date, asset):
    if MonthlyStakingReport.query.filter(
        MonthlyStakingReport.asset == asset,
        MonthlyStakingReport.report_date == report_date,
    ).first():
        return

    end_date = next_month(report_date.year, report_date.month)
    if not DailyStakingReport.query.filter(
        DailyStakingReport.asset == asset,
        DailyStakingReport.report_date == end_date - datetime.timedelta(days=1),
    ).first():
        return
    daily_reports = DailyStakingReport.query.filter(
        DailyStakingReport.asset == asset,
        DailyStakingReport.report_date >= report_date,
        DailyStakingReport.report_date < end_date,
    ).all()
    daily_report_map = {item.report_date: item for item in daily_reports}
    last_report = MonthlyStakingReport.query.filter(
        MonthlyStakingReport.asset == asset,
        MonthlyStakingReport.report_date == last_month(report_date.year, report_date.month),
    ).first()
    if last_report:
        history_user_ids = set(BitMap.deserialize(last_report.user_bitmap))
        history_income_user_ids = set(BitMap.deserialize(last_report.income_user_bitmap))
    else:
        history_user_ids = set()
        history_income_user_ids = set()
    report: MonthlyStakingReport = MonthlyStakingReport(report_date=report_date, asset=asset)
    last_daily_report = daily_report_map[end_date - datetime.timedelta(days=1)]
    user_ids, income_user_ids = set(), set()
    total_income = user_income_amount = total_staking_amount = 0
    new_staking_amount = new_unstaking_amount = 0
    for item in daily_reports:
        if item.user_bitmap:
            user_ids.update(BitMap.deserialize(item.user_bitmap))
        if item.income_user_bitmap:
            income_user_ids.update(BitMap.deserialize(item.income_user_bitmap))
        total_income += item.reward_amount
        user_income_amount += item.user_reward_amount
        total_staking_amount += item.staking_amount
        new_staking_amount += item.new_staking_amount
        new_unstaking_amount += item.new_unstaking_amount
    report.reward_user_count = len(income_user_ids)
    report.user_count = last_daily_report.user_count
    report.new_user_count = len(user_ids - history_user_ids)
    report.new_reward_user_count = len(income_user_ids - history_income_user_ids)
    report.new_staking_amount = new_staking_amount
    report.new_unstaking_amount = new_unstaking_amount
    report.net_staking_amount = new_staking_amount - new_unstaking_amount
    report.staking_amount = last_daily_report.staking_amount
    report.unstaking_amount = last_daily_report.unstaking_amount
    report.reward_amount = total_income
    report.user_reward_amount = user_income_amount
    report.system_reward_amount = total_income - user_income_amount
    report.income_user_bitmap = BitMap(income_user_ids).serialize()
    report.user_bitmap = BitMap(user_ids).serialize()
    report.history_user_bitmap = BitMap(user_ids | history_user_ids).serialize()
    report.history_income_user_bitmap = BitMap(income_user_ids | history_income_user_ids).serialize()
    income_rate = 0
    if total_staking_amount > 0:
        income_rate = total_income * 365 / total_staking_amount
    summary = WalletClient().get_staking_summary(asset)
    income_rate = summary['apr']
    income_rate = income_rate * (1 - StakingOperation(asset).get_configs()["system_sharing_ratio"])
    report.income_rate = quantize_amount(income_rate, PrecisionEnum.RATE_PLACES)
    report_at = date_to_datetime(report_date + datetime.timedelta(days=1))
    report.system_staking_amount = StakingOperation(asset).get_total_staking_amount(report_at)
    db.session.add(report)


def _update_site_staking_report(report_date, report_type: ReportType):
    depend_model = DailyStakingReport if report_type == ReportType.DAILY else MonthlyStakingReport
    report_model = DailyStakingSiteReport if report_type == ReportType.DAILY else MonthlyStakingSiteReport
    reports = depend_model.query.filter(depend_model.report_date == report_date).all()
    
    all_assets = StakingAccount.query.filter(
        StakingAccount.status == StakingAccount.Status.OPEN
    ).with_entities(StakingAccount.asset).all()
    all_assets = {item.asset for item in all_assets}
    reported_assets = {item.asset for item in reports}
    missed_assets = all_assets - reported_assets
    if missed_assets:
        current_app.logger.warning(f"update site staking report {report_date} missed assets: {missed_assets}. skipped.")
        return
    if report_type == ReportType.DAILY:
        last_report_date = report_date - datetime.timedelta(days=1)
    else:
        last_report_date = last_month(report_date.year, report_date.month)
    last_reports = depend_model.query.filter(depend_model.report_date 
                                             == last_report_date).with_entities(
                                                 depend_model.asset, depend_model.history_user_bitmap,
                                             ).all()
    if not last_reports:    
        return
    last_history_user_ids = set()
    for item in last_reports:
        last_history_user_ids.update(set(BitMap.deserialize(item.history_user_bitmap)))

    all_user_ids, history_user_ids = set(), set()
    
    income_user_ids, history_income_user_ids = set(), set()
    if report_type == ReportType.DAILY:
        price_map = AssetPrice.get_close_price_map(report_date)
    else:
        next_month_ = next_month(report_date.year, report_date.month)
        price_map = AssetPrice.get_close_price_map(next_month_ - datetime.timedelta(days=1))

    staking_amount = new_staking_amount \
        = new_unstaking_amount = reward_amount = user_reward_amount = system_reward_amount = system_staking_amount = 0

    finish_wallet_reward = bool(WalletAssetRewardHistory.query.filter(
        WalletAssetRewardHistory.report_date == report_date,
        WalletAssetRewardHistory.asset == WalletAssetRewardHistory.PLACEHOLDER_ASSET,
    ).first())
    wallet_reward_usd = Decimal()
    if finish_wallet_reward:
        if report_type == ReportType.MONTHLY:
            DailyStakingSiteReport.query.filter(
                DailyStakingSiteReport.report_date > last_report_date,
                DailyStakingSiteReport.report_date < last_report_date + relativedelta(months=1),
                ).with_entities(
                func.sum(DailyStakingSiteReport.wallet_reward_usd).label("total_wallet_reward_usd"),
            ).scalar() or Decimal()
        else:
            w_q = WalletAssetRewardHistory.query.filter(
                WalletAssetRewardHistory.report_date == report_date,
                WalletAssetRewardHistory.asset != WalletAssetRewardHistory.PLACEHOLDER_ASSET,
            ).all()
            for v in w_q:
                wallet_reward_usd += v.amount * price_map.get(v.asset, Decimal())

    income_rate = 0
    for report in reports:
        report: DailyStakingReport
        all_user_ids.update(BitMap.deserialize(report.user_bitmap))
        history_user_ids.update(BitMap.deserialize(report.history_user_bitmap))
        income_user_ids.update(BitMap.deserialize(report.income_user_bitmap))
        history_income_user_ids.update(BitMap.deserialize(report.history_income_user_bitmap))
        staking_amount += report.staking_amount * price_map.get(report.asset, 0)
        new_staking_amount += report.new_staking_amount * price_map.get(report.asset, 0)
        new_unstaking_amount += report.new_unstaking_amount * price_map.get(report.asset, 0)
        reward_amount += report.reward_amount * price_map.get(report.asset, 0)
        user_reward_amount += report.user_reward_amount * price_map.get(report.asset, 0)
        system_reward_amount += report.system_reward_amount * price_map.get(report.asset, 0)
        system_staking_amount += report.system_staking_amount * price_map.get(report.asset, 0)
    if staking_amount > 0:
        if report_type == ReportType.DAILY:
            income_rate = user_reward_amount * 365 / staking_amount
        else:
            income_rate = user_reward_amount * 12 / staking_amount
        income_rate = quantize_amount(income_rate, PrecisionEnum.RATE_PLACES)
    new_user_ids = all_user_ids - last_history_user_ids
    report = report_model.get_or_create(report_date=report_date)
    report.staking_amount = quantize_amount(staking_amount, PrecisionEnum.CASH_PLACES)
    report.new_staking_amount = quantize_amount(new_staking_amount, PrecisionEnum.CASH_PLACES)
    report.new_unstaking_amount = quantize_amount(new_unstaking_amount, PrecisionEnum.CASH_PLACES)
    report.net_staking_amount = report.new_staking_amount - report.new_unstaking_amount
    report.reward_amount = quantize_amount(reward_amount, PrecisionEnum.CASH_PLACES)
    report.user_reward_amount = quantize_amount(user_reward_amount, PrecisionEnum.CASH_PLACES)
    report.system_reward_amount = quantize_amount(system_reward_amount, PrecisionEnum.CASH_PLACES)
    report.system_staking_amount = quantize_amount(system_staking_amount, PrecisionEnum.CASH_PLACES)
    report.wallet_reward_usd = quantize_amount(wallet_reward_usd, PrecisionEnum.CASH_PLACES)
    report.income_rate = income_rate
    report.user_count = len(all_user_ids)
    report.new_user_count = len(new_user_ids)
    report.user_bitmap = BitMap(all_user_ids).serialize()
    report.history_user_bitmap = BitMap(history_user_ids).serialize()
    report.income_user_bitmap = BitMap(income_user_ids).serialize()
    report.history_income_user_bitmap = BitMap(history_income_user_ids).serialize()
    db.session_add_and_commit(report)

    

@scheduled(crontab(minute="*/10", hour="1,2"))
@lock_call()
def update_daily_staking_report_schedule():
    today_ = today()
    accounts = StakingAccount.query.filter(
        StakingAccount.status == StakingAccount.Status.OPEN
    ).with_entities(StakingAccount.asset).all()
    for item in accounts:
        asset = item.asset
        last_record = DailyStakingReport.query.filter(
            DailyStakingReport.asset == asset,
        ).order_by(DailyStakingReport.report_date.desc()).with_entities(
            DailyStakingReport.report_date
        ).first()
        if last_record:
            start_date = last_record.report_date + datetime.timedelta(days=1)
        else:
            start_date = today_ - datetime.timedelta(days=7)
        while start_date < today_:
            _update_daily_staking_report(start_date, asset)
            start_date += datetime.timedelta(days=1)
        db.session.commit()


@scheduled(crontab(day_of_month=1, hour="2,3", minute="*/20"))
@lock_call()
def update_monthly_staking_report_schedule():
    today_ = today()
    report_date = last_month(today_.year, today_.month)
    accounts = StakingAccount.query.filter(
        StakingAccount.status == StakingAccount.Status.OPEN
    ).with_entities(StakingAccount.asset).all()
    for item in accounts:
        asset = item.asset
        _update_monthly_staking_report(report_date, asset)
    db.session.commit()


@scheduled(crontab(minute="*/20", hour="2"))
@lock_call()
def update_daily_site_staking_report_schedule():
    today_ = today()
    last_record = DailyStakingSiteReport.query.order_by(DailyStakingSiteReport.report_date.desc()).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = today_ - datetime.timedelta(days=7)
    while start_date < today_:
        _update_site_staking_report(start_date, ReportType.DAILY)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(day_of_month=1, hour="3", minute="*/20"))
@lock_call()
def update_monthly_site_staking_report_schedule():
    today_ = today()
    report_date = last_month(today_.year, today_.month)
    r = MonthlyStakingSiteReport.query.filter(
        MonthlyStakingSiteReport.report_date == report_date
    ).first()
    if r:
        return
    _update_site_staking_report(report_date, ReportType.MONTHLY)