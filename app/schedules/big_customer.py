# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app

from app.business import ExchangeLogDB, VipHelper, TradeSummaryDB, PerpetualSummaryDB, lock_call
from app.business.admin_tag import AdminTagHelper, update_big_customer_admin_tags
from app.business.big_customer.base import BigCustomerConfig
from app.business.user import UserRepository
from app.caches.statistics import BigUserCustomerStatisticCache
from app.caches.user_tag import UserMaxPositionUpdatedFinishedCache
from app.common import CeleryQueues
from app.models import db, UserTradeSummary, User, BigUserCustomer, BusinessSystemUserRecord
from app.utils import route_module_to_celery_queue, scheduled, today, batch_iter
from app.utils.date_ import date_to_datetime, current_timestamp, today_datetime

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


class BigCustomerStatisticUpdater:
    def __init__(self, force=False):
        self.force = force
        self.cache = BigUserCustomerStatisticCache()
        self.trade_amount_map = defaultdict(lambda: defaultdict(Decimal))
        self.cet_balance_dic = defaultdict(Decimal)
        self.cur_balance_dic = defaultdict(Decimal)
        self.valid_cet_balance_dic = defaultdict(Decimal)
        self.all_position_dic = defaultdict(Decimal)
        self.sub_main_dic = UserRepository.get_sub_main_acc_dic()
        self.need_delete_user_ids = self.get_need_delete_user_ids()
        self.user_ids = set()
        self.report_date = today()

    def get_need_delete_user_ids(self):
        inner_user_ids = {v.id for v in User.query.filter(
            User.user_type == User.UserType.INTERNAL_MAKER).with_entities(User.id).all()}
        business_user_ids = {v.user_id for v in BusinessSystemUserRecord.query.filter(
            BusinessSystemUserRecord.status == BusinessSystemUserRecord.Status.VALID
        ).with_entities(BusinessSystemUserRecord.user_id).all()}
        exclude_user_ids = self.get_exclude_user_ids()
        return inner_user_ids | business_user_ids | exclude_user_ids

    def is_data_up_to_date(self):
        last_updated_at = int(self.cache.hget('timestamp') or 0)
        return not self.force and last_updated_at >= today_datetime().timestamp()

    def check_required_data(self, yesterday):
        if not TradeSummaryDB.is_data_completed(
                yesterday) or not PerpetualSummaryDB.is_data_completed(yesterday):
            return False

        log_table = ExchangeLogDB.user_slice_balance_log_table()
        balance_where = (f"`report_date` = '{yesterday}' and "
                         "`table` = 'user_slice_account_balance_sum' and `status` = 'finish'")
        if not log_table.select(where=balance_where):
            current_app.logger.warning(f"check {balance_where} error")
            return False

        _today = today()
        if not UserMaxPositionUpdatedFinishedCache(_today).exists():
            current_app.logger.warning(f"{_today} max position no finish")
            return False
        return True

    def collect_cet_balance(self, query_end, cet_balance_amount):
        ts = int(date_to_datetime(query_end).timestamp())
        cet_balance = VipHelper.get_user_cet_snap_shot_balance(ts)
        for user_id, balance in cet_balance.items():
            main_id = self.sub_main_dic.get(user_id, user_id)
            self.cet_balance_dic[main_id] += balance

        self.valid_cet_balance_dic = {k: v for k, v in self.cet_balance_dic.items() if
                                      v >= cet_balance_amount}

    def get_sub_user_ids(self, user_ids):
        return [k for k, v in self.sub_main_dic.items() if v in user_ids]

    @classmethod
    def get_exclude_user_ids(cls) -> set[int]:
        """获取排除用户"""
        return AdminTagHelper.query_clear_whitelist_user_ids()

    def collect_cur_user_balance_usd(self, user_ids):
        _user_ids = [i for i in user_ids if i not in self.cur_balance_dic]
        _user_ids.extend(self.get_sub_user_ids(_user_ids))
        ts = int(date_to_datetime(today()).timestamp())
        table = ExchangeLogDB.user_account_balance_sum_table(ts)
        for chunk_user_ids in batch_iter(_user_ids, 5000):
            user_id_str = ','.join(map(str, chunk_user_ids))
            rows = table.select(
                'user_id', 'balance_usd',
                where=f' user_id in ({user_id_str})'
            )
            for (user_id, balance_usd) in rows:
                main_id = self.sub_main_dic.get(user_id, user_id)
                self.cur_balance_dic[main_id] += balance_usd

    def collect_cur_balance_amount(self, query_end):
        ts = int(date_to_datetime(query_end).timestamp())
        cur_balance = VipHelper.get_user_snapshot_balance_usd(ts)
        for user_id, balance in cur_balance.items():
            main_id = self.sub_main_dic.get(user_id, user_id)
            self.cur_balance_dic[main_id] += balance

    def collect_trade_amount(self, user_ids):
        _user_ids = [i for i in user_ids if i not in self.trade_amount_map]
        for chunk_user_ids in batch_iter(_user_ids, 2000):
            self.trade_amount_map.update(
                VipHelper.list_trade_amount(chunk_user_ids, today() - timedelta(days=30), today()))

    def update_cet_tag_user(self):
        user_ids = set(self.valid_cet_balance_dic.keys()) - self.need_delete_user_ids
        big_user_customer_list = BigUserCustomer.query.all()
        big_user_user_ids = {i.user_id for i in big_user_customer_list}
        self.collect_trade_amount(user_ids | big_user_user_ids)
        self.collect_cur_user_balance_usd(user_ids | big_user_user_ids)
        new_user_ids = user_ids - big_user_user_ids
        for chunk_user_ids in batch_iter(new_user_ids, 2000):
            exist_user_ids = {i.id for i in User.query.filter(
                User.id.in_(chunk_user_ids)
            ).with_entities(
                User.id
            ).all()}
            for user_id in chunk_user_ids:
                if user_id not in exist_user_ids:
                    continue
                r = BigUserCustomer(user_id=user_id)
                r.is_big_cet_type = user_id in self.valid_cet_balance_dic
                self.update_user_data(r)
                db.session.add(r)
            db.session.commit()
        for r in big_user_customer_list:
            user_id = r.user_id
            if r.is_big_cet_type is None:  # 不曾达标过只有满足条件才会设置为True或者不改变
                r.is_big_cet_type = True if user_id in self.valid_cet_balance_dic else None
            elif r.is_big_cet_type is not None:
                r.is_big_cet_type = user_id in self.valid_cet_balance_dic
            self.update_user_data(r)
        db.session.commit()
        self.update_last_updated_at()

    def update_balance_tag_user(self, valid_all_balance_dic):
        user_ids = set(valid_all_balance_dic.keys()) - self.need_delete_user_ids
        big_user_customer_list = BigUserCustomer.query.all()
        big_user_user_ids = {i.user_id for i in big_user_customer_list}
        self.collect_trade_amount(user_ids | big_user_user_ids)
        self.collect_cur_user_balance_usd(user_ids | big_user_user_ids)
        new_user_ids = user_ids - big_user_user_ids
        for chunk_user_ids in batch_iter(new_user_ids, 2000):
            exist_user_ids = {i.id for i in User.query.filter(
                User.id.in_(chunk_user_ids)
            ).with_entities(
                User.id
            ).all()}
            for user_id in chunk_user_ids:
                if user_id not in exist_user_ids:
                    continue
                r = BigUserCustomer(user_id=user_id)
                r.is_big_balance_type = user_id in valid_all_balance_dic
                self.update_user_data(r)
                db.session.add(r)
            db.session.commit()
        for r in big_user_customer_list:
            user_id = r.user_id
            if r.is_big_balance_type is None:
                r.is_big_balance_type = True if user_id in valid_all_balance_dic else None
            elif r.is_big_balance_type is not None:
                r.is_big_balance_type = user_id in valid_all_balance_dic
            self.update_user_data(r)
        db.session.commit()
        self.update_last_updated_at()

    def update_user_data(self, item):
        if self.cet_balance_dic:
            item.cet_amount = self.cet_balance_dic[item.user_id]
        if self.all_position_dic:
            item.highest_balance_usd = self.all_position_dic[item.user_id]
        item.balance_usd = self.cur_balance_dic[item.user_id]
        item.spot_trade_amount = self.trade_amount_map[item.user_id].get(
            UserTradeSummary.System.SPOT, 0)
        item.perpetual_trade_amount = self.trade_amount_map[item.user_id].get(
            UserTradeSummary.System.PERPETUAL, 0)
        if any([item.is_big_cet_type, item.is_big_balance_type]):
            item.status = BigUserCustomer.StatusType.PASS
        else:
            item.status = BigUserCustomer.StatusType.INVALID

    def update_deleted_users(self, need_delete_user_ids):
        BigUserCustomer.query.filter(
            BigUserCustomer.user_id.in_(need_delete_user_ids)
        ).update(
            dict(status=BigUserCustomer.StatusType.DELETED),
            synchronize_session=False
        )
        db.session.commit()
        self.update_last_updated_at()

    def update_last_updated_at(self):
        self.cache.hmset({
            'timestamp': current_timestamp(to_int=True),
        })

    def run(self):
        if self.is_data_up_to_date():
            return

        yesterday = today() - timedelta(days=1)
        if not self.check_required_data(yesterday):
            return

        query_end = today()
        config = BigCustomerConfig()
        cet_balance_amount = config.cet_amount
        history_max_position = config.history_max_position

        self.collect_cet_balance(query_end, cet_balance_amount)
        self.update_cet_tag_user()
        self.update_last_updated_at()

        self.all_position_dic = ExchangeLogDB.get_user_max_balance_table_data()
        valid_all_position_dic = {k: v for k, v in self.all_position_dic.items() if v >= history_max_position}

        self.update_balance_tag_user(valid_all_position_dic)
        self.update_deleted_users(self.need_delete_user_ids)
        update_big_customer_admin_tags.delay()


@scheduled(crontab(minute='40', hour="1-3"))
@lock_call()
def big_customer_statistic_schedule(force=False):
    updater = BigCustomerStatisticUpdater(force=force)
    updater.run()
