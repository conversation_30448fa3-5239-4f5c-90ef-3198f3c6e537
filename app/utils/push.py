# -*- coding: utf-8 -*-
from collections import defaultdict
import datetime
import uuid
from enum import Enum
from typing import List, Union, Optional, Any
from urllib.parse import urlsplit, urlunsplit, urlparse, parse_qsl, urlencode, urlunparse, quote

import google.cloud.bigquery as bq

from app.utils.net import url_join
from . import JsonRPC2Client, current_timestamp
from ..config import config
from ..utils import RESTClient


class PushChannel(Enum):
    JPUSH = 'jg'
    APNS = 'apns'
    FCM = 'fcm'


class AppPagePath(Enum):
    """参考文档：https://viabtc.yuque.com/r.d/ozs5x1/ggxzr3"""
    HOME = 'coinex://app/home'
    BORROWING_RECORD = 'coinex://app/margin_borrowing_record'
    DEPOSIT_RECORD = 'coinex://app/deposit_withdraw_record?type=deposit'
    WITHDRAW_RECORD = 'coinex://app/deposit_withdraw_record?type=withdraw'
    DEPOSIT = 'coinex://app/deposit?coin={asset}'
    WITHDRAW = 'coinex://app/withdraw?coin={asset}'
    SPOT_MARKET_DETAIL = 'coinex://app/market_info?market={market}'
    PERPETUAL_MARKET_DETAIL = 'coinex://app/perpetual_market_info?market={market}'
    PERPETUAL_TRADE = 'coinex://app/perpetual?market={market}&tradeType={trade_type}'
    SPOT_TRADE = 'coinex://app/trade?market={market}&accountType={account}&tradeType={trade_type}'

    PERPETUAL_ORDER = 'coinex://app/perpetual_order_list?type={type}'
    SPOT_ORDER = 'coinex://app/exchange_order_list?type={type}'
    AUTO_INVEST_ORDER_DETAIL = 'coinex://app/auto_invest_detail?id={id}'
    SPOT_GRID_DETAIL = 'coinex://app/spot_grid_detail?id={id}'
    # 合约type取值：
    # current_position - 当前持仓，history_position - 历史持仓，funding_rate - 资金费率
    # current_order - 当前委托，history_order - 历史委托，deal_record - 成交记录
    # 现货type取值：
    # current_order - 当前委托，history_order - 历史委托，deal_record - 成交记录

    ASSET_EXCHANGE_ORDER = "coinex://app/asset_exchange_order_list?id={id}"  # 待定

    QUICK_NEWS_DETAIL = 'coinex://app/quick_news_detail?id={id}'
    ARTICLE_DETAIL = 'coinex://app/article_detail?id={id}'

    QUOTATION = 'coinex://app/quotation?quotation_page={quotation_page}&news_page={news_page}'

    P2P_ORDER_DETAIL = 'coinex://app/p2p/order/detail?id={order_id}'
    SPOT_PAGE = 'coinex://app/asset?tab=spot'
    P2P_PAY_CHANNEL = 'coinex://app/p2p/receivable_accounts'
    REWARD_CENTER = 'coinex://app/reward_center?tab={tab}'
    MESSAGE_CENTER = 'coinex://app/message_center?tab={type}'   # type - message 消息; interactive 互动
    PAYMENT_RECORD = 'coinex://app/payment_record?tab={tab}'   # 收款记录｜付款记录，tab：receive 收款，pay 付款
    COPY_TRADING = 'coinex://app/copy_trading'
    COPY_TRADING_HOME = 'coinex://app/copy_trading/home'
    COPY_TRADING_TRANSACTION = 'coinex://app/copy_trading/transaction'
    COPY_TRADING_MY_FOLLOW = 'coinex://app/copy_trading/my_follow_order'
    COPY_TRADING_MY_LEAD = 'coinex://app/copy_trading/my_lead_order'

    DEMO_TRADING = 'coinex://app/demo_trading'
    KYC = 'coinex://app/kyc'
    REFER_PAGE = 'coinex://app/refer'

    @property
    def domain_url(self):
        pared = urlsplit(self.value)
        return urlunsplit((pared.scheme, pared.netloc, pared.path, "", ""))


SITE_URL = config['SITE_URL']
SUPPORT_URL = config["SUPPORT_URL"]

class WebPagePath(Enum):
    BORROWING_RECORD = url_join(SITE_URL, "/asset/margin/record")
    IEO_RECORD = url_join(SITE_URL, "/dock/order")
    ORDER_CURRENT_NORMAL_RECORD = url_join(SITE_URL, "/exchange/current?orderType=normal")
    ORDER_HISTORY_NORMAL_RECORD = url_join(SITE_URL, "/exchange/history?orderType=normal")
    ORDER_HISTORY_STOP_RECORD = url_join(SITE_URL, "/exchange/history?orderType=stop")
    DEPOSIT_RECORD = url_join(SITE_URL, "/asset/deposit/record")
    EXCHANGE_RECORD = url_join(SITE_URL, "/swap/order?status=FINISHED")
    PERPETUAL_LIQ_RECORD = url_join(SITE_URL, "/futures/")
    PERPETUAL_DEAL_RECORD = url_join(SITE_URL, "/futures/order/deal-record")
    PERPETUAL_HISTORY_RECORD = url_join(SITE_URL, "/futures/order/history-delegation?orderType=normal")
    PERPETUAL_CURRENT_HISTORY_RECORD = url_join(SITE_URL, "/futures/order/current-delegation?orderType=normal")
    PERPETUAL_CURRENT_POSITION_RECORD = url_join(SITE_URL, "/futures/order/current-position")
    PERPETUAL_STOP_HISTORY_RECORD = url_join(SITE_URL, "/futures/order/history-delegation?orderType=stop")
    PERPETUAL_HISTORY_POSITION_RECORD = url_join(SITE_URL, "/futures/order/history-position")
    WITHDRAW_RECORD = url_join(SITE_URL, "/asset/withdraw/record")
    MARGIN_MARKET = url_join(SITE_URL, "/exchange/")
    MARGIN_ASSET = url_join(SITE_URL, "/asset/margin")
    MARGIN_RECORD = url_join(SITE_URL, "/asset/margin/record")
    MARGIN_HISTORY = url_join(SITE_URL, "/asset/history/margin")
    LEVEL_INFO = url_join(SITE_URL, "/my/info/level")
    VIP_PAGE = url_join(SITE_URL, "/vip")
    BORROWING_FEE_PAGE = url_join(SITE_URL, "/fees?type=borrowing")
    REFER_PAGE = url_join(SITE_URL, "/activity/refer")
    FIAT_HISTORY = url_join(SITE_URL, "/trade-crypto/order/fiat")
    FIAT_ORDER = url_join(SITE_URL, "/trade-crypto")
    PLEDGE_ACCOUNT_ASSET_HISTORY = url_join(SITE_URL, "/asset/history/loans")
    REWARD_CENTER_PAGE = url_join(SITE_URL, "/reward-center?type=award")
    INVESTMENT_RECORD = url_join(SITE_URL, "/asset/finance/record")
    INTERACTIVE_MESSAGE = url_join(SITE_URL, "/interactive-message")
    MESSAGE = url_join(SITE_URL, "/messages")
    MESSAGE_DETAIL = url_join(SITE_URL, "/messages/detail")
    SIGNIN = url_join(SITE_URL, "/signin")
    ACCOUNT_SETTING = url_join(SITE_URL, "/my/info/security")
    ACCOUNT_BASIC = url_join(SITE_URL, "/my/info/basic")
    ACCOUNT_FORBID = url_join(SITE_URL, "/my/info/basic/forbid")
    ACCOUNT_UNFROZEN = url_join(SITE_URL, "/my/info/basic/unfrozen")
    RESET_WITHDRAW_PASSWORD = url_join(SITE_URL, "/account/reset-withdraw-pwd")

    # 帮助中心
    SUPPORT_CENTER_SUBMIT_TICKET = url_join(SUPPORT_URL, "/hc/requests/new")  # 提交工单
    ABNORMAL_DEP_APPLY_FEE_PAGE = url_join(SITE_URL, "/help/sections/articles/**************")  # 充值找回手续费说明页面

    # 大使激励包落地页
    AMBASSADOR_APPLY_PAGE = url_join(SITE_URL, "/activity/apply?type=1")
    AMBASSADOR_PACKAGE_RUN_PAGE = url_join(SITE_URL, "/ambassador-panel/incentive-package?status=RUNNING")
    AMBASSADOR_PACKAGE_FINISHED_PAGE = url_join(SITE_URL, "/ambassador-panel/incentive-package?status=FINISHED")

    ONCHAIN_ORDER_CURRENT = url_join(SITE_URL, "/onchain/order/current")
    ONCHAIN_ORDER_HISTORY = url_join(SITE_URL, "/onchain/order/history")

    DEPOSIT = url_join(SITE_URL, "/asset/deposit")
    WITHDRAW = url_join(SITE_URL, "/asset/withdraw")
    SPOT_MARKET = url_join(SITE_URL, "/exchange/")
    PERPETUAL_MARKET = url_join(SITE_URL, "/futures/")
    AMBASSADOR_ACTIVITY = url_join(SITE_URL, "/activity/ambassador")
    DEPOSIT_APPLICATION = url_join(SITE_URL, "/asset/deposit/material/supplement/")
    DEPOSIT_RECOVERY = url_join(SITE_URL, "/asset/deposit/recovery")
    STRATEGY_AUTO_INVEST = url_join(SITE_URL, "/strategy/auto-invest/")
    APIKEY = url_join(SITE_URL, "/apikey")
    APIKEY_RENEWAL = url_join(SITE_URL, "/apikey/renewal")
    KYC = url_join(SITE_URL, "/my/info/kyc/individual")
    KYT = url_join(SITE_URL, "/kyt/material")
    EDD = url_join(SITE_URL, "/edd/material/")
    BROKER = url_join(SITE_URL, "/broker")
    LIVENESS_VERIFY = url_join(SITE_URL, "/liveness-verification")

    def format_page(self, path='', **params):
        """
        path: 路径参数
        params：query string 参数
        """
        url_parts = list(urlparse(self.value))
        ori_path = url_parts[2]
        extra_path = quote(path, safe='/')
        new_path = f"{ori_path.rstrip('/')}/{extra_path.lstrip('/')}" if path else ori_path
        url_parts[2] = new_path

        query = dict(parse_qsl(url_parts[4]))
        query.update(params)
        url_parts[4] = urlencode(query)
        new_url = urlunparse(url_parts)
        return new_url


class PushType(Enum):
    MANUAL_PUSH = 'manual_push'  # 手动推送
    AUTO_PUSH = 'auto_push'  # 系统自动推送
    BROADCAST = 'broadcast'  # 系统广播
    STRATEGY_PUSH = 'strategy_push'  # 策略配置push


class PushTag(Enum):
    """用于广播的tag"""
    INFORMATION = 'information'  # 资讯
    NEW_ASSET_RISE = 'new_asset_rise'    # 新币上涨提醒
    ASSET_PRICE_BREAK = 'asset_price_break'   # 币种价格突破（BTC，ETH）


class PushTagMutex(Enum):
    # 推送Tag互斥域
    INFORMATION = 'information'
    NEW_ASSET_RISE = 'new_asset_rise'  # 新币上涨提醒
    ASSET_PRICE_BREAK = 'asset_price_break'  # 币种价格突破（BTC，ETH）


PushTagMutexDic = {
    PushTag.INFORMATION: PushTagMutex.INFORMATION,
    PushTag.NEW_ASSET_RISE: PushTagMutex.NEW_ASSET_RISE,
    PushTag.ASSET_PRICE_BREAK: PushTagMutex.ASSET_PRICE_BREAK,
}


class MobilePusher:
    _client = RESTClient(config["INNER_MOBILE_PUSH_CONFIG"]["url"])
    _app_key = config["INNER_MOBILE_PUSH_CONFIG"]["app_key"]
    _api_version = config["INNER_MOBILE_PUSH_CONFIG"]["api_version"]

    @JsonRPC2Client.retry(3, timeout=5, max_sleep=60)
    def send_device_report(self, push_id: str, channel: PushChannel, lang: str, info: dict,
                           user_id: Optional[int] = None):
        body = dict(
            lang=lang,
            push_id=push_id,
            channel=channel.value,
            info=info,
            app_key=self._app_key
        )
        if user_id:
            body.update(user_id=str(user_id))
        return self._client.post(
            api=f'/{self._api_version}/report',
            json=body
        )

    @JsonRPC2Client.retry(3, timeout=5, max_sleep=60)
    def send_device_report_logout(self, push_id: str, channel: PushChannel,
                                  user_id: Optional[int] = None):
        body = dict(
            push_id=push_id,
            channel=channel.value,
            app_key=self._app_key
        )
        if user_id:
            body.update(user_id=str(user_id))
        return self._client.post(
            api=f'/{self._api_version}/report/logout',
            json=body
        )

    @JsonRPC2Client.retry(3, timeout=5, max_sleep=60)
    def delete_device_report(self, user_id: int):

        body = dict(
            app_key=self._app_key,
            user_id=str(user_id)
        )
        return self._client.post(
            api=f'/{self._api_version}/report/delete',
            json=body
        )

    @JsonRPC2Client.retry(3, timeout=5, max_sleep=10)
    def update_tag_push_ids(
            self,
            tag_name: str,
            channel: PushChannel,
            mutex: PushTagMutex,
            add_push_ids: Union[str, List[str]],
            remove_push_ids: Union[str, List[str]]
    ):
        if isinstance(add_push_ids, str):
            add_push_ids = [add_push_ids]
        if isinstance(remove_push_ids, str):
            remove_push_ids = [remove_push_ids]
        data = dict(
            app_key=self._app_key,
            channel=channel.value,
            mutex=mutex.value,
            add_push_ids=add_push_ids,
            remove_push_ids=remove_push_ids,
        )
        return self._client.post(
            api=f'/{self._api_version}/tags/{tag_name}',
            json=data
        )
        
    @JsonRPC2Client.retry(3, timeout=5, max_sleep=10)
    def cancel_push_tasks(self, task_ids: List[str]):
        """
        批量取消推送任务
        """
        data = dict(
            app_key=self._app_key,
            task_ids=task_ids
        )
        return self._client.post(
            api=f'/{self._api_version}/push/cancel',
            json=data
        )

    def send_mobile_push_by_lang(self,
                                 langs: Union[str, List[str]], message: str, title: str = '',
                                 url: str = '', ttl: Optional[int] = None, created_at: int = None,):
        if isinstance(langs, str):
            langs = [langs]
        data = dict(
            app_key=self._app_key,
            tags=langs,
            title=title,
            content=message,
            nonce=str(uuid.uuid4()),
            action_url=url,
            created_at=created_at or current_timestamp(to_int=True),
        )
        if ttl:
            data['ttl'] = ttl
        return self._client.post(
            api=f'/{self._api_version}/push/by_tag',
            json=data
        )

    def send_mobile_push_by_tags(self,
                                 tags: str | List[str], message: str,
                                 business_push_id: Any, platform: str = '', title: str = '',
                                 url: str = '', ttl: Optional[int] = None, extras: dict = None, created_at: int = None):
        if isinstance(tags, str):
            tags = [tags]
        data = dict(
            app_key=self._app_key,
            tags=tags,
            title=title,
            content=message,
            nonce=str(uuid.uuid4()),
            action_url=url,
            analytics_label=str(business_push_id),
            created_at=created_at or current_timestamp(to_int=True),
        )
        if platform:
            data['platform'] = platform
        if ttl:
            data['ttl'] = ttl
        if extras:
            data['extras'] = extras
        return self._client.post(
            api=f'/{self._api_version}/push/by_tag',
            json=data
        )

    def send_mobile_push_by_user_ids(self, user_ids: str | List[str], message: str,
                                     title: str = '', url: str = '',
                                     ttl: Optional[int] = None,
                                     uniq_key: str = '',
                                     extras: dict = None,
                                     created_at: int = None,
                                     ):
        from ..business.user import UserVisitPermissionHelper

        if isinstance(user_ids, int):
            user_ids = [str(user_ids)]
        filter_user_ids = [int(i) for i in user_ids]
        disabled_user_ids = {str(user_id) for user_id in UserVisitPermissionHelper.get_user_ids(
            [UserVisitPermissionHelper.UserVisitType.CLEARED_USER], filter_user_ids)}
        valid_user_ids = set(user_ids) - disabled_user_ids
        if not valid_user_ids:
            return
        data = dict(
            app_key=self._app_key,
            user_ids=list(valid_user_ids),
            title=title,
            content=message,
            nonce=str(uuid.uuid4()),
            action_url=url,
            created_at=created_at or current_timestamp(to_int=True),
        )
        if extras and (business_push_id := extras.get("business_push_id")):
            data['analytics_label'] = str(business_push_id)
        if ttl:
            data['ttl'] = ttl
        if uniq_key:
            data['uniq_key'] = uniq_key
        if extras:
            data['extras'] = extras
        return self._client.post(
            api=f'/{self._api_version}/push/by_user',
            json=data
        )

    def get_push_report_slice_day(self, **params: Any):
        return self._client.get(
            api=f'/{self._api_version}/push_report/slice/day',
            **params
        )['data']

    def get_push_report_group_by(self, **params: Any):
        return self._client.get(
            api=f'/{self._api_version}/push_report/drill_down/period',
            **params
        )['data']

    def get_push_report_detail(self):
        """
        推送报告明细(仅FCM平台)
        """

        def _get_from_api():
            params = dict(page_size=500, app_key=self._app_key)
            page_token = ''
            result = defaultdict(lambda: defaultdict(int))
            while True:
                if page_token:
                    params['page_token'] = page_token
                data = self._client.get(api=f'/{self._api_version}/push_report/fcm/delivery',
                                        **params)['data']
                for item in data['androidDeliveryData']:
                    id_ = item.get('analyticsLabel')
                    if not id_:
                        continue
                    report_date = datetime.date(item['date']['year'],
                                                item['date']['month'],
                                                item['date']['day'])
                    delivered_count = int(item['data'].get('countMessagesAccepted', 0))
                    if percents := item['data'].get('messageOutcomePercents'):
                        delivered_count *= percents.get('delivered', 100) / 100
                    result[id_][report_date] += int(delivered_count)
                page_token = data.get('pageToken', '')
                if not page_token:
                    break
            return result
        
        def _get_from_bigquery():
            conf = config['GOOGLE_BIGQUERY_CONFIG']
            credentials = conf['credentials']
            client = bq.Client.from_service_account_info(credentials)
            # Perform a query.
            query = f'SELECT date(event_timestamp) d, analytics_label a, count(*) c FROM `{conf["fcm_table"]}` ' +\
            'WHERE event="MESSAGE_DELIVERED" group by analytics_label, date(event_timestamp)'
            query_job = client.query(query)
            rows = query_job.result()

            result = defaultdict(lambda: defaultdict(int))
            for row in rows:
                if not row.a:
                    continue
                result[row.a][row.d] = row.c 
            return result

        result = _get_from_api()
        
        bigquery_result = _get_from_bigquery()
        for id_, item in bigquery_result.items():
            for k, v in item.items():
                result[id_][k] = v
        return result
