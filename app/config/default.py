# -*- coding: utf-8 -*-

SITE_URL = 'https://www.coinex.com'
SUPPORT_URL = 'https://support.coinex.com'
BROKER_AUTH_URL = 'https://www.coinex.com/broker/authorization'
API_DOC_URL = 'https://viabtc.github.io/coinex_api_en_doc/'

BYPASS_2FA = False

MARGIN_LIQUIDATION_ORDER_CHECK_PERIOD = 60

MARGIN_LOAN_MIN_AMOUNT_USD = 50

# noinspection SpellCheckingInspection
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_timeout': 15,
    'max_overflow': 15,
}

UPLOAD_STATIC_URL = 'https://file.coinexstatic.com'

SYSTEM_ADMIN_EMAIL_SIGNATURE = "CoinEx"

EMAIL_PREFIX = ""

PUSH_PREFIX = ""

SMS_PREFIX_EN = '[CoinEx]'
SMS_PREFIX_CN = '【C站】'

# 导出报表时最多导出的条数
EXPORT_ITEM_MAX_COUNT = 10000
# 访问server接口时请求的每页最大条数
SERVER_CLIENT_PAGE_LIMIT = 100
# 批量循环访问server接口时每个循环请求的最大条数
SERVER_CLIENT_BATCH_PAGE_LIMIT = 1000
# web请求最大页数
WEB_MAX_PAGE = 1000
# web请求最大limit
WEB_MAX_LIMIT = 1000

BINDING_ADDRESS_CET_ASSET_URL = "https://www.coinex.net/res/addresses/{address}"

# enable data backup
BACKUP_ENABLED = True

MONITOR_ENABLED = True
