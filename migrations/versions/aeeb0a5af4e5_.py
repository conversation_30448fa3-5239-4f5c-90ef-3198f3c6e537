"""empty message

Revision ID: aeeb0a5af4e5
Revises: f07c2060eda3
Create Date: 2025-10-10 17:04:23.862053

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = 'aeeb0a5af4e5'
down_revision = 'f07c2060eda3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('new_user_investment_summary',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('amount', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('inv_type', app.models.base.StringEnum('CURRENT', 'COUPON', 'EQUITY'), nullable=False, comment='理财类型'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'asset', 'inv_type', name='user_id_asset_inv_type_uniq')
    )
    with op.batch_alter_table('new_user_investment_summary', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_new_user_investment_summary_asset'), ['asset'], unique=False)
        batch_op.create_index(batch_op.f('ix_new_user_investment_summary_report_date'), ['report_date'], unique=False)
        batch_op.create_index(batch_op.f('ix_new_user_investment_summary_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('new_user_investment_summary', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_new_user_investment_summary_report_date'))
        batch_op.drop_index(batch_op.f('ix_new_user_investment_summary_asset'))
    op.drop_table('new_user_investment_summary')
    # ### end Alembic commands ###
