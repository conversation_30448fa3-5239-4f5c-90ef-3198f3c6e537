"""empty message

Revision ID: 51da31123b21
Revises: 14426d56ff8a
Create Date: 2025-10-14 16:30:37.978697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '51da31123b21'
down_revision = '14426d56ff8a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('onchain_token_hot_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('chain', app.models.base.StringEnum('SOL', 'ERC20', 'BSC'), nullable=False),
    sa.Column('contract', sa.String(length=64), nullable=False),
    sa.Column('symbol', sa.String(length=64), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('is_top', sa.Boolean(), nullable=False),
    sa.Column('expired_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('admin_user_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('chain', 'contract', name='chain_contract_uniq')
    )
    with op.batch_alter_table('onchain_token_hot_config', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_onchain_token_hot_config_token_id'), ['token_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('onchain_token_hot_config', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_onchain_token_hot_config_token_id'))
    op.drop_table('onchain_token_hot_config')
    # ### end Alembic commands ###
