import pytest

from sqlalchemy import create_engine, Column, Integer, String, DateTime, or_
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Query
from sqlalchemy.sql.expression import BinaryExpression
from sqlalchemy.sql.operators import in_op

from unittest.mock import patch
from app.models.base import BatchQuery


BaseQuery = Query


Base = declarative_base()


class MockUser(Base):
    __tablename__ = 'mock_users'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    created_at = Column(DateTime)


@pytest.fixture
def mock_session():
    engine = create_engine('sqlite:///:memory:')
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()


@pytest.fixture
def batch_query(mock_session):
    return BatchQuery([MockUser], session=mock_session)


def test_set_batch_size(batch_query):
    query = batch_query
    assert query.batch_size == 1000
    query.set_batch_size(500)
    assert query.batch_size == 500
    assert isinstance(query.set_batch_size(200), BatchQuery)


def test_find_largest_condition_no_in_condition():
    where_clause = MockUser.id == 1
    assert BatchQuery.find_largest_condition(where_clause) is None


def test_find_largest_condition_single_in():
    where_clause = MockUser.id.in_([1, 2, 3])
    largest = BatchQuery.find_largest_condition(where_clause)
    assert isinstance(largest, BinaryExpression)
    assert largest.operator == in_op
    assert largest.right.value == [1, 2, 3]


def test_find_largest_condition_multiple_in():
    where_clause = or_(MockUser.id.in_([1, 2]), MockUser.name.in_(['a', 'b', 'c', 'd']))
    largest = BatchQuery.find_largest_condition(where_clause)
    assert isinstance(largest, BinaryExpression)
    assert largest.operator == in_op
    assert largest.right.value == ['a', 'b', 'c', 'd']


def test_batch_all_no_in_condition(batch_query):
    query = batch_query.filter(MockUser.id == 1)
    with pytest.raises(ValueError):
        list(query.batch_all())


@patch.object(BatchQuery, 'all')
def test_batch_all_single_batch(mock_all, batch_query):
    query = batch_query.filter(MockUser.id.in_([1, 2, 3]))
    mock_all.return_value = [{'id': 1}, {'id': 2}, {'id': 3}]
    results = list(query.batch_all())
    assert results == [{'id': 1}, {'id': 2}, {'id': 3}]
    mock_all.assert_called_once()


@patch.object(BatchQuery, 'all')
def test_batch_all_multiple_batches(mock_all, batch_query):
    in_values = list(range(1, 2001))
    query = batch_query.filter(MockUser.id.in_(in_values))
    mock_all.side_effect = [
        [{'id': i} for i in range(1, 1001)],
        [{'id': i} for i in range(1001, 2001)],
    ]
    results = list(query.batch_all())
    assert len(results) == 2000
    assert mock_all.call_count == 2


@patch.object(BatchQuery, 'all')
def test_batch_all_complex_where_clause(mock_all, batch_query):
    in_values_large = list(range(1, 2001))
    in_values_small = ['a', 'b']
    query = batch_query.filter(or_(MockUser.id.in_(in_values_large), MockUser.name.in_(in_values_small), MockUser.created_at > '2020-01-01'))
    mock_all.side_effect = [
        [{'id': i} for i in range(1, 1001)],
        [{'id': i} for i in range(1001, 2001)],
    ]
    results = list(query.batch_all())
    assert len(results) == 2000
    assert mock_all.call_count == 2


@patch.object(BatchQuery, 'all')
def test_batch_all_custom_batch_size(mock_all, batch_query):
    in_values = list(range(1, 51))
    query = batch_query.set_batch_size(20).filter(MockUser.id.in_(in_values)).order_by(MockUser.id.desc())
    mock_all.side_effect = [
        [{'id': i} for i in range(1, 21)],
        [{'id': i} for i in range(21, 41)],
        [{'id': i} for i in range(41, 51)],
    ]
    results = list(query.batch_all())
    assert len(results) == 50
    assert mock_all.call_count == 3


@patch.object(BatchQuery, 'all')
def test_batch_all_with_set_in_values(mock_all, batch_query):
    in_set = {2, 3, 6, 7, 8, 9, 7}
    query = batch_query.set_batch_size(1).filter(or_(MockUser.id.in_(in_set), MockUser.name == '100'))
    sorted_set = sorted(in_set)
    mock_all.side_effect = [[{'id': v}] for v in sorted_set]
    results = list(query.batch_all())
    assert len(results) == len(sorted_set)
    assert mock_all.call_count == len(sorted_set)