# -*- coding: utf-8 -*-
"""测试理财缓存相关功能"""
from datetime import datetime, date, timedelta
from decimal import Decimal
from pprint import pprint

import pytest
from flask import g

from app import Language
from app.caches.investment import InvIncEquityYesIncomeCache
from app.models import User
from app.models.base import db
from app.utils.date_ import today
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 20044
USER_ID_2 = 20045


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


def create_investment_summary_data(user_id, asset, amount, inv_type, report_date=None):
    """创建用户理财汇总数据"""
    from app.models.investment import NewUserInvestmentSummary
    if report_date is None:
        report_date = today()
    return NewUserInvestmentSummary(
        user_id=user_id,
        asset=asset,
        amount=amount,
        inv_type=inv_type,
        report_date=report_date,
    )


def create_day_interest_data(user_id, asset, interest_amount, report_date, status):
    """创建每日利息数据"""
    from app.models.investment import UserDayInterestHistory
    return UserDayInterestHistory(
        user_id=user_id,
        asset=asset,
        interest_asset=asset,
        interest_amount=interest_amount,
        report_date=report_date,
        status=status,
    )


def create_equity_interest_data(user_id, asset, interest_amount, report_date, status, user_equity_id=1):
    """创建权益加息数据"""
    from app.models.equity_center import UserDailyIncEquityHistory
    return UserDailyIncEquityHistory(
        user_id=user_id,
        user_equity_id=user_equity_id,
        system_user_id=1,
        asset=asset,
        interest_amount=interest_amount,
        report_date=report_date,
        status=status,
    )


def clear_investment_summary_data(user_ids):
    """清理理财汇总数据"""
    from app.models.investment import NewUserInvestmentSummary
    NewUserInvestmentSummary.query.filter(
        NewUserInvestmentSummary.user_id.in_(user_ids)
    ).delete()
    db.session.commit()


def clear_yesterday_interest_data(user_ids, yesterday):
    """清理昨日利息数据"""
    from app.models.investment import UserDayInterestHistory
    from app.models.equity_center import UserDailyIncEquityHistory
    
    UserDayInterestHistory.query.filter(
        UserDayInterestHistory.user_id.in_(user_ids),
        UserDayInterestHistory.report_date == yesterday,
    ).delete()
    
    UserDailyIncEquityHistory.query.filter(
        UserDailyIncEquityHistory.user_id.in_(user_ids),
        UserDailyIncEquityHistory.report_date == yesterday,
    ).delete()
    
    db.session.commit()


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestUserInvSummaryCache:
    """测试 UserInvSummaryCache 类"""
    
    def test_reload_with_single_user(self, tcontext):
        """测试单用户数据重新加载"""
        with tcontext:
            from app.caches.investment import UserInvSummaryCache
            from app.models.investment import NewUserInvestmentSummary
            
            clear_investment_summary_data([USER_ID, USER_ID_2])
            
            # 创建测试数据
            records = [
                create_investment_summary_data(USER_ID, "BTC", Decimal("1.5"), NewUserInvestmentSummary.InvType.CURRENT),
                create_investment_summary_data(USER_ID, "USDT", Decimal("1000.0"), NewUserInvestmentSummary.InvType.CURRENT),
            ]
            for record in records:
                db.session.add(record)
            db.session.commit()
            
            # 重新加载并验证
            UserInvSummaryCache.reload()
            cache = UserInvSummaryCache(USER_ID)
            
            # 验证缓存存在且数据正确
            assert cache.exists()
            raw_data = cache.hgetall()
            assert "BTC" in raw_data
            assert "USDT" in raw_data
    
    def test_reload_with_multiple_users_and_aggregation(self, tcontext):
        """测试多用户数据重新加载和同资产聚合"""
        with tcontext:
            from app.caches.investment import UserInvSummaryCache
            from app.models.investment import NewUserInvestmentSummary
            
            clear_investment_summary_data([USER_ID, USER_ID_2])
            
            # 创建测试数据：User1有多条BTC记录需要聚合，User2有USDT
            records = [
                create_investment_summary_data(USER_ID, "BTC", Decimal("1.0"), NewUserInvestmentSummary.InvType.CURRENT),
                create_investment_summary_data(USER_ID, "BTC", Decimal("0.5"), NewUserInvestmentSummary.InvType.EQUITY),
                create_investment_summary_data(USER_ID, "BTC", Decimal("0.3"), NewUserInvestmentSummary.InvType.COUPON),
                create_investment_summary_data(USER_ID_2, "USDT", Decimal("500.0"), NewUserInvestmentSummary.InvType.CURRENT),
            ]
            for record in records:
                db.session.add(record)
            db.session.commit()
            
            # 重新加载并验证
            UserInvSummaryCache.reload()
            
            # 验证用户1的BTC聚合 (1.0 + 0.5 + 0.3 = 1.8)
            cache1 = UserInvSummaryCache(USER_ID)
            assert cache1.exists()
            data1 = cache1.hgetall()
            assert Decimal(data1["BTC"]) == Decimal("1.8")
            
            # 验证用户2
            cache2 = UserInvSummaryCache(USER_ID_2)
            assert cache2.exists()
            data2 = cache2.hgetall()
            assert Decimal(data2["USDT"]) == Decimal("500.0")


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestUserYesInvSummaryCache:
    """测试 UserYesInvSummaryCache 类"""
    
    def test_reload_with_site_and_equity_interest(self, tcontext):
        """测试站内理财和权益加息利息的重新加载与聚合"""
        with tcontext:
            from app.caches.investment import UserYesInvSummaryCache
            from app.models.investment import UserDayInterestHistory
            from app.models.equity_center import UserDailyIncEquityHistory
            
            yesterday = today() - timedelta(days=1)
            clear_yesterday_interest_data([USER_ID], yesterday)
            
            # 创建站内理财利息
            site_record = create_day_interest_data(
                USER_ID, "BTC", Decimal("0.001"), yesterday, UserDayInterestHistory.Status.SUCCESS
            )
            db.session.add(site_record)
            
            # 创建权益加息利息
            eq_record = create_equity_interest_data(
                USER_ID, "BTC", Decimal("0.002"), yesterday, UserDailyIncEquityHistory.Status.FINISHED
            )
            db.session.add(eq_record)
            db.session.commit()
            
            # 重新加载并验证
            UserYesInvSummaryCache.reload()
            cache = UserYesInvSummaryCache(USER_ID)
            
            # 验证BTC利息聚合 (0.001 + 0.002 = 0.003)
            assert cache.exists()
            data = cache.hgetall()
            assert Decimal(data["BTC"]) == Decimal("0.003")
            
            # 验证TTL已设置
            ttl = cache.ttl()
            assert 0 < ttl <= UserYesInvSummaryCache.TTL
    
    def test_reload_filters_status_and_date(self, tcontext):
        """测试reload过滤非成功状态和非昨日数据"""
        with tcontext:
            from app.caches.investment import UserYesInvSummaryCache
            from app.models.investment import UserDayInterestHistory
            from app.models.equity_center import UserDailyIncEquityHistory
            
            yesterday = today() - timedelta(days=1)
            two_days_ago = today() - timedelta(days=2)
            
            # 清理数据
            UserDayInterestHistory.query.filter(
                UserDayInterestHistory.user_id == USER_ID,
                UserDayInterestHistory.report_date.in_([yesterday, two_days_ago]),
            ).delete()
            UserDailyIncEquityHistory.query.filter(
                UserDailyIncEquityHistory.user_id == USER_ID,
                UserDailyIncEquityHistory.report_date.in_([yesterday, two_days_ago]),
            ).delete()
            db.session.commit()
            
            # 创建昨日成功状态的记录（应该被加载）
            success_record = create_day_interest_data(
                USER_ID, "BTC", Decimal("0.001"), yesterday, UserDayInterestHistory.Status.SUCCESS
            )
            db.session.add(success_record)
            
            # 创建昨日待发放状态的记录（应该被过滤）
            pending_record = create_day_interest_data(
                USER_ID, "USDT", Decimal("100.0"), yesterday, UserDayInterestHistory.Status.PENDING
            )
            db.session.add(pending_record)
            
            # 创建两天前的记录（应该被过滤）
            old_record = create_day_interest_data(
                USER_ID, "ETH", Decimal("10.0"), two_days_ago, UserDayInterestHistory.Status.SUCCESS
            )
            db.session.add(old_record)
            
            # 创建昨日已完成的权益记录（应该被加载）
            finished_eq = create_equity_interest_data(
                USER_ID, "CET", Decimal("0.5"), yesterday, UserDailyIncEquityHistory.Status.FINISHED, user_equity_id=1
            )
            db.session.add(finished_eq)
            
            # 创建昨日待发放的权益记录（应该被过滤）
            pending_eq = create_equity_interest_data(
                USER_ID, "BNB", Decimal("1.0"), yesterday, UserDailyIncEquityHistory.Status.CREATED, user_equity_id=2
            )
            db.session.add(pending_eq)
            
            db.session.commit()
            
            # 重新加载并验证
            UserYesInvSummaryCache.reload()
            cache = UserYesInvSummaryCache(USER_ID)
            
            # 验证：只包含昨日且成功/已完成状态的记录
            assert cache.exists()
            data = cache.hgetall()
            assert "BTC" in data  # 昨日SUCCESS
            assert "CET" in data  # 昨日FINISHED
            assert "USDT" not in data  # 昨日但PENDING
            assert "ETH" not in data  # 两天前
            assert "BNB" not in data  # 昨日但CREATED
    
    def test_reload_with_multiple_users(self, tcontext):
        """测试多用户数据重新加载"""
        with tcontext:
            from app.caches.investment import UserYesInvSummaryCache
            from app.models.investment import UserDayInterestHistory
            
            yesterday = today() - timedelta(days=1)
            clear_yesterday_interest_data([USER_ID, USER_ID_2], yesterday)
            
            # 创建多个用户的数据
            records = [
                create_day_interest_data(USER_ID, "BTC", Decimal("0.001"), yesterday, UserDayInterestHistory.Status.SUCCESS),
                create_day_interest_data(USER_ID_2, "USDT", Decimal("5.0"), yesterday, UserDayInterestHistory.Status.SUCCESS),
            ]
            for record in records:
                db.session.add(record)
            db.session.commit()
            
            # 重新加载并验证
            UserYesInvSummaryCache.reload()
            
            # 验证用户1
            cache1 = UserYesInvSummaryCache(USER_ID)
            assert cache1.exists()
            data1 = cache1.hgetall()
            assert Decimal(data1["BTC"]) == Decimal("0.001")
            
            # 验证用户2
            cache2 = UserYesInvSummaryCache(USER_ID_2)
            assert cache2.exists()
            data2 = cache2.hgetall()
            assert Decimal(data2["USDT"]) == Decimal("5.0")


@pytest.mark.usefixtures('module_setup')
class TestCacheReload:

    def test_user_inv_summary_cache_reload(self, tcontext):
        """测试 UserInvSummaryCache.reload 方法"""
        with tcontext:
            from app.caches.investment import UserInvSummaryCache, UserYesInvSummaryCache
            UserInvSummaryCache.reload()
            UserYesInvSummaryCache.reload()
            InvIncEquityYesIncomeCache.reload()
