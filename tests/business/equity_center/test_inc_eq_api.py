# -*- coding: utf-8 -*-
import pytest
from pprint import pprint
from decimal import Decimal

from flask import g

from app import Language
from app.models import db
from app.models.equity_center import EquityBaseInfo, UserInvestIncreaseEquity, UserEquity, EquityType, EquitySendApply, UserCashbackEquity
from app.models.user import User
from app.common.constants import BusinessParty
from datetime import datetime, timedelta

from app.utils import now
from tests.common.mock_mysql import patch_mysql

USER_ID = 20044


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_mysql')
class TestInvestIncreaseEquityAdminApi:
    """理财加息权益管理端API测试"""

    ASSETS = ["BTC", "ETH", "USDT", "CET", "USDC"]
    
    def test_invest_increase_equity_list(self, tcontext):
        """测试理财加息权益列表接口"""
        with tcontext:
            url = '/admin/equity-center/invest-increase/list'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            params_list = [
                {},  # 无参数
                {'equity_id': 1, 'status': 'OPEN'},
                {'page': 1, 'limit': 10},
                {'page': 2, 'limit': 5},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0
                assert "items" in resp.json["data"]
                assert "total" in resp.json["data"]
                assert "extra" in resp.json["data"]

    def test_invest_increase_equity_create(self, tcontext):
        """测试理财加息权益创建接口"""
        with tcontext:
            url = '/admin/equity-center/invest-increase/list'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            data_list = [
                {
                    'increase_rate': 5.0,
                    'principal_amount_limit': '1000',
                    'assets': ['USDT', 'BTC'],
                    'activation_days': 7,
                    'usable_days': 30,
                    'total_interest': '100',
                    'remark': '测试理财加息权益1'
                },
            ]
            
            for data in data_list:
                resp = client.post(url, json=data)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_invest_increase_equity_status_update(self, tcontext):
        """测试理财加息权益状态更新接口"""
        with tcontext:
            client = tcontext.app.test_client()
            
            # 先创建一个测试权益
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试状态更新",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 测试不同状态更新
            test_cases = [
                {'id': test_equity.id, 'status': 'CLOSE'},
                {'id': test_equity.id, 'status': 'OPEN'},
            ]
            for test_case in test_cases:
                resp = client.patch('/admin/equity-center/invest-increase/list', json=test_case)
                pprint(resp.json)
                assert resp.json["code"] == 0
                
                EquityBaseInfo.query.get(test_equity.id).status = EquityBaseInfo.Status[test_case['status']]
            

    def test_invest_increase_equity_user_list(self, tcontext):
        """测试理财加息权益用户记录接口"""
        with tcontext:

            url = '/admin/equity-center/invest-increase/user-equity'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            params_list = [
                {},  # 无参数
                {'user_id': USER_ID},
                {'status': 'USING'},
                {'equity_id': 1},
                {'business_type': 'MISSION'},
                {'business_type': 'PLATFORM_SEND'},
                {'source_value': 'PLATFORM_SEND-84'},
                {'start_time': 1640995200},  # 2022-01-01
                {'end_time': 1672531200},    # 2023-01-01
                {'user_id': USER_ID, 'status': 'PENDING'},
                {'equity_id': 1, 'business_type': 'MISSION'},
                {'page': 1, 'limit': 10},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                assert resp.json["code"] == 0
                assert "items" in resp.json["data"]
                assert "total" in resp.json["data"]
                
    def test_app_investment_summary(self, tcontext):
        """测试app首页理财信息接口 - 无加息权益情况"""
        with tcontext:
            url = '/res/app/invest/summary'
            client = tcontext.app.test_client()
            
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0
            # 验证返回的数据结构
            data = resp.json["data"]
            assert "asset" in data
            assert "logo" in data
            assert "rate" in data
            assert data["asset"] == "USDT"
    
    def test_app_investment_summary_with_increase_equity(self, tcontext):
        """测试app首页理财信息接口 - 有加息权益情况"""
        with tcontext:
            from app.models.equity_center import (
                UserEquity, 
                UserInvestIncreaseEquity, 
                EquityType,
                EquityBaseInfo
            )
            from datetime import datetime, timedelta
            from decimal import Decimal
            
            # 创建权益基础信息
            equity_base = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                status=EquityBaseInfo.Status.OPEN,
                creator=1,
                remark="测试理财加息权益",
                cost_asset="USD",
                cost_amount=Decimal('100'),
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT", 
                    "principal_amount_limit": 1000,
                    "assets": ["USDT"],
                    "activation_days": 10,
                    "usable_days": 30
                }
            )
            db.session_add_and_commit(equity_base)
            
            # 创建用户权益基础记录
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=equity_base.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.USING,
                finished_at=datetime.now() + timedelta(days=30)
            )
            db.session_add_and_commit(user_equity)
            
            # 创建用户理财加息权益详情
            user_detail = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                investment_asset='USDT',
                increase_amount=Decimal('0'),
                increase_usd=Decimal('0'),
                active_at=datetime.now(),
                finished_at=datetime.now() + timedelta(days=30),
                payout_date=None,
                increase_rate=Decimal('0.05'),
                principal_asset='USDT',
                principal_amount_limit=Decimal('1000'),
                assets=['USDT'],
                usable_days=30,
                activation_days=10
            )
            db.session_add_and_commit(user_detail)
            
            url = '/res/app/invest/summary'
            client = tcontext.app.test_client()
            
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0
            # 验证返回的数据结构
            data = resp.json["data"]
            assert data["asset"] == "USDT"
            assert Decimal(data["rate"]) > Decimal('0.05')


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_mysql')
class TestInvestIncreaseEquityFrontendApi:
    """理财加息权益前端API测试"""

    def test_invest_increase_asset_selection(self, tcontext):
        """测试用户选择理财加息币种接口"""
        with tcontext:
            url = '/res/reward-center/invest-increase-asset'
            client = tcontext.app.test_client()
            
            # 创建测试权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试前端选择币种",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建用户权益
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.PENDING
            )
            db.session_add_and_commit(user_equity)
            
            # 创建用户理财加息权益详情
            user_invest_equity = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                increase_rate=Decimal("0.05"),
                principal_asset="USDT",
                principal_amount_limit=Decimal("1000"),
                assets=["USDT", "BTC", "ETH"],
                usable_days=30,
                activation_days=7
            )
            db.session_add_and_commit(user_invest_equity)
            
            # 测试不同币种选择
            test_cases = [
                {'user_equity_id': user_equity.id, 'asset': 'USDT'},
                {'user_equity_id': user_equity.id, 'asset': 'BTC'},
                {'user_equity_id': user_equity.id, 'asset': 'ETH'},
            ]

            for test_case in test_cases:
                resp = client.post(url, json=test_case)
                pprint(resp.json)
                # 注意：这个接口可能需要特定的认证，所以可能返回401或其他错误码
                # 这里主要测试接口结构是否正确
                assert resp.status_code in [200, 401, 403]  # 根据实际认证情况调整


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_mysql')
class TestEquitySendApplyApi:
    """权益发放申请API测试"""

    def test_equity_send_apply_create(self, tcontext):
        """测试理财加息权益发放创建接口"""
        with tcontext:
            url = '/admin/equity-center/send-apply/list'
            client = tcontext.app.test_client()
            
            # 先创建一个理财加息权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试理财加息权益发放",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 测试创建权益发放申请
            send_at = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)  # 1小时后发放
            data = {
                'title': '测试理财加息权益发放',
                'business_party': BusinessParty.GROWTH.name,
                'send_type': EquitySendApply.SendType.DELIVERY.name,
                'send_at': send_at,
                'equity_type': EquityType.INVEST_INCREASE.name,
                'equity_id': test_equity.id,
                'user_selection_type': EquitySendApply.UserSelectionType.MANUAL.name,
                'manual_user_list': f'{USER_ID},20018,20019',  # 手动指定用户
                'remark': '测试理财加息权益发放申请'
            }
            
            resp = client.post(url, json=data)
            pprint(resp.json)
            assert resp.json["code"] == 0
            assert "id" in resp.json["data"]
            
            # 验证数据库中的记录
            apply_id = resp.json["data"]["id"]
            apply = EquitySendApply.query.get(apply_id)
            assert apply is not None
            assert apply.title == data['title']
            assert apply.business_party.name == data['business_party']
            assert apply.equity_type == EquityType.INVEST_INCREASE
            assert apply.equity_id == test_equity.id
            assert apply.status == EquitySendApply.Status.CREATED

    def test_equity_send_apply_audit_pass(self, tcontext):
        """测试权益发放审核通过接口"""
        with tcontext:
            # 先创建一个测试权益发放申请
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试审核通过",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建权益发放申请
            send_at = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)
            apply = EquitySendApply(
                title='测试审核通过申请',
                business_party=BusinessParty.GROWTH,
                send_type=EquitySendApply.SendType.DELIVERY,
                equity_id=test_equity.id,
                equity_type=EquityType.INVEST_INCREASE,
                total_send_count=50,
                send_at=datetime.fromtimestamp(send_at / 1000),
                creator=1,
                remark='测试审核通过',
                user_selection_type=EquitySendApply.UserSelectionType.MANUAL,
                group_ids=[],
                group_user_count=0,
                group_user_ids=b'',
                send_user_ids=b'',
                status=EquitySendApply.Status.CREATED
            )
            db.session_add_and_commit(apply)
            
            # 测试审核通过
            url = f'/admin/equity-center/send-apply/{apply.id}'
            client = tcontext.app.test_client()
            
            audit_data = {
                'status': EquitySendApply.Status.PASSED.name
            }
            
            resp = client.patch(url, json=audit_data)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 验证状态已更新
            db.session.refresh(apply)
            assert apply.status == EquitySendApply.Status.PASSED

    def test_equity_send_apply_list(self, tcontext):
        """测试权益发放申请列表查询接口"""
        with tcontext:
            url = '/admin/equity-center/send-apply/list'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            params_list = [
                {},  # 无参数
                {'equity_type': EquityType.INVEST_INCREASE.name},
                {'status': EquitySendApply.Status.CREATED.name},
                {'page': 1, 'limit': 10},
                {'equity_type': EquityType.INVEST_INCREASE.name, 'status': EquitySendApply.Status.CREATED.name},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0
                assert "items" in resp.json["data"]
                assert "total" in resp.json["data"]
                assert "extra" in resp.json["data"]

    def test_equity_send_apply_update(self, tcontext):
        """测试权益发放申请修改接口"""
        with tcontext:
            # 先创建一个测试权益发放申请
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试修改申请",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建权益发放申请
            send_at = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)
            apply = EquitySendApply(
                title='测试修改申请',
                business_party=BusinessParty.GROWTH,
                send_type=EquitySendApply.SendType.DELIVERY,
                equity_id=test_equity.id,
                equity_type=EquityType.INVEST_INCREASE,
                total_send_count=30,
                send_at=datetime.fromtimestamp(send_at / 1000),
                creator=1,
                remark='测试修改申请',
                user_selection_type=EquitySendApply.UserSelectionType.MANUAL,
                group_ids=[],
                group_user_count=0,
                group_user_ids=b'',
                send_user_ids=b'',
                status=EquitySendApply.Status.CREATED
            )
            db.session_add_and_commit(apply)
            
            # 测试修改申请
            url = f'/admin/equity-center/send-apply/{apply.id}'
            client = tcontext.app.test_client()
            
            new_send_at = int((datetime.now() + timedelta(hours=2)).timestamp() * 1000)
            update_data = {
                'title': '修改后的测试申请',
                'business_party': BusinessParty.BRAND.name,
                'send_type': EquitySendApply.SendType.DELIVERY.name,
                'send_at': new_send_at,
                'equity_type': EquityType.INVEST_INCREASE.name,
                'equity_id': test_equity.id,
                'total_send_count': 60,
                'user_selection_type': EquitySendApply.UserSelectionType.MANUAL.name,
                'manual_user_list': f'{USER_ID}',
                'remark': '修改后的测试申请'
            }
            
            resp = client.put(url, json=update_data)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 验证修改结果
            db.session.refresh(apply)
            assert apply.title == update_data['title']
            assert apply.business_party.name == update_data['business_party']
            assert apply.total_send_count == update_data['total_send_count']
            assert apply.remark == update_data['remark']

    def test_equity_send_apply_audit_reject(self, tcontext):
        """测试权益发放审核拒绝接口"""
        with tcontext:
            # 先创建一个测试权益发放申请
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试审核拒绝",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建权益发放申请
            send_at = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)
            apply = EquitySendApply(
                title='测试审核拒绝申请',
                business_party=BusinessParty.GROWTH,
                send_type=EquitySendApply.SendType.DELIVERY,
                equity_id=test_equity.id,
                equity_type=EquityType.INVEST_INCREASE,
                total_send_count=25,
                send_at=datetime.fromtimestamp(send_at / 1000),
                creator=1,
                remark='测试审核拒绝',
                user_selection_type=EquitySendApply.UserSelectionType.MANUAL,
                group_ids=[],
                group_user_count=0,
                group_user_ids=b'',
                send_user_ids=b'',
                status=EquitySendApply.Status.CREATED
            )
            db.session_add_and_commit(apply)
            
            # 测试审核拒绝
            url = f'/admin/equity-center/send-apply/{apply.id}'
            client = tcontext.app.test_client()
            
            reject_data = {
                'status': EquitySendApply.Status.REJECTED.name
            }
            
            resp = client.patch(url, json=reject_data)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 验证状态已更新
            db.session.refresh(apply)
            assert apply.status == EquitySendApply.Status.REJECTED


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_mysql')
class TestRewardCenterInvestIncreaseApi:
    """理财加息权益前端奖励中心API测试"""

    def test_reward_center_rewards_list(self, tcontext):
        """测试奖励中心-我的奖励列表接口（理财加息场景）"""
        with tcontext:
            url = '/res/reward-center/rewards'
            client = tcontext.app.test_client()
            
            # 创建测试理财加息权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试奖励中心理财加息权益",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建用户权益记录
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.PENDING,
                finished_at=now() - timedelta(days=1)
            )
            db.session_add_and_commit(user_equity)
            
            # 创建用户理财加息权益详情
            user_invest_equity = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                increase_rate=Decimal("0.05"),
                principal_asset="USDT",
                principal_amount_limit=Decimal("1000"),
                assets=["USDT", "BTC", "ETH"],
                usable_days=30,
                activation_days=7
            )
            db.session_add_and_commit(user_invest_equity)
            
            # 测试旧版本App请求时，有理财加息奖励会抛出升级异常
            old_android_headers = {
                "PLATFORM": "Android",
                "BUILD": "4009"  # 小于4010，触发旧版本逻辑
            }
            resp = client.get(url, headers=old_android_headers)
            pprint(resp.json)
            assert resp.json["code"] == 32  # AppUpgradeRequired的response_code
            
            # 测试旧版本iOS App请求时，有理财加息奖励会抛出升级异常
            old_ios_headers = {
                "PLATFORM": "iOS",
                "BUILD": "101"  # 小于102，触发旧版本逻辑
            }
            resp = client.get(url, headers=old_ios_headers)
            pprint(resp.json)
            assert resp.json["code"] == 32  # AppUpgradeRequired的response_code
            
            # 测试新版本App请求时，有理财加息奖励不会抛出异常
            new_android_headers = {
                "PLATFORM": "Android",
                "BUILD": "4010"  # 大于等于4010，新版本
            }
            resp = client.get(url, headers=new_android_headers)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 测试新版本iOS App请求时，有理财加息奖励不会抛出异常
            new_ios_headers = {
                "PLATFORM": "iOS",
                "BUILD": "102"  # 大于等于102，新版本
            }
            resp = client.get(url, headers=new_ios_headers)
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            # 测试不同参数组合（使用新版本App）
            # params_list = [
            #     {},  # 无参数，获取所有奖励
            #     {'reward_type': EquityType.INVEST_INCREASE.name},  # 只获取理财加息类型
            #     {'status': 'PENDING'},  # 只获取待激活状态
            #     {'page': 1, 'limit': 10},  # 分页参数
            #     {'reward_type': EquityType.INVEST_INCREASE.name, 'status': 'PENDING'},  # 组合条件
            # ]
            
            # for params in params_list:
            #     resp = client.get(url, query_string=params, headers=new_android_headers)
            #     pprint(resp.json)
            #     assert resp.json["code"] == 0
                
            #     # 验证返回的数据结构
            #     if data := resp.json["data"]:
            #         item = data["data"][0]
            #         # 验证理财加息权益特有字段
            #         if item["reward_type"] == EquityType.INVEST_INCREASE.name:
            #             fields = ["active_at", "assets", "increase_rate", "principal_asset", "principal_amount_limit", "usable_days", "investment_asset", "finished_at"]
            #             for field in fields:
            #                 assert field in item["extra"]

    def test_reward_center_rewards_list_with_different_status(self, tcontext):
        """测试不同状态下的理财加息权益列表"""
        with tcontext:
            url = '/res/reward-center/rewards'
            client = tcontext.app.test_client()
            
            # 创建测试权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试不同状态理财加息权益",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建不同状态的用户权益
            statuses = [
                UserEquity.Status.PENDING,
                UserEquity.Status.USING,
                UserEquity.Status.EXPIRED,
                UserEquity.Status.FINISHED,
            ]
            
            created_user_equities = []
            for status in statuses:
                user_equity = UserEquity(
                    user_id=USER_ID,
                    equity_id=test_equity.id,
                    type=EquityType.INVEST_INCREASE,
                    business_id=len(created_user_equities) + 1,
                    business_type=UserEquity.BusinessType.PLATFORM_SEND,
                    status=status
                )
                db.session_add_and_commit(user_equity)
                created_user_equities.append(user_equity)
                
                # 创建对应的理财加息权益详情
                user_invest_equity = UserInvestIncreaseEquity(
                    user_id=USER_ID,
                    user_equity_id=user_equity.id,
                    increase_rate=Decimal("0.05"),
                    principal_asset="USDT",
                    principal_amount_limit=Decimal("1000"),
                    assets=["USDT", "BTC"],
                    usable_days=30,
                    activation_days=7
                )
                db.session_add_and_commit(user_invest_equity)
            
            # 测试获取所有状态的权益
            resp = client.get(url)
            assert resp.json["code"] == 0
            assert len(resp.json["data"]) >= len(statuses)
            
            # 测试按状态筛选
            for status in statuses:
                front_status_map = {
                    UserEquity.Status.PENDING: 'PENDING',
                    UserEquity.Status.USING: 'USING',
                    UserEquity.Status.EXPIRED: 'EXPIRED',
                    UserEquity.Status.FINISHED: 'FINISHED',
                }
                front_status = front_status_map[status]
                
                resp = client.get(url, query_string={'status': front_status})
                assert resp.json["code"] == 0
                
                # 验证返回的数据状态正确
                for item in resp.json["data"]["data"]:
                    if item["reward_type"] == EquityType.INVEST_INCREASE.name:
                        assert item["status"] == front_status

    def test_reward_center_reward_detail(self, tcontext):
        """测试奖励中心-奖励详情接口（理财加息场景）"""
        with tcontext:
            # 创建测试理财加息权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试奖励详情理财加息权益",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建用户权益记录
            finish_at = datetime.now() + timedelta(days=37)  # 假设权益在37天后完成
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.PENDING,
                finished_at=finish_at
            )
            db.session_add_and_commit(user_equity)
            
            # 创建用户理财加息权益详情
            user_invest_equity = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                increase_rate=Decimal("0.05"),
                principal_asset="USDT",
                principal_amount_limit=Decimal("1000"),
                assets=["USDT", "BTC", "ETH"],
                usable_days=30,
                activation_days=7,
                investment_asset="USDT",  # 用户选择的币种
                active_at=datetime.now(),
                finished_at=finish_at
            )
            db.session_add_and_commit(user_invest_equity)
            
            # 测试获取奖励详情
            url = '/res/reward-center/reward-detail'
            client = tcontext.app.test_client()
            
            resp = client.get(url, query_string={'reward_id': user_equity.id})
            pprint(resp.json)
            assert resp.json["code"] == 0
            
            detail = resp.json["data"]
            
            # 验证基础字段
            assert detail["reward_id"] == user_equity.id
            assert detail["reward_type"] == EquityType.INVEST_INCREASE.name
            assert detail["status"] == "PENDING"
            assert detail["created_at"] is not None
            assert detail["expired_at"] is not None
            assert detail["value"] is not None
            assert detail["value_type"] is not None
            
            # 验证理财加息权益特有字段
            assert detail["active_at"] is not None
            assert detail["assets"] == ["USDT", "BTC", "ETH"]
            assert detail["increase_rate"] == str(0.05)
            assert detail["principal_asset"] == "USDT"
            assert detail["principal_amount_limit"] == str(1000)
            assert detail["usable_days"] == 30
            assert detail["investment_asset"] == "USDT"
            assert detail["finished_at"] is not None
            assert detail["business_type"] == "PLATFORM_SEND"
            
            # 验证来源信息
            assert detail["source"] == "PLATFORM_SEND"
            assert detail["source_desc"] is not None
            
            # 验证奖励说明
            assert detail["reward_desc"] is not None
            assert "加息权益" in detail["reward_desc"]
            assert "激活" in detail["reward_desc"]
            assert "币种" in detail["reward_desc"]

    def test_reward_center_reward_detail_not_found(self, tcontext):
        """测试获取不存在的奖励详情"""
        with tcontext:
            url = '/res/reward-center/reward-detail'
            client = tcontext.app.test_client()
            
            # 测试不存在的奖励ID
            resp = client.get(url, query_string={'reward_id': 99999})
            assert resp.json["code"] != 0  # 应该返回错误

    def test_reward_center_total_reward(self, tcontext):
        """测试获取不存在的奖励详情"""
        with tcontext:
            user_id = 10
            g.user = g.auth_user = User.query.get(user_id)
            url = '/res/reward-center/total-reward'
            client = tcontext.app.test_client()

            # 测试不存在的奖励ID
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0  # 应该返回错误


    def test_get_task_detail(self, tcontext):
        with tcontext:
            user_id = 20045
            g.user = g.auth_user = User.query.get(user_id)
            url = '/res/reward-center/tasks'
            client = tcontext.app.test_client()
            
            old_android_headers = {
                "PLATFORM": "Android",
                "BUILD": "4009"  # 小于4010，触发旧版本逻辑
            }
            resp = client.get(url, headers=old_android_headers)
            pprint(resp.json)
            assert resp.json["code"] == 32  # AppUpgradeRequired的response_code
            
            # 测试旧版本iOS App请求时，有理财加息奖励会抛出升级异常
            old_ios_headers = {
                "PLATFORM": "iOS",
                "BUILD": "101"  # 小于102，触发旧版本逻辑
            }
            resp = client.get(url, headers=old_ios_headers)
            pprint(resp.json)
            assert resp.json["code"] == 32  # AppUpgradeRequired的response_code
            


    def test_reward_center_reward_detail_other_user(self, tcontext):
        """测试获取其他用户的奖励详情（应该失败）"""
        with tcontext:
            # 创建其他用户的权益
            other_user_id = 20045
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试其他用户权益",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            other_user_equity = UserEquity(
                user_id=other_user_id,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.PENDING
            )
            db.session_add_and_commit(other_user_equity)
            
            # 当前用户尝试获取其他用户的权益详情
            url = '/res/reward-center/reward-detail'
            client = tcontext.app.test_client()
            
            resp = client.get(url, query_string={'reward_id': other_user_equity.id})
            assert resp.json["code"] != 0  # 应该返回错误，无法访问其他用户的权益

    def test_reward_center_old_total_reward(self, tcontext):
        """旧版本安卓理财错误"""
        with tcontext:
            url = '/res/reward-center/total-reward'
            client = tcontext.app.test_client()
            
            # 创建测试理财加息权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试奖励中心理财加息权益",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建用户权益记录
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.PENDING,
                finished_at=now() - timedelta(days=1)
            )
            db.session_add_and_commit(user_equity)
            
            # 测试旧版本App请求时，有理财加息奖励会抛出升级异常
            old_android_headers = {
                "PLATFORM": "Android",
                "BUILD": "4009"  # 小于4010，触发旧版本逻辑
            }
            resp = client.get(url, headers=old_android_headers)
            pprint(resp.json)
            assert resp.json["code"] == 32

            # 测试旧版本App请求时，有理财加息奖励会抛出升级异常
            old_android_headers = {
                "PLATFORM": "Android",
                "BUILD": "4010"
            }
            resp = client.get(url, headers=old_android_headers)
            pprint(resp.json)
            assert resp.json["code"] == 0


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_mysql')
class TestInvestIncreaseRewardsApi:
    """理财加息权益奖励弹窗列表API测试"""

    def test_invest_increase_rewards_list(self, tcontext):
        """测试任务中心-理财加息权益奖励弹窗列表接口"""
        with tcontext:
            url = '/res/reward-center/invest-increase-rewards'
            client = tcontext.app.test_client()
            
            # 创建测试理财加息权益基础信息
            test_equity = EquityBaseInfo(
                type=EquityType.INVEST_INCREASE,
                creator=1,
                remark="测试理财加息权益奖励弹窗",
                cost_asset="USDT",
                cost_amount=Decimal("100"),
                status=EquityBaseInfo.Status.OPEN,
                extra_data={
                    "increase_rate": 0.05,
                    "principal_asset": "USDT",
                    "principal_amount_limit": "1000",
                    "assets": ["USDT", "BTC", "ETH"],
                    "activation_days": 7,
                    "usable_days": 30,
                }
            )
            db.session_add_and_commit(test_equity)
            
            # 创建不同状态的用户权益记录
            # 1. USING 状态且未过期的权益
            using_equity = UserEquity(
                user_id=USER_ID,
                equity_id=test_equity.id,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.PLATFORM_SEND,
                status=UserEquity.Status.USING,
                finished_at=now() + timedelta(days=30)  # 30天后过期
            )
            db.session_add_and_commit(using_equity)
            
            user_invest_equities = [
                UserInvestIncreaseEquity(
                    user_id=USER_ID,
                    user_equity_id=using_equity.id,
                    increase_rate=Decimal("0.05"),
                    principal_asset="USDT",
                    principal_amount_limit=Decimal("1000"),
                    assets=["USDT", "BTC", "ETH"],
                    usable_days=30,
                    activation_days=7,
                    investment_asset="USDT",
                    active_at=now() - timedelta(days=5),
                    finished_at=now() + timedelta(days=30)
                ),
            ]
            
            for invest_equity in user_invest_equities:
                db.session_add_and_commit(invest_equity)
            
            # 测试接口调用
            resp = client.get(url)
            print(resp.text)
            pprint(resp.json)
            assert resp.json["code"] == 0

