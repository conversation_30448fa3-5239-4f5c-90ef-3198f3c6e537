import os
import sys
import click

from decimal import Decimal

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


@click.command()
@click.argument('chain')
@click.argument('contract')
@click.argument('asset')
@click.option('--execute')
def main(chain: str, contract: str, asset: str, execute: bool):
    from app.common.onchain import Chain
    from app.common.constants import BalanceBusiness
    from app.assets.asset import get_asset
    from app.exceptions.not_found import AssetNotFound
    from app.business.clients.server import ServerClient
    from app.business.clients.server import ServerResponseCode
    from app.business.onchain.base import OnchainAddressHelper
    from app.business.onchain.token import get_token_by_chain_and_contract
    from app.models import db
    from app.models.onchain import OnchainOrder
    from app.models.onchain import OnchainTokenBalance
    from app.models.onchain import OnchainTokenBlocklist
    from app.models.onchain import OnchainAssetToSpotHistory
    from app.utils.onchain import decimal_add
    from app.utils.upload import upload_file
    from app.utils.export import ExcelExporter
    from app.utils import quantize_amount

    chain = Chain.display_name_to_enum(chain)
    contract = OnchainAddressHelper(chain).normalise_address(contract)
    asset = asset.upper()

    # 检查chain+contract是否存在
    token = get_token_by_chain_and_contract(chain, contract)
    if not token:
        raise ValueError(f'未找到指定Token: {chain.name}.{contract}')
    # 检查asset是否存在
    try:
        get_asset(asset)
    except AssetNotFound:
        raise ValueError(f'未找到指定Asset: {asset}')

    # 检查是否加入Token黑名单
    block: OnchainTokenBlocklist = OnchainTokenBlocklist.query.filter(
        OnchainTokenBlocklist.chain == chain,
        OnchainTokenBlocklist.contract == contract,
    ).first()
    if not block:
        raise ValueError(f'指定Token未加入黑名单: {chain.name}.{contract}')
    if block.block_type not in OnchainTokenBlocklist.BlockType.can_not_trade_type():
        raise ValueError(
            f'指定Token黑名单类型未包括禁止交易: {chain.name}.{contract}, status: {block.block_type.value}'
        )

    # 检查是否有未完成的订单
    orders = OnchainOrder.query.filter(
        OnchainOrder.token_id == token.id,
        OnchainOrder.status.in_(
            {OnchainOrder.Status.CREATED, OnchainOrder.Status.PROCESSING, OnchainOrder.Status.TO_FINISH}
        ),
    ).all()
    if len(orders) > 0:
        for order in orders:
            print(f'存在未完成订单: {order.to_dict(enum_to_name=True)}')
        raise ValueError(f'存在上述未完成的订单, 请先联系钱包解决')

    # 检查是否有冻结中的余额
    balances = OnchainTokenBalance.query.filter(
        OnchainTokenBalance.token_id == token.id,
    ).all()
    need_migrate_count, frozen_count = 0, 0
    for balance in balances:
        if Decimal(balance.frozen) > Decimal():
            print(f'存在frozen不为0的余额记录: {balance.to_dict(enum_to_name=True)}')
            frozen_count += 1
        if Decimal(balance.available) > Decimal():
            need_migrate_count += 1
    if frozen_count > 0:
        raise ValueError(f'存在上述冻结的余额记录, 请先联系钱包解决')
    if need_migrate_count == 0:
        raise ValueError(f'未找到余额大于0的记录')

    data = []
    total_amount = Decimal()

    if not execute:
        for balance in balances:
            if not Decimal(balance.available):
                continue
            amount = quantize_amount(balance.available, 8)
            data.append(dict(user_id=balance.user_id, amount=amount))
            total_amount += amount
    else:
        for balance in balances:
            balance: OnchainTokenBalance

            history = OnchainAssetToSpotHistory.query.filter(
                OnchainAssetToSpotHistory.token_id == balance.token_id,
                OnchainAssetToSpotHistory.user_id == balance.user_id,
            ).first()
            if not history:
                if not Decimal(balance.available):
                    continue
                history = OnchainAssetToSpotHistory(
                    user_id=balance.user_id,
                    token_id=balance.token_id,
                    asset=asset,
                    amount=balance.available,
                    status=OnchainAssetToSpotHistory.Status.CREATED,
                )
                db.session_add_and_commit(history)

            if history.status == OnchainAssetToSpotHistory.Status.CREATED:
                # OnchainBalance表扣减
                balance.available = '0'
                history.status = OnchainAssetToSpotHistory.Status.DEDUCTED
                db.session.commit()

            if history.status == OnchainAssetToSpotHistory.Status.DEDUCTED:
                # Server增加资产
                try:
                    amount = quantize_amount(history.amount, 8)
                    if amount > 0:
                        ServerClient().add_user_balance(
                            history.user_id, history.asset, amount, BalanceBusiness.ONCHAIN_ASSET_TO_SPOT, history.id,
                        )
                    history.status = OnchainAssetToSpotHistory.Status.FINISHED
                    db.session.commit()
                    data.append(dict(user_id=history.user_id, amount=amount))
                    total_amount += amount
                except ServerClient.BadResponse as e:
                    print(f'调用Server增加资产错误: {e}, e_code: {e.code}')
                    if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                        print(f'资产处理重复: {history.to_dict(enum_to_name=True)}')
                        history.status = OnchainAssetToSpotHistory.Status.FINISHED
                        db.session.commit()

            print(f'资产迁移成功: {history.to_dict(enum_to_name=True)}')

    print(f'资产迁移完成, asset: {asset}, count: {len(data)}, total_amount: {total_amount}')

    stream = ExcelExporter(
        data_list=data,
        fields=['user_id', 'amount'],
        headers=['user_id', 'amount'],
    ).export_streams()
    print('迁移用户名单:')
    print(upload_file(stream, 'xlsx'))


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
