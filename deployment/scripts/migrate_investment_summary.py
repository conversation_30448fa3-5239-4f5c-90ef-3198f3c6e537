# -*- coding: utf-8 -*-
"""
数据迁移脚本：将 UserInvestmentSummary 数据迁移到 NewUserInvestmentSummary
"""
from init_base import app
from app.caches.investment import UserInvSummaryCache
from app.schedules.investment import update_investment_yes_summary_schedule
from datetime import datetime

from sqlalchemy import func

from app.models.activity import Coupon, CouponDailyBalanceHistory
from app.models.base import db
from app.models.equity_center import UserInvestIncreaseEquity
from app.models.investment import UserInvestmentSummary, NewUserInvestmentSummary
from app.utils.date_ import today


BATCH_SIZE = 3000  # 批处理大小


def old_summary_to_new_summary():
    print(f"[{datetime.now()}] 开始数据迁移...")
    
    # 步骤1：分批读取 UserInvestmentSummary 表所有数据
    old_data_list = []
    offset = 0
    
    while True:
        batch = UserInvestmentSummary.query.order_by(
            UserInvestmentSummary.id.asc()
        ).limit(BATCH_SIZE).offset(offset).all()
        
        if not batch:
            break
            
        old_data_list.extend(batch)
        offset += BATCH_SIZE
    
    print(f"[{datetime.now()}] UserInvestmentSummary 表共 {len(old_data_list)} 条数据")
    
    # 步骤2：读取 NewUserInvestmentSummary 表所有数据
    new_data_list = []
    offset = 0
    
    while True:
        batch = NewUserInvestmentSummary.query.order_by(
            NewUserInvestmentSummary.id.asc()
        ).limit(BATCH_SIZE).offset(offset).all()
        
        if not batch:
            break
            
        new_data_list.extend(batch)
        offset += BATCH_SIZE
        print(f"[{datetime.now()}] 已读取 {len(new_data_list)} 条新数据...")
    
    print(f"[{datetime.now()}] NewUserInvestmentSummary 表共 {len(new_data_list)} 条数据")
    
    # 步骤3：构建新表已有数据的唯一标识集合
    # 根据 report_date, user_id, asset, inv_type 组成唯一标识
    existing_keys = dict()
    for item in new_data_list:
        key = (item.user_id, item.asset, item.inv_type)
        existing_keys[key] = item.report_date
    
    print(f"[{datetime.now()}] 新表已有 {len(existing_keys)} 条唯一记录")
    
    # 步骤4：筛选出未迁移的数据
    # UserInvestmentSummary 没有 inv_type 字段，默认使用 CURRENT（活期理财）
    to_migrate = []
    to_update = []
    default_inv_type = NewUserInvestmentSummary.InvType.CURRENT
    
    for old_item in old_data_list:
        key = (old_item.user_id, old_item.asset, default_inv_type)
        # 情况1：key 不在 existing_keys 中，需要迁移
        if key not in existing_keys:
            to_migrate.append(old_item)
        # 情况2：key 在 existing_keys 中，但 old_item.report_date 大于现有的 report_date，需要迁移
        elif old_item.report_date > existing_keys[key]:
            to_update.append(old_item)
    
    print(f"[{datetime.now()}] 共有 {len(to_migrate)} 条数据需要迁移")
    print(f"[{datetime.now()}] 共有 {len(to_update)} 条数据需要更新")
    
    if not to_migrate and not to_update:
        print(f"[{datetime.now()}] 没有需要迁移或更新的数据，脚本结束")
        return
    
    # 步骤5：分批写入新表
    if to_migrate:
        print(f"[{datetime.now()}] 开始分批写入数据...")
        total = len(to_migrate)
        migrated_count = 0
        
        for i in range(0, total, BATCH_SIZE):
            batch = to_migrate[i:i + BATCH_SIZE]
            
            # 批量创建新记录
            new_records = []
            for old_item in batch:
                new_record = NewUserInvestmentSummary(
                    report_date=old_item.report_date,
                    user_id=old_item.user_id,
                    asset=old_item.asset,
                    amount=old_item.amount,
                    inv_type=default_inv_type
                )
                new_records.append(new_record)
            
            # 批量插入
            db.session.bulk_save_objects(new_records)
            db.session.commit()
            
            migrated_count += len(batch)
            progress = (migrated_count / total) * 100
            print(f"[{datetime.now()}] 进度: {migrated_count}/{total} ({progress:.2f}%)")
        
        print(f"[{datetime.now()}] 数据迁移完成！共迁移 {migrated_count} 条数据")
    
    # 步骤6：分批更新现有数据
    if to_update:
        print(f"[{datetime.now()}] 开始分批更新数据...")
        total = len(to_update)
        updated_count = 0
        
        db.session.expunge_all()
        for i in range(0, total, BATCH_SIZE):
            batch = to_update[i:i + BATCH_SIZE]
            
            # 批量更新记录
            for old_item in batch:
                # 查询现有记录并更新
                existing_record = NewUserInvestmentSummary.query.filter_by(
                    user_id=old_item.user_id,
                    asset=old_item.asset,
                    inv_type=default_inv_type
                ).first()
                
                if existing_record:
                    NewUserInvestmentSummary.query.filter_by(id=existing_record.id).update(
                        {
                            NewUserInvestmentSummary.report_date: old_item.report_date,
                            NewUserInvestmentSummary.amount: old_item.amount,
                        }
                    )
            db.session.commit()
            
            updated_count += len(batch)
            progress = (updated_count / total) * 100
            print(f"[{datetime.now()}] 进度: {updated_count}/{total} ({progress:.2f}%)")
        
        print(f"[{datetime.now()}] 数据更新完成！共更新 {updated_count} 条数据")
    

def write_inc_coupon_data():
    print(f"[{datetime.now()}] 开始写入理财加息卷数据...")
    model = CouponDailyBalanceHistory
    rows = (
            model.query.filter(
                model.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
            )
            .group_by(
                model.user_id,
                model.asset,
            )
            .with_entities(
                model.user_id,
                model.asset,
                func.sum(model.amount).label("total_amount"),
            )
            .all()
        )
    td = today()
    exists_rows = NewUserInvestmentSummary.query.filter(
        NewUserInvestmentSummary.inv_type == NewUserInvestmentSummary.InvType.COUPON,
    ).all()
    exists_keys = {(row.user_id, row.asset) for row in exists_rows}
    print(f"[{datetime.now()}] 新表已有 {len(exists_keys)} 条唯一记录")
    write_count = 0
    for row in rows:
        if (row.user_id, row.asset) in exists_keys:
            continue
        new_rows = NewUserInvestmentSummary(
            report_date=td,
            user_id=row.user_id,
            asset=row.asset,
            amount=row.total_amount,
            inv_type=NewUserInvestmentSummary.InvType.COUPON,
        )
        write_count += 1
        db.session.add(new_rows)
    db.session.commit()
    print(f"[{datetime.now()}] 理财加息卷迁移 {write_count} 条数据")
    

def write_inc_eq_interest_data():
    print(f"[{datetime.now()}] 开始写入活期加息权益数据...")
    model = UserInvestIncreaseEquity
    rows = (
        model.query.filter(
            model.increase_amount > 0,
        )
        .group_by(
            model.user_id,
            model.investment_asset,
        ).with_entities(
            model.user_id,
            model.investment_asset,
            func.sum(model.increase_amount).label("total_amount"),
        )
    )
    exists_rows = NewUserInvestmentSummary.query.filter(
        NewUserInvestmentSummary.inv_type == NewUserInvestmentSummary.InvType.EQUITY,
    ).all()
    exists_keys = {(row.user_id, row.asset) for row in exists_rows}
    print(f"[{datetime.now()}] 新表已有 {len(exists_keys)} 条唯一记录")
    td = today()
    write_count = 0
    for row in rows:
        if (row.user_id, row.investment_asset) in exists_keys:
            continue
        new_rows = NewUserInvestmentSummary(
            report_date=td,
            user_id=row.user_id,
            asset=row.investment_asset,
            amount=row.total_amount,
            inv_type=NewUserInvestmentSummary.InvType.EQUITY,
        )
        write_count += 1
        db.session.add(new_rows)
    db.session.commit()
    print(f"[{datetime.now()}] 活期加息权益迁移 {write_count} 条数据")


def reload_cache():
    update_investment_yes_summary_schedule()
    redis = UserInvSummaryCache.redis
    patterns = [
        'investment_inc_total_income*',
        'inv_inc_equity_total_income*',
    ]
    # 使用 scan_iter 找到所有匹配的键并删除
    for pattern in patterns:
        keys_to_delete = list(redis.scan_iter(match=pattern, count=1000))
        if keys_to_delete:
            redis.delete(*keys_to_delete)
            print(f"[{datetime.now()}] 删除了 {len(keys_to_delete)} 个匹配 '{pattern}' 的缓存键")


def main(): 
    old_summary_to_new_summary()
    write_inc_coupon_data()
    write_inc_eq_interest_data()
    reload_cache()



if __name__ == '__main__':
    with app.app_context():
        main()

