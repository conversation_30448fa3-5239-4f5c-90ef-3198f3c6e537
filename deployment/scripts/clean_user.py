#-*- coding: utf-8 -*-
import os
import random
import re
import sys
import time
from datetime import date, datetime
from decimal import Decimal
from typing import List, Union
import json
from functools import update_wrapper
from dateutil.tz import UTC
import csv

from sqlalchemy import inspect
from sqlalchemy.exc import IntegrityError

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app import create_app
create_app().app_context().push()

from app.common import Language, Currency
from app.config import config
from app.business import (SPOT_ACCOUNT_ID, PerpetualHistoryDB,
                          PerpetualServerClient, PerpetualSummaryDB,
                          PerpetualSysHistoryDB, PriceManager, ServerClient,
                          TradeHistoryDB, TradeSummaryDB, UserPreferences)
from app.business.amm import get_user_amm_assets, remove_liquidity
from app.business.investment import BalanceTransferOperation
from app.business.margin.transfer import MarginTransferOperation
from app.business.margin.repayment import MarginForceFlatHelper
from app.business.perpetual.balance import perpetual_transfer_out
from app.business.sub_account import SubAccountManager
from app.business.fee_constant import (DEFAULT_MIN_CONTRACT_MAKER_FEE,
                                      DEFAULT_MIN_CONTRACT_TAKER_FEE)
from app.business.lock import CacheLock, LockKeys
from app.business.strategy.grid import terminate_spot_grid_strategy
from app.business.auto_invest import auto_invest_plan_terminated_task
from app.business.pledge.repay import PledgeAccountRepayHelper
from app.business.credit import CreditFlatOperation
from app.business.admin_tag import AdminTagHelper
from app.models import (AdminOperationLog, BalanceUpdateBusiness, ClearedUser, File,
                        AssetInvestmentConfig, KycVerification, UserLiquidity, GiftHistory,
                        LoginHistory, MarginLoanOrder,
                        OperationLog, SecurityToolHistory, SubAccount, MarketMaker,
                        SubAccountAssetTransfer, User, UserLoginState, CreditUser, db,
                        CreditBalance)
from app.models.strategy import UserStrategy
from app.models.pledge import PledgePosition, PledgeRepayHistory
from app.models.auto_invest import AutoInvestPlan
from app.models.referral import (ReferralHistory, ReferralCodeAssetDetail, ReferralAssetDetail, ReferralAssetSummary,
                                 DailyUserReferralSlice, DailyUserReferralTypeSlice, Referral)
from app.models.monthly import MonthlyReferralCodeAssetDetail
from app.caches.auth import UserLoginTokenCache
from app.utils import ExternalDB, amount_to_str, datetime_to_time, timestamp_to_datetime
from app.utils.amount import amount_to_str, quantize_amount
from app.utils.parser import JsonEncoder
from app.utils import batch_iter, AWSBucketPublic
from app.utils import GeoIP
from app.common import get_country


prices = PriceManager.assets_to_usd()


def saferpc(fn, *args, **kwargs):
    for _ in range(20):
        try:
            return fn(*args, **kwargs)
        except:
            print(f'warning: {fn.__name__} failed, retry...')
            time.sleep(5)
    else:
        raise RuntimeError(f'{fn.__name__} failed')

def safe(fn):
    def deco(*args, **kwargs):
        return saferpc(fn, *args, **kwargs)
    return update_wrapper(deco, fn)


class UserManager:

    def __init__(self):
        self.ip_libs = self.prepare_ip_libs()

    def backup_data(self, data: Union[db.Model, List[db.Model]]):
        def _format(v):
            if v is None:
                return ''
            if isinstance(v, str):
                return v
            if isinstance(v, Decimal):
                return amount_to_str(v)
            if isinstance(v, (date, datetime)):
                return str(datetime_to_time(v))
            return str(v)

        if isinstance(data, db.Model):
            data = [data]

        columns = data[0].__table__.columns
        fname = data[0].__tablename__ + '.csv'
        exists = os.path.exists(fname)
        with open(data[0].__tablename__ + '.csv', 'a') as f:
            if not exists:
                f.write(','.join(x.name for x in columns))
                f.write('\n')
            for row in data:
                f.write(','.join('"' + _format(getattr(row, x.name)) + '"' for x in columns))
                f.write('\n')

    def backup_user(self, user_id):
        user = User.query.get(user_id)
        self.backup_data(user)
        kycs = KycVerification.query.filter(KycVerification.user_id == user_id).all()
        if kycs:
            self.backup_data(kycs)

    def clear_user_info(self, user_id):
        user = User.query.get(user_id)
        # 主账户ip
        ip, location, code = self.pick_ip()
        loc = user.registration_location.lower()
        if '中国' in loc or 'china' in loc or user.location_code in ('CHN', 'HKG', 'TWN'):
            user.registration_ip = ip
            user.registration_location = location
            user.location_code = code
        # 基本信息
        user.mobile_country_code = None
        user.mobile_num = None
        user.email = None
        user.kyc_status = User.KYCStatus.NONE
        user.name = ''
        # 子账户ip
        subs = SubAccount.query.filter(SubAccount.main_user_id == user_id).all()
        for sub in subs:
            sub_user = User.query.get(sub.user_id)
            sub_user.registration_ip = user.registration_ip
            sub_user.registration_location = user.registration_location
            sub_user.location_code = user.location_code
            sub_user.name = ''
        db.session.commit()
        # kyc
        KycVerification.query.filter(KycVerification.user_id == user_id).delete(synchronize_session=False)
        # 登录历史
        LoginHistory.query.filter(LoginHistory.user_id == user_id).delete(synchronize_session=False)
        UserLoginState.query.filter(UserLoginState.user_id == user_id).delete(synchronize_session=False)
        # 日志
        OperationLog.query.filter(OperationLog.user_id == user_id).delete(synchronize_session=False)
        AdminOperationLog.query.filter(AdminOperationLog.user_id == user_id).delete(synchronize_session=False)
        SecurityToolHistory.query.filter(SecurityToolHistory.user_id == user_id).delete(synchronize_session=False)

        db.session.commit()
        # 语言
        pref = UserPreferences(user_id)
        pref.currency = Currency.USD
        pref.language = Language.EN_US
        if pref.app_language:
            pref.app_language = Language.EN_US
        if pref.web_language:
            pref.web_language = Language.EN_US


    def clear_refer(self, user_id):
        ReferralHistory.query.filter(ReferralHistory.referrer_id == user_id).delete(synchronize_session=False)
        ReferralHistory.query.filter(ReferralHistory.referree_id == user_id).delete(synchronize_session=False)

        ReferralCodeAssetDetail.query.filter(ReferralCodeAssetDetail.user_id == user_id).delete(synchronize_session=False)

        ReferralAssetDetail.query.filter(ReferralAssetDetail.user_id == user_id).delete(synchronize_session=False)
        ReferralAssetDetail.query.filter(ReferralAssetDetail.referree_id == user_id).delete(synchronize_session=False)

        ReferralAssetSummary.query.filter(ReferralAssetSummary.user_id == user_id).delete(synchronize_session=False)
        ReferralAssetSummary.query.filter(ReferralAssetSummary.referree_id == user_id).delete(synchronize_session=False)

        DailyUserReferralSlice.query.filter(DailyUserReferralSlice.user_id == user_id).delete(synchronize_session=False)
        MonthlyReferralCodeAssetDetail.query.filter(MonthlyReferralCodeAssetDetail.user_id == user_id).delete(synchronize_session=False)
        DailyUserReferralTypeSlice.query.filter(DailyUserReferralTypeSlice.user_id == user_id).delete(synchronize_session=False)

        refs = Referral.query.filter(Referral.user_id == user_id).all()
        for ref in refs:
            ReferralCodeAssetDetail.query.filter(ReferralCodeAssetDetail.referral_id == ref.id).delete(synchronize_session=False)
            ReferralAssetDetail.query.filter(ReferralAssetDetail.referral_id == ref.id).delete(synchronize_session=False)
            ReferralAssetSummary.query.filter(ReferralAssetSummary.referral_id == ref.id).delete(synchronize_session=False)
            DailyUserReferralSlice.query.filter(DailyUserReferralSlice.referral_id == ref.id).delete(synchronize_session=False)
            MonthlyReferralCodeAssetDetail.query.filter(MonthlyReferralCodeAssetDetail.referral_id == ref.id).delete(synchronize_session=False)
            DailyUserReferralTypeSlice.query.filter(DailyUserReferralTypeSlice.referral_id == ref.id).delete(synchronize_session=False)

        Referral.query.filter(Referral.user_id == user_id).delete(synchronize_session=False)

        db.session.commit()

    def clear_gift_history(self, user_id):
        # 不确定返佣和交易挖矿类型，全部删掉
        GiftHistory.query.filter(GiftHistory.user_id == user_id).delete(synchronize_session=False)
        db.session.commit()

    def clear_kyc_photo(self, user_id):
        rows = File.query.filter(File.user_id == user_id).with_entities(File.key).all()
        if not rows:
            return
        for row in rows:
            if not AWSBucketPublic.delete_file(row.key):  # 线上的public桶有删除权限吗？
                raise RuntimeError('delete file failed')
        try:
            File.query.filter(File.user_id == user_id).delete(synchronize_session=False)
        except IntegrityError: # 可能有其他业务上传的文件，外键关联无法删除
            db.session.rollback()
            print("ignore user", user_id, "files")
            return
        db.session.commit()


    def prepare_ip_libs(self):
        blacklist = ('中国', '香港', '台湾', '美国', 'china', 'hong kong', 'taiwan', 'america', 'united states', 'iran')
        last = 1 << 32
        result = []
        for _ in range(10):
            r = User.query.filter(User.id < last) \
                    .order_by(User.id.desc()) \
                    .with_entities(User.registration_ip, User.registration_location) \
                    .limit(10000).all()
            if not r:
                break
            last = r[-1][0]

            for ip, location in r:
                if not location:
                    continue
                location = location.lower()
                if any(x in location for x in blacklist):
                    continue
                country = get_country(GeoIP(ip).country_code)
                if not country:
                    continue
                code = country.iso_3
                if code in ('CHN', 'HKG', 'TWN', 'USA', 'IRN'):
                    continue
                result.append((ip, location, code))
        return result

    def pick_ip(self):
        i = random.randint(0, len(self.ip_libs) - 1)
        return self.ip_libs[i]


class UserExpoter:

    @classmethod
    def is_china_email(cls, email):
        black_emails = ('qq.com', '163.com', 'sohu.com', 'foxmail.com', 'sina.com', 'sina.cn', '139.com', '126.com')
        return email and email.split('@')[-1].lower() in black_emails
    
    @classmethod
    def get_market_makers(cls):
        rows = MarketMaker.query.filter(MarketMaker.status == MarketMaker.StatusType.PASS) \
                          .with_entities(MarketMaker.user_id).all()
        return {x for x, in rows}
    
    @classmethod
    def get_whitelist(cls):
        return AdminTagHelper.query_clear_whitelist_user_ids()

    @classmethod
    def get_cleared_list(cls):
        rows = ClearedUser.query.filter(ClearedUser.valid == True).all()
        ow, cl = set(), set()
        for row in rows:
            if row.status == ClearedUser.Status.FORBIDDEN:
                cl.add(row.user_id)
            else:
                ow.add(row.user_id)
        return ow, cl

    @classmethod
    def find_users(cls):
        """只包含主账户"""
        last = 0
        result = set()
        while True:
            rows = User.query.filter(User.id > last) \
                .filter(User.user_type != User.UserType.SUB_ACCOUNT) \
                .order_by(User.id).with_entities(User.id, User.email, User.location_code, User.registration_location, User.mobile_country_code) \
                .limit(10000).all()
            for row in rows:
                if cls.is_china_email(row.email):
                    result.add(row.id)
                    continue
                if row.mobile_country_code == 86:
                    result.add(row.id)
                    continue
                if row.location_code == 'CHN' or row.location_code == 'CN':
                    result.add(row.id)
                    continue
                loc = row.registration_location.lower()
                if '中国' in loc or 'china' in loc:
                    result.add(row.id)
                    continue

            if len(rows) < 10000:
                break
            last = rows[-1].id

        last = 0
        while True:
            rows = KycVerification.query.filter(KycVerification.id > last) \
                .order_by(KycVerification.id).with_entities(KycVerification.id, KycVerification.user_id, KycVerification.country) \
                .limit(10000).all()
            for row in rows:
                if row.country == 'CHN' or row.country == 'CN':
                    result.add(row.user_id)
                    continue

            if len(rows) < 10000:
                break
            last = rows[-1].id

        return result
    
    @classmethod
    def get_last_history_time(cls, user_ids):
        c = ServerClient()

        def get_last_history(user_id):
            page = 1
            while True:
                rows = saferpc(c.get_user_balance_history, user_id, page=page, limit=100)
                for x in rows:
                    if x['business'] in ('system', 'gift'):
                        continue
                    return x
                if not rows or len(rows) < 100:
                    break
                page += 1
            return None

        v = None
        for uid in user_ids:
            history = get_last_history(uid)
            if not history:
                continue
            if v is None or v > history['time']:
                v = history['time']
        if v is not None:
            v = timestamp_to_datetime(v)
        return v


    @classmethod
    def get_user_group(cls, user_id, maker_list, whitelist, ow_list, cleared_list):
        subs = SubAccount.query.filter(SubAccount.main_user_id == user_id).all()
        user_ids = [user_id] + [x.user_id for x in subs]
        balance = cet_amount = 0
        for sub_id in user_ids:
            b, c = BalanceManager.get_user_balance(sub_id)
            balance += b
            cet_amount += c

        if user_id in maker_list:
            return 'big_customer', balance, 'market maker'

        if cet_amount > 1000000 or balance > 200000:
            return 'big_customer', balance, 'high balance'

        if user_id in whitelist:
            return 'normal', balance, 'in whitelist'

        if user_id in cleared_list:
            return 'clear', balance, 'in clear list'

        def low_balance():
            return balance < 1

        def china_kyc():
            kyc = KycVerification.query.filter(KycVerification.user_id == user_id,
                                               KycVerification.status == KycVerification.Status.PASSED).first()
            return not kyc or kyc.country in ('CHN', 'CN')

        def active():
            last_login = LoginHistory.query.filter(LoginHistory.user_id == user_id) \
                .order_by(LoginHistory.id.desc()).first()
            last_history_time = cls.get_last_history_time(user_ids)
            rs = []
            if last_login:
                rs.append(last_login.created_at)
            if last_history_time:
                rs.append(last_history_time)
            if rs:
                return max(rs) >= datetime(2021, 1, 1, tzinfo=UTC)
            return False

        def only_withdraw():
            return user_id in ow_list

        if china_kyc():
            if low_balance():
                return 'clear', balance, 'low balance'
            else:
                if not active():
                    return 'clear', balance, 'not active'
                if only_withdraw():
                    return 'only_withdraw', balance, ''
                return 'normal', balance, ''
        else:
            if only_withdraw():
                return 'only_withdraw', balance, ''
            else:
                return 'normal', balance, ''

    @classmethod
    def export_users(cls):
        """
        group: big_customer, clear, only_withdraw, normal
        """
        print('find user...')
        user_ids = cls.find_users()
        print('total', len(user_ids), 'users')
        maker_list = cls.get_market_makers()
        whitelist = cls.get_whitelist()
        ow_list, cleared_list = cls.get_cleared_list()
        result = []
        count = 0
        for user_id in user_ids:
            count += 1
            if count % 100 == 0:
                print(count, '/', len(user_ids), sep='')
            group, balance, reason = cls.get_user_group(user_id, maker_list, whitelist, ow_list, cleared_list)
            result.append((user_id, group, balance, reason))
        with open('user.csv', 'w') as fp:
            fp.write('user_id,group,balance,reason\n')
            for user_id, group, balance, reason in result:
                balance = amount_to_str(balance, 2)
                fp.write(f'{user_id},{group},{balance},{reason}\n')

    @classmethod
    def load_users(cls):
        result = []
        with open('user.csv') as fp:
            fp.readline()
            while (line := fp.readline()):
                if not (line := line.strip()):
                    continue
                user_id, group, balance, reason = line.split(',')
                user_id = int(user_id)
                result.append((user_id, group))
        if cls.has_invalid_user({int(x) for x, g in result if g in ('clear', 'only_withdraw')}):
            raise ValueError('system or credit user cannot be cleared')
        return result

    @classmethod
    def has_invalid_user(cls, user_ids):
        system_users = {v for k, v in config.items() if k.endswith('_USER_ID')}
        credit_users = CreditUser.query.filter(
                CreditUser.status == CreditUser.StatusType.PASS,
                CreditUser.unfinished_credit_balance > 0
            ).with_entities(CreditUser.user_id).all()
        credit_users = {x for x, in credit_users}

        invalid_users = (system_users | credit_users) & set(user_ids)
        if invalid_users:
            print('this users cannot be cleared: %s' % invalid_users)
            return True
        return False


START = 1546300800 # 2019-01-01

class ServerDBManager:

    def __init__(self, DB: ExternalDB, tables):
        r = []
        for table in tables:
            if '%' in table:
                r.extend(self.list_all_tables(DB, table))
            else:
                r.append(table)
        self.DB = DB
        self.tables = tables

    @staticmethod
    def delete_from_table(DB: ExternalDB, table, user_id):
        if table.startswith('order_history'):
            field = "create_time"
        else:
            field = "time"
        cur = DB.cursor()
        if table.startswith('balance_history'):
            cur.execute(f'DELETE FROM `{table}` WHERE user_id = {user_id} AND account = 0 AND time < {START}')
            cur.execute(f'DELETE FROM `{table}` WHERE user_id = {user_id} AND account = 0 AND business = "refer"')
        else:
            cur.execute(f'DELETE FROM `{table}` WHERE user_id = {user_id} AND {field} < {START}')
        cur.close()

    @staticmethod
    def list_all_tables(DB: ExternalDB, table):
        m = re.compile(table.replace('%', r'\d+'))
        cur = DB.cursor()
        cur.execute(f"show tables like '{table}'")
        r = cur.fetchall()
        return [x for x, in r if m.fullmatch(x)]

    def delete_user_id(self, user_id):
        for table in self.tables:
            self.delete_from_table(self.DB, table, user_id)


class HistoryDBManager:

    def __init__(self, DB: ExternalDB, tables):
        self.DB = DB
        self.tables = tables

    def delete_user_id(self, user_id):
        for table in self.tables:
            DB, table = self.DB.user_to_db_and_table(user_id, table)
            ServerDBManager.delete_from_table(DB, table, user_id)


def init_db_manager():
    # ServerDBManager(TradeSummaryDB, ['user_trade_summary_%', 'user_fee_summary_%', 'user_detail_%'])
    return HistoryDBManager(TradeHistoryDB, ['balance_history', 'order_history', 'user_deal_history'])


class BalanceManager:

    @classmethod
    def get_user_balance(cls, user_id):
        """不含子账户"""
        cet = 0
        r = saferpc(ServerClient().get_user_accounts_balances, user_id)
        spot = 0
        for _, vs in r.items():
            for asset, v in vs.items():
                total = sum(v.values(), 0)
                spot += prices.get(asset, 0) * total
                if asset == 'CET':
                    cet += total

        r = saferpc(PerpetualServerClient().get_user_balances, user_id)
        perpetual = 0
        for asset, v in r.items():
            total = sum(v.values(), 0)
            perpetual += prices.get(asset, 0) * total
            if asset == 'CET':
                cet += total

        amm = 0
        r = get_user_amm_assets(user_id)
        for asset, v in r.items():
            amm += prices.get(asset, 0) * v
            if asset == 'CET':
                cet += v

        return spot + perpetual + amm, cet

    @classmethod
    def cancel_spot_order(cls, user_id):
        c = ServerClient()
        while True:
            orders = saferpc(c.user_pending_orders, user_id, account_id=-1, page=1, limit=100)
            if orders:
                print(f'cancel {user_id} spot order')
            for order in orders:
                if order['source'] == 'system':
                    continue
                c.cancel_user_order(user_id, order['market'], order['id'])
            if len(orders) < 100:
                break

        while True:
            orders = saferpc(c.user_pending_stop_orders, user_id, account_id=-1, page=1, limit=100)
            if orders:
                print(f'cancel {user_id} spot stop order')
            for order in orders:
                c.cancel_user_stop_order(user_id, order['market'], order['id'])
            if not orders:
                break

    @classmethod
    def cancel_perpetual_order(cls, user_id):
        c = PerpetualServerClient()
        while True:
            orders = saferpc(c.pending_order, user_id, None, limit=100)['records']
            if orders:
                print(f'cancel {user_id} perpetual order')
            for order in orders:
                if order['source'] == 'system':
                    continue
                c.cancel_order(user_id, order['market'], order['order_id'])
            if len(orders) < 100:
                break

        while True:
            orders = saferpc(c.pending_stop, user_id, None, limit=100)['records']
            if orders:
                print(f'cancel {user_id} perpetual stop order')
            for order in orders:
                c.cancel_stop(user_id, order['market'], order['order_id'])
            if not orders:
                break

    @classmethod
    def cancel_amm(cls, user_id):
        rows = UserLiquidity.query.filter(UserLiquidity.user_id == user_id, UserLiquidity.liquidity > 0).all()
        if rows:
            print(f'remove {user_id} amm liquidity')
        for row in rows:
            remove_liquidity(row.market, user_id)

    @classmethod
    def close_margin_postion(cls, user_id):
        orders = MarginLoanOrder.query.filter(MarginLoanOrder.user_id == user_id,
                                              MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS).all()
        if orders:
            print(f'close {user_id} margin position')
        tool = MarginForceFlatHelper(user_id)
        for order in orders:
            tool.add_order(order)
        tool.force_flat()

    @classmethod
    def close_perpetual_position(cls, user_id):
        c = PerpetualServerClient()
        positions = saferpc(c.position_pending, user_id)
        if positions:
            print(f'close {user_id} perpetual position')
            saferpc(c.position_close_all, 
                user_id=user_id,
                taker_fee_rate=str(DEFAULT_MIN_CONTRACT_TAKER_FEE),
                maker_fee_rate=str(DEFAULT_MIN_CONTRACT_MAKER_FEE),
                source='system'
            )

    @classmethod
    def cancel_strategy_order(cls, user_id):
        rows = UserStrategy.query.filter(UserStrategy.user_id == user_id,
                                         UserStrategy.status != UserStrategy.Status.TERMINATED).all()
        if rows:
            print(f'cancel {user_id} strategy order')
        for row in rows:
            if row.type == UserStrategy.Type.SPOT_GRID:
                terminate_spot_grid_strategy(row.id)
            elif row.type == UserStrategy.Type.AUTO_INVEST:
                plan = AutoInvestPlan.query.filter(AutoInvestPlan.strategy_id == row.id).first()
                auto_invest_plan_terminated_task(plan.id)

    @classmethod
    def close_pledge_position(cls, user_id):
        positions = PledgePosition.query.filter(PledgePosition.user_id == user_id,
                                                PledgePosition.status == PledgePosition.Status.BORROWING).all()
        if positions:
            print(f'close {user_id} pledge position')
        for position in positions:
            with CacheLock(key=LockKeys.user_pledge(position.user_id, position.loan_asset)):
                db.session.rollback()
                PledgeAccountRepayHelper(position.id, PledgeRepayHistory.Type.REPAY_BY_PLEDGE).start_close_repay()

    @classmethod
    def transfer_investment_balance(cls, user_id):
        c = ServerClient()
        balance = saferpc(c.get_user_balances, user_id, account_id=AssetInvestmentConfig.ACCOUNT_ID)
        for asset, vs in balance.items():
            if vs['frozen'] > 0:
                raise ValueError(f'user {user_id} has frozen investment balance ')
            if vs['lock'] > 0:
                raise ValueError(f'user {user_id} has lock investment balance')
            if vs['available'] > 0:
                operation = BalanceTransferOperation(
                    user=User.query.get(user_id),
                    transfer_from=AssetInvestmentConfig.ACCOUNT_ID,
                    transfer_to=SPOT_ACCOUNT_ID,
                    asset=asset,
                    amount=vs['available'])
                print(f"transfer {user_id} investment {vs['available']} {asset} to spot")
                operation.transfer()

    @classmethod
    def transfer_margin_balance(cls, user_id):
        c = ServerClient()
        balance = saferpc(c.get_user_accounts_balances, user_id)
        for account, bs in balance.items():
            if not SPOT_ACCOUNT_ID < int(account) < AssetInvestmentConfig.ACCOUNT_ID:
                continue
            for asset, vs in bs.items():
                if vs['frozen'] > 0:
                    raise ValueError(f'user {user_id} has frozen margin balance ')
                if vs['lock'] > 0:
                    raise ValueError(f'user {user_id} has lock margin balance')
                if vs['available'] > 0:
                    print(f"transfer {user_id} margin {vs['available']} {asset} to spot")
                    operation = MarginTransferOperation(
                        user_id=user_id,
                        transfer_from=int(account),
                        transfer_to=SPOT_ACCOUNT_ID,
                        asset=asset,
                        amount=vs['available'])
                    operation.transfer()

    @classmethod
    def transfer_perpetual_balance(cls, user_id):
        q = lambda v: quantize_amount(v, 8)
        c = PerpetualServerClient()
        balance = saferpc(c.get_user_balances, user_id)
        for asset, vs in balance.items():
            if q(vs['frozen']) > 0:
                raise ValueError(f'user {user_id} has frozen perpetual balance ')
            if q(vs['margin']) > 0:
                raise ValueError(f'user {user_id} has margin perpetual balance')
            if q(vs['profit_unreal']) > 0:
                raise ValueError(f'user {user_id} has profit_unreal perpetual balance')
            if q(vs['available']) > 0:
                print(f"transfer {user_id} perpetual {vs['available']} {asset} to spot")
                if not perpetual_transfer_out(user_id, asset, q(vs['available'])):
                    raise RuntimeError('perpetual transfer failed')

    @classmethod
    def transfer_sub_balance_to_main(cls, user_id, main_user_id):
        """归集用户的资产到现货账户后，此方法划转子账户的现货账户资产到主账户"""
        c = ServerClient()
        balance = saferpc(c.get_user_balances, user_id)
        for asset, vs in balance.items():
            if vs['frozen'] > 0:
                raise ValueError(f'user {user_id} has frozen spot balance ')
            if vs['lock'] > 0:
                raise ValueError(f'user {user_id} has lock spot balance')
            if vs['available'] > 0:
                print(f"transfer {user_id} subaccount {vs['available']} {asset} to main")
                SubAccountManager.transfer_asset(
                    main_user_id=main_user_id,
                    source_user_id=user_id,
                    source_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                    target_user_id=main_user_id,
                    target_account_type=SubAccountAssetTransfer.AccountType.SPOT,
                    asset=asset,
                    amount=vs['available'],
                )

    @classmethod
    def get_user_spot_balance(cls, user_id):
        """获取主账户现货账户资产，应先归集子账户的资产到主账户"""
        c = ServerClient()
        balance = saferpc(c.get_user_balances, user_id)
        r = {}
        for asset, vs in balance.items():
            if vs['frozen'] > 0:
                raise ValueError(f'user {user_id} has frozen spot balance ')
            if vs['lock'] > 0:
                raise ValueError(f'user {user_id} has lock spot balance')
            if vs['available'] > 0:
                r[asset] = vs['available']
        return r

    @classmethod
    def transfer_balance_to_admin(cls, user_id):
        """归集子账户的资产到主账户后，该方法划转现货资产到管理员账户"""
        admin_user_id = 5255992
        c = ServerClient()
        balance = saferpc(c.get_user_balances, user_id)
        for asset, vs in balance.items():
            if vs['frozen'] > 0:
                raise ValueError(f'user {user_id} has frozen spot balance ')
            if vs['lock'] > 0:
                raise ValueError(f'user {user_id} has lock spot balance')
            if vs['available'] > 0:
                print(f"transfer {user_id} {vs['available']} {asset} to admin")
                bid = BalanceUpdateBusiness.new_id(user_id, asset, -vs['available'])
                try:
                    saferpc(c.add_user_balance, user_id, asset, -vs['available'], 'system', bid)
                except:
                    detail = {'user_id': user_id, 'asset': asset, 'amount': amount_to_str(-vs['available']), 'bid': bid}
                    print('transfer to admin deduct user balance failed: %s' % json.dumps(detail))
                    raise
                bid = BalanceUpdateBusiness.new_id(admin_user_id, asset, vs['available'])
                try:
                    saferpc(c.add_user_balance, admin_user_id, asset, vs['available'], 'system', bid)
                except:
                    detail = {'user_id': admin_user_id, 'asset': asset, 'amount': amount_to_str(vs['available']), 'bid': bid}
                    print('transfer to admin add admin balance failed: %s' % json.dumps(detail))
                    raise

    @classmethod
    def transfer_to_credit(cls, from_user, to_user):
        """划转欠款的币到授信账户"""
        c = ServerClient()
        balances = c.get_user_balances(from_user)
        rows = CreditBalance.query.filter(CreditBalance.user_id == to_user,
                                          CreditBalance.unflat_amount > 0).all()
        for row in rows:
            asset = row.asset
            amount = row.unflat_amount
            if asset not in balances:
                print(f'skip offline asset {asset}')
                continue
            amount = min(balances[asset]['available'], amount)
            if amount <= 0:
                continue
            print(f"transfer {amount} {asset}")
            bid = BalanceUpdateBusiness.new_id(from_user, asset, -amount)
            try:
                saferpc(c.add_user_balance, from_user, asset, -amount, 'system', bid)
            except:
                detail = {'user_id': from_user, 'asset': asset, 'amount': amount_to_str(-amount), 'bid': bid}
                print('transfer to admin deduct user balance failed: %s' % json.dumps(detail))
                raise
            bid = BalanceUpdateBusiness.new_id(to_user, asset, amount)
            try:
                saferpc(c.add_user_balance, to_user, asset, amount, 'system', bid)
            except:
                detail = {'user_id': to_user, 'asset': asset, 'amount': amount_to_str(amount), 'bid': bid}
                print('transfer to admin add admin balance failed: %s' % json.dumps(detail))
                raise

    @classmethod
    def flat_credit(cls, user_id):
        """归还授信"""
        c = ServerClient()
        balances = c.get_user_balances(user_id)
        rows = CreditBalance.query.filter(CreditBalance.user_id == user_id,
                                          CreditBalance.unflat_amount > 0).all()
        for row in rows:
            asset = row.asset
            amount = row.unflat_amount
            if asset not in balances:
                print(f'skip offline asset {asset}')
                continue
            amount = min(balances[asset]['available'], amount)
            if amount <= 0:
                continue
            print(f"flat {amount} {asset}")
            CreditFlatOperation(user_id, asset, amount, None).flat()

    @classmethod
    def transfer_to(cls, from_user, to_user, assets):
        """划转资产到指定账户"""
        c = ServerClient()
        balances = c.get_user_balances(from_user)
        for asset in assets:
            amount = balances[asset]['available']
            if amount <= 0:
                continue
            print(f"transfer {amount} {asset}")
            bid = BalanceUpdateBusiness.new_id(from_user, asset, -amount)
            try:
                saferpc(c.add_user_balance, from_user, asset, -amount, 'system', bid)
            except:
                detail = {'user_id': from_user, 'asset': asset, 'amount': amount_to_str(-amount), 'bid': bid}
                print('transfer to admin deduct user balance failed: %s' % json.dumps(detail))
                raise
            bid = BalanceUpdateBusiness.new_id(to_user, asset, amount)
            try:
                saferpc(c.add_user_balance, to_user, asset, amount, 'system', bid)
            except:
                detail = {'user_id': to_user, 'asset': asset, 'amount': amount_to_str(amount), 'bid': bid}
                print('transfer to admin add admin balance failed: %s' % json.dumps(detail))
                raise


class ClearUserTool:

    def __init__(self, user_id):
        self.main_user_id = user_id
        subs = SubAccount.query.filter(SubAccount.main_user_id == user_id).all()
        self.sub_user_ids = [sub.user_id for sub in subs]
        self.all_user_ids = [self.main_user_id, *self.sub_user_ids]

    def set_cleared(self):
        row = ClearedUser.query.filter(ClearedUser.user_id == self.main_user_id).first()
        if not row:
            row = ClearedUser(
                user_id=self.main_user_id,
                status=ClearedUser.Status.FORBIDDEN,
                valid=True,
                remark='cn clean'
            )
            db.session.add(row)
        else:
            row.status = ClearedUser.Status.FORBIDDEN
            row.remark = 'cn clean'
            row.valid = True
        db.session.commit()

        tokens = UserLoginTokenCache(self.main_user_id).clear_tokens()
        UserLoginState.clear_tokens(tokens)

    def transfer_to_spot_check(self, user_id):
        row = UserStrategy.query.filter(UserStrategy.user_id == user_id,
                                         UserStrategy.status != UserStrategy.Status.TERMINATED).first()
        if row:
            raise ValueError(f'user {user_id} has strategy')

        row = UserLiquidity.query.filter(UserLiquidity.user_id == user_id, UserLiquidity.liquidity > 0).first()
        if row:
            raise ValueError(f'user {user_id} has amm liquidity')

        margin_order = MarginLoanOrder.query.filter(MarginLoanOrder.user_id == user_id,
                                                    MarginLoanOrder.status.in_((MarginLoanOrder.StatusType.PASS, MarginLoanOrder.StatusType.BURST))).first()
        if margin_order:
            raise ValueError(f'user {user_id} has margin loan order')
        
        porision = PledgePosition.query.filter(PledgePosition.user_id == user_id,
                                               PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES)).first()
        if porision:
            raise ValueError(f'user {user_id} has pledge position')

        positions = saferpc(PerpetualServerClient().position_pending, user_id)
        if positions:
            raise ValueError(f'user {user_id} has perpetual position')
        
        c = ServerClient()
        orders = saferpc(c.user_pending_orders, user_id, account_id=-1, page=1, limit=1)
        orders2 = saferpc(c.user_pending_stop_orders, user_id, account_id=-1, page=1, limit=1)
        if orders or orders2:
            raise ValueError(f'user {user_id} has spot order')
        
        c = PerpetualServerClient()
        orders = saferpc(c.pending_order, user_id, None, limit=1)['records']
        orders2 = saferpc(c.pending_stop, user_id, None, limit=1)['records']
        if orders or orders2:
            raise ValueError(f'user {user_id} has perpetual order')

    def transfer_to_main_check(self, user_id):
        c = ServerClient()
        balance = saferpc(c.get_user_accounts_balances, user_id)
        for account, bs in balance.items():
            if int(account) == 0:
                continue
            for _, vs in bs.items():
                if vs['available'] > 0:
                    raise ValueError(f'user {user_id} has balance in account {account}')

    def step1(self):
        """撤销用户所有未完结的业务"""
        self.set_cleared()
        for user_id in self.all_user_ids:
            BalanceManager.cancel_spot_order(user_id)
            BalanceManager.cancel_perpetual_order(user_id)
            BalanceManager.cancel_strategy_order(user_id)
            BalanceManager.cancel_amm(user_id)
            BalanceManager.close_margin_postion(user_id)
            BalanceManager.close_perpetual_position(user_id)
            BalanceManager.close_pledge_position(user_id)

    def step2(self):
        """划转用户的资产到现货账户"""
        for user_id in self.all_user_ids:
            self.transfer_to_spot_check(user_id)
            BalanceManager.transfer_investment_balance(user_id)
            BalanceManager.transfer_margin_balance(user_id)
            BalanceManager.transfer_perpetual_balance(user_id)

    def step3(self):
        """划转子账户资产到主账户"""
        self.transfer_to_main_check(self.main_user_id)
        for user_id in self.sub_user_ids:
            self.transfer_to_main_check(user_id)
            BalanceManager.transfer_sub_balance_to_main(user_id, self.main_user_id)

    @classmethod
    def step4(cls, user_ids):
        """备份主账户资产"""
        emails = {}
        for uids in batch_iter(user_ids, 100):
            rs = User.query.filter(User.id.in_(uids)).with_entities(User.id, User.email).all()
            emails.update(rs)
        count = 0
        total = len(user_ids)
        with open('user_balance.csv', 'w') as fp:
            w = csv.writer(fp)
            w.writerow(['user_id', 'email', 'balance', 'total_usd'])
            for user_id in user_ids:
                count += 1
                if count % 100 == 0:
                    print('progress: ', count, '/', total, sep='')
                balance = BalanceManager.get_user_spot_balance(user_id)
                if not balance:
                    continue
                total_usd = sum(prices.get(a, 0) * v for a, v in balance.items())
                total_usd = amount_to_str(total_usd, 2)
                w.writerow([user_id, emails[user_id] or '', json.dumps(balance, cls=JsonEncoder), total_usd])

    @classmethod
    def step5(cls, user_id):
        """划转主账户资产到admin账户"""
        BalanceManager.transfer_balance_to_admin(user_id)

    def delete_balance_history(self):
        for user_id in self.all_user_ids:
            for E_DB in (TradeHistoryDB, PerpetualHistoryDB):
                DB, table = E_DB.user_to_db_and_table(user_id, 'balance_history')
                cur = DB.cursor()
                cur.execute(f"DELETE from {table} where user_id={user_id}")
