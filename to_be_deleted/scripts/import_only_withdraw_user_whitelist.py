# -*- coding: utf-8 -*-
import os
import sys

import click
from werkzeug.datastructures import FileStorage

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


@click.command()
@click.argument('import_file', type=click.File('rb'))
@click.option('--i-know-what-i-am-doing', is_flag=True)
def main(import_file, i_know_what_i_am_doing):
    # OnlyWithdrawalWhitelistUser 不再使用，在Admin Tag中使用员工标签来处理
    return
    # return
    # from app.models import User, OnlyWithdrawalWhitelistUser, db
    # from app.utils.importer import get_table_rows
    # head_list = ["email", "remark"]
    # data = get_table_rows(FileStorage(import_file), head_list, False)
    # for row in data:
    #     user = User.query.filter(User.email == row['email']).first()
    #     if not user:
    #         print('user %s not exists' % row['email'])
    #         return
    #     if not i_know_what_i_am_doing:
    #         continue
    #     record = OnlyWithdrawalWhitelistUser.query.filter(OnlyWithdrawalWhitelistUser.user_id == user.id).first()
    #     if record:
    #         record.remark = row['remark']
    #         record.status = OnlyWithdrawalWhitelistUser.Status.VALID
    #     else:
    #         db.session.add(OnlyWithdrawalWhitelistUser(
    #             user_id=user.id,
    #             status=OnlyWithdrawalWhitelistUser.Status.VALID,
    #             remark=row['remark']
    #         ))
    #     db.session.commit()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
